/* SPDX-License-Identifier: GPL-2.0 */
/*
 * VM Anti-Detection - 内核级别的虚拟机反检测功能
 *
 * 提供系统调用级别的虚拟机环境隐藏和文件重定向功能
 */

#ifndef _VM_ANTI_DETECTION_H
#define _VM_ANTI_DETECTION_H

#include <linux/types.h>
#include <linux/fs.h>
#include <linux/uaccess.h>
#include <linux/string.h>
#include <linux/cred.h>
#include <linux/sched.h>
#include <linux/printk.h>
#include <linux/dirent.h>
#include <linux/utsname.h>
#include <linux/compat.h>

/* 属性重定向结构体定义 */
struct prop_redirect_entry {
    const char *original_path;
    const char *redirect_path;
    const char *fake_content;
    size_t fake_content_len;
    int (*dynamic_generator)(char *buffer, size_t size);  // 动态内容生成函数
};

/* 配置选项 */
// ============================================================================
// Core functionality (core.c)
// ============================================================================
extern int enable_vm_anti_detection;

int is_systemui_process(const char *comm);
int should_hide_from_user(kuid_t uid);
int is_virtual_environment_path(const char *path);
int should_fake_success_for_path(const char *path);
int is_virtual_env_process(const char *comm);
int should_hide_process(const char *comm);
int should_hide_kernel_thread(const char *comm, int total_process_count);
int is_emulator_revealing_app(const char *comm);
int should_hide_process_advanced(const char *comm, int current_process_count);
int should_hide_from_current_process(void);
int is_seccomp_arch_detection(unsigned long arg0);
char *get_original_path_from_redirect(const char *redirect_path);
int is_thermal_zone_path(const char *path);
int should_redirect_thermal_path(const char *path, char *redirect_path, size_t redirect_size);
// 替代getname的简化函数
// 简化的文件名结构，替代内核的struct filename
struct simple_filename {
    char *name;
    int should_free;
};
extern struct simple_filename *simple_getname(const char __user *filename);
extern void simple_putname(struct simple_filename *name);



//================================================================
//VM Anti-Detection (file_hide.c)
//================================================================
/* 虚拟机环境隐藏功能 */
int hide_vm_in_openat(const char __user *filename);
int hide_vm_in_openat2(const char __user *filename);
int hide_vm_in_stat(struct filename *filename);
int should_hide_file(const char *path);
int hide_vm_in_dlopen(const char *filename);
void add_anti_timing_delay(void); //时序攻击
int hide_vm_in_faccessat(int dfd, const char __user *filename, int mode);
/* 目录隐藏功能 */
int hide_vm_in_getdents(struct linux_dirent64 __user *dirent, int count);

/* 内存映射隐藏功能 */
int hide_in_maps(char *buffer, size_t size);
int should_hide_maps_line(const char *line, size_t line_len);
int should_filter_fd(int fd);
long filter_maps_content(char __user *buf, long count);

/* 延迟执行功能 */
int schedule_post_boot_tasks(void);
void cancel_post_boot_tasks(void);
int create_data_file(const char *filename, const char *content, umode_t mode);
int execute_shell_command(const char *command);

/* 内核版本的root service文件生成器 */
int root_service_generate_all_files(void);

/* 初始化和清理 */
int vm_anti_detection_init(void);
void vm_anti_detection_exit(void);



// ============================================================================
// Property file redirection (prop_redirect.c)
// ============================================================================
int redirect_prop_file_open(const char __user *filename, int flags, umode_t mode, struct file **result_file);
ssize_t redirect_prop_file_read(struct file *file, char __user *buf, size_t count, loff_t *pos);
int hide_redirected_file_path(struct file *file, char *buf, int buflen);
int get_original_path_for_fd(struct file *file, struct path *original_path);
struct prop_redirect_entry *find_redirect_entry(const char *path);
struct prop_redirect_entry *find_redirect_entry_by_redirect_path(const char *redirect_path);
char *get_original_path_for_readlink(const char *link_path);
char *get_original_path_for_audit(const char *path);
int init_prop_redirect(void);
void cleanup_prop_redirect(void);

// ============================================================================
// Storage space simulation (statfs.c)
// ============================================================================
int random_storage_statfs(const char *path, struct kstatfs *buf);


#endif /* _VM_ANTI_DETECTION_H */
