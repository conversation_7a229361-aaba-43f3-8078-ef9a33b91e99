// SPDX-License-Identifier: GPL-2.0
/*
 * Magisk Hide Statfs
 *
 * Handle storage space simulation for Android device emulation
 */

#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/fs.h>
#include <linux/statfs.h>
#include <linux/uaccess.h>
#include <linux/string.h>
#include <linux/slab.h>
#include <linux/cred.h>
#include <linux/sched.h>
#include <linux/time.h>
#include <linux/random.h>
#include <linux/magic.h>
#include "magisk_hide.h"

// 真实手机存储参数配置
#define DEFAULT_TOTAL_SIZE (256ULL * 1024 * 1024 * 1024)  // 256GB (现代旗舰机标配)
#define DEFAULT_BASE_AVAILABLE_SIZE (120ULL * 1024 * 1024 * 1024)  // 基础120GB可用
#define DEFAULT_RANDOM_RANGE (16ULL * 1024 * 1024 * 1024)   // ±8GB随机范围
#define DEFAULT_BLOCK_SIZE 4096

// 支持的存储容量选项 (GB)
static const u64 supported_storage_sizes[] = {
    64ULL * 1024 * 1024 * 1024,   // 64GB
    128ULL * 1024 * 1024 * 1024,  // 128GB
    256ULL * 1024 * 1024 * 1024,  // 256GB
    512ULL * 1024 * 1024 * 1024,  // 512GB
    1024ULL * 1024 * 1024 * 1024, // 1TB
};

// 对应的可用空间比例 (百分比，避免浮点运算)
static const int available_ratios_percent[] = {
    50,  // 64GB -> ~32GB可用 (50%)
    48,  // 128GB -> ~61GB可用 (48%)
    47,  // 256GB -> ~120GB可用 (47%)
    46,  // 512GB -> ~235GB可用 (46%)
    45,  // 1TB -> ~460GB可用 (45%)
};

// 存储模拟配置
static struct {
    u64 total_size;
    u64 base_available_size;
    u64 random_range;
    int enable_randomization;
    int storage_profile;  // 0=64GB, 1=128GB, 2=256GB, 3=512GB, 4=1TB
    int initialized;
} storage_config = {
    .total_size = DEFAULT_TOTAL_SIZE,
    .base_available_size = DEFAULT_BASE_AVAILABLE_SIZE,
    .random_range = DEFAULT_RANDOM_RANGE,
    .enable_randomization = 1,  // 默认启用随机化
    .storage_profile = 2,       // 默认256GB配置
    .initialized = 0
};

// 存储路径匹配 - 支持更多Android存储路径
static const char* emulated_storage_paths[] = {
    "/storage/emulated/0",
    "/storage/emulated",
    "/data/media/0",
    "/data/media",
    "/sdcard",
    "/storage/self/primary",
    "/mnt/sdcard",
    "/android_asset",
    "/data/data",
    "/data/user/0",
    NULL
};

// ============================================================================
// 设备指纹算法 - 与 prop_redirect.c 保持完全一致
// ============================================================================

/**
 * 生成设备指纹 - 每个模拟器实例唯一但3个月内稳定
 *
 * 注意：此函数与 prop_redirect.c 中的 generate_device_fingerprint() 完全一致
 *
 * @return 64位设备指纹值
 */
static u64 generate_statfs_device_fingerprint(void)
{
    struct timespec64 ts;

    ktime_get_real_ts64(&ts);

    // 实例唯一特征：基于进程内存布局，每个模拟器实例都不同
    u64 instance_unique = (u64)current->mm->start_brk ^     // 堆起始地址
                         (u64)current->mm->env_start ^      // 环境变量地址
                         (u64)current->mm->mmap_base;       // mmap基址

    // 时间稳定特征：3个月内保持不变 (86400秒/天 * 90天)
    u64 time_stable = ts.tv_sec / (86400 * 90);  // 每3个月变化一次

    // 额外的实例区分：使用内核变量地址增加唯一性
    u64 kernel_unique = (u64)&storage_config >> 12;

    // 组合生成设备指纹：确保每个实例唯一，但在3个月内稳定
    u64 device_fingerprint = (instance_unique >> 12) ^ time_stable ^ kernel_unique;

    return device_fingerprint;
}

// 从配置中读取存储设置
static void load_storage_config(void)
{
    int profile_index;

    if (storage_config.initialized)
        return;

    // 使用与 prop_redirect.c 完全一致的设备指纹算法
    u64 device_fingerprint = generate_statfs_device_fingerprint();
    profile_index = device_fingerprint % ARRAY_SIZE(supported_storage_sizes);

    // 应用选择的存储配置
    storage_config.storage_profile = profile_index;
    storage_config.total_size = supported_storage_sizes[profile_index];
    storage_config.base_available_size = (storage_config.total_size * available_ratios_percent[profile_index]) / 100;
    storage_config.random_range = storage_config.total_size / 16;  // 总容量的1/16作为随机范围

    storage_config.initialized = 1;

    printk("Storage config loaded: profile=%d, total=%lluGB, available=%lluGB, random=%s\n",
               storage_config.storage_profile,
               (unsigned long long)(storage_config.total_size / (1024*1024*1024)),
               (unsigned long long)(storage_config.base_available_size / (1024*1024*1024)),
               storage_config.enable_randomization ? "enabled" : "disabled");
}

// 简单的随机数生成器 (基于时间和PID)
static u64 get_random_offset(void)
{
    static u64 seed = 0;
    static time64_t last_update = 0;
    time64_t current_time;
    struct timespec64 ts;
    
    ktime_get_real_ts64(&ts);
    current_time = ts.tv_sec;
    
    // 初始化种子或每60秒更新一次
    if (seed == 0 || (current_time - last_update) > 60) {
        seed = (u64)current_time ^ ((u64)current->pid << 16) ^ ((u64)ts.tv_nsec << 8);
        last_update = current_time;
        
        // 添加一些基于内存地址的熵
        uintptr_t addr_entropy = (uintptr_t)&seed;
        seed ^= addr_entropy;
    }
    
    // 改进的线性同余生成器
    seed = (seed * 1664525 + 1013904223) & 0xFFFFFFFF;
    
    // 使用配置的随机范围
    load_storage_config();
    u64 random_range = storage_config.random_range;
    
    // 返回 -random_range/2 到 +random_range/2 的随机偏移
    u64 random_offset = (seed % random_range);
    return random_offset - (random_range / 2);
}

// 获取模拟的可用空间 (会变化)
static u64 get_simulated_available_size(void)
{
    struct timespec64 ts;
    time64_t current_time;
    s64 available;
    u64 min_available, max_available;
    
    load_storage_config();
    
    if (!storage_config.enable_randomization) {
        // 如果禁用随机化，返回固定值
        return storage_config.base_available_size;
    }
    
    ktime_get_real_ts64(&ts);
    current_time = ts.tv_sec;
    
    // 添加基于小时的慢变化 (模拟用户使用模式)
    s64 hourly_factor = ((current_time / 3600) % 24) * 100 * 1024 * 1024; // 每小时变化100MB
    
    // 添加基于分钟的快变化 (模拟应用活动)
    s64 minute_factor = ((current_time / 60) % 60) * 20 * 1024 * 1024; // 每分钟变化20MB
    
    available = storage_config.base_available_size + get_random_offset() + hourly_factor - minute_factor;
    
    // 确保不超过总容量，也不小于最小值
    min_available = storage_config.total_size / 6;  // 最小为总容量的1/6
    max_available = storage_config.total_size - (8ULL * 1024 * 1024 * 1024); // 总容量-8GB
    
    if (available < min_available) {
        available = min_available;
    } else if (available > max_available) {
        available = max_available;
    }
    
    return (u64)available;
}

// 获取模拟的空闲空间 (通常比可用空间稍大一些)
static u64 get_simulated_free_size(void)
{
    u64 available;
    u64 base_reserved, random_reserved;
    
    load_storage_config();
    available = get_simulated_available_size();
    
    if (!storage_config.enable_randomization) {
        // 如果禁用随机化，返回固定差值
        return available + (512ULL * 1024 * 1024);
    }
    
    // 空闲空间通常比可用空间多一些，但这个差值也应该有变化
    base_reserved = 512ULL * 1024 * 1024;  // 基础512MB保留空间
    random_reserved = (get_random_offset() / 8) % (256ULL * 1024 * 1024); // 额外0-256MB随机保留
    
    return available + base_reserved + random_reserved;
}

// 检查路径是否为模拟存储路径
static int is_emulated_storage_path(const char *path)
{
    int i;
    size_t len;
    
    if (!path) 
        return 0;
    
    for (i = 0; emulated_storage_paths[i] != NULL; i++) {
        if (strcmp(path, emulated_storage_paths[i]) == 0) {
            return 1;
        }
        // 也检查子路径
        len = strlen(emulated_storage_paths[i]);
        if (strncmp(path, emulated_storage_paths[i], len) == 0 && 
            (path[len] == '/' || path[len] == '\0')) {
            return 1;
        }
    }
    return 0;
}

// 填充真实手机的 statfs 信息
static void fill_realistic_phone_statfs(struct kstatfs *buf)
{
    u64 available_bytes, free_bytes;
    u64 base_inodes, inode_variance;
    
    if (!buf) 
        return;
    
    load_storage_config();
    memset(buf, 0, sizeof(struct kstatfs));

    // 文件系统类型 - 使用 EXT4 (现代Android手机的标准文件系统)
    buf->f_type = EXT4_SUPER_MAGIC;

    // 块大小
    buf->f_bsize = DEFAULT_BLOCK_SIZE;
    buf->f_frsize = DEFAULT_BLOCK_SIZE;
    
    // 获取动态的空间信息
    available_bytes = get_simulated_available_size();
    free_bytes = get_simulated_free_size();
    
    // 总块数和可用块数
    buf->f_blocks = storage_config.total_size / DEFAULT_BLOCK_SIZE;
    buf->f_bavail = available_bytes / DEFAULT_BLOCK_SIZE;
    buf->f_bfree = free_bytes / DEFAULT_BLOCK_SIZE;
    
    // inode 信息 (也可以稍微随机化)
    base_inodes = 1000000;
    inode_variance = 0;
    
    if (storage_config.enable_randomization) {
        inode_variance = (get_random_offset() / (1024 * 1024)) % 100000; // 0-100k变化
    }
    
    buf->f_files = base_inodes + inode_variance;  // 基础100万 + 变化
    buf->f_ffree = buf->f_files - 100000;         // 可用比总数少10万
    
    // 文件名最大长度
    buf->f_namelen = 255;
    
    // 文件系统标志 (模拟可读写存储)
    buf->f_flags = 0;
    
    if (storage_config.enable_randomization) {
        printk("Statfs intercepted (randomized): total=%lluGB, available=%lluMB, free=%lluMB\n", 
                (unsigned long long)(storage_config.total_size / (1024*1024*1024)),
                (unsigned long long)(available_bytes / (1024*1024)),
                (unsigned long long)(free_bytes / (1024*1024)));
    } else {
        printk("Statfs intercepted (fixed): total=%lluGB, available=%lluMB\n", 
                (unsigned long long)(storage_config.total_size / (1024*1024*1024)),
                (unsigned long long)(available_bytes / (1024*1024)));
    }
}

// statfs 拦截函数 - 仅处理拦截逻辑，不调用原始函数
int random_storage_statfs(const char *path, struct kstatfs *buf)
{
    kuid_t uid;
    
    if (!enable_vm_anti_detection)
        return 0;
    
    uid = current_uid();
    if (!should_hide_from_current_process())
        return 0;
    
    // 检查是否为模拟存储路径
    if (is_emulated_storage_path(path)) {
        printk("Statfs intercepted for UID %u, path: %s\n", 
                from_kuid(&init_user_ns, uid), path ? path : "NULL");
        fill_realistic_phone_statfs(buf);
        return 1;  // 成功拦截
    }
    
    // 对于非目标路径，返回0表示没有拦截
    return 0;
}

EXPORT_SYMBOL(random_storage_statfs);
