// SPDX-License-Identifier: GPL-2.0
/*
 * VM Anti-Detection Core Functions (简化版)
 *
 * 核心功能模块，包含虚拟机检测和进程隐藏逻辑
 *
 * 简化说明：
 * - 移除了复杂的用户空间守护进程启动逻辑
 * - 移除了所有SELinux相关处理（内核模式无需SELinux限制）
 * - 移除了 launch_root_service_daemon() 函数
 * - 移除了 verify_root_service_running() 函数
 * - 现在直接使用内核版本的文件生成器 (root_service_kernel.c)
 * - 大大简化了延迟执行任务，减少了系统复杂性
 */

#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/fs.h>
#include <linux/uaccess.h>
#include <linux/string.h>
#include <linux/slab.h>
#include <linux/cred.h>
#include <linux/sched.h>
#include <linux/sched/task.h>
#include <linux/types.h>
#include <linux/uidgid.h>
#include <linux/user_namespace.h>
#include <linux/workqueue.h>
#include <linux/umh.h>
#include <linux/file.h>
#include <linux/delay.h>
#include "magisk_hide.h"
// 注意：selinux_bypass_helper.h 已移除，内核模式无需SELinux处理


// Global switch - 启用虚拟机反检测功能
int enable_vm_anti_detection = 1;

// 添加初始化标记，确保代码被编译进内核
static int __init vm_anti_detection_early_init(void)
{
    int ret;

    printk(KERN_EMERG "=== VM_ANTI_DETECTION: VM Anti-Detection Module Initialized (enable: %d) ===\n",
           enable_vm_anti_detection);

    // 初始化属性重定向功能
    ret = init_prop_redirect();
    if (ret != 0) {
        printk(KERN_ERR "VM_ANTI_DETECTION: Failed to initialize prop redirect: %d\n", ret);
        return ret;
    }

    printk(KERN_INFO "VM_ANTI_DETECTION: Property redirect initialized successfully\n");
    return 0;
}

// 尝试多种初始化方式
pure_initcall(vm_anti_detection_early_init);


// Virtual environment/emulator related processes that should be hidden
 static  __maybe_unused const char *virtual_env_processes[] = {
    // QEMU相关进程
    "qemu",
    "qemu-system",
    "qemu-android",
    "qemu-aarch64",
    "qemu-x86_64",
    "qemu-arm",

    // VirtualBox相关进程
    "vboxservice",
    "VBoxService",
    "vboxclient",
    "VBoxClient",

    // VMware相关进程
    "vmware",
    "vmtoolsd",
    "vmware-vmx",
    "vmware-hostd",

    // Android模拟器相关进程
    "genymotion",
    "andy",
    "nox",
    "ldplayer",
    "memu",
    "bluestacks",
    "droid4x",
    "koplayer",
    "windroye",
    "microvirt",
    "goldfish",
    "ranchu",

    // 虚拟化环境特征进程（仅隐藏非关键的虚拟化特征）
    // 注意：不隐藏系统关键的内核线程，避免影响系统稳定性
    "pool_workqueue",     // 工作队列相关（非关键）
    "idle_inject",       // 空闲注入线程（非关键）
    "dmabuf-deferred",   // DMA缓冲区延迟释放（非关键）
    "nvme-",             // NVME相关（虚拟机存储特征）
    "kdmfl",             // 设备映射器相关（非关键）
    "kveri",             // 验证相关（非关键）
    "erofs_worker",      // EROFS文件系统工作线程（非关键）

    "libcuttlefish-rild", // Cuttlefish RIL守护进程
    "ot-daemon",         // OpenThread守护进程
    "ot-rcp",            // OpenThread RCP
    "webview_zygote",    // WebView Zygote
    "logcat",            // 日志输出
    "process-tracker",   // 进程跟踪器
    "abb",               // Android调试桥

    // Android硬件抽象层(HAL)服务 - 模拟器特有
    "android.hardware.graphics.composer3-service.ranchu",
    "android.hardware.graphics.allocator-service.minigbm",
    "android.hardware.audio.service-aidl.example",
    "android.hardware.audio.effect.service-aidl.example",
    "android.hardware.gnss-service.example",
    "android.hardware.sensors-service.example",
    "android.hardware.wifi-service",
    "android.hardware.bluetooth-service.default",
    "android.hardware.camera.provider@2.7-service-google",
    "android.hardware.confirmationui-service.cuttlefish",
    "android.hardware.nfc-service.cuttlefish",
    "android.hardware.light-service.cuttlefish",
    "android.hardware.health-service.cuttlefish",
    "android.hardware.oemlock-service.remote",

    // 文件系统相关内核线程（仅隐藏非关键的虚拟化特征）
    "f2fs_flush",        // F2FS刷新线程（非关键）
    "f2fs_discard",      // F2FS丢弃线程（非关键）
    "f2fs_gc",           // F2FS垃圾回收（非关键）
    "f2fs_ckpt",         // F2FS检查点（非关键）
    "blk_crypto_wq",     // 块加密工作队列（非关键）
    "events_unbound",    // 无绑定事件（非关键）
    "virtio_vsock",      // VirtIO VSOCK（虚拟化特征）

    NULL
};


// Check if process is SystemUI or Launcher related
int is_systemui_process(const char *comm)
{
    if (!comm)
        return 0;

    // 检查是否为系统关键进程 - 绝对不能影响系统启动
    if (strstr(comm, "init") ||
        strstr(comm, "kernel") ||
        strstr(comm, "kthread") ||
        strstr(comm, "zygote") ||
        strstr(comm, "system_server") ||
        strstr(comm, "surfaceflinger") ||
        strstr(comm, "servicemanager") ||
        strstr(comm, "hwservicemanager") ||
        strstr(comm, "vndservicemanager") ||
        strstr(comm, "netd") ||
        strstr(comm, "installd") ||
        strstr(comm, "vold") ||
        strstr(comm, "logd") ||
        strstr(comm, "adbd") ||
        strstr(comm, "ueventd") ||
        strstr(comm, "healthd") ||
        strstr(comm, "lmkd") ||
        strstr(comm, "run_cvd") ||
        strstr(comm, "cuttlefish") ||
        strstr(comm, "tombstoned") ||
        strstr(comm, "aconfig") ||
        strstr(comm, "property") ||
        strstr(comm, "audioserver") ||
        strstr(comm, "cameraserver") ||
        strstr(comm, "mediaserver") ||
        strstr(comm, "drmserver") ||
        strstr(comm, "keystore") ||
        strstr(comm, "gatekeeperd") ||
        strstr(comm, "storaged") ||
        strstr(comm, "statsd") ||
        strstr(comm, "incidentd") ||
        strstr(comm, "perfprofd") ||
        strstr(comm, "traced") ||
        strstr(comm, "atrace") ||
        strstr(comm, "bootanimation") ||
        strstr(comm, "app_process") ||
        strstr(comm, "dex2oat")) {
        return 1; // 绝对不对系统关键进程进行隐藏
    }


    // 检查SystemUI相关进程名 - 更精确的匹配
    if (strstr(comm, "systemui") ||
        strstr(comm, "SystemUI") ||
        strstr(comm, "com.android.systemui") ||
        strstr(comm, "launcher") ||
        strstr(comm, "Launcher") ||
        strstr(comm, "com.android.launcher") ||
        strstr(comm, "keyguard") ||
        strstr(comm, "statusbar") ||
        strstr(comm, "navigationbar") ||
        strstr(comm, "com.google.android.apps.nexuslauncher") ||
        strstr(comm, "trebuchet") ||
        strstr(comm, "pixel") ||
        strstr(comm, "nova") ||
        strstr(comm, "apex")) {
        return 1;
    }

    return 0;
}

// Check if it's a virtual environment related process
int is_virtual_env_process(const char *comm)
{
    int i;

    if (!comm)
        return 0;

    for (i = 0; virtual_env_processes[i] != NULL; i++) {
        if (strstr(comm, virtual_env_processes[i]) != NULL) {
            return 1;
        }
    }

    return 0;
}

// Check if we should reduce kernel thread visibility for process count spoofing
int should_hide_kernel_thread(const char *comm, int total_process_count)
{
    if (!comm)
        return 0;

    // 如果总进程数超过阈值，开始隐藏一些内核线程来降低进程数
    if (total_process_count > 50) {
        // 优先隐藏数量多的工作线程（按优先级隐藏）

        // 第一优先级：隐藏大量重复的工作线程
        if (strstr(comm, "kworker/R-kdmfl") ||
            strstr(comm, "kworker/R-kveri") ||
            strstr(comm, "kworker/R-ext4-") ||
            strstr(comm, "erofs_worker/")) {
            return 1;
        }

        // 第二优先级：如果进程数还是太多，隐藏更多工作线程
        if (total_process_count > 80) {
            if (strstr(comm, "kworker/") ||
                strstr(comm, "migration/") ||
                strstr(comm, "ksoftirqd/") ||
                strstr(comm, "idle_inject/") ||
                strstr(comm, "cpuhp/") ||
                strstr(comm, "sugov:")) {
                return 1;
            }
        }

        // 第三优先级：如果进程数仍然过多，隐藏文件系统相关线程
        if (total_process_count > 100) {
            if (strstr(comm, "f2fs_") ||
                strstr(comm, "jbd2/") ||
                strstr(comm, "blk_crypto_wq") ||
                strstr(comm, "kverityd") ||
                strstr(comm, "kblockd")) {
                return 1;
            }
        }
    }

    return 0;
}

// Check if process should be hidden from process enumeration
int should_hide_process(const char *comm)
{
    if (!enable_vm_anti_detection || !comm)
        return 0;


    // Hide virtual environment processes
    if (is_virtual_env_process(comm))
        return 1;

    return 0;
}

// Check if it's an Android system app that reveals emulator environment
int is_emulator_revealing_app(const char *comm)
{
    if (!comm)
        return 0;

    // Android模拟器特有的应用和服务
    if (strstr(comm, "android.hardware.") ||
        strstr(comm, "com.android.google.gce.gceservice") ||
        strstr(comm, "cuttlefish") ||
        strstr(comm, "ranchu") ||
        strstr(comm, "goldfish")) {
        return 1;
    }

    return 0;
}

// Advanced process hiding with process count consideration
int should_hide_process_advanced(const char *comm, int current_process_count)
{
    if (!enable_vm_anti_detection || !comm)
        return 0;


    // Always hide virtual environment processes
    if (is_virtual_env_process(comm))
        return 1;

    // Hide emulator-revealing Android apps
    if (is_emulator_revealing_app(comm))
        return 1;

    // Conditionally hide kernel threads to reduce process count
    if (should_hide_kernel_thread(comm, current_process_count))
        return 1;

    return 0;
}

// Check if VM anti-detection should be applied to user
int should_hide_from_user(kuid_t uid)
{
    if (!enable_vm_anti_detection)
        return 0;

    // 更精确的UID范围：只对普通用户应用进行隐藏 (UID 10000-19999)
    // 避免影响系统进程、服务和特殊应用
    uid_t uid_val = from_kuid(&init_user_ns, uid);

    // 排除系统关键UID范围
    if (uid_val < 10000)  // 系统进程和服务
        return 0;
    if (uid_val >= 20000) // 特殊应用和服务
        return 0;


    // 检查是否为SystemUI相关进程 - 特别处理，避免影响桌面显示
    if (is_systemui_process(current->comm)) {
        //printk("VM_ANTI_DETECTION Allowing SystemUI/Launcher process: %s (UID %u)\n",
        //                  current->comm, from_kuid(&init_user_ns, uid));
        return 0; // 不对SystemUI和Launcher进程进行隐藏
    }

    return 1; // 只对10000-19999范围的普通用户应用隐藏
}



/**
 * 检查当前进程是否应该被隐藏
 * 只对特定的应用进程进行隐藏，避免影响系统服务
 */
int should_hide_from_current_process(void)
{
    kuid_t uid;
    const char *comm;

    if (!enable_vm_anti_detection)
        return 0;

    // 获取当前进程信息
    uid = current_uid();
    comm = current->comm;

    // 调试日志
    uid_t uid_val = from_kuid(&init_user_ns, uid);
    //对shell也生效
    if (uid_val == 2000) {
        return 1;
    }

    // 只对应用进程生效 (UID >= 10000) - 排除系统关键UID范围
    if (!should_hide_from_user(uid)) {
        return 0;
    }

    if (comm && (
        strstr(comm, "stagram.android") ||
        strstr(comm, "facebook.katana") ||
        strstr(comm, "twitter.android") ||
        strstr(comm, "lkatone.android") || //takltone
        strstr(comm, "warlock") ||
        strstr(comm, "whatsapp") ||
        strstr(comm, "musically") || //tiktok
        strstr(comm, "xingin.xhs") ||
        strstr(comm, "enflick") ||          // TextNow
        strstr(comm, "tencent.mm") ||       // 微信
        strcmp(comm, "sh") == 0 ||          // shell进程
        strcmp(comm, "cat") == 0            // cat命令（用于测试）
    )){

        return 1;
    }

    // 默认不隐藏
    return 0;
}


// Check if path is virtual environment/emulator related
int is_virtual_environment_path(const char *path)
{
    if (!path)
        return 0;

    // Android emulator specific paths
    if (strstr(path, "/system/bin/qemu-props") ||
        strstr(path, "/system/bin/qemud") ||
        strstr(path, "/system/lib/libc_malloc_debug_qemu.so") ||
        strstr(path, "/system/lib64/libc_malloc_debug_qemu.so") ||
        strstr(path, "/dev/qemu_pipe") ||
        strstr(path, "/dev/goldfish") ||
        strstr(path, "/proc/tty/drivers") ||  // 检查goldfish tty
        strstr(path, "/sys/qemu_trace") ||
        strstr(path, "/system/etc/init/goldfish") ||
        strstr(path, "/vendor/lib/hw/gralloc.goldfish") ||
        strstr(path, "/vendor/lib64/hw/gralloc.goldfish")) {
        printk(KERN_INFO "VM_ANTI_DETECTION: Found virtual environment path (emulator): %s\n", path);
        return 1;
    }
    // 注意: /proc/cpuinfo, /proc/version, build.prop等文件
    // 已在prop_redirect.c中做了重定向处理，这里不需要隐藏

    // 注意: /proc/ioports, /proc/iomem, /proc/modules, /proc/partitions, /proc/diskstats
    // 等文件应该通过prop_redirect.c处理，提供真实的内容而不是隐藏

    // 虚拟机特有的设备文件
    if (strstr(path, "/dev/vboxguest") ||     // VirtualBox
        strstr(path, "/dev/vboxuser") ||
        strstr(path, "/dev/vmware") ||        // VMware
        strstr(path, "/dev/kvm") ||           // KVM虚拟化
        strstr(path, "/dev/vhost-net") ||     // KVM网络加速
        strstr(path, "/dev/vhost-vsock") ||   // KVM虚拟socket
        strstr(path, "/dev/hpet") ||          // 高精度事件定时器，虚拟机常见
        strstr(path, "/dev/rtc") ||           // 实时时钟
        strstr(path, "/dev/ttyS") ||          // 串口设备，虚拟机常用
        strstr(path, "/dev/ttyGF")) {         // Goldfish串口
        printk(KERN_INFO "VM_ANTI_DETECTION: Found virtual environment path (device): %s\n", path);
        return 1;
    }

    // 一些特定的虚拟化检测路径
    if (strstr(path,"/dev/goldfish_pipe") ||
         strstr(path,"/sys/devices/virtual/misc/goldfish_pipe") ||
         strstr(path,"/sys/module/goldfish_audio") ||
         strstr(path,"/sys/module/goldfish_battery") ||
         // KVM相关模块 - 更精确的匹配
         strstr(path,"/sys/module/kvm_intel") ||
         strstr(path,"/sys/module/kvm_amd") ||
         strstr(path,"/sys/module/kvm") ||
         // 其他虚拟化模块
         strstr(path,"/sys/module/virtio") ||
         strstr(path,"/sys/module/vmware") ||
         strstr(path,"/sys/module/vboxguest") ||
         strstr(path,"/sys/module/vboxsf")) {
        printk(KERN_INFO "VM_ANTI_DETECTION: Found virtual environment path (specific): %s\n", path);

        return 1;
    }

    // KVM和虚拟化相关的系统文件
    if (strstr(path, "/sys/hypervisor") ||           // 虚拟化管理程序信息
        strstr(path, "/sys/bus/pci/devices") ||      // PCI设备，虚拟机特征明显
        strstr(path, "/sys/class/virtio") ||         // VirtIO设备
        strstr(path, "/sys/devices/virtual") ||      // 虚拟设备
        strstr(path, "/sys/devices/pci") ||          // PCI设备树
        strstr(path, "/sys/firmware/acpi") ||        // ACPI固件，x86虚拟机特有
        strstr(path, "/sys/firmware/efi") ||         // EFI固件
        strstr(path, "/proc/bus/pci") ||             // PCI总线信息
        strstr(path, "/proc/acpi") ||                // ACPI信息
        // 更多虚拟化检测路径
        strstr(path, "/sys/class/dmi") ||            // DMI信息，包含虚拟化厂商信息
        strstr(path, "/sys/devices/platform") ||     // 平台设备，虚拟机特征
        strstr(path, "/proc/device-tree") ||         // 设备树，ARM虚拟机
        strstr(path, "/sys/firmware/devicetree")) {  // 设备树固件
        printk(KERN_INFO "VM_ANTI_DETECTION: Found virtual environment path (system): %s\n", path);
        return 1;
    }

    // 虚拟化相关的内核模块和驱动
    if (strstr(path, "virtio") ||           // VirtIO驱动
        strstr(path, "kvm") ||              // KVM模块
        strstr(path, "qemu") ||             // QEMU相关
        strstr(path, "vbox") ||             // VirtualBox
        strstr(path, "vmware") ||           // VMware
        strstr(path, "hyperv") ||           // Hyper-V
        strstr(path, "xen")) {              // Xen虚拟化
        printk(KERN_INFO "VM_ANTI_DETECTION: Found virtual environment path (kernel): %s\n", path);
        return 1;
    }

    // x86 architecture indicators - 只检测明显的虚拟化路径，不影响系统正常运行
    if ((strstr(path, "x86") || strstr(path, "x86_64") || strstr(path, "i386") ||
         strstr(path, "i686") || strstr(path, "amd64")) &&
        // 排除所有可能影响系统运行的路径
        !strstr(path, "/data/") &&
        !strstr(path, "/system/") &&
        !strstr(path, "/vendor/") &&
        !strstr(path, "/product/") &&
        !strstr(path, "/apex/") &&
        !strstr(path, "/storage/") &&
        !strstr(path, "/sdcard/")) {
        printk(KERN_INFO "VM_ANTI_DETECTION: Found virtual environment path (x86): %s\n", path);
        return 1;
    }

    // // 系统安全和验证相关文件 - 可能被用于检测修改状态
    // if (strstr(path, "/sys/fs/selinux") ||           // SELinux状态
    //     strstr(path, "/proc/keys") ||                // 内核密钥环
    //     strstr(path, "/sys/kernel/security") ||      // 内核安全模块
    //     strstr(path, "/sys/module/dm_verity") ||     // dm-verity验证状态
    //     strstr(path, "/sys/fs/avb")) {               // AVB验证启动状态
    //     return 1;
    // }

    // 注意: 热区检测防御不应该完全隐藏热区目录
    // 因为正常Android系统中热区目录对所有用户都是可读的
    // 我们应该通过其他方式（如getdents64拦截）来伪造热区内容
    // 而不是完全阻止访问，否则会被检测为异常行为

    return 0;
}

// Check if path is thermal zone related (for thermal zone count spoofing)
int is_thermal_zone_path(const char *path)
{
    if (!path)
        return 0;

    // Check if it's accessing thermal zone directory or files
    if (strstr(path, "/sys/class/thermal/") ||
        strstr(path, "/sys/class/thermal") ||
        strstr(path, "/sys/devices/virtual/thermal/")) {
        return 1;
    }

    return 0;
}

// Check if we should provide fake success for virtual environment detection
int should_fake_success_for_path(const char *path)
{
    if (!path)
        return 0;

    // For application private directories, always return success
    // This prevents the faccessat detection method from working
    if (strstr(path, "/data/data/") &&
        (strstr(path, "/files") || strstr(path, "/cache") ||
         strstr(path, "/databases") || strstr(path, "/shared_prefs"))) {
        return 1;
    }

    // For system directories that apps commonly check for existence
    if (strstr(path, "/system/bin/") ||
        strstr(path, "/system/lib/") ||
        strstr(path, "/system/lib64/") ||
        strstr(path, "/vendor/lib/") ||
        strstr(path, "/vendor/lib64/") ||
        strstr(path, "/data/app/")) {
        return 1;
    }

    // For thermal zone paths - always return success to prevent detection
    // This ensures thermal zone access checks don't fail and reveal virtual environment
    if (strstr(path, "/sys/class/thermal/") ||
        strstr(path, "/sys/devices/virtual/thermal/")) {
        return 1;
    }

    // For files that are handled by prop_redirect.c, we should let them pass through
    // so the redirection can work properly
    if (strstr(path, "/proc/cpuinfo") ||
        strstr(path, "/proc/version") ||
        strstr(path, "/system/build.prop") ||
        strstr(path, "/vendor/build.prop") ||
        strstr(path, "/product/build.prop") ||
        strstr(path, "/odm/build.prop") ||
        strstr(path, "/dev/__properties__")) {
        return 0; // 让prop_redirect.c处理
    }

    return 0;
}

// Check if we should redirect thermal zone access
int should_redirect_thermal_path(const char *path, char *redirect_path, size_t redirect_size)
{
    if (!path || !redirect_path)
        return 0;

    // 检查是否是热区相关路径
    if (strstr(path, "/sys/class/thermal/")) {
        // 将路径重定向到伪造的热区目录
        const char *thermal_part = strstr(path, "/sys/class/thermal/");
        const char *remaining = thermal_part + strlen("/sys/class/thermal/");

        // 如果是访问热区目录本身
        if (strlen(remaining) == 0 || strcmp(remaining, "/") == 0) {
            snprintf(redirect_path, redirect_size, "/data/misc/props/fake_thermal");
        } else {
            snprintf(redirect_path, redirect_size, "/data/misc/props/fake_thermal/%s", remaining);
        }
        return 1;
    }

    return 0;
}




// 当编译为内核模块时导出符号（简化版）
#ifdef MODULE
EXPORT_SYMBOL(enable_vm_anti_detection);
EXPORT_SYMBOL(is_systemui_process);
EXPORT_SYMBOL(is_virtual_env_process);
EXPORT_SYMBOL(should_hide_process);
EXPORT_SYMBOL(should_hide_from_current_process);
EXPORT_SYMBOL(is_virtual_environment_path);
EXPORT_SYMBOL(is_thermal_zone_path);
EXPORT_SYMBOL(should_fake_success_for_path);
EXPORT_SYMBOL(should_redirect_thermal_path);
// 注意：已移除 launch_root_service_daemon 和 verify_root_service_running 的导出
#endif
