// SPDX-License-Identifier: GPL-2.0
/*
 * VM Anti-Detection Property Redirect
 *
 * Handle property file redirection with path hiding to prevent virtual machine detection
 */

#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/fs.h>
#include <linux/file.h>
#include <linux/uaccess.h>
#include <linux/string.h>
#include <linux/slab.h>
#include <linux/cred.h>
#include <linux/sched.h>
#include <linux/dcache.h>
#include <linux/path.h>
#include <linux/namei.h>
#include "magisk_hide.h"
#include <linux/time.h>
#include <linux/timekeeping.h>
#include <linux/hash.h>
#include <linux/hashtable.h>
#include <linux/rculist.h>
#include <linux/spinlock.h>
#include <linux/types.h>
#include <linux/stat.h>
#include <linux/mm.h>
#include <linux/gfp.h>
#include <linux/fcntl.h>
#include <linux/mnt_idmapping.h>

// Access mode constants for faccessat
#ifndef F_OK
#define F_OK 0  /* Test for existence */
#endif
#ifndef R_OK
#define R_OK 4  /* Test for read permission */
#endif
#ifndef W_OK
#define W_OK 2  /* Test for write permission */
#endif
#ifndef X_OK
#define X_OK 1  /* Test for execute permission */
#endif
#include <linux/errno.h>

// 属性文件重定向映射表 - 结构体定义在 magisk_hide.h 中

// 重定向统计信息
struct redirect_statistics {
    unsigned long total_redirects;
    unsigned long successful_redirects;
    unsigned long failed_redirects;
    unsigned long dynamic_generations;
};

static struct redirect_statistics redirect_stats;

// 动态内容生成函数声明 - 只保留真正需要实时变化的文件
static int generate_dynamic_proc_stat(char *buffer, size_t size);
static int generate_dynamic_proc_uptime(char *buffer, size_t size);
static int generate_dynamic_proc_loadavg(char *buffer, size_t size);
static int generate_dynamic_proc_meminfo(char *buffer, size_t size);
static int generate_dynamic_proc_diskstats(char *buffer, size_t size);
static int generate_dynamic_block_stat(char *buffer, size_t size);
static int generate_fake_block_directory(char *buffer, size_t size);
static int generate_fake_dev_block_directory(char *buffer, size_t size);
static int generate_fake_proc_partitions(char *buffer, size_t size);
static int generate_dynamic_storage_size(char *buffer, size_t size);
static int generate_dynamic_storage_manfid(char *buffer, size_t size);
static int generate_dynamic_storage_serial(char *buffer, size_t size);
static int generate_dynamic_storage_name(char *buffer, size_t size);
static int generate_dynamic_storage_model(char *buffer, size_t size);
static int generate_dynamic_storage_scheduler(char *buffer, size_t size);
static int generate_dynamic_storage_queue_depth(char *buffer, size_t size);
static int generate_dynamic_storage_rotational(char *buffer, size_t size);
static int generate_dynamic_partition_size(char *buffer, size_t size);
static int generate_dynamic_partition_start(char *buffer, size_t size);
static int generate_partition_p1_size(char *buffer, size_t size);
static int generate_partition_p1_start(char *buffer, size_t size);
static int generate_partition_p3_size(char *buffer, size_t size);
static int generate_partition_p3_start(char *buffer, size_t size);
static int generate_partition_p6_size(char *buffer, size_t size);
static int generate_partition_p6_start(char *buffer, size_t size);
static int generate_partition_p8_size(char *buffer, size_t size);
static int generate_partition_p8_start(char *buffer, size_t size);
static int generate_filtered_proc_mounts(char *buffer, size_t size);

// 设备指纹算法 - 统一的设备唯一标识生成
static u64 generate_device_fingerprint(void);

// 内部辅助函数声明
// static int create_fallback_content(struct prop_redirect_entry *entry, char *buffer, size_t size);
static int validate_redirect_table(void);
static void cleanup_redirect_files(void);
static void print_redirect_stats(void);
static struct file *create_virtual_file(struct prop_redirect_entry *entry);
static struct file *create_dynamic_file(struct prop_redirect_entry *entry);

// 动态生成 /proc/stat 内容
static int generate_dynamic_proc_stat(char *buffer, size_t size)
{
    struct timespec64 ts;
    unsigned long uptime_sec;
    unsigned int rand1, rand2, rand3;

    // 获取当前时间
    ktime_get_real_ts64(&ts);
    uptime_sec = ts.tv_sec - 1729000800; // 启动时间戳

    // 生成伪随机数 (基于时间)
    rand1 = (unsigned int)(ts.tv_nsec % 10000);
    rand2 = (unsigned int)((ts.tv_nsec / 1000) % 1000);
    rand3 = (unsigned int)((ts.tv_nsec / 1000000) % 100);

    return snprintf(buffer, size,
        "cpu  %lu %lu %lu %lu %u %u %u 0 0 0\n"
        "cpu0 %lu %lu %lu %lu %u %u %u 0 0 0\n"
        "cpu1 %lu %lu %lu %lu %u %u %u 0 0 0\n"
        "cpu2 %lu %lu %lu %lu %u %u %u 0 0 0\n"
        "cpu3 %lu %lu %lu %lu %u %u %u 0 0 0\n"
        "cpu4 %lu %lu %lu %lu %u %u %u 0 0 0\n"
        "cpu5 %lu %lu %lu %lu %u %u %u 0 0 0\n"
        "cpu6 %lu %lu %lu %lu %u %u %u 0 0 0\n"
        "cpu7 %lu %lu %lu %lu %u %u %u 0 0 0\n"
        "intr %lu %u 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n"
        "ctxt %lu\n"
        "btime 1729000800\n"
        "processes %lu\n"
        "procs_running %u\n"
        "procs_blocked %u\n"
        "softirq %lu %u %u %u %u %u 0 %u %u 0 %u\n",
        // CPU总计
        uptime_sec * 100 + rand1, uptime_sec * 10 + rand2,
        uptime_sec * 50 + rand3, uptime_sec * 1000 + rand1,
        rand2 % 100, rand1 % 1000, rand3 % 100,
        // CPU0 (平均分配)
        (uptime_sec * 100 + rand1) / 8, (uptime_sec * 10 + rand2) / 8,
        (uptime_sec * 50 + rand3) / 8, (uptime_sec * 1000 + rand1) / 8,
        rand2 % 10, rand1 % 100, rand3 % 10,
        // CPU1-7 使用相同的平均值
        (uptime_sec * 100 + rand1) / 8, (uptime_sec * 10 + rand2) / 8,
        (uptime_sec * 50 + rand3) / 8, (uptime_sec * 1000 + rand1) / 8,
        rand2 % 10, rand1 % 100, rand3 % 10,
        (uptime_sec * 100 + rand1) / 8, (uptime_sec * 10 + rand2) / 8,
        (uptime_sec * 50 + rand3) / 8, (uptime_sec * 1000 + rand1) / 8,
        rand2 % 10, rand1 % 100, rand3 % 10,
        (uptime_sec * 100 + rand1) / 8, (uptime_sec * 10 + rand2) / 8,
        (uptime_sec * 50 + rand3) / 8, (uptime_sec * 1000 + rand1) / 8,
        rand2 % 10, rand1 % 100, rand3 % 10,
        (uptime_sec * 100 + rand1) / 8, (uptime_sec * 10 + rand2) / 8,
        (uptime_sec * 50 + rand3) / 8, (uptime_sec * 1000 + rand1) / 8,
        rand2 % 10, rand1 % 100, rand3 % 10,
        (uptime_sec * 100 + rand1) / 8, (uptime_sec * 10 + rand2) / 8,
        (uptime_sec * 50 + rand3) / 8, (uptime_sec * 1000 + rand1) / 8,
        rand2 % 10, rand1 % 100, rand3 % 10,
        (uptime_sec * 100 + rand1) / 8, (uptime_sec * 10 + rand2) / 8,
        (uptime_sec * 50 + rand3) / 8, (uptime_sec * 1000 + rand1) / 8,
        rand2 % 10, rand1 % 100, rand3 % 10,
        (uptime_sec * 100 + rand1) / 8, (uptime_sec * 10 + rand2) / 8,
        (uptime_sec * 50 + rand3) / 8, (uptime_sec * 1000 + rand1) / 8,
        rand2 % 10, rand1 % 100, rand3 % 10,
        // 中断和上下文切换
        uptime_sec * 1000 + rand1, rand2 % 1000,
        uptime_sec * 5000 + rand1,
        // 进程信息
        uptime_sec / 10 + rand3,
        1 + (rand1 % 5), rand2 % 3,
        // 软中断
        uptime_sec * 500 + rand1, rand2 % 1000, rand1 % 10000,
        rand3 % 100, rand2 % 1000, rand1 % 100,
        rand3 % 100, rand2 % 1000, rand1 % 10000
    );
}

// 动态生成 /proc/uptime 内容
static int generate_dynamic_proc_uptime(char *buffer, size_t size)
{
    struct timespec64 ts;
    unsigned long uptime_sec;

    ktime_get_real_ts64(&ts);
    uptime_sec = ts.tv_sec - 1729000800;

    return snprintf(buffer, size, "%lu.%02lu %lu.%02lu\n",
        uptime_sec, (ts.tv_nsec / 10000000) % 100,
        uptime_sec + 1000, (ts.tv_nsec / 10000000) % 100);
}

// 动态生成 /proc/loadavg 内容
static int generate_dynamic_proc_loadavg(char *buffer, size_t size)
{
    struct timespec64 ts;
    unsigned int load1, load2, load3;

    ktime_get_real_ts64(&ts);
    load1 = (ts.tv_nsec / 1000000) % 200;  // 0.00-1.99
    load2 = (ts.tv_nsec / 100000) % 250;   // 0.00-2.49
    load3 = (ts.tv_nsec / 10000) % 300;    // 0.00-2.99

    return snprintf(buffer, size, "%u.%02u %u.%02u %u.%02u 1/%u %u\n",
        load1 / 100, load1 % 100,
        load2 / 100, load2 % 100,
        load3 / 100, load3 % 100,
        (unsigned int)(1500 + (ts.tv_nsec % 500)),  // 进程总数
        (unsigned int)(10000 + (ts.tv_nsec % 1000)) // 最后进程ID
    );
}

// 动态生成 /proc/meminfo 内容 - 模拟12GB RAM
static int generate_dynamic_proc_meminfo(char *buffer, size_t size)
{
    struct timespec64 ts;
    unsigned long total_mem = 12582912; // 12GB in KB
    unsigned long free_mem, available_mem, cached_mem;

    ktime_get_real_ts64(&ts);

    // 基于时间动态变化内存使用情况
    free_mem = 6000000 + (ts.tv_nsec / 1000000) % 4000000;  // 6-10GB free
    available_mem = free_mem + 1000000 + (ts.tv_nsec / 100000) % 1000000;
    cached_mem = 1000000 + (ts.tv_nsec / 10000) % 500000;

    return snprintf(buffer, size,
        "MemTotal:       %lu kB\n"
        "MemFree:        %lu kB\n"
        "MemAvailable:   %lu kB\n"
        "Buffers:        %lu kB\n"
        "Cached:         %lu kB\n"
        "SwapCached:            0 kB\n"
        "Active:         %lu kB\n"
        "Inactive:       %lu kB\n"
        "Active(anon):   %lu kB\n"
        "Inactive(anon): %lu kB\n"
        "Active(file):   %lu kB\n"
        "Inactive(file): %lu kB\n"
        "Unevictable:    %lu kB\n"
        "Mlocked:               0 kB\n"
        "SwapTotal:       4194304 kB\n"
        "SwapFree:        4194304 kB\n"
        "Dirty:                64 kB\n"
        "Writeback:             0 kB\n"
        "AnonPages:      %lu kB\n"
        "Mapped:         %lu kB\n"
        "Shmem:          %lu kB\n"
        "KReclaimable:   %lu kB\n"
        "Slab:           %lu kB\n"
        "SReclaimable:   %lu kB\n"
        "SUnreclaim:     %lu kB\n"
        "KernelStack:    %lu kB\n"
        "PageTables:     %lu kB\n"
        "NFS_Unstable:          0 kB\n"
        "Bounce:                0 kB\n"
        "WritebackTmp:          0 kB\n"
        "CommitLimit:    10485760 kB\n"
        "Committed_AS:   %lu kB\n"
        "VmallocTotal:   258867200 kB\n"
        "VmallocUsed:    %lu kB\n"
        "VmallocChunk:          0 kB\n"
        "Percpu:         %lu kB\n"
        "AnonHugePages:         0 kB\n"
        "ShmemHugePages:        0 kB\n"
        "ShmemPmdMapped:        0 kB\n"
        "FileHugePages:         0 kB\n"
        "FilePmdMapped:         0 kB\n"
        "CmaTotal:         262144 kB\n"
        "CmaFree:        %lu kB\n"
        "HugePages_Total:       0\n"
        "HugePages_Free:        0\n"
        "HugePages_Rsvd:        0\n"
        "HugePages_Surp:        0\n"
        "Hugepagesize:       2048 kB\n"
        "Hugetlb:               0 kB\n",
        total_mem, free_mem, available_mem,
        200000 + (ts.tv_nsec / 1000) % 100000,  // Buffers
        cached_mem,
        total_mem - free_mem - 1000000,         // Active
        cached_mem / 2,                         // Inactive
        (total_mem - free_mem) / 2,            // Active(anon)
        200000 + (ts.tv_nsec / 100) % 50000,   // Inactive(anon)
        cached_mem / 2,                         // Active(file)
        cached_mem / 3,                         // Inactive(file)
        100000 + (ts.tv_nsec / 10) % 50000,    // Unevictable
        (total_mem - free_mem) / 3,            // AnonPages
        200000 + (ts.tv_nsec) % 100000,        // Mapped
        10000 + (ts.tv_nsec / 1000000) % 5000, // Shmem
        100000 + (ts.tv_nsec / 100000) % 50000, // KReclaimable
        200000 + (ts.tv_nsec / 10000) % 100000, // Slab
        100000 + (ts.tv_nsec / 1000) % 50000,   // SReclaimable
        100000 + (ts.tv_nsec / 100) % 50000,    // SUnreclaim
        10000 + (ts.tv_nsec / 10) % 5000,       // KernelStack
        20000 + (ts.tv_nsec) % 10000,           // PageTables
        3000000 + (ts.tv_nsec / 1000) % 1000000, // Committed_AS
        10000 + (ts.tv_nsec / 100) % 5000,      // VmallocUsed
        1000 + (ts.tv_nsec / 10) % 500,         // Percpu
        100000 + (ts.tv_nsec) % 100000          // CmaFree
    );
}

// 全局变量用于保存累积的磁盘统计信息
static struct {
    unsigned long last_update_time;
    unsigned long total_reads;
    unsigned long total_writes;
    unsigned long total_sectors_read;
    unsigned long total_sectors_written;
    unsigned long total_read_time;
    unsigned long total_write_time;
    unsigned long io_in_progress;
    unsigned long total_io_time;
    unsigned long weighted_io_time;
    unsigned long boot_time;
    unsigned long access_count;  // 访问次数，用于模拟使用模式
    bool initialized;
} disk_stats_state = {0};

// 获取系统负载相关的动态因子
static unsigned long get_dynamic_load_factor(void)
{
    struct timespec64 ts;
    unsigned long factor;

    ktime_get_real_ts64(&ts);

    // 基于时间的动态因子：模拟一天中的使用模式
    unsigned long hour_of_day = (ts.tv_sec / 3600) % 24;
    unsigned long minute_of_hour = (ts.tv_sec / 60) % 60;

    // 模拟真实使用模式：白天活跃，夜间较少
    if (hour_of_day >= 8 && hour_of_day <= 22) {
        // 白天：较高活动
        factor = 100 + (hour_of_day - 8) * 10 + minute_of_hour / 2;
    } else {
        // 夜间：较低活动
        factor = 20 + hour_of_day * 2 + minute_of_hour / 10;
    }

    // 添加随机波动
    factor += (ts.tv_nsec / 10000000) % 50;

    return factor;
}

// 动态生成 /proc/diskstats 内容 - 模拟真实的磁盘I/O统计
static int generate_dynamic_proc_diskstats(char *buffer, size_t size)
{
    struct timespec64 ts;
    unsigned long current_time, time_delta;
    unsigned long reads_increment, writes_increment;
    unsigned long read_time_increment, write_time_increment;
    int len = 0;
    int i;

    ktime_get_real_ts64(&ts);
    current_time = ts.tv_sec;

    // 获取动态负载因子
    unsigned long load_factor = get_dynamic_load_factor();

    // 初始化或更新统计状态
    if (!disk_stats_state.initialized) {
        disk_stats_state.boot_time = current_time - (ts.tv_nsec / 1000000) % 3600; // 模拟启动时间
        disk_stats_state.last_update_time = current_time;
        disk_stats_state.access_count = 0;

        // 基于"启动时间"的初始统计
        unsigned long uptime = current_time - disk_stats_state.boot_time;
        disk_stats_state.total_reads = 150000 + uptime * 20 + (current_time % 50000);
        disk_stats_state.total_writes = 75000 + uptime * 10 + (current_time % 25000);
        disk_stats_state.total_sectors_read = disk_stats_state.total_reads * 8;
        disk_stats_state.total_sectors_written = disk_stats_state.total_writes * 8;
        disk_stats_state.total_read_time = disk_stats_state.total_reads * 2;
        disk_stats_state.total_write_time = disk_stats_state.total_writes * 4;
        disk_stats_state.io_in_progress = (ts.tv_nsec / 100000000) % 3;
        disk_stats_state.total_io_time = uptime * 100 + (ts.tv_nsec / 1000000) % 10000;
        disk_stats_state.weighted_io_time = disk_stats_state.total_io_time * 2;
        disk_stats_state.initialized = true;
    } else {
        time_delta = current_time - disk_stats_state.last_update_time;
        disk_stats_state.access_count++;

        if (time_delta > 0) {
            // 基于负载因子和访问模式的智能增长
            unsigned long base_read_rate = (load_factor * time_delta) / 10;  // 基础读取率
            unsigned long base_write_rate = (load_factor * time_delta) / 20; // 基础写入率

            // 模拟突发I/O活动
            if (disk_stats_state.access_count % 10 == 0) {
                base_read_rate *= 3;  // 每10次访问有一次突发读取
            }
            if (disk_stats_state.access_count % 15 == 0) {
                base_write_rate *= 2; // 每15次访问有一次突发写入
            }

            reads_increment = base_read_rate + (ts.tv_nsec / 10000000) % (base_read_rate / 2 + 1);
            writes_increment = base_write_rate + (ts.tv_nsec / 20000000) % (base_write_rate / 2 + 1);

            // 模拟真实的延迟特性
            read_time_increment = reads_increment * (2 + (ts.tv_nsec / 50000000) % 3);
            write_time_increment = writes_increment * (4 + (ts.tv_nsec / 30000000) % 6);

            disk_stats_state.total_reads += reads_increment;
            disk_stats_state.total_writes += writes_increment;
            disk_stats_state.total_sectors_read += reads_increment * (8 + (ts.tv_nsec / 40000000) % 8);
            disk_stats_state.total_sectors_written += writes_increment * (8 + (ts.tv_nsec / 35000000) % 8);
            disk_stats_state.total_read_time += read_time_increment;
            disk_stats_state.total_write_time += write_time_increment;
            disk_stats_state.io_in_progress = (ts.tv_nsec / 100000000) % 4;
            disk_stats_state.total_io_time += time_delta * (100 + load_factor / 10);
            disk_stats_state.weighted_io_time += time_delta * (150 + load_factor / 5);
            disk_stats_state.last_update_time = current_time;
        }
    }

    // 生成主存储设备统计 - 模拟UFS存储 (mmcblk0)
    // 注意：这里不显示任何vda、dm-*等虚拟机设备
    len += snprintf(buffer + len, size - len,
        " 179       0 mmcblk0 %lu %lu %lu %lu %lu %lu %lu %lu %lu %lu %lu\n",
        disk_stats_state.total_reads,
        disk_stats_state.total_reads / 20, // merged reads
        disk_stats_state.total_sectors_read,
        disk_stats_state.total_read_time,
        disk_stats_state.total_writes,
        disk_stats_state.total_writes / 15, // merged writes
        disk_stats_state.total_sectors_written,
        disk_stats_state.total_write_time,
        disk_stats_state.io_in_progress,
        disk_stats_state.total_io_time,
        disk_stats_state.weighted_io_time);

    // 生成分区统计 - 基于真实Android分区布局
    struct {
        const char *name;
        int partition_num;
        int read_ratio_percent;   // 读取比例 (百分比，避免浮点运算)
        int write_ratio_percent;  // 写入比例 (百分比，避免浮点运算)
    } partitions[] = {
        {"mmcblk0p1", 1, 5, 1},     // bootloader (5%, 1%)
        {"mmcblk0p2", 2, 3, 1},     // misc (3%, 1%)
        {"mmcblk0p3", 3, 15, 5},    // boot (15%, 5%)
        {"mmcblk0p4", 4, 10, 3},    // recovery (10%, 3%)
        {"mmcblk0p5", 5, 25, 15},   // system (25%, 15%)
        {"mmcblk0p6", 6, 8, 5},     // vendor (8%, 5%)
        {"mmcblk0p7", 7, 5, 3},     // product (5%, 3%)
        {"mmcblk0p8", 8, 20, 60},   // userdata (20%, 60%)
        {"mmcblk0p9", 9, 4, 2},     // cache (4%, 2%)
        {"mmcblk0p10", 10, 5, 5},   // metadata (5%, 5%)
    };

    for (i = 0; i < sizeof(partitions) / sizeof(partitions[0]); i++) {
        unsigned long part_reads = (disk_stats_state.total_reads * partitions[i].read_ratio_percent) / 100;
        unsigned long part_writes = (disk_stats_state.total_writes * partitions[i].write_ratio_percent) / 100;
        unsigned long part_sectors_read = part_reads * 8;
        unsigned long part_sectors_written = part_writes * 8;
        unsigned long part_read_time = part_reads * 2;
        unsigned long part_write_time = part_writes * 4;

        len += snprintf(buffer + len, size - len,
            " 179      %2d %s %lu %lu %lu %lu %lu %lu %lu %lu 0 %lu %lu\n",
            partitions[i].partition_num, partitions[i].name,
            part_reads, part_reads / 20, part_sectors_read, part_read_time,
            part_writes, part_writes / 15, part_sectors_written, part_write_time,
            (part_read_time + part_write_time) / 2, part_read_time + part_write_time);

        if (len >= size - 200) break; // 防止缓冲区溢出
    }

    return len;
}

// 动态生成块设备统计信息 - 用于 /sys/block/*/stat 文件
static int generate_dynamic_block_stat(char *buffer, size_t size)
{
    struct timespec64 ts;
    unsigned long current_time;
    unsigned long reads, writes, sectors_read, sectors_written;
    unsigned long read_time, write_time, io_time, weighted_time;
    int len = 0;

    ktime_get_real_ts64(&ts);
    current_time = ts.tv_sec;

    // 基于当前时间和纳秒生成动态统计
    reads = 50000 + (current_time % 100000) + (ts.tv_nsec / 1000000) % 5000;
    writes = 25000 + (current_time % 50000) + (ts.tv_nsec / 2000000) % 2500;
    sectors_read = reads * 8 + (ts.tv_nsec / 5000000) % 1000;
    sectors_written = writes * 8 + (ts.tv_nsec / 10000000) % 500;
    read_time = reads * 2 + (ts.tv_nsec / 20000000) % 100;
    write_time = writes * 4 + (ts.tv_nsec / 15000000) % 200;
    io_time = (current_time % 86400) * 50 + (ts.tv_nsec / 30000000) % 1000;
    weighted_time = io_time * 2 + (ts.tv_nsec / 25000000) % 500;

    // 格式: reads reads_merged sectors_read time_reading writes writes_merged sectors_written time_writing in_flight io_ticks time_in_queue
    len = snprintf(buffer, size, "%lu %lu %lu %lu %lu %lu %lu %lu %ld %lu %lu\n",
                   reads, reads / 20, sectors_read, read_time,
                   writes, writes / 15, sectors_written, write_time,
                   (ts.tv_nsec / 100000000) % 3, // 0-2个正在进行的IO
                   io_time, weighted_time);

    return len;
}

// 动态生成伪造的块设备目录 - 隐藏虚拟机存储设备
static int generate_fake_block_directory(char *buffer, size_t size)
{
    int len = 0;
    struct timespec64 ts;
    struct tm tm_result;
    char time_str[32];

    // 获取当前时间
    ktime_get_real_ts64(&ts);
    time64_to_tm(ts.tv_sec, 0, &tm_result);

    // 格式化时间字符串 (YYYY-MM-DD HH:MM)
    snprintf(time_str, sizeof(time_str), "%04ld-%02d-%02d %02d:%02d",
             tm_result.tm_year + 1900, tm_result.tm_mon + 1, tm_result.tm_mday,
             tm_result.tm_hour, tm_result.tm_min);

    // 模拟真实Android手机的块设备目录结构，使用当前时间
    len += snprintf(buffer + len, size - len,
        "total 0\n"
        "drwxr-xr-x  2 <USER> <GROUP> 0 %s .\n"
        "dr-xr-xr-x 13 <USER> <GROUP> 0 %s ..\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p1 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p1\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p2 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p2\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p3 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p3\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p4 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p4\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p5 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p5\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p6 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p6\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p7 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p7\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p8 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p8\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p9 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p9\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p10 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p10\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p11 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p11\n"
        "lrwxrwxrwx  1 root root 0 %s mmcblk0p12 -> ../devices/platform/soc/1d84000.ufshc/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p12\n",
        time_str, time_str, time_str, time_str, time_str, time_str, time_str, time_str,
        time_str, time_str, time_str, time_str, time_str, time_str, time_str);

    return len;
}

// 动态生成伪造的 /dev/block 目录 - 隐藏虚拟机设备节点
static int generate_fake_dev_block_directory(char *buffer, size_t size)
{
    int len = 0;
    struct timespec64 ts;
    struct tm tm_result;
    char time_str[32];

    // 获取当前时间
    ktime_get_real_ts64(&ts);
    time64_to_tm(ts.tv_sec, 0, &tm_result);

    // 格式化时间字符串 (YYYY-MM-DD HH:MM)
    snprintf(time_str, sizeof(time_str), "%04ld-%02d-%02d %02d:%02d",
             tm_result.tm_year + 1900, tm_result.tm_mon + 1, tm_result.tm_mday,
             tm_result.tm_hour, tm_result.tm_min);

    // 模拟真实Android手机的设备块目录结构，使用当前时间
    len += snprintf(buffer + len, size - len,
        "total 0\n"
        "drwxr-xr-x  4 <USER> <GROUP> 120 %s .\n"
        "drwxr-xr-x 15 <USER> <GROUP> 3200 %s ..\n"
        "drwxr-xr-x  2 <USER> <GROUP>  60 %s bootdevice\n"
        "drwxr-xr-x  2 <USER> <GROUP>  60 %s platform\n"
        "brw-------  1 root root 179,0 %s mmcblk0\n"
        "brw-------  1 root root 179,1 %s mmcblk0p1\n"
        "brw-------  1 root root 179,2 %s mmcblk0p2\n"
        "brw-------  1 root root 179,3 %s mmcblk0p3\n"
        "brw-------  1 root root 179,4 %s mmcblk0p4\n"
        "brw-------  1 root root 179,5 %s mmcblk0p5\n"
        "brw-------  1 root root 179,6 %s mmcblk0p6\n"
        "brw-------  1 root root 179,7 %s mmcblk0p7\n"
        "brw-------  1 root root 179,8 %s mmcblk0p8\n"
        "brw-------  1 root root 179,9 %s mmcblk0p9\n"
        "brw-------  1 root root 179,10 %s mmcblk0p10\n"
        "brw-------  1 root root 179,11 %s mmcblk0p11\n"
        "brw-------  1 root root 179,12 %s mmcblk0p12\n",
        time_str, time_str, time_str, time_str, time_str, time_str, time_str, time_str,
        time_str, time_str, time_str, time_str, time_str, time_str, time_str, time_str, time_str);

    return len;
}

// ============================================================================
// 统一存储配置 - 与statfs.c保持一致
// ============================================================================

// 统一存储配置结构
static struct {
    u64 total_size_sectors;
    int storage_profile;
    const char *device_model;
    const char *device_vendor;
    const char *manfid;
    const char *serial_prefix;
    bool initialized;
} unified_storage_config = {0};

// ============================================================================
// 设备指纹算法 - 统一的设备唯一标识生成
// ============================================================================

/**
 * 生成设备指纹 - 每个模拟器实例唯一但2个月内稳定
 *
 * 算法特点：
 * 1. 实例唯一性：基于进程内存布局，每个模拟器实例都不同
 * 2. 时间稳定性：2个月内保持不变，模拟真实设备行为
 * 3. 一致性保证：所有模块使用相同算法，确保信息一致
 *
 * @return 64位设备指纹值
 */
static u64 generate_device_fingerprint(void)
{
    struct timespec64 ts;

    ktime_get_real_ts64(&ts);

    // 实例唯一特征：基于进程内存布局，每个模拟器实例都不同
    u64 instance_unique = (u64)current->mm->start_brk ^     // 堆起始地址
                         (u64)current->mm->env_start ^      // 环境变量地址
                         (u64)current->mm->mmap_base;       // mmap基址

    // 时间稳定特征：3个月内保持不变 (86400秒/天 * 90天)
    u64 time_stable = ts.tv_sec / (86400 * 90);  // 每3个月变化一次

    // 额外的实例区分：使用内核变量地址增加唯一性
    u64 kernel_unique = (u64)&unified_storage_config >> 12;

    // 组合生成设备指纹：确保每个实例唯一，但在3个月内稳定
    u64 device_fingerprint = (instance_unique >> 12) ^ time_stable ^ kernel_unique;

    return device_fingerprint;
}

// 加载统一存储配置信息（与statfs.c保持一致）
static void load_unified_storage_config(void)
{
    int profile_index;

    if (unified_storage_config.initialized)
        return;

    // 使用统一的设备指纹算法选择存储配置
    u64 device_fingerprint = generate_device_fingerprint();
    profile_index = device_fingerprint % 5;  // 0-4对应5种存储配置

    unified_storage_config.storage_profile = profile_index;

    // 根据配置设置设备信息 - 使用真实的多厂商配置
    switch (profile_index) {
        case 0: // 64GB - 入门级eMMC (Samsung)
            unified_storage_config.total_size_sectors = (64ULL * 1024 * 1024 * 1024) / 512;
            unified_storage_config.device_model = "SAMSUNG KLMAG1JETD-B041";
            unified_storage_config.device_vendor = "SAMSUNG";
            unified_storage_config.manfid = "0x15";  // Samsung
            unified_storage_config.serial_prefix = "S4Z2NX0N";
            break;
        case 1: // 128GB - 中端UFS2.1 (SK Hynix)
            unified_storage_config.total_size_sectors = (128ULL * 1024 * 1024 * 1024) / 512;
            unified_storage_config.device_model = "HYNIX H9HQ21AFAMZDAR";
            unified_storage_config.device_vendor = "HYNIX";
            unified_storage_config.manfid = "0x90";  // SK Hynix
            unified_storage_config.serial_prefix = "HY4A8B2C";
            break;
        case 2: // 256GB - 高端UFS3.0 (Samsung)
            unified_storage_config.total_size_sectors = (256ULL * 1024 * 1024 * 1024) / 512;
            unified_storage_config.device_model = "SAMSUNG KLUEG8UHDB-B0E1";
            unified_storage_config.device_vendor = "SAMSUNG";
            unified_storage_config.manfid = "0x15";  // Samsung
            unified_storage_config.serial_prefix = "S5Z3PX1M";
            break;
        case 3: // 512GB - 旗舰UFS3.1 (Micron)
            unified_storage_config.total_size_sectors = (512ULL * 1024 * 1024 * 1024) / 512;
            unified_storage_config.device_model = "MICRON MT128GASAQ4U21";
            unified_storage_config.device_vendor = "MICRON";
            unified_storage_config.manfid = "0x2C";  // Micron
            unified_storage_config.serial_prefix = "MT7F9E4D";
            break;
        case 4: // 1TB - 顶级UFS4.0 (Samsung)
            unified_storage_config.total_size_sectors = (1024ULL * 1024 * 1024 * 1024) / 512;
            unified_storage_config.device_model = "SAMSUNG KLUEG8U1EM-B0E1";
            unified_storage_config.device_vendor = "SAMSUNG";
            unified_storage_config.manfid = "0x15";  // Samsung
            unified_storage_config.serial_prefix = "S6Z4QX2N";
            break;
    }

    unified_storage_config.initialized = true;

    printk("Unified storage config loaded: profile=%d, size=%lluGB, model=%s\n",
           profile_index,
           (unified_storage_config.total_size_sectors * 512) / (1024ULL * 1024 * 1024),
           unified_storage_config.device_model);
}

// 动态生成存储设备大小（与statfs配置一致）
static int generate_dynamic_storage_size(char *buffer, size_t size)
{
    load_unified_storage_config();
    return snprintf(buffer, size, "%llu\n", unified_storage_config.total_size_sectors);
}

// 动态生成存储设备制造商ID
static int generate_dynamic_storage_manfid(char *buffer, size_t size)
{
    load_unified_storage_config();
    return snprintf(buffer, size, "%s\n", unified_storage_config.manfid);
}

// 生成固定的存储设备序列号（基于设备指纹）
static int generate_dynamic_storage_serial(char *buffer, size_t size)
{
    load_unified_storage_config();

    // 使用统一的设备指纹生成固定的序列号后缀
    u64 device_fingerprint = generate_device_fingerprint();

    // 生成固定的4位后缀：基于设备指纹，确保每个设备唯一但固定
    u32 suffix = (u32)((device_fingerprint >> 16) % 10000);  // 0000-9999，设备固定

    return snprintf(buffer, size, "%s%04u\n",
                   unified_storage_config.serial_prefix,
                   suffix);
}

// 动态生成存储设备名称
static int generate_dynamic_storage_name(char *buffer, size_t size)
{
    load_unified_storage_config();

    // 根据存储配置生成对应的真实设备名称
    switch (unified_storage_config.storage_profile) {
        case 0: return snprintf(buffer, size, "AJTD41\n");    // 64GB Samsung eMMC
        case 1: return snprintf(buffer, size, "H9HQ21\n");    // 128GB Hynix UFS
        case 2: return snprintf(buffer, size, "UHDB0E\n");    // 256GB Samsung UFS3.0
        case 3: return snprintf(buffer, size, "GASAQ4\n");    // 512GB Micron UFS3.1
        case 4: return snprintf(buffer, size, "U1EM0E\n");    // 1TB Samsung UFS4.0
        default: return snprintf(buffer, size, "UHDB0E\n");
    }
}

// 动态生成存储设备模型
static int generate_dynamic_storage_model(char *buffer, size_t size)
{
    load_unified_storage_config();
    return snprintf(buffer, size, "%s\n", unified_storage_config.device_model);
}

// 动态生成I/O调度器信息
static int generate_dynamic_storage_scheduler(char *buffer, size_t size)
{
    load_unified_storage_config();

    // 根据存储类型和Android版本生成合适的调度器信息
    // 现代Android手机通常使用以下调度器之一：

    switch (unified_storage_config.storage_profile) {
        case 0: // 64GB - 入门级，可能使用cfq
            return snprintf(buffer, size, "noop deadline [cfq] bfq\n");
        case 1: // 128GB - 中端，通常使用deadline
            return snprintf(buffer, size, "noop [deadline] cfq bfq\n");
        case 2: // 256GB - 高端，使用bfq或mq-deadline
            return snprintf(buffer, size, "[mq-deadline] kyber bfq none\n");
        case 3: // 512GB - 旗舰，使用mq-deadline
            return snprintf(buffer, size, "[mq-deadline] kyber bfq none\n");
        case 4: // 1TB - 顶级旗舰，使用最新的调度器
            return snprintf(buffer, size, "[mq-deadline] kyber bfq none\n");
        default:
            return snprintf(buffer, size, "[mq-deadline] kyber bfq none\n");
    }
}

// 动态生成存储队列深度信息
static int generate_dynamic_storage_queue_depth(char *buffer, size_t size)
{
    load_unified_storage_config();

    // 根据存储类型生成合适的队列深度
    // UFS存储通常有较高的队列深度，eMMC较低
    switch (unified_storage_config.storage_profile) {
        case 0: // 64GB - 可能是eMMC
            return snprintf(buffer, size, "32\n");
        case 1: // 128GB - eMMC或入门UFS
            return snprintf(buffer, size, "32\n");
        case 2: // 256GB - UFS 2.1/3.0
            return snprintf(buffer, size, "64\n");
        case 3: // 512GB - UFS 3.1
            return snprintf(buffer, size, "64\n");
        case 4: // 1TB - UFS 4.0
            return snprintf(buffer, size, "128\n");
        default:
            return snprintf(buffer, size, "64\n");
    }
}

// 动态生成存储旋转信息（SSD/Flash为0，HDD为1）
static int generate_dynamic_storage_rotational(char *buffer, size_t size)
{
    // 所有现代手机存储都是非旋转的（Flash存储）
    return snprintf(buffer, size, "0\n");
}

// Android分区布局定义（基于真实手机分区表）
static struct {
    const char *name;
    u64 size_mb;        // 分区大小（MB）
    u64 start_offset;   // 起始偏移（MB）
    bool is_userdata;   // 是否为用户数据分区（需要动态计算大小）
} android_partitions[] = {
    {"mmcblk0p1",  16,    1,    false},  // bootloader
    {"mmcblk0p2",  1,     17,   false},  // misc
    {"mmcblk0p3",  16,    18,   false},  // boot
    {"mmcblk0p4",  16,    34,   false},  // recovery
    {"mmcblk0p5",  1024,  50,   false},  // system
    {"mmcblk0p6",  512,   1074, false},  // vendor
    {"mmcblk0p7",  256,   1586, false},  // product
    {"mmcblk0p8",  0,     1842, true},   // userdata (动态计算)
    {"mmcblk0p9",  128,   0,    false},  // cache (在userdata后)
    {"mmcblk0p10", 8,     0,    false},  // metadata (在cache后)
    {"mmcblk0p11", 16,    0,    false},  // persist (在metadata后)
    {"mmcblk0p12", 32,    0,    false},  // modem (在persist后)
};

// 根据路径解析分区信息
static int parse_partition_info(const char *path, int *partition_num, bool *is_size)
{
    const char *p;

    // 查找 mmcblk0p 后的数字
    p = strstr(path, "mmcblk0p");
    if (!p) return -1;

    p += 8; // 跳过 "mmcblk0p"
    *partition_num = 0;

    // 解析分区号
    while (*p >= '0' && *p <= '9') {
        *partition_num = *partition_num * 10 + (*p - '0');
        p++;
    }

    // 检查是请求size还是start
    if (strstr(path, "/size")) {
        *is_size = true;
    } else if (strstr(path, "/start")) {
        *is_size = false;
    } else {
        return -1;
    }

    return 0;
}

// 动态生成分区大小 - 智能函数，根据路径查找分区表
static int generate_dynamic_partition_size(char *buffer, size_t size)
{
    // int partition_num;
    // bool is_size;
    u64 partition_size_sectors;
    int i;

    load_unified_storage_config();

    // 这是一个通用函数，需要通过某种方式获取当前处理的路径
    // 由于无法直接获取路径，我们使用默认的分区大小
    // 但保留分区表查找逻辑供将来使用

    // 查找分区表中的信息（示例逻辑）
    for (i = 0; i < sizeof(android_partitions) / sizeof(android_partitions[0]); i++) {
        if (strstr(android_partitions[i].name, "p2") || strstr(android_partitions[i].name, "p5")) {
            // 找到匹配的分区，使用分区表中的大小
            if (android_partitions[i].is_userdata) {
                // 用户数据分区需要动态计算
                u64 total_mb = (unified_storage_config.total_size_sectors * 512) / (1024 * 1024);
                u64 system_partitions_mb = 2048;
                u64 userdata_mb = total_mb - system_partitions_mb;
                partition_size_sectors = (userdata_mb * 1024 * 1024) / 512;
            } else {
                // 使用分区表中定义的固定大小
                partition_size_sectors = (android_partitions[i].size_mb * 1024 * 1024) / 512;
            }
            return snprintf(buffer, size, "%llu\n", partition_size_sectors);
        }
    }

    // 默认大小（如果没有在分区表中找到）
    partition_size_sectors = (512ULL * 1024 * 1024) / 512; // 默认512MB
    return snprintf(buffer, size, "%llu\n", partition_size_sectors);
}

// 动态生成分区起始位置 - 智能函数，根据分区表返回起始位置
static int generate_dynamic_partition_start(char *buffer, size_t size)
{
    u64 start_sectors;
    int i;

    load_unified_storage_config();

    // 查找分区表中的起始位置信息
    for (i = 0; i < sizeof(android_partitions) / sizeof(android_partitions[0]); i++) {
        if (strstr(android_partitions[i].name, "p2") || strstr(android_partitions[i].name, "p5")) {
            // 找到匹配的分区，使用分区表中的起始位置
            start_sectors = (android_partitions[i].start_offset * 1024 * 1024) / 512;
            return snprintf(buffer, size, "%llu\n", start_sectors);
        }
    }

    // 默认起始位置（如果没有在分区表中找到）
    start_sectors = (100ULL * 1024 * 1024) / 512; // 100MB后开始
    return snprintf(buffer, size, "%llu\n", start_sectors);
}

// 智能分区信息生成器 - 使用路径解析和分区表
static int generate_smart_partition_info(char *buffer, size_t size, const char *path)
{
    int partition_num;
    bool is_size;
    int i;
    u64 result_sectors;

    if (!path || parse_partition_info(path, &partition_num, &is_size) != 0) {
        return -1; // 解析失败
    }

    load_unified_storage_config();

    // 在分区表中查找对应的分区
    for (i = 0; i < sizeof(android_partitions) / sizeof(android_partitions[0]); i++) {
        // 提取分区号进行匹配
        const char *p = strstr(android_partitions[i].name, "p");
        if (p) {
            int table_partition_num = 0;
            p++; // 跳过 'p'
            while (*p >= '0' && *p <= '9') {
                table_partition_num = table_partition_num * 10 + (*p - '0');
                p++;
            }

            if (table_partition_num == partition_num) {
                // 找到匹配的分区
                if (is_size) {
                    // 返回分区大小
                    if (android_partitions[i].is_userdata) {
                        // 用户数据分区动态计算
                        u64 total_mb = (unified_storage_config.total_size_sectors * 512) / (1024 * 1024);
                        u64 system_partitions_mb = 2048;
                        u64 userdata_mb = total_mb - system_partitions_mb;
                        result_sectors = (userdata_mb * 1024 * 1024) / 512;
                    } else {
                        result_sectors = (android_partitions[i].size_mb * 1024 * 1024) / 512;
                    }
                } else {
                    // 返回分区起始位置
                    result_sectors = (android_partitions[i].start_offset * 1024 * 1024) / 512;
                }
                return snprintf(buffer, size, "%llu\n", result_sectors);
            }
        }
    }

    return -1; // 未找到匹配的分区
}

// 智能分区包装函数 - 为特定分区提供路径
static int generate_partition_p3_size(char *buffer, size_t size)
{
    return generate_smart_partition_info(buffer, size, "/sys/block/mmcblk0/mmcblk0p3/size");
}

static int generate_partition_p3_start(char *buffer, size_t size)
{
    return generate_smart_partition_info(buffer, size, "/sys/block/mmcblk0/mmcblk0p3/start");
}

static int generate_partition_p6_size(char *buffer, size_t size)
{
    return generate_smart_partition_info(buffer, size, "/sys/block/mmcblk0/mmcblk0p6/size");
}

static int generate_partition_p6_start(char *buffer, size_t size)
{
    return generate_smart_partition_info(buffer, size, "/sys/block/mmcblk0/mmcblk0p6/start");
}

// 专用生成器：mmcblk0p1分区大小
static int generate_partition_p1_size(char *buffer, size_t size)
{
    // mmcblk0p1 通常是bootloader分区，固定16MB
    u64 size_sectors = (16ULL * 1024 * 1024) / 512;
    return snprintf(buffer, size, "%llu\n", size_sectors);
}

// 专用生成器：mmcblk0p1分区起始位置
static int generate_partition_p1_start(char *buffer, size_t size)
{
    // mmcblk0p1 通常从1MB开始（跳过MBR等）
    u64 start_sectors = (1ULL * 1024 * 1024) / 512;
    return snprintf(buffer, size, "%llu\n", start_sectors);
}

// 专用生成器：mmcblk0p8分区大小（用户数据分区）
static int generate_partition_p8_size(char *buffer, size_t size)
{
    u64 total_mb, system_partitions_mb, userdata_mb, userdata_sectors;

    load_unified_storage_config();

    // 计算用户数据分区大小
    total_mb = (unified_storage_config.total_size_sectors * 512) / (1024 * 1024);
    system_partitions_mb = 2048; // 系统分区总计约2GB
    userdata_mb = total_mb - system_partitions_mb;

    // 转换为扇区数
    userdata_sectors = (userdata_mb * 1024 * 1024) / 512;

    return snprintf(buffer, size, "%llu\n", userdata_sectors);
}

// 专用生成器：mmcblk0p8分区起始位置（用户数据分区）
static int generate_partition_p8_start(char *buffer, size_t size)
{
    // 用户数据分区在系统分区后开始（约1842MB处）
    u64 start_sectors = (1842ULL * 1024 * 1024) / 512;
    return snprintf(buffer, size, "%llu\n", start_sectors);
}

// 动态生成伪造的 /proc/partitions - 隐藏虚拟机分区特征
static int generate_fake_proc_partitions(char *buffer, size_t size)
{
    int len = 0;
    u64 total_blocks, userdata_blocks;
    u64 total_mb, system_partitions_mb, userdata_mb;

    load_unified_storage_config();

    // 总块数（512字节块）
    total_blocks = unified_storage_config.total_size_sectors;

    // 计算用户数据分区大小（与分区生成器保持一致）
    total_mb = (total_blocks * 512) / (1024 * 1024);
    system_partitions_mb = 2048; // 系统分区总计约2GB
    userdata_mb = total_mb - system_partitions_mb;
    userdata_blocks = (userdata_mb * 1024 * 1024) / 512;

    // 模拟真实Android手机的分区表，大小与statfs和分区生成器一致
    len += snprintf(buffer + len, size - len,
        "major minor  #blocks  name\n\n"
        " 179        0  %llu mmcblk0\n"
        " 179        1      32768 mmcblk0p1\n"    // 16MB bootloader
        " 179        2       2048 mmcblk0p2\n"    // 1MB misc
        " 179        3      32768 mmcblk0p3\n"    // 16MB boot
        " 179        4      32768 mmcblk0p4\n"    // 16MB recovery
        " 179        5    2097152 mmcblk0p5\n"    // 1GB system
        " 179        6    1048576 mmcblk0p6\n"    // 512MB vendor
        " 179        7     524288 mmcblk0p7\n"    // 256MB product
        " 179        8  %llu mmcblk0p8\n"         // 用户数据分区（动态计算）
        " 179        9     262144 mmcblk0p9\n"    // 128MB cache
        " 179       10      16384 mmcblk0p10\n"   // 8MB metadata
        " 179       11      32768 mmcblk0p11\n"   // 16MB persist
        " 179       12      65536 mmcblk0p12\n",  // 32MB modem
        total_blocks,
        userdata_blocks);

    return len;
}

// 动态生成过滤后的 /proc/mounts 内容 - 隐藏Magisk相关挂载点
static int generate_filtered_proc_mounts(char *buffer, size_t size)
{
    struct file *file;
    char *line_buf, *temp_buf;
    loff_t pos = 0;
    ssize_t bytes_read;
    int len = 0;
    char *line_start, *line_end;
    bool should_hide_line;

    if (!buffer || size < 1024)
        return 0;

    // 分配临时缓冲区
    temp_buf = kmalloc(PAGE_SIZE, GFP_KERNEL);
    if (!temp_buf)
        return 0;

    line_buf = kmalloc(512, GFP_KERNEL);
    if (!line_buf) {
        kfree(temp_buf);
        return 0;
    }

    // 打开真实的/proc/mounts文件
    file = filp_open("/proc/mounts", O_RDONLY, 0);
    if (IS_ERR(file)) {
        kfree(temp_buf);
        kfree(line_buf);
        return 0;
    }

    // 读取文件内容
    bytes_read = kernel_read(file, temp_buf, PAGE_SIZE - 1, &pos);
    filp_close(file, NULL);

    if (bytes_read <= 0) {
        kfree(temp_buf);
        kfree(line_buf);
        return 0;
    }

    temp_buf[bytes_read] = '\0';

    // 逐行处理，过滤Magisk相关挂载点
    line_start = temp_buf;
    while (line_start && *line_start && len < size - 256) {
        line_end = strchr(line_start, '\n');
        if (line_end) {
            *line_end = '\0';
        }

        // 检查是否为需要隐藏的挂载点
        should_hide_line = false;

        // 检查Magisk相关挂载点模式
        if (strstr(line_start, "magisk") ||
            strstr(line_start, "/sbin/.magisk") ||
            strstr(line_start, "tmpfs /sbin") ||
            strstr(line_start, "/data/adb") ||
            strstr(line_start, "/debug_ramdisk") ||
            strstr(line_start, "overlay /system") ||
            strstr(line_start, "overlay /vendor") ||
            strstr(line_start, "overlay /product")) {
            should_hide_line = true;
        }

        // 检查虚拟机和模拟器特征挂载点
        if (strstr(line_start, "vboxsf") ||           // VirtualBox共享文件夹
            strstr(line_start, "vmhgfs") ||           // VMware共享文件夹
            strstr(line_start, "9p") ||               // QEMU 9P文件系统
            strstr(line_start, "/dev/vda") ||         // 虚拟磁盘设备
            strstr(line_start, "/dev/vdb") ||
            strstr(line_start, "/dev/vdc") ||
            strstr(line_start, "/dev/hda") ||         // IDE虚拟磁盘
            strstr(line_start, "/dev/hdb") ||
            strstr(line_start, "/dev/hdc") ||
            strstr(line_start, "cuttlefish") ||       // Android模拟器
            strstr(line_start, "gce") ||              // Google Compute Engine
            strstr(line_start, "virtio") ||           // VirtIO设备
            strstr(line_start, "qemu") ||             // QEMU相关
            strstr(line_start, "goldfish") ||         // Android模拟器
            strstr(line_start, "ranchu") ||           // Android模拟器
            strstr(line_start, "/dev/block/vold") ||  // 模拟器存储
            strstr(line_start, "fuse.gvfsd") ||      // GNOME虚拟文件系统
            strstr(line_start, "/android_root") ||    // 模拟器根目录
            strstr(line_start, "/android_vendor") ||  // 模拟器vendor
            strstr(line_start, "tmpfs /android_root") ||
            strstr(line_start, "tmpfs /mnt/androidwritable")) {
            should_hide_line = true;
        }

        // 如果不需要隐藏，添加到输出缓冲区
        if (!should_hide_line) {
            int line_len = strlen(line_start);
            if (len + line_len + 1 < size) {
                strcpy(buffer + len, line_start);
                len += line_len;
                buffer[len++] = '\n';
            }
        }

        // 移动到下一行
        if (line_end) {
            *line_end = '\n';
            line_start = line_end + 1;
        } else {
            break;
        }
    }

    buffer[len] = '\0';

    kfree(temp_buf);
    kfree(line_buf);

    printk("VM_ANTI_DETECTION: Generated filtered /proc/mounts with %d bytes\n", len);
    return len;
}

// ============================================================================
// 新增的动态生成器 - 用于需要随机变化的硬件信息
// ============================================================================

// 生成动态电池电量 (20-95%)
static int generate_dynamic_battery_capacity(char *buffer, size_t size)
{
    struct timespec64 ts;
    int capacity;

    ktime_get_real_ts64(&ts);

    // 基于时间生成缓慢变化的电池电量
    capacity = 20 + ((ts.tv_sec / 300) % 76); // 每5分钟变化1%，范围20-95%

    return snprintf(buffer, size, "%d\n", capacity);
}

// 生成动态电池电压 (3700-4200mV)
static int generate_dynamic_battery_voltage(char *buffer, size_t size)
{
    struct timespec64 ts;
    int voltage;

    ktime_get_real_ts64(&ts);

    // 电压与电量相关，范围3.7V-4.2V
    voltage = 3700 + ((ts.tv_sec / 100) % 500); // 3700-4200mV

    return snprintf(buffer, size, "%d\n", voltage);
}

// 生成动态电池温度 (250-400, 表示25.0-40.0°C)
static int generate_dynamic_battery_temp(char *buffer, size_t size)
{
    struct timespec64 ts;
    int temp;

    ktime_get_real_ts64(&ts);

    // 电池温度缓慢变化，范围25-40°C
    temp = 250 + ((ts.tv_sec / 600) % 150); // 每10分钟变化，250-400 (25.0-40.0°C)

    return snprintf(buffer, size, "%d\n", temp);
}



// 生成动态CPU频率
static int generate_dynamic_cpu_freq(char *buffer, size_t size, int cpu_id)
{
    struct timespec64 ts;
    unsigned int freq;
    unsigned int little_freqs[] = {300000, 576000, 768000, 1017600, 1248000, 1324800, 1516800, 1612800, 1708800, 1803000};
    unsigned int big_freqs[] = {300000, 652800, 825600, 979200, 1132800, 1363200, 1536000, 1747200, 1843200, 1996800, 2131200, 2419200};
    unsigned int prime_freqs[] = {300000, 704000, 844800, 960000, 1075200, 1190400, 1305600, 1420800, 1555200, 1670400, 1785600, 1900800, 2016000, 2131200, 2246400, 2361600, 2476800, 2592000, 2707200, 2822400, 2995200};

    ktime_get_real_ts64(&ts);

    // 根据CPU ID选择不同的频率表
    if (cpu_id <= 3) {
        // 小核 (CPU0-3)
        int freq_count = sizeof(little_freqs) / sizeof(little_freqs[0]);
        freq = little_freqs[(ts.tv_sec / 10 + cpu_id) % freq_count];
    } else if (cpu_id <= 6) {
        // 大核 (CPU4-6)
        int freq_count = sizeof(big_freqs) / sizeof(big_freqs[0]);
        freq = big_freqs[(ts.tv_sec / 8 + cpu_id) % freq_count];
    } else {
        // 超大核 (CPU7)
        int freq_count = sizeof(prime_freqs) / sizeof(prime_freqs[0]);
        freq = prime_freqs[(ts.tv_sec / 5 + cpu_id) % freq_count];
    }

    return snprintf(buffer, size, "%u\n", freq);
}

// 生成动态CPU温度 (30000-70000, 表示30-70°C)
static int generate_dynamic_cpu_temp(char *buffer, size_t size, int zone_id)
{
    struct timespec64 ts;
    int temp;

    ktime_get_real_ts64(&ts);

    // 根据时间和zone_id生成温度，模拟不同的热区
    temp = 30000 + ((ts.tv_sec / 60 + zone_id * 1000) % 40000); // 30-70°C

    return snprintf(buffer, size, "%d\n", temp);
}

// 生成基于设备指纹的固定WiFi MAC地址
static int generate_stable_wifi_mac(char *buffer, size_t size)
{
    static char wifi_mac[18] = {0};
    static bool initialized = false;

    if (!initialized) {
        // 使用统一的设备指纹生成固定MAC地址
        u64 device_fingerprint = generate_device_fingerprint();

        // 根据设备指纹选择厂商前缀，增加多样性
        const char* vendor_prefixes[] = {
            "f4:f5:e8",  // Google
            "ac:37:43",  // HTC
            "28:6a:ba",  // Samsung
            "40:4e:36",  // Huawei
            "64:bc:0c"   // Xiaomi
        };

        int vendor_index = (device_fingerprint >> 8) % 5;
        const char* vendor_prefix = vendor_prefixes[vendor_index];

        // 生成固定的后3字节
        u8 mac_suffix[3];
        mac_suffix[0] = (u8)((device_fingerprint >> 16) & 0xFF);
        mac_suffix[1] = (u8)((device_fingerprint >> 24) & 0xFF);
        mac_suffix[2] = (u8)((device_fingerprint >> 32) & 0xFF);

        // 确保MAC地址的本地管理位正确设置（第一字节的第二位为0，表示全局唯一）
        mac_suffix[0] &= 0xFE;  // 清除本地管理位

        snprintf(wifi_mac, sizeof(wifi_mac), "%s:%02x:%02x:%02x",
                vendor_prefix, mac_suffix[0], mac_suffix[1], mac_suffix[2]);

        initialized = true;
    }

    return snprintf(buffer, size, "%s\n", wifi_mac);
}

// 生成基于设备指纹的固定蓝牙MAC地址
static int generate_stable_bt_mac(char *buffer, size_t size)
{
    static char bt_mac[18] = {0};
    static bool initialized = false;

    if (!initialized) {
        // 使用统一的设备指纹，但生成不同于WiFi的MAC地址
        u64 device_fingerprint = generate_device_fingerprint();

        // 蓝牙使用不同的厂商前缀组合
        const char* bt_vendor_prefixes[] = {
            "f0:f1:f2",  // Google Bluetooth
            "ac:36:42",  // HTC Bluetooth
            "28:6a:bb",  // Samsung Bluetooth
            "40:4d:35",  // Huawei Bluetooth
            "64:bb:0b"   // Xiaomi Bluetooth
        };

        int vendor_index = (device_fingerprint >> 12) % 5;  // 不同于WiFi的偏移
        const char* vendor_prefix = bt_vendor_prefixes[vendor_index];

        // 生成与WiFi不同但固定的后3字节
        u8 mac_suffix[3];
        mac_suffix[0] = (u8)((device_fingerprint >> 20) & 0xFF);  // 不同偏移
        mac_suffix[1] = (u8)((device_fingerprint >> 28) & 0xFF);
        mac_suffix[2] = (u8)((device_fingerprint >> 36) & 0xFF);

        // 确保MAC地址格式正确
        mac_suffix[0] &= 0xFE;  // 清除本地管理位

        snprintf(bt_mac, sizeof(bt_mac), "%s:%02x:%02x:%02x",
                vendor_prefix, mac_suffix[0], mac_suffix[1], mac_suffix[2]);

        initialized = true;
    }

    return snprintf(buffer, size, "%s\n", bt_mac);
}

// 生成递增的网络流量统计
static int generate_network_rx_bytes(char *buffer, size_t size)
{
    struct timespec64 ts;
    unsigned long bytes;

    ktime_get_real_ts64(&ts);

    // 模拟累积的接收字节数
    bytes = (ts.tv_sec - 1729000000) * 1024 + (ts.tv_nsec / 1000000);

    return snprintf(buffer, size, "%lu\n", bytes);
}

static int generate_network_tx_bytes(char *buffer, size_t size)
{
    struct timespec64 ts;
    unsigned long bytes;

    ktime_get_real_ts64(&ts);

    // 模拟累积的发送字节数 (通常比接收少)
    bytes = (ts.tv_sec - 1729000000) * 512 + (ts.tv_nsec / 2000000);

    return snprintf(buffer, size, "%lu\n", bytes);
}

// CPU频率生成器包装函数 - 为每个CPU核心创建特定的生成器
static int generate_cpu0_freq(char *buffer, size_t size) { return generate_dynamic_cpu_freq(buffer, size, 0); }
static int generate_cpu1_freq(char *buffer, size_t size) { return generate_dynamic_cpu_freq(buffer, size, 1); }
static int generate_cpu2_freq(char *buffer, size_t size) { return generate_dynamic_cpu_freq(buffer, size, 2); }
static int generate_cpu3_freq(char *buffer, size_t size) { return generate_dynamic_cpu_freq(buffer, size, 3); }
static int generate_cpu4_freq(char *buffer, size_t size) { return generate_dynamic_cpu_freq(buffer, size, 4); }
static int generate_cpu5_freq(char *buffer, size_t size) { return generate_dynamic_cpu_freq(buffer, size, 5); }
static int generate_cpu6_freq(char *buffer, size_t size) { return generate_dynamic_cpu_freq(buffer, size, 6); }
static int generate_cpu7_freq(char *buffer, size_t size) { return generate_dynamic_cpu_freq(buffer, size, 7); }

// CPU温度生成器包装函数
static int generate_thermal_zone0_temp(char *buffer, size_t size) { return generate_dynamic_cpu_temp(buffer, size, 0); }
static int generate_thermal_zone1_temp(char *buffer, size_t size) { return generate_dynamic_cpu_temp(buffer, size, 1); }

// ============================================================================
// 重定向映射表 - 完整列表，适用于真实手机环境
// 注意: 模拟器和真实手机的文件权限可能不同，保持完整列表确保兼容性
static struct prop_redirect_entry prop_redirects[] = {
    // 属性文件重定向 - 真实手机上通常是644权限，用户应用可读
    {
        .original_path = "/system/build.prop",
        .redirect_path = "/data/misc/props/system_build.prop",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/vendor/build.prop",
        .redirect_path = "/data/misc/props/vendor_build.prop",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/product/build.prop",
        .redirect_path = "/data/misc/props/product_build.prop",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/odm/build.prop",
        .redirect_path = "/data/misc/props/odm_build.prop",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/system/etc/prop.default",
        .redirect_path = "/data/misc/props/system_prop_default.prop",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 系统信息文件重定向
    {
        .original_path = "/proc/version",
        .redirect_path = "/data/misc/props/proc_version",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/cmdline",
        .redirect_path = "/data/misc/props/proc_cmdline",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/cpuinfo",
        .redirect_path = "/data/misc/props/proc_cpuinfo",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 其他proc文件 - 虚拟环境检测相关
    {
        .original_path = "/proc/ioports",
        .redirect_path = "/data/misc/props/proc_ioports",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/iomem",
        .redirect_path = "/data/misc/props/proc_iomem",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/modules",
        .redirect_path = "/data/misc/props/proc_modules",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/partitions",
        .redirect_path = "/data/misc/props/proc_partitions",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/diskstats",
        .redirect_path = "/data/misc/props/proc_diskstats",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_proc_diskstats
    },
    {
        .original_path = "/proc/stat",
        .redirect_path = "/data/misc/props/proc_stat",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_proc_stat
    },
    {
        .original_path = "/proc/uptime",
        .redirect_path = "/data/misc/props/proc_uptime",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_proc_uptime
    },
    {
        .original_path = "/proc/loadavg",
        .redirect_path = "/data/misc/props/proc_loadavg",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_proc_loadavg
    },
    {
        .original_path = "/proc/misc",
        .redirect_path = "/data/misc/props/proc_misc",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // /proc/mounts - 动态过滤Magisk相关挂载点
    {
        .original_path = "/proc/mounts",
        .redirect_path = "/data/misc/props/proc_mounts",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_filtered_proc_mounts
    },
    // /proc/self/attr/current - SELinux上下文伪造
    {
        .original_path = "/proc/self/attr/current",
        .redirect_path = "/data/misc/props/selinux_context",
        .fake_content = "u:r:untrusted_app:s0:c123,c456\n",
        .fake_content_len = 32,
        .dynamic_generator = NULL
    },
    // 新增实际可访问的文件
    {
        .original_path = "/proc/filesystems",
        .redirect_path = "/data/misc/props/proc_filesystems",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/net/arp",
        .redirect_path = "/data/misc/props/proc_net_arp",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // /sys 文件重定向 - 8核心CPU频率文件 (cpu0-cpu7)
    // CPU0 频率文件
    {
        .original_path = "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu0_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu0_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq",
        .redirect_path = "/data/misc/props/sys_cpu0_cur_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_cpu0_freq
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu0/cpufreq/scaling_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu0_scaling_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu0/cpufreq/scaling_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu0_scaling_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU1 频率文件
    {
        .original_path = "/sys/devices/system/cpu/cpu1/cpufreq/cpuinfo_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu1_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu1/cpufreq/cpuinfo_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu1_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu1/cpufreq/scaling_cur_freq",
        .redirect_path = "/data/misc/props/sys_cpu1_cur_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_cpu1_freq
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu1/cpufreq/scaling_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu1_scaling_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu1/cpufreq/scaling_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu1_scaling_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU2 频率文件
    {
        .original_path = "/sys/devices/system/cpu/cpu2/cpufreq/cpuinfo_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu2_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu2/cpufreq/cpuinfo_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu2_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu2/cpufreq/scaling_cur_freq",
        .redirect_path = "/data/misc/props/sys_cpu2_cur_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_cpu2_freq
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu2/cpufreq/scaling_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu2_scaling_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu2/cpufreq/scaling_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu2_scaling_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU3 频率文件
    {
        .original_path = "/sys/devices/system/cpu/cpu3/cpufreq/cpuinfo_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu3_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu3/cpufreq/cpuinfo_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu3_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu3/cpufreq/scaling_cur_freq",
        .redirect_path = "/data/misc/props/sys_cpu3_cur_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_cpu3_freq
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu3/cpufreq/scaling_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu3_scaling_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu3/cpufreq/scaling_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu3_scaling_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/online",
        .redirect_path = "/data/misc/props/sys_cpu_online",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU4 频率文件
    {
        .original_path = "/sys/devices/system/cpu/cpu4/cpufreq/cpuinfo_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu4_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu4/cpufreq/cpuinfo_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu4_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu4/cpufreq/scaling_cur_freq",
        .redirect_path = "/data/misc/props/sys_cpu4_cur_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_cpu4_freq
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu4/cpufreq/scaling_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu4_scaling_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu4/cpufreq/scaling_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu4_scaling_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU5 频率文件
    {
        .original_path = "/sys/devices/system/cpu/cpu5/cpufreq/cpuinfo_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu5_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu5/cpufreq/cpuinfo_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu5_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu5/cpufreq/scaling_cur_freq",
        .redirect_path = "/data/misc/props/sys_cpu5_cur_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_cpu5_freq
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu5/cpufreq/scaling_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu5_scaling_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu5/cpufreq/scaling_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu5_scaling_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/present",
        .redirect_path = "/data/misc/props/sys_cpu_present",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU6 频率文件
    {
        .original_path = "/sys/devices/system/cpu/cpu6/cpufreq/cpuinfo_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu6_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu6/cpufreq/cpuinfo_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu6_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu6/cpufreq/scaling_cur_freq",
        .redirect_path = "/data/misc/props/sys_cpu6_cur_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_cpu6_freq
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu6/cpufreq/scaling_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu6_scaling_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu6/cpufreq/scaling_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu6_scaling_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU7 频率文件
    {
        .original_path = "/sys/devices/system/cpu/cpu7/cpufreq/cpuinfo_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu7_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu7/cpufreq/cpuinfo_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu7_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu7/cpufreq/scaling_cur_freq",
        .redirect_path = "/data/misc/props/sys_cpu7_cur_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_cpu7_freq
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu7/cpufreq/scaling_max_freq",
        .redirect_path = "/data/misc/props/sys_cpu7_scaling_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu7/cpufreq/scaling_min_freq",
        .redirect_path = "/data/misc/props/sys_cpu7_scaling_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // CPU拓扑信息重定向 - 8核心CPU (cpu0-cpu7)
    // CPU0 拓扑信息
    {
        .original_path = "/sys/devices/system/cpu/cpu0/topology/core_id",
        .redirect_path = "/data/misc/props/sys_cpu0_core_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu0/topology/physical_package_id",
        .redirect_path = "/data/misc/props/sys_cpu0_package_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu0/topology/core_siblings",
        .redirect_path = "/data/misc/props/sys_cpu0_core_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu0/topology/core_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu0_core_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu0/topology/thread_siblings",
        .redirect_path = "/data/misc/props/sys_cpu0_thread_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu0/topology/thread_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu0_thread_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU1 拓扑信息
    {
        .original_path = "/sys/devices/system/cpu/cpu1/topology/core_id",
        .redirect_path = "/data/misc/props/sys_cpu1_core_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu1/topology/physical_package_id",
        .redirect_path = "/data/misc/props/sys_cpu1_package_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu1/topology/core_siblings",
        .redirect_path = "/data/misc/props/sys_cpu1_core_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu1/topology/core_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu1_core_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu1/topology/thread_siblings",
        .redirect_path = "/data/misc/props/sys_cpu1_thread_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu1/topology/thread_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu1_thread_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU2 拓扑信息
    {
        .original_path = "/sys/devices/system/cpu/cpu2/topology/core_id",
        .redirect_path = "/data/misc/props/sys_cpu2_core_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu2/topology/physical_package_id",
        .redirect_path = "/data/misc/props/sys_cpu2_package_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu2/topology/core_siblings",
        .redirect_path = "/data/misc/props/sys_cpu2_core_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu2/topology/core_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu2_core_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu2/topology/thread_siblings",
        .redirect_path = "/data/misc/props/sys_cpu2_thread_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu2/topology/thread_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu2_thread_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/possible",
        .redirect_path = "/data/misc/props/sys_cpu_possible",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU3 拓扑信息
    {
        .original_path = "/sys/devices/system/cpu/cpu3/topology/core_id",
        .redirect_path = "/data/misc/props/sys_cpu3_core_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu3/topology/physical_package_id",
        .redirect_path = "/data/misc/props/sys_cpu3_package_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu3/topology/core_siblings",
        .redirect_path = "/data/misc/props/sys_cpu3_core_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu3/topology/core_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu3_core_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu3/topology/thread_siblings",
        .redirect_path = "/data/misc/props/sys_cpu3_thread_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu3/topology/thread_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu3_thread_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU4 拓扑信息
    {
        .original_path = "/sys/devices/system/cpu/cpu4/topology/core_id",
        .redirect_path = "/data/misc/props/sys_cpu4_core_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu4/topology/physical_package_id",
        .redirect_path = "/data/misc/props/sys_cpu4_package_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu4/topology/core_siblings",
        .redirect_path = "/data/misc/props/sys_cpu4_core_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu4/topology/core_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu4_core_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu4/topology/thread_siblings",
        .redirect_path = "/data/misc/props/sys_cpu4_thread_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu4/topology/thread_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu4_thread_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 温度传感器防护 - 虚拟机通常没有真实的温度传感器
    {
        .original_path = "/sys/class/thermal/thermal_zone0/temp",
        .redirect_path = "/data/misc/props/sys_thermal_zone0_temp",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_thermal_zone0_temp
    },
    // CPU5 拓扑信息
    {
        .original_path = "/sys/devices/system/cpu/cpu5/topology/core_id",
        .redirect_path = "/data/misc/props/sys_cpu5_core_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu5/topology/physical_package_id",
        .redirect_path = "/data/misc/props/sys_cpu5_package_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu5/topology/core_siblings",
        .redirect_path = "/data/misc/props/sys_cpu5_core_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu5/topology/core_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu5_core_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu5/topology/thread_siblings",
        .redirect_path = "/data/misc/props/sys_cpu5_thread_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu5/topology/thread_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu5_thread_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // CPU6 拓扑信息
    {
        .original_path = "/sys/devices/system/cpu/cpu6/topology/core_id",
        .redirect_path = "/data/misc/props/sys_cpu6_core_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu6/topology/physical_package_id",
        .redirect_path = "/data/misc/props/sys_cpu6_package_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu6/topology/core_siblings",
        .redirect_path = "/data/misc/props/sys_cpu6_core_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu6/topology/core_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu6_core_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu6/topology/thread_siblings",
        .redirect_path = "/data/misc/props/sys_cpu6_thread_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu6/topology/thread_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu6_thread_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/thermal/thermal_zone1/temp",
        .redirect_path = "/data/misc/props/sys_thermal_zone1_temp",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_thermal_zone1_temp
    },
    // CPU7 拓扑信息
    {
        .original_path = "/sys/devices/system/cpu/cpu7/topology/core_id",
        .redirect_path = "/data/misc/props/sys_cpu7_core_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu7/topology/physical_package_id",
        .redirect_path = "/data/misc/props/sys_cpu7_package_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu7/topology/core_siblings",
        .redirect_path = "/data/misc/props/sys_cpu7_core_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu7/topology/core_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu7_core_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu7/topology/thread_siblings",
        .redirect_path = "/data/misc/props/sys_cpu7_thread_siblings",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpu7/topology/thread_siblings_list",
        .redirect_path = "/data/misc/props/sys_cpu7_thread_siblings_list",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // CPU集群策略信息重定向 - 大小核架构
    // 小核集群策略 (CPU0-3, 通常是效率核心)
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy0/affected_cpus",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy0_affected_cpus",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy0/cpuinfo_max_freq",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy0_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy0/cpuinfo_min_freq",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy0_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy0/scaling_governor",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy0_governor",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy0/scaling_available_frequencies",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy0_available_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 大核集群策略 (CPU4-6, 通常是性能核心)
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy4/affected_cpus",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy4_affected_cpus",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy4/cpuinfo_max_freq",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy4_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy4/cpuinfo_min_freq",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy4_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy4/scaling_governor",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy4_governor",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy4/scaling_available_frequencies",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy4_available_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/thermal/thermal_zone0/type",
        .redirect_path = "/data/misc/props/sys_thermal_zone0_type",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 超大核集群策略 (CPU7, 通常是超高性能核心)
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy7/affected_cpus",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy7_affected_cpus",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy7/cpuinfo_max_freq",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy7_max_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy7/cpuinfo_min_freq",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy7_min_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy7/scaling_governor",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy7_governor",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/system/cpu/cpufreq/policy7/scaling_available_frequencies",
        .redirect_path = "/data/misc/props/sys_cpufreq_policy7_available_freq",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // CPU集合信息重定向 - Android调度器使用
    {
        .original_path = "/dev/cpuset/foreground/cpus",
        .redirect_path = "/data/misc/props/dev_cpuset_foreground_cpus",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/dev/cpuset/background/cpus",
        .redirect_path = "/data/misc/props/dev_cpuset_background_cpus",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/dev/cpuset/system-background/cpus",
        .redirect_path = "/data/misc/props/dev_cpuset_system_background_cpus",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/dev/cpuset/top-app/cpus",
        .redirect_path = "/data/misc/props/dev_cpuset_top_app_cpus",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/dev/cpuset/restricted/cpus",
        .redirect_path = "/data/misc/props/dev_cpuset_restricted_cpus",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 电池和电源管理重定向 - 模拟器通常没有真实电池
    // ============================================================================
    {
        .original_path = "/sys/class/power_supply/battery/capacity",
        .redirect_path = "/data/misc/props/sys_battery_capacity",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_battery_capacity
    },
    {
        .original_path = "/sys/class/power_supply/battery/status",
        .redirect_path = "/data/misc/props/sys_battery_status",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/power_supply/battery/health",
        .redirect_path = "/data/misc/props/sys_battery_health",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/power_supply/battery/technology",
        .redirect_path = "/data/misc/props/sys_battery_technology",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/power_supply/battery/voltage_now",
        .redirect_path = "/data/misc/props/sys_battery_voltage",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_battery_voltage
    },
    {
        .original_path = "/sys/class/power_supply/battery/current_now",
        .redirect_path = "/data/misc/props/sys_battery_current",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/power_supply/battery/temp",
        .redirect_path = "/data/misc/props/sys_battery_temp",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_battery_temp
    },
    {
        .original_path = "/sys/class/power_supply/usb/online",
        .redirect_path = "/data/misc/props/sys_usb_online",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/power_supply/ac/online",
        .redirect_path = "/data/misc/props/sys_ac_online",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 网络接口伪造防护
    // ============================================================================
    {
        .original_path = "/proc/net/dev",
        .redirect_path = "/data/misc/props/proc_net_dev",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/net/route",
        .redirect_path = "/data/misc/props/proc_net_route",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/net/arp",
        .redirect_path = "/data/misc/props/proc_net_arp",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/net/wlan0/address",
        .redirect_path = "/data/misc/props/sys_net_wlan0_address",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_stable_wifi_mac
    },
    {
        .original_path = "/sys/class/net/wlan0/statistics/rx_bytes",
        .redirect_path = "/data/misc/props/sys_net_wlan0_rx_bytes",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_network_rx_bytes
    },
    {
        .original_path = "/sys/class/net/wlan0/statistics/tx_bytes",
        .redirect_path = "/data/misc/props/sys_net_wlan0_tx_bytes",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_network_tx_bytes
    },
    {
        .original_path = "/sys/class/net/rmnet0/address",
        .redirect_path = "/data/misc/props/sys_net_rmnet0_address",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 内核模块隐藏防护
    // ============================================================================
    {
        .original_path = "/proc/modules",
        .redirect_path = "/data/misc/props/proc_modules",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/module/binder/parameters/debug_mask",
        .redirect_path = "/data/misc/props/sys_module_binder_debug_mask",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/module/lowmemorykiller/parameters/debug_level",
        .redirect_path = "/data/misc/props/sys_module_lmk_debug_level",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // PCI/USB设备防护
    // ============================================================================
    {
        .original_path = "/proc/bus/pci/devices",
        .redirect_path = "/data/misc/props/proc_bus_pci_devices",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/bus/usb/devices",
        .redirect_path = "/data/misc/props/proc_bus_usb_devices",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/bus/pci/devices",
        .redirect_path = "/data/misc/props/sys_bus_pci_devices",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/bus/usb/devices",
        .redirect_path = "/data/misc/props/sys_bus_usb_devices",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 内存信息防护
    // ============================================================================
    {
        .original_path = "/proc/meminfo",
        .redirect_path = "/data/misc/props/proc_meminfo",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_proc_meminfo
    },
    {
        .original_path = "/proc/vmstat",
        .redirect_path = "/data/misc/props/proc_vmstat",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/zoneinfo",
        .redirect_path = "/data/misc/props/proc_zoneinfo",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/buddyinfo",
        .redirect_path = "/data/misc/props/proc_buddyinfo",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 真实手机上可能存在的USB设备信息文件
    {
        .original_path = "/sys/class/android_usb/android0/iSerial",
        .redirect_path = "/data/misc/props/sys_usb_serial",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/android_usb/android0/iManufacturer",
        .redirect_path = "/data/misc/props/sys_usb_manufacturer",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/android_usb/android0/iProduct",
        .redirect_path = "/data/misc/props/sys_usb_product",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 设备树文件 - 真实手机上通常存在
    {
        .original_path = "/proc/device-tree/model",
        .redirect_path = "/data/misc/props/device_tree_model",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/device-tree/compatible",
        .redirect_path = "/data/misc/props/device_tree_compatible",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 网络接口地址 - 真实手机上通常存在
    {
        .original_path = "/sys/class/net/wlan0/address",
        .redirect_path = "/data/misc/props/sys_net_wlan0_address",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // SoC设备序列号 - 设备唯一标识符
    {
        .original_path = "/sys/devices/soc0/serial_number",
        .redirect_path = "/data/misc/props/sys_soc0_serial_number",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // SoC设备ID
    {
        .original_path = "/sys/devices/soc0/soc_id",
        .redirect_path = "/data/misc/props/sys_soc0_soc_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // SoC版本信息
    {
        .original_path = "/sys/devices/soc0/revision",
        .redirect_path = "/data/misc/props/sys_soc0_revision",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // SoC系列信息
    {
        .original_path = "/sys/devices/soc0/family",
        .redirect_path = "/data/misc/props/sys_soc0_family",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // SoC设备型号
    {
        .original_path = "/sys/devices/soc0/machine",
        .redirect_path = "/data/misc/props/sys_soc0_machine",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 系统UUID
    {
        .original_path = "/proc/sys/kernel/random/uuid",
        .redirect_path = "/data/local/tmp/props/proc_sys_kernel_random_uuid",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 存储设备CID
    {
        .original_path = "/sys/block/mmcblk0/device/cid",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0_device_cid",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 存储设备名称 - 动态生成，与statfs配置一致
    {
        .original_path = "/sys/block/mmcblk0/device/name",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0_device_name",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_name
    },
    // 存储设备制造商ID - 动态生成，与statfs配置一致
    {
        .original_path = "/sys/block/mmcblk0/device/manfid",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0_device_manfid",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_manfid
    },
    // 存储设备序列号 - 动态生成，与statfs配置一致
    {
        .original_path = "/sys/block/mmcblk0/device/serial",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0_device_serial",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_serial
    },
    // 存储设备大小 - 动态生成，与statfs配置一致
    {
        .original_path = "/sys/block/mmcblk0/size",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0_size",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_size
    },
    // 存储设备队列信息 - 动态生成，与存储配置一致
    {
        .original_path = "/sys/block/mmcblk0/queue/scheduler",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0_queue_scheduler",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_scheduler
    },
    {
        .original_path = "/sys/block/mmcblk0/queue/nr_requests",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0_queue_nr_requests",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_queue_depth
    },
    {
        .original_path = "/sys/block/mmcblk0/queue/rotational",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0_queue_rotational",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_rotational
    },

    // ============================================================================
    // 其他存储设备重定向 - 覆盖更多存储检测点
    // ============================================================================
    // sda设备（虚拟机常见）
    {
        .original_path = "/sys/block/sda/device/model",
        .redirect_path = "/data/misc/props/sys_block_sda_device_model",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_model
    },
    {
        .original_path = "/sys/block/sda/stat",
        .redirect_path = "/data/misc/props/sys_block_sda_stat",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_block_stat
    },
    {
        .original_path = "/sys/block/sda/device/vendor",
        .redirect_path = "/data/misc/props/sys_block_sda_device_vendor",
        .fake_content = "SAMSUNG\n",
        .fake_content_len = 8,
        .dynamic_generator = NULL
    },
    // vda设备（KVM虚拟机）- 重定向为真实手机存储
    {
        .original_path = "/sys/block/vda/device/model",
        .redirect_path = "/data/misc/props/sys_block_vda_device_model",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_model
    },
    {
        .original_path = "/sys/block/vda/device/vendor",
        .redirect_path = "/data/misc/props/sys_block_vda_device_vendor",
        .fake_content = "SAMSUNG\n",
        .fake_content_len = 8,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/block/vda/size",
        .redirect_path = "/data/misc/props/sys_block_vda_size",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_size
    },
    {
        .original_path = "/sys/block/vda/stat",
        .redirect_path = "/data/misc/props/sys_block_vda_stat",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_block_stat
    },
    // vdb设备
    {
        .original_path = "/sys/block/vdb/device/model",
        .redirect_path = "/data/misc/props/sys_block_vdb_device_model",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_model
    },
    {
        .original_path = "/sys/block/vdb/size",
        .redirect_path = "/data/misc/props/sys_block_vdb_size",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_size
    },
    {
        .original_path = "/sys/block/vdb/stat",
        .redirect_path = "/data/misc/props/sys_block_vdb_stat",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_block_stat
    },
    // vdc设备
    {
        .original_path = "/sys/block/vdc/device/model",
        .redirect_path = "/data/misc/props/sys_block_vdc_device_model",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_model
    },
    {
        .original_path = "/sys/block/vdc/size",
        .redirect_path = "/data/misc/props/sys_block_vdc_size",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_storage_size
    },
    {
        .original_path = "/sys/block/vdc/stat",
        .redirect_path = "/data/misc/props/sys_block_vdc_stat",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_block_stat
    },
    // 存储设备统计信息 - 动态生成
    {
        .original_path = "/sys/block/mmcblk0/stat",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0_stat",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_block_stat
    },
    // 分区信息 - 动态生成，与存储配置一致
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p1/size",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p1_size",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_partition_p1_size
    },
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p1/start",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p1_start",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_partition_p1_start
    },

    // ============================================================================
    // 块设备目录重定向 - 隐藏虚拟机存储特征
    // ============================================================================
    // 重定向整个块设备目录，隐藏vda/vdb/vdc等虚拟机设备
    {
        .original_path = "/sys/block",
        .redirect_path = "/data/misc/props/sys_block_directory",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_fake_block_directory
    },
    // 重定向设备块目录，隐藏虚拟机设备节点
    {
        .original_path = "/dev/block",
        .redirect_path = "/data/misc/props/dev_block_directory",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_fake_dev_block_directory
    },

    // ============================================================================
    // 其他分区信息 - 使用通用生成器
    // ============================================================================
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p2/size",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p2_size",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_partition_size
    },
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p2/start",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p2_start",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_partition_start
    },
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p5/size",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p5_size",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_partition_size
    },
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p5/start",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p5_start",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_dynamic_partition_start
    },

    // ============================================================================
    // 智能分区信息 - 使用分区表和路径解析
    // ============================================================================
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p3/size",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p3_size",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_partition_p3_size
    },
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p3/start",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p3_start",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_partition_p3_start
    },
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p6/size",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p6_size",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_partition_p6_size
    },
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p6/start",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p6_start",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_partition_p6_start
    },

    // ============================================================================
    // 用户数据分区信息（最重要的分区）
    // ============================================================================
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p8/size",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p8_size",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_partition_p8_size
    },
    {
        .original_path = "/sys/block/mmcblk0/mmcblk0p8/start",
        .redirect_path = "/data/misc/props/sys_block_mmcblk0p8_start",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_partition_p8_start
    },

    // ============================================================================
    // 虚拟机设备隐藏 - 直接隐藏访问
    // ============================================================================
    // 隐藏VirtIO设备访问
    {
        .original_path = "/sys/block/vda",
        .redirect_path = NULL,  // 直接返回ENOENT
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/block/vdb",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/block/vdc",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 隐藏Device Mapper设备访问 (dm-0 到 dm-9)
    {
        .original_path = "/sys/block/dm-0",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/block/dm-1",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/block/dm-2",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/block/dm-3",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/block/dm-4",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 隐藏SCSI设备访问 (sda, sdb)
    {
        .original_path = "/sys/block/sda",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/block/sdb",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 设备节点隐藏 - 隐藏 /dev 中的虚拟机设备
    // ============================================================================
    // 隐藏VirtIO设备节点
    {
        .original_path = "/dev/vda",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/dev/vdb",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/dev/vdc",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 隐藏Device Mapper设备节点
    {
        .original_path = "/dev/mapper",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 分区和磁盘信息重定向 - 隐藏虚拟机存储特征
    // ============================================================================
    {
        .original_path = "/proc/partitions",
        .redirect_path = "/data/misc/props/proc_partitions",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_fake_proc_partitions
    },

    // ============================================================================
    // 虚拟化特征隐藏 - 隐藏hypervisor等虚拟化标志
    // ============================================================================
    {
        .original_path = "/sys/hypervisor",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/xen",
        .redirect_path = NULL,
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // 启动ID
    {
        .original_path = "/proc/sys/kernel/random/boot_id",
        .redirect_path = "/data/local/tmp/props/proc_sys_kernel_random_boot_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // DMI信息 - 部分真实设备可能有
    {
        .original_path = "/sys/class/dmi/id/sys_vendor",
        .redirect_path = "/data/misc/props/sys_dmi_vendor",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/dmi/id/product_name",
        .redirect_path = "/data/misc/props/sys_dmi_product",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // dm-verity 状态文件 - 防止安全检测
    {
        .original_path = "/sys/module/dm_verity/parameters/status",
        .redirect_path = "/data/misc/props/sys_dm_verity_status",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // AVB (Android Verified Boot) 相关文件 - 防止验证启动检测
    {
        .original_path = "/sys/fs/avb/",
        .redirect_path = "/data/misc/props/sys_fs_avb_dir",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // /dev 目录下的属性文件 - Android系统属性服务核心文件
    {
        .original_path = "/dev/__properties__",
        .redirect_path = "/data/misc/props/dev_properties",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // 其他 /dev 下的重要属性文件
    {
        .original_path = "/dev/socket/property_service",
        .redirect_path = "/data/misc/props/dev_socket_property_service",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/dev/kmsg",
        .redirect_path = "/data/misc/props/dev_kmsg",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    // ============================================================================
    // 一些虚拟机检测
    // ============================================================================
    {
        .original_path = "/sys/module/goldfish_battery",
        .redirect_path = "/data/misc/props/fake_empty_dir111",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/module/kvm_intel",
        .redirect_path = "/data/misc/props/fake_empty_dir111",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/module/kvm_amd",
        .redirect_path = "/data/misc/props/fake_empty_dir111",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 传感器和输入设备重定向 - 基于实际存在的路径
    // ============================================================================
    {
        .original_path = "/sys/class/misc/sensors",
        .redirect_path = "/data/misc/props/sys_misc_sensors",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/bus/iio/devices/iio:device0/name",
        .redirect_path = "/data/misc/props/sys_iio_device0_name",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/bus/iio/devices/iio:device1/name",
        .redirect_path = "/data/misc/props/sys_iio_device1_name",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/bus/iio/devices/iio:device0/in_accel_scale",
        .redirect_path = "/data/misc/props/sys_iio_accel_scale",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/bus/iio/devices/iio:device1/in_gyro_scale",
        .redirect_path = "/data/misc/props/sys_iio_gyro_scale",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // Cuttlefish特有的虚拟化检测路径
    // ============================================================================
    {
        .original_path = "/sys/devices/virtual/misc/goldfish_pipe",
        .redirect_path = "/data/misc/props/sys_goldfish_pipe",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/virtual/misc/goldfish_sync",
        .redirect_path = "/data/misc/props/sys_goldfish_sync",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/virtual/misc/goldfish_address_space",
        .redirect_path = "/data/misc/props/sys_goldfish_address_space",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/pci0000:00/0000:00:01.0/virtio0",
        .redirect_path = "/data/misc/props/sys_virtio0",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/devices/pci0000:00/0000:00:02.0/virtio1",
        .redirect_path = "/data/misc/props/sys_virtio1",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/bus/virtio/devices/virtio0/device",
        .redirect_path = "/data/misc/props/sys_virtio_device0",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/bus/virtio/devices/virtio1/device",
        .redirect_path = "/data/misc/props/sys_virtio_device1",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 蓝牙和无线网络重定向
    // ============================================================================
    {
        .original_path = "/sys/class/bluetooth/hci0/address",
        .redirect_path = "/data/misc/props/sys_bluetooth_address",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = generate_stable_bt_mac
    },
    {
        .original_path = "/sys/class/bluetooth/hci0/name",
        .redirect_path = "/data/misc/props/sys_bluetooth_name",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/net/wireless",
        .redirect_path = "/data/misc/props/proc_net_wireless",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/net/wlan0/operstate",
        .redirect_path = "/data/misc/props/sys_net_wlan0_operstate",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 输入设备重定向 - 触摸屏和按键
    // ============================================================================
    {
        .original_path = "/proc/bus/input/devices",
        .redirect_path = "/data/misc/props/proc_bus_input_devices",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/input/input0/name",
        .redirect_path = "/data/misc/props/sys_input0_name",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/input/input1/name",
        .redirect_path = "/data/misc/props/sys_input1_name",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 显示和GPU重定向
    // ============================================================================
    {
        .original_path = "/sys/class/graphics/fb0/modes",
        .redirect_path = "/data/misc/props/sys_graphics_fb0_modes",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/graphics/fb0/virtual_size",
        .redirect_path = "/data/misc/props/sys_graphics_fb0_virtual_size",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/backlight/panel0-backlight/brightness",
        .redirect_path = "/data/misc/props/sys_backlight_brightness",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/drm/card0/device/vendor",
        .redirect_path = "/data/misc/props/sys_drm_vendor",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/drm/card0/device/device",
        .redirect_path = "/data/misc/props/sys_drm_device",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 音频设备重定向
    // ============================================================================
    {
        .original_path = "/proc/asound/cards",
        .redirect_path = "/data/misc/props/proc_asound_cards",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/asound/devices",
        .redirect_path = "/data/misc/props/proc_asound_devices",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/sound/card0/id",
        .redirect_path = "/data/misc/props/sys_sound_card0_id",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 摄像头设备重定向
    // ============================================================================
    {
        .original_path = "/sys/class/video4linux/video0/name",
        .redirect_path = "/data/misc/props/sys_video0_name",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/video4linux/video1/name",
        .redirect_path = "/data/misc/props/sys_video1_name",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 安全和加密设备重定向
    // ============================================================================
    {
        .original_path = "/sys/class/misc/hw_random/rng_available",
        .redirect_path = "/data/misc/props/sys_hw_random_available",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // 硬件平台信息重定向 - 隐藏x86特征
    // ============================================================================
    {
        .original_path = "/proc/device-tree/model",
        .redirect_path = "/data/misc/props/proc_dt_model",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/proc/device-tree/compatible",
        .redirect_path = "/data/misc/props/proc_dt_compatible",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/firmware/devicetree/base/model",
        .redirect_path = "/data/misc/props/sys_dt_model",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },

    // ============================================================================
    // DMI信息重定向 - x86特有，需要隐藏
    // ============================================================================
    {
        .original_path = "/sys/class/dmi/id/product_name",
        .redirect_path = "/data/misc/props/sys_dmi_product_name",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/dmi/id/sys_vendor",
        .redirect_path = "/data/misc/props/sys_dmi_sys_vendor",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/dmi/id/board_vendor",
        .redirect_path = "/data/misc/props/sys_dmi_board_vendor",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    {
        .original_path = "/sys/class/dmi/id/bios_vendor",
        .redirect_path = "/data/misc/props/sys_dmi_bios_vendor",
        .fake_content = NULL,
        .fake_content_len = 0,
        .dynamic_generator = NULL
    },
    { NULL, NULL, NULL, 0, NULL } // 结束标记
};

// ============================================================================
// 无锁分层查找优化 - 基于路径前缀的快速查找
// ============================================================================

// 路径前缀分类
enum path_category {
    PATH_PROC = 0,      // /proc/
    PATH_SYS,           // /sys/
    PATH_DEV,           // /dev/
    PATH_SYSTEM,        // /system/
    PATH_VENDOR,        // /vendor/
    PATH_DATA,          // /data/
    PATH_OTHER,         // 其他路径
    PATH_CATEGORY_MAX
};

// 每个分类的条目列表
struct category_entries {
    struct prop_redirect_entry **entries;
    int count;
    int capacity;
};

// 分层查找表 - 初始化后只读，无需锁
static struct category_entries category_tables[PATH_CATEGORY_MAX];
static bool fast_lookup_initialized = false;

// 根据路径前缀快速分类
static enum path_category categorize_path(const char *path)
{
    if (!path)
        return PATH_OTHER;

    // 使用简单的前缀匹配，避免复杂的字符串操作
    switch (path[0]) {
        case '/':
            if (path[1] == 'p' && path[2] == 'r' && path[3] == 'o' && path[4] == 'c' && path[5] == '/')
                return PATH_PROC;
            if (path[1] == 's' && path[2] == 'y' && path[3] == 's' && path[4] == '/')
                return PATH_SYS;
            if (path[1] == 'd' && path[2] == 'e' && path[3] == 'v' && path[4] == '/')
                return PATH_DEV;
            if (path[1] == 's' && path[2] == 'y' && path[3] == 's' && path[4] == 't' &&
                path[5] == 'e' && path[6] == 'm' && path[7] == '/')
                return PATH_SYSTEM;
            if (path[1] == 'v' && path[2] == 'e' && path[3] == 'n' && path[4] == 'd' &&
                path[5] == 'o' && path[6] == 'r' && path[7] == '/')
                return PATH_VENDOR;
            if (path[1] == 'd' && path[2] == 'a' && path[3] == 't' && path[4] == 'a' && path[5] == '/')
                return PATH_DATA;
            break;
    }
    return PATH_OTHER;
}

// 无锁分层查找初始化
static int init_fast_lookup(void)
{
    int i, category;
    enum path_category cat;
    int category_counts[PATH_CATEGORY_MAX] = {0};


    if (fast_lookup_initialized)
        return 0;

    // 第一遍：统计每个分类的条目数量
    for (i = 0; prop_redirects[i].original_path != NULL; i++) {
        cat = categorize_path(prop_redirects[i].original_path);
        category_counts[cat]++;
    }

    // 为每个分类分配内存
    for (category = 0; category < PATH_CATEGORY_MAX; category++) {
        if (category_counts[category] > 0) {
            category_tables[category].entries = kmalloc(
                category_counts[category] * sizeof(struct prop_redirect_entry *),
                GFP_KERNEL);
            if (!category_tables[category].entries) {
                printk(KERN_ERR "Failed to allocate category table %d\n", category);
                goto cleanup;
            }
            category_tables[category].capacity = category_counts[category];
            category_tables[category].count = 0;
        } else {
            category_tables[category].entries = NULL;
            category_tables[category].capacity = 0;
            category_tables[category].count = 0;
        }
    }

    // 第二遍：填充分类表
    for (i = 0; prop_redirects[i].original_path != NULL; i++) {
        cat = categorize_path(prop_redirects[i].original_path);
        int idx = category_tables[cat].count++;
        category_tables[cat].entries[idx] = &prop_redirects[i];
    }

    // 内存屏障确保所有写入完成
    smp_wmb();
    fast_lookup_initialized = true;

    printk(KERN_INFO "Fast lookup tables initialized:\n");
    for (category = 0; category < PATH_CATEGORY_MAX; category++) {
        if (category_tables[category].count > 0) {
            printk(KERN_INFO "  Category %d: %d entries\n",
                   category, category_tables[category].count);
        }
    }
    return 0;

cleanup:
    for (category = 0; category < PATH_CATEGORY_MAX; category++) {
        kfree(category_tables[category].entries);
        category_tables[category].entries = NULL;
        category_tables[category].count = 0;
        category_tables[category].capacity = 0;
    }
    return -ENOMEM;
}

// 清理分层查找表
static void cleanup_fast_lookup(void)
{
    int category;

    if (!fast_lookup_initialized)
        return;

    for (category = 0; category < PATH_CATEGORY_MAX; category++) {
        kfree(category_tables[category].entries);
        category_tables[category].entries = NULL;
        category_tables[category].count = 0;
        category_tables[category].capacity = 0;
    }

    fast_lookup_initialized = false;
    printk(KERN_INFO "Fast lookup tables cleaned up\n");
}

// 无锁快速查找函数 - 基于路径分类
static struct prop_redirect_entry* find_redirect_entry_fast(const char *path)
{
    enum path_category cat;
    struct category_entries *table;
    int i;

    if (!path || !fast_lookup_initialized)
        return NULL;

    // 根据路径前缀快速定位到对应的分类表
    cat = categorize_path(path);
    table = &category_tables[cat];

    if (!table->entries || table->count == 0)
        return NULL;

    // 在小的分类表中线性搜索（通常只有几十个条目）
    for (i = 0; i < table->count; i++) {
        if (strcmp(table->entries[i]->original_path, path) == 0) {
            return table->entries[i];
        }
    }

    return NULL;
}

// 简化的检测记录 - 避免与app_detection冲突
static void record_simple_detection(const char *path)
{
    static unsigned long last_log_time = 0;
    unsigned long current_time = ktime_get_real_seconds();

    // 限制日志频率，避免日志洪水
    if (current_time - last_log_time >= 5) {
        const char *detection_type = "UNKNOWN";

        if (strstr(path, "/proc/cpuinfo") || strstr(path, "/sys/devices/system/cpu")) {
            detection_type = "CPU_INFO";
        } else if (strstr(path, "/system/build.prop") || strstr(path, "build.prop")) {
            detection_type = "BUILD_INFO";
        } else if (strstr(path, "/sys/class/net") || strstr(path, "/proc/net")) {
            detection_type = "NETWORK_INFO";
        } else if (strstr(path, "/sys/class/sensors") || strstr(path, "/sys/bus/iio")) {
            detection_type = "SENSOR_INFO";
        }

        printk("VM_ANTI_DETECTION: Detection intercepted: %s accessed %s (%s)\n",
                         current->comm, path, detection_type);
        last_log_time = current_time;
    }
}

// 检查路径是否需要重定向 - 优化版本，移除app_detection依赖
struct prop_redirect_entry* find_redirect_entry(const char *path)
{
    struct prop_redirect_entry *entry = NULL;

    // 优先使用无锁分层查找
    if (fast_lookup_initialized) {
        entry = find_redirect_entry_fast(path);
    } else {
        // 后备：线性搜索（仅在初始化失败时使用）
        if (!path)
            return NULL;

        for (entry = prop_redirects; entry->original_path; entry++) {
            if (strcmp(path, entry->original_path) == 0) {
                break;
            }
        }
        if (!entry->original_path) {
            entry = NULL;
        }
    }

    // 如果找到重定向条目，记录简单的检测信息（仅用于调试）
    if (entry && path) {
        record_simple_detection(path);
    }

    return entry;
}

// 属性文件打开拦截
int redirect_prop_file_open(const char __user *filename, int flags, umode_t mode, struct file **result_file)
{
    struct filename *name;
    struct prop_redirect_entry *entry;
    kuid_t uid;
    int ret = 0;

    if (!enable_vm_anti_detection || !filename)
        return 0;

    uid = current_uid();
    if (!should_hide_from_user(uid))
        return 0;

    // 从用户空间复制文件名
    name = getname(filename);
    if (IS_ERR(name) || !name || !name->name)
        return 0;

    // 查找是否需要重定向
    entry = find_redirect_entry(name->name);
    if (entry) {
        printk("VM_ANTI_DETECTION: Property file redirect for UID %u: %s\n",
                          from_kuid(&init_user_ns, uid), name->name);

        // 更新统计信息
        redirect_stats.total_redirects++;

        if (entry->fake_content) {
            // 创建内存中的虚拟文件
            *result_file = create_virtual_file(entry);
            if (*result_file) {
                redirect_stats.successful_redirects++;
                putname(name);
                return 1;
            } else {
                redirect_stats.failed_redirects++;
            }
        } else if (entry->redirect_path) {
            // 重定向到另一个文件
            *result_file = filp_open(entry->redirect_path, flags, mode);
            if (!IS_ERR(*result_file)) {
                redirect_stats.successful_redirects++;
                printk("VM_ANTI_DETECTION: Successfully redirected %s to %s\n",
                                 name->name, entry->redirect_path);
                putname(name);
                return 1;
            } else {
                // printk("VM_ANTI_DETECTION: Failed to open redirect file: %s, error: %ld\n",
                //                 entry->redirect_path, PTR_ERR(*result_file));
                redirect_stats.failed_redirects++;
                *result_file = NULL;
            }
        } else if (entry->dynamic_generator) {
            // 动态生成内容
            *result_file = create_dynamic_file(entry);
            if (*result_file) {
                redirect_stats.successful_redirects++;
                redirect_stats.dynamic_generations++;
                putname(name);
                return 1;
            } else {
                redirect_stats.failed_redirects++;
            }
        } else {
            // redirect_path = NULL 表示直接隐藏文件（返回ENOENT）
            printk("VM_ANTI_DETECTION: Hiding file access for UID %u: %s\n",
                             from_kuid(&init_user_ns, uid), name->name);
            redirect_stats.successful_redirects++;
            putname(name);
            return -1;  // 返回-1表示应该返回ENOENT
        }
    }

    putname(name);
    return ret;
}

// 创建目录的辅助函数
static int create_directory_if_needed(const char *path)
{
    // 简化实现：只处理 /data/local/tmp/props 目录
    if (strstr(path, "/data/local/tmp/props/")) {
        struct path parent_path;
        int ret = kern_path("/data/local/tmp/props", LOOKUP_FOLLOW, &parent_path);
        if (ret != 0) {
            // 目录不存在，需要创建
            printk("VM_ANTI_DETECTION: Creating directory /data/local/tmp/props\n");

            // 这里简化处理：假设 /data/local/tmp 已存在
            // 在实际部署时，应该通过init脚本或其他方式预先创建目录
            // mkdir -p /data/local/tmp/props && chmod 755 /data/local/tmp/props

            return 0; // 假设创建成功
        } else {
            path_put(&parent_path);
            return 0; // 目录已存在
        }
    }
    return 0;
}

// 自动复制文件的辅助函数
static int auto_copy_file(const char *src_path, const char *dst_path)
{
    struct file *src_file, *dst_file;
    char *buffer;
    ssize_t bytes_read, bytes_written;
    loff_t src_pos = 0, dst_pos = 0;
    int ret = 0;

    // 分配缓冲区
    buffer = kmalloc(PAGE_SIZE, GFP_KERNEL);
    if (!buffer)
        return -ENOMEM;

    // 打开源文件
    src_file = filp_open(src_path, O_RDONLY, 0);
    if (IS_ERR(src_file)) {
        kfree(buffer);
        return PTR_ERR(src_file);
    }

    // 创建目标目录
    create_directory_if_needed(dst_path);

    // 创建目标文件，设置权限为644（所有用户可读）
    dst_file = filp_open(dst_path, O_WRONLY | O_CREAT | O_TRUNC, 0644);
    if (IS_ERR(dst_file)) {
        filp_close(src_file, NULL);
        kfree(buffer);
        return PTR_ERR(dst_file);
    }

    // 确保文件权限正确设置
    struct inode *inode = file_inode(dst_file);
    if (inode) {
        // 设置文件权限为644（owner可读写，group和other可读）
        inode->i_mode = (inode->i_mode & ~S_IALLUGO) | S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH;
        mark_inode_dirty(inode);
    }

    // 复制文件内容
    while ((bytes_read = kernel_read(src_file, buffer, PAGE_SIZE, &src_pos)) > 0) {
        bytes_written = kernel_write(dst_file, buffer, bytes_read, &dst_pos);
        if (bytes_written != bytes_read) {
            ret = -EIO;
            break;
        }
    }

    if (bytes_read < 0)
        ret = bytes_read;

    // 清理
    filp_close(dst_file, NULL);
    filp_close(src_file, NULL);
    kfree(buffer);

    printk("VM_ANTI_DETECTION: Auto-copied %s to %s (result: %d)\n",
           src_path, dst_path, ret);

    return ret;
}

// 从重定向文件读取内容的辅助函数
static ssize_t read_redirect_file(const char *redirect_path, char __user *buf, size_t count, loff_t *pos)
{
    struct file *redirect_file;
    ssize_t ret;
    char *kernel_buf;

    // 打开重定向文件
    redirect_file = filp_open(redirect_path, O_RDONLY, 0);
    if (IS_ERR(redirect_file)) {
        printk("VM_ANTI_DETECTION: Failed to open redirect file: %s, error: %ld\n",
                          redirect_path, PTR_ERR(redirect_file));
        return 0;
    }

    // 分配内核缓冲区
    kernel_buf = kmalloc(count, GFP_KERNEL);
    if (!kernel_buf) {
        filp_close(redirect_file, NULL);
        return -ENOMEM;
    }

    // 设置文件位置并读取到内核缓冲区
    ret = kernel_read(redirect_file, kernel_buf, count, pos);

    if (ret > 0) {
        // 复制到用户空间
        if (copy_to_user(buf, kernel_buf, ret)) {
            ret = -EFAULT;
        } else {
            printk("VM_ANTI_DETECTION: Read %zd bytes from redirect file: %s\n", ret, redirect_path);
        }
    }

    kfree(kernel_buf);
    filp_close(redirect_file, NULL);
    return ret;
}

// 属性文件access检查重定向
int redirect_prop_file_access(const char __user *filename, int mode, int *result)
{
    struct filename *name;
    struct prop_redirect_entry *entry;
    kuid_t uid;
    int ret = 0;

    if (!enable_vm_anti_detection || !filename || !result)
        return 0;

    uid = current_uid();
    if (!should_hide_from_user(uid))
        return 0;

    // 从用户空间复制文件名
    name = getname(filename);
    if (IS_ERR(name) || !name || !name->name)
        return 0;

    // 查找是否需要重定向
    entry = find_redirect_entry(name->name);
    if (entry) {
        printk("VM_ANTI_DETECTION: Property file access redirect for UID %u: %s\n",
                          from_kuid(&init_user_ns, uid), name->name);

        if (entry->redirect_path) {
            // 检查重定向文件的存在性
            struct path path;
            int lookup_error = kern_path(entry->redirect_path, LOOKUP_FOLLOW, &path);
            if (lookup_error == 0) {
                // 重定向文件存在，检查权限
                struct inode *inode = d_inode(path.dentry);
                int access_mask = 0;

                // 将mode转换为内核权限掩码
                if (mode & R_OK) access_mask |= MAY_READ;
                if (mode & W_OK) access_mask |= MAY_WRITE;
                if (mode & X_OK) access_mask |= MAY_EXEC;

                struct mnt_idmap *idmap = mnt_idmap(path.mnt);
                int access_result = inode_permission(idmap, inode, access_mask);
                path_put(&path);
                *result = access_result;
                ret = 1; // 表示已处理
            } else {
                // 重定向文件不存在
                *result = -ENOENT;
                ret = 1; // 表示已处理
            }
        } else if (entry->fake_content || entry->dynamic_generator) {
            // 对于内存中的假内容或动态生成的内容，总是返回存在
            *result = 0; // 成功
            ret = 1; // 表示已处理
        } else {
            // redirect_path为NULL表示隐藏文件
            *result = -ENOENT;
            ret = 1; // 表示已处理
        }
    }

    putname(name);
    return ret;
}

// 属性文件读取拦截
ssize_t redirect_prop_file_read(struct file *file, char __user *buf, size_t count, loff_t *pos)
{
    char *path_buf, *full_path;
    struct prop_redirect_entry *entry;
    kuid_t uid;
    ssize_t ret = 0;
    size_t copy_len;

    if (!enable_vm_anti_detection || !file)
        return 0;

    uid = current_uid();
    if (!should_hide_from_current_process()) {
        // 调试日志：显示为什么不进行重定向
        uid_t uid_val = from_kuid(&init_user_ns, uid);
        if (uid_val == 2000) {
            printk("VM_ANTI_DETECTION: redirect_prop_file_read - should_hide_from_current_process returned 0 for UID %u\n", uid_val);
        }
        return 0;
    }

    // 调试日志：确认进入重定向逻辑
    printk("VM_ANTI_DETECTION: redirect_prop_file_read - entering redirect logic for UID %u\n",
           from_kuid(&init_user_ns, uid));

    // 获取文件路径
    path_buf = kmalloc(PATH_MAX, GFP_KERNEL);
    if (!path_buf)
        return 0;

    full_path = d_path(&file->f_path, path_buf, PATH_MAX);
    if (IS_ERR(full_path)) {
        kfree(path_buf);
        return 0;
    }

    // 调试日志：显示正在读取的文件路径
    printk("VM_ANTI_DETECTION: redirect_prop_file_read checking path: %s\n", full_path);

    // 查找是否需要重定向
    entry = find_redirect_entry(full_path);
    if (entry) {
        printk("VM_ANTI_DETECTION: Property file read redirect for UID %u: %s\n",
                          from_kuid(&init_user_ns, uid), full_path);

        if (entry->dynamic_generator) {
            // 使用动态生成函数
            char *dynamic_buf;
            int generated_len;

            dynamic_buf = kmalloc(PAGE_SIZE, GFP_KERNEL);
            if (!dynamic_buf) {
                ret = -ENOMEM;
            } else {
                generated_len = entry->dynamic_generator(dynamic_buf, PAGE_SIZE);
                if (generated_len > 0) {
                    if (*pos >= generated_len) {
                        ret = 0; // EOF
                    } else {
                        // 计算要复制的字节数
                        copy_len = min(count, (size_t)(generated_len - *pos));

                        // 复制动态生成的内容到用户空间
                        if (copy_to_user(buf, dynamic_buf + *pos, copy_len)) {
                            ret = -EFAULT;
                        } else {
                            *pos += copy_len;
                            ret = copy_len;
                            printk("VM_ANTI_DETECTION: Served dynamic content for %s: %zd bytes\n",
                                            full_path, copy_len);
                        }
                    }
                } else {
                    ret = -EIO;
                }
                kfree(dynamic_buf);
            }
        } else if (entry->redirect_path) {
            // 检查重定向文件是否存在，如果不存在则尝试自动复制
            struct file *test_file = filp_open(entry->redirect_path, O_RDONLY, 0);
            if (IS_ERR(test_file)) {
                // 重定向文件不存在，尝试从 /data/misc/props/ 自动复制
                char *src_path = NULL;

                // 构造源文件路径：将 /data/local/tmp/props/ 替换为 /data/misc/props/
                if (strstr(entry->redirect_path, "/data/local/tmp/props/")) {
                    src_path = kmalloc(PATH_MAX, GFP_KERNEL);
                    if (src_path) {
                        const char *filename = strrchr(entry->redirect_path, '/');
                        if (filename) {
                            snprintf(src_path, PATH_MAX, "/data/misc/props%s", filename);

                            // 检查源文件是否存在
                            struct file *src_test = filp_open(src_path, O_RDONLY, 0);
                            if (!IS_ERR(src_test)) {
                                filp_close(src_test, NULL);

                                printk("VM_ANTI_DETECTION: Redirect file %s not found, attempting auto-copy from %s\n",
                                       entry->redirect_path, src_path);

                                int copy_result = auto_copy_file(src_path, entry->redirect_path);
                                if (copy_result == 0) {
                                    printk("VM_ANTI_DETECTION: Auto-copy successful\n");
                                } else {
                                    printk("VM_ANTI_DETECTION: Auto-copy failed with error %d\n", copy_result);
                                }
                            } else {
                                printk("VM_ANTI_DETECTION: Source file %s not found, cannot auto-copy\n", src_path);
                            }
                        }
                        kfree(src_path);
                    }
                }
            } else {
                filp_close(test_file, NULL);
            }

            // 从重定向文件读取内容
            ret = read_redirect_file(entry->redirect_path, buf, count, pos);
        } else if (entry->fake_content) {
            // 使用内存中的假内容
            if (*pos >= entry->fake_content_len) {
                ret = 0; // EOF
            } else {
                // 计算要复制的字节数
                copy_len = min(count, entry->fake_content_len - *pos);

                // 复制假内容到用户空间
                if (copy_to_user(buf, entry->fake_content + *pos, copy_len)) {
                    ret = -EFAULT;
                } else {
                    *pos += copy_len;
                    ret = copy_len;
                }
            }
        }

        kfree(path_buf);
        return ret; // 返回实际读取的字节数，表示已处理
    }

    kfree(path_buf);
    return 0; // 返回0表示没有拦截，继续正常处理
}

// 文件路径隐藏 - 防止通过文件句柄获取真实路径
int hide_redirected_file_path(struct file *file, char *buf, int buflen)
{
    char *path_buf, *full_path;
    struct prop_redirect_entry *entry;
    kuid_t uid;
    int ret = 0;

    if (!enable_vm_anti_detection || !file || !buf)
        return 0;

    uid = current_uid();
    if (!should_hide_from_user(uid))
        return 0;

    // 获取文件路径
    path_buf = kmalloc(PATH_MAX, GFP_KERNEL);
    if (!path_buf)
        return 0;

    full_path = d_path(&file->f_path, path_buf, PATH_MAX);
    if (IS_ERR(full_path)) {
        kfree(path_buf);
        return 0;
    }

    // 查找是否是重定向的文件
    entry = find_redirect_entry(full_path);
    if (entry) {
        printk("VM_ANTI_DETECTION: Hiding redirected file path for UID %u: %s -> %s\n",
                          from_kuid(&init_user_ns, uid), full_path, entry->original_path);

        // 返回原始路径而不是重定向后的路径
        strncpy(buf, entry->original_path, buflen - 1);
        buf[buflen - 1] = '\0';
        ret = 1; // 表示已处理
    }

    kfree(path_buf);
    return ret;
}

// 获取文件描述符的原始路径（用于隐藏重定向）
int get_original_path_for_fd(struct file *file, struct path *original_path)
{
    char *path_buf, *full_path;
    struct prop_redirect_entry *entry;
    kuid_t uid;
    int ret = -1;

    if (!enable_vm_anti_detection || !file || !original_path)
        return -1;

    uid = current_uid();
    if (!should_hide_from_user(uid))
        return -1;

    // 获取当前文件路径
    path_buf = kmalloc(PATH_MAX, GFP_KERNEL);
    if (!path_buf)
        return -1;

    full_path = d_path(&file->f_path, path_buf, PATH_MAX);
    if (IS_ERR(full_path)) {
        kfree(path_buf);
        return -1;
    }

    // 查找是否是重定向的文件
    entry = find_redirect_entry_by_redirect_path(full_path);
    if (entry && entry->original_path) {
        // 构造原始路径的path结构
        struct path orig_path;
        if (kern_path(entry->original_path, LOOKUP_FOLLOW, &orig_path) == 0) {
            *original_path = orig_path;
            ret = 0;
            printk("VM_ANTI_DETECTION: Found original path for redirect: %s -> %s\n",
                             full_path, entry->original_path);
        }
    }

    kfree(path_buf);
    return ret;
}

// 通过重定向路径查找映射条目
struct prop_redirect_entry *find_redirect_entry_by_redirect_path(const char *redirect_path)
{
    struct prop_redirect_entry *entry;

    if (!redirect_path)
        return NULL;

    for (entry = prop_redirects; entry->original_path; entry++) {
        if (entry->redirect_path &&
            strcmp(entry->redirect_path, redirect_path) == 0) {
            return entry;
        }
    }

    return NULL;
}

// 获取readlink的原始路径（用于隐藏重定向）
char *get_original_path_for_readlink(const char *redirect_path)
{
    struct prop_redirect_entry *entry;
    char *original_path = NULL;

    if (!redirect_path)
        return NULL;

    // 查找是否是重定向的文件
    entry = find_redirect_entry_by_redirect_path(redirect_path);
    if (entry && entry->original_path) {
        original_path = kmalloc(strlen(entry->original_path) + 1, GFP_KERNEL);
        if (original_path) {
            strcpy(original_path, entry->original_path);
        }
    }

    return original_path;
}

// 获取审计日志的原始路径（用于隐藏重定向）
char *get_original_path_for_audit(const char *redirect_path)
{
    struct prop_redirect_entry *entry;
    char *original_path = NULL;

    if (!redirect_path)
        return NULL;

    // 查找是否是重定向的文件
    entry = find_redirect_entry_by_redirect_path(redirect_path);
    if (entry && entry->original_path) {
        original_path = kmalloc(strlen(entry->original_path) + 1, GFP_ATOMIC);
        if (original_path) {
            strcpy(original_path, entry->original_path);
            printk("VM_ANTI_DETECTION: Hidden audit path: %s -> %s\n",
                             redirect_path, original_path);
        }
    }

    return original_path;
}

// 初始化属性重定向模块
int init_prop_redirect(void)
{
    int ret;

    printk(KERN_INFO "Initializing property redirect system...\n");

    // 验证重定向表
    ret = validate_redirect_table();
    if (ret < 0) {
        printk(KERN_ERR "Invalid redirect table: %d\n", ret);
        return ret;
    }

    // 初始化统计信息
    memset(&redirect_stats, 0, sizeof(redirect_stats));

    // 初始化快速查找表
    ret = init_fast_lookup();
    if (ret < 0) {
        printk(KERN_ERR "Failed to initialize fast lookup tables: %d\n", ret);
        return ret;
    }

    printk(KERN_INFO "Property redirect system initialized successfully\n");
    printk(KERN_INFO "Loaded %zu redirect entries\n", ARRAY_SIZE(prop_redirects));

    return 0;
}

// 验证重定向表的有效性
static int validate_redirect_table(void)
{
    int i;
    int valid_entries = 0;

    for (i = 0; i < ARRAY_SIZE(prop_redirects); i++) {
        // 检查是否是结束标记
        if (!prop_redirects[i].original_path) {
            // 这应该是结束标记，验证其他字段也是NULL
            if (prop_redirects[i].redirect_path == NULL &&
                prop_redirects[i].fake_content == NULL &&
                prop_redirects[i].dynamic_generator == NULL) {
                // 这是有效的结束标记，停止验证
                break;
            } else {
                printk(KERN_ERR "Invalid redirect entry %d: incomplete NULL entry\n", i);
                return -EINVAL;
            }
        }

        // 检查路径长度
        if (strlen(prop_redirects[i].original_path) >= PATH_MAX) {
            printk(KERN_ERR "Invalid redirect entry %d: original_path too long\n", i);
            return -EINVAL;
        }

        if (prop_redirects[i].redirect_path &&
            strlen(prop_redirects[i].redirect_path) >= PATH_MAX) {
            printk(KERN_ERR "Invalid redirect entry %d: redirect_path too long\n", i);
            return -EINVAL;
        }

        valid_entries++;
    }

    printk(KERN_INFO "Redirect table validation passed: %d valid entries\n", valid_entries);
    return 0;
}

// 清理临时文件（可选）
static void cleanup_redirect_files(void)
{
    int i;
    struct file *file;

    // 可选：删除临时创建的重定向文件
    // 注意：通常我们不删除这些文件，因为它们可能包含用户自定义的内容

    for (i = 0; i < ARRAY_SIZE(prop_redirects); i++) {
        if (!prop_redirects[i].redirect_path)
            continue;

        // 检查文件是否存在
        file = filp_open(prop_redirects[i].redirect_path, O_RDONLY, 0);
        if (!IS_ERR(file)) {
            filp_close(file, NULL);
            printk(KERN_DEBUG "Redirect file preserved: %s\n",
                   prop_redirects[i].redirect_path);
        }
    }
}

// 打印统计信息
static void print_redirect_stats(void)
{
    printk(KERN_INFO "Property redirect statistics:\n");
    printk(KERN_INFO "  Total redirects: %lu\n", redirect_stats.total_redirects);
    printk(KERN_INFO "  Successful redirects: %lu\n", redirect_stats.successful_redirects);
    printk(KERN_INFO "  Failed redirects: %lu\n", redirect_stats.failed_redirects);
    printk(KERN_INFO "  Dynamic generations: %lu\n", redirect_stats.dynamic_generations);
}

// 清理属性重定向模块
void cleanup_prop_redirect(void)
{
    printk(KERN_INFO "Cleaning up property redirect system...\n");

    // 打印统计信息
    print_redirect_stats();

    // 清理重定向文件（可选）
    cleanup_redirect_files();

    // 清理快速查找表
    cleanup_fast_lookup();

    // 清理统计信息
    memset(&redirect_stats, 0, sizeof(redirect_stats));

    printk(KERN_INFO "Property redirect system cleaned up\n");
}

// 创建虚拟文件（基于fake_content）
static struct file *create_virtual_file(struct prop_redirect_entry *entry)
{
    struct file *file;
    char temp_path[64];
    loff_t pos = 0;
    ssize_t bytes_written;

    if (!entry || !entry->fake_content || entry->fake_content_len == 0)
        return NULL;

    // 创建临时文件路径
    snprintf(temp_path, sizeof(temp_path), "/tmp/magisk_virtual_%p", entry);

    // 创建临时文件
    file = filp_open(temp_path, O_WRONLY | O_CREAT | O_TRUNC, 0644);
    if (IS_ERR(file)) {
        return NULL;
    }

    // 写入fake内容
    bytes_written = kernel_write(file, entry->fake_content, entry->fake_content_len, &pos);
    if (bytes_written != entry->fake_content_len) {
        filp_close(file, NULL);
        return NULL;
    }

    // 重新以只读方式打开
    filp_close(file, NULL);
    file = filp_open(temp_path, O_RDONLY, 0);
    if (IS_ERR(file)) {
        return NULL;
    }

    return file;
}

// 创建动态文件（基于dynamic_generator）
static struct file *create_dynamic_file(struct prop_redirect_entry *entry)
{
    struct file *file;
    char temp_path[64];
    char *buffer;
    loff_t pos = 0;
    ssize_t bytes_written;
    int content_len;

    if (!entry || !entry->dynamic_generator)
        return NULL;

    // 分配缓冲区
    buffer = kmalloc(PAGE_SIZE, GFP_KERNEL);
    if (!buffer)
        return NULL;

    // 生成动态内容
    content_len = entry->dynamic_generator(buffer, PAGE_SIZE);
    if (content_len <= 0) {
        kfree(buffer);
        return NULL;
    }

    // 创建临时文件路径
    snprintf(temp_path, sizeof(temp_path), "/tmp/magisk_dynamic_%p", entry);

    // 创建临时文件
    file = filp_open(temp_path, O_WRONLY | O_CREAT | O_TRUNC, 0644);
    if (IS_ERR(file)) {
        kfree(buffer);
        return NULL;
    }

    // 写入动态内容
    bytes_written = kernel_write(file, buffer, content_len, &pos);
    kfree(buffer);

    if (bytes_written != content_len) {
        filp_close(file, NULL);
        return NULL;
    }

    // 重新以只读方式打开
    filp_close(file, NULL);
    file = filp_open(temp_path, O_RDONLY, 0);
    if (IS_ERR(file)) {
        return NULL;
    }

    return file;
}

#ifdef MODULE
EXPORT_SYMBOL(redirect_prop_file_open);
EXPORT_SYMBOL(redirect_prop_file_read);
EXPORT_SYMBOL(redirect_prop_file_access);
EXPORT_SYMBOL(hide_redirected_file_path);
EXPORT_SYMBOL(get_original_path_for_fd);
EXPORT_SYMBOL(find_redirect_entry);
EXPORT_SYMBOL(find_redirect_entry_by_redirect_path);
EXPORT_SYMBOL(get_original_path_for_readlink);
EXPORT_SYMBOL(get_original_path_for_audit);
EXPORT_SYMBOL(init_prop_redirect);
EXPORT_SYMBOL(cleanup_prop_redirect);
#endif