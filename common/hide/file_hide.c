// SPDX-License-Identifier: GPL-2.0
/*
 * VM Anti-Detection System Calls
 *
 * System call hiding functionality module, handles virtual machine detection hiding for various system calls
 */

#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/fs.h>
#include <linux/uaccess.h>
#include <linux/string.h>
#include <linux/slab.h>
#include <linux/cred.h>
#include <linux/sched.h>
#include <linux/sched/task.h>
#include <linux/types.h>
#include <linux/uidgid.h>
#include <linux/user_namespace.h>
#include <linux/namei.h>
#include <linux/delay.h>
#include "magisk_hide.h"


// openat system call hiding
int hide_vm_in_openat(const char __user *filename)
{
    kuid_t uid;
    struct filename *name;
    int ret = 0;

    if (!filename)
        return 0;

    uid = current_uid();
    if (!should_hide_from_current_process())
        return 0;

    //系统进程不拦截
    if(is_systemui_process(current->comm)) {
        return 0; // 不隐藏系统关键文件
    }

    // 从用户空间复制文件名
    name = getname(filename);
    if (IS_ERR(name))
        return 0;

    if(is_virtual_environment_path(name->name)) {
        printk("VM_ANTI_DETECTION Hiding openat for UID %u: %s\n",
                          from_kuid(&init_user_ns, uid), name->name);
        ret = 1; // Hide this path
    }

    // 检查是否为dlopen尝试打开的虚拟机相关库文件
    if (ret == 0 && hide_vm_in_dlopen(name->name)) {
        printk("VM_ANTI_DETECTION Hiding dlopen via openat for UID %u: %s\n",
                          from_kuid(&init_user_ns, uid), name->name);
        ret = 1; // Hide this library file
    }

    // 添加时序攻击防护
    add_anti_timing_delay();

    putname(name);
    return ret;
}

// openat2 system call hiding
int hide_vm_in_openat2(const char __user *filename)
{
    return hide_vm_in_openat(filename);
}

// 过滤 getdents64 返回的目录项
int hide_vm_in_getdents(struct linux_dirent64 __user *dirent, int count)
{
    kuid_t uid;
    struct linux_dirent64 *src_entry, *dst_entry;
    char *src_buffer, *dst_buffer;
    int src_offset = 0;
    int dst_offset = 0;
    int should_hide;
    char d_name[256];
    int original_count = count;

    // 基本检查
    if (!enable_vm_anti_detection || !dirent || count <= 0)
        return count;

    uid = current_uid();
    if (!should_hide_from_current_process())
        return count;

    // 分配源和目标缓冲区
    src_buffer = kmalloc(count, GFP_KERNEL);
    if (!src_buffer) {
        return count;  // 分配失败，返回原始数据
    }

    dst_buffer = kmalloc(count, GFP_KERNEL);
    if (!dst_buffer) {
        kfree(src_buffer);
        return count;  // 分配失败，返回原始数据
    }

    // 从用户空间复制数据到源缓冲区
    if (copy_from_user(src_buffer, dirent, count)) {
        kfree(src_buffer);
        kfree(dst_buffer);
        return count;  // 复制失败，返回原始数据
    }

    // 安全地遍历和过滤目录项
    while (src_offset < original_count) {
        src_entry = (struct linux_dirent64 *)(src_buffer + src_offset);

        // 严格的边界检查
        if (src_entry->d_reclen == 0 ||
            src_entry->d_reclen < sizeof(struct linux_dirent64) ||
            src_offset + src_entry->d_reclen > original_count ||
            dst_offset + src_entry->d_reclen > count) {
            break;  // 数据不完整或缓冲区不足，停止处理
        }

        // 安全地复制文件名
        size_t name_len = strnlen(src_entry->d_name, sizeof(d_name) - 1);
        memcpy(d_name, src_entry->d_name, name_len);
        d_name[name_len] = '\0';

        // 检查是否应该隐藏此目录项
        should_hide = 0;

        // 检查虚拟化相关的目录项
        if (strstr(d_name, "kvm") ||
            strstr(d_name, "goldfish") ||
            strstr(d_name, "virtio") ||
            strstr(d_name, "vmware") ||
            strstr(d_name, "vbox") ||
            strstr(d_name, "qemu")) {
            should_hide = 1;
        }

        if (!should_hide) {
            // 保留此目录项：复制到目标缓冲区
            dst_entry = (struct linux_dirent64 *)(dst_buffer + dst_offset);
            memcpy(dst_entry, src_entry, src_entry->d_reclen);
            dst_offset += src_entry->d_reclen;
        }

        // 移动到下一个源目录项
        src_offset += src_entry->d_reclen;
    }

    // 将过滤后的数据复制回用户空间
    if (dst_offset > 0 && copy_to_user(dirent, dst_buffer, dst_offset)) {
        // 复制失败，返回原始数据
        kfree(src_buffer);
        kfree(dst_buffer);
        return count;
    }

    kfree(src_buffer);
    kfree(dst_buffer);

    return dst_offset;  // 返回过滤后的大小
}


// stat 相关系统调用隐藏
int hide_vm_in_stat(struct filename *filename)
{
    kuid_t uid;
    const char *path;

    if (!enable_vm_anti_detection || !filename)
        return 0;

    uid = current_uid();
    if (!should_hide_from_current_process())
        return 0;

    path = filename->name;
    if (is_virtual_environment_path(path)) {
        printk("VM_ANTI_DETECTION Hiding stat for UID %u: %s\n",
                          from_kuid(&init_user_ns, uid), path);
        return 1; // 隐藏此文件状态
    }

    return 0;
}

// stat 系统调用隐藏 (用户空间文件名版本)
int hide_vm_in_stat_user(const char __user *filename)
{
    struct filename *name;
    kuid_t uid;
    int ret = 0;

    if (!enable_vm_anti_detection || !filename)
        return 0;

    uid = current_uid();
    if (!should_hide_from_current_process())
        return 0;

    // 从用户空间复制文件名
    name = getname(filename);
    if (IS_ERR(name) || !name || !name->name)
        return 0;

    if (is_virtual_environment_path(name->name)) {
        printk("VM_ANTI_DETECTION Hiding stat for UID %u: %s\n",
                          from_kuid(&init_user_ns, uid), name->name);
        ret = 1; // 隐藏此文件状态
    }

    putname(name);
    return ret;
}

// access 系统调用隐藏
int hide_vm_in_access(const char __user *filename)
{
    kuid_t uid;
    struct filename *name;
    int ret = 0;

    if (!enable_vm_anti_detection || !filename)
        return 0;

    uid = current_uid();
    if (!should_hide_from_current_process())
        return 0;

    // 从用户空间复制文件名
    name = getname(filename);
    if (IS_ERR(name) || !name || !name->name)
        return 0;

    if (is_virtual_environment_path(name->name)) {
        printk("VM_ANTI_DETECTION Hiding access for UID %u: %s\n",
                          from_kuid(&init_user_ns, uid), name->name);
        ret = 1; // 隐藏此访问检查
    }

    putname(name);
    return ret;
}

// faccessat 系统调用隐藏 - 智能处理虚拟环境检测
int hide_vm_in_faccessat(int dfd, const char __user *filename, int mode)
{
    kuid_t uid;
    struct filename *name;
    int ret = 0;

    if (!enable_vm_anti_detection || !filename)
        return 0;

    uid = current_uid();
    if (!should_hide_from_current_process())
        return 0;

    // 从用户空间复制文件名
    name = getname(filename);
    if (IS_ERR(name) || !name || !name->name)
        return 0;

    // 对于虚拟环境特征路径，返回文件不存在
    if (is_virtual_environment_path(name->name)) {
        printk("VM_ANTI_DETECTION Hiding VM faccessat for UID %u: %s (mode=%d)\n",
                          from_kuid(&init_user_ns, uid), name->name, mode);
        ret = 1; // 隐藏虚拟环境特征
    }
    // 对于应用私有目录等正常路径，确保返回成功以防止虚拟环境检测
    else if (should_fake_success_for_path(name->name)) {
        printk("VM_ANTI_DETECTION Faking success faccessat for UID %u: %s (mode=%d)\n",
                          from_kuid(&init_user_ns, uid), name->name, mode);
        ret = -2; // 特殊返回值，表示应该返回成功
    }

    putname(name);
    return ret;
}


// 检查路径是否需要属性伪造（虚拟机环境相关）
int needs_attribute_spoofing(const char *path)
{
    if (!path)
        return 0;

    // 虚拟机环境相关的系统路径，需要伪造以隐藏修改痕迹
    if (strstr(path, "/system/bin/") ||
        strstr(path, "/system/lib/") ||
        strstr(path, "/system/lib64/") ||
        strstr(path, "/vendor/lib/") ||
        strstr(path, "/vendor/lib64/")) {
        return 1;
    }

    // 检查虚拟机环境可能修改的特定文件
    if (strstr(path, "build.prop") ||
        strstr(path, "default.prop") ||
        strstr(path, "prop.default")) {
        return 1;
    }

    return 0;
}

// 伪造文件属性，让它们看起来像原始系统文件
int spoof_file_attributes(struct kstat *stat, const char *path)
{
    if (!stat || !path)
        return 0;

    if(!should_hide_from_current_process()) {
        return 0;
    }

    if (!needs_attribute_spoofing(path))
        return 0;

    // 伪造时间戳，使用Android 15的真实构建时间
    // Android 15 (API 35) 发布时间: 2024年10月15日
    struct timespec64 fake_time = {
        .tv_sec = 1729008000,  // Oct 15, 2024 12:00:00 UTC (Android 15 发布时间)
        .tv_nsec = 0
    };

    // 将所有时间戳设置为相同的伪造时间
    stat->atime = fake_time;
    stat->mtime = fake_time;
    stat->ctime = fake_time;

    // 根据路径设置合适的用户和组
    if (strstr(path, "/system/bin/") || strstr(path, "/system/xbin/")) {
        // 系统可执行文件: root:shell
        stat->uid = KUIDT_INIT(0);     // root
        stat->gid = KGIDT_INIT(2000);  // shell group
    } else if (strstr(path, "/system/lib/") || strstr(path, "/system/lib64/")) {
        // 系统库文件: root:root
        stat->uid = KUIDT_INIT(0);     // root
        stat->gid = KGIDT_INIT(0);     // root
    } else if (strstr(path, "/vendor/lib/") || strstr(path, "/vendor/lib64/")) {
        // Vendor库文件: root:root
        stat->uid = KUIDT_INIT(0);     // root
        stat->gid = KGIDT_INIT(0);     // root
    } else if (strstr(path, "/system/framework/")) {
        // Framework文件: root:root
        stat->uid = KUIDT_INIT(0);     // root
        stat->gid = KGIDT_INIT(0);     // root
    } else if (strstr(path, "/system/app/") || strstr(path, "/system/priv-app/")) {
        // 系统应用: root:root
        stat->uid = KUIDT_INIT(0);     // root
        stat->gid = KGIDT_INIT(0);     // root
    } else if (strstr(path, "/system/etc/") || strstr(path, "/vendor/etc/") ||
               strstr(path, "/product/etc/") || strstr(path, "/system_ext/etc/")) {
        // 配置文件: root:root
        stat->uid = KUIDT_INIT(0);     // root
        stat->gid = KGIDT_INIT(0);     // root
    } else {
        // 默认: root:root
        stat->uid = KUIDT_INIT(0);     // root
        stat->gid = KGIDT_INIT(0);     // root
    }

    // 根据文件类型和路径设置合适的权限
    if (S_ISREG(stat->mode)) {
        if (strstr(path, "/bin/") || strstr(path, "/xbin/")) {
            // 可执行文件: 755 (rwxr-xr-x)
            stat->mode = (stat->mode & S_IFMT) | 0755;
        } else if (strstr(path, "/lib/") || strstr(path, "/lib64/")) {
            // 库文件: 644 (rw-r--r--)
            stat->mode = (stat->mode & S_IFMT) | 0644;
        } else if (strstr(path, "build.prop") || strstr(path, "default.prop") ||
                   strstr(path, "prop.default")) {
            // 属性文件: 644 (rw-r--r--)
            stat->mode = (stat->mode & S_IFMT) | 0644;
        } else if (strstr(path, "sepolicy")) {
            // SELinux策略文件: 644 (rw-r--r--)
            stat->mode = (stat->mode & S_IFMT) | 0644;
        } else if (strstr(path, "init.rc") || strstr(path, "fstab")) {
            // 初始化脚本和fstab: 644 (rw-r--r--)
            stat->mode = (stat->mode & S_IFMT) | 0644;
        } else if (strstr(path, "/framework/")) {
            // Framework文件: 644 (rw-r--r--)
            stat->mode = (stat->mode & S_IFMT) | 0644;
        } else {
            // 其他普通文件: 644 (rw-r--r--)
            stat->mode = (stat->mode & S_IFMT) | 0644;
        }
    } else if (S_ISDIR(stat->mode)) {
        if (strstr(path, "/system/") || strstr(path, "/vendor/") ||
            strstr(path, "/product/") || strstr(path, "/system_ext/")) {
            // 系统目录: 755 (rwxr-xr-x)
            stat->mode = (stat->mode & S_IFMT) | 0755;
        } else {
            // 其他目录: 755 (rwxr-xr-x)
            stat->mode = (stat->mode & S_IFMT) | 0755;
        }
    }

    printk("VM_ANTI_DETECTION Spoofed attributes for: %s (uid=%u, gid=%u, mode=%o)\n",
                      path, from_kuid(&init_user_ns, stat->uid),
                      from_kgid(&init_user_ns, stat->gid), stat->mode & 07777);
    return 1;  // 属性已被伪造
}

// 检测seccomp架构检测的特殊标志
#define DETECT_X86_FLAG_1 0x12345678  // 常见的检测标志值
#define DETECT_X86_FLAG_2 0xDEADBEEF  // 另一个常见标志
#define DETECT_X86_FLAG_3 0xCAFEBABE  // 第三个常见标志

// CPU特征检测标志
#define DETECT_CPUID_FLAG_1 0x80000000  // CPUID最大功能号检测
#define DETECT_CPUID_FLAG_2 0x80000001  // 扩展处理器信息
#define DETECT_CPUID_FLAG_3 0x40000000  // 虚拟化检测
#define DETECT_CPUID_FLAG_4 0x00000001  // 基本处理器信息

// 虚拟化检测标志
#define DETECT_VM_FLAG_1 0x564D5868    // VMware检测标志 "VMXh"
#define DETECT_VM_FLAG_2 0x58656E65    // Xen检测标志 "Xene"
#define DETECT_VM_FLAG_3 0x4B564D76    // KVM检测标志 "KVMv"

// 防御seccomp架构检测 - 检测是否是架构检测调用
int is_seccomp_arch_detection(unsigned long arg0)
{
    // 检测常见的架构检测标志
    if (arg0 == DETECT_X86_FLAG_1 ||
        arg0 == DETECT_X86_FLAG_2 ||
        arg0 == DETECT_X86_FLAG_3) {
        return 1; // 标记为架构检测调用
    }

    // 检测CPU特征检测标志
    if (arg0 == DETECT_CPUID_FLAG_1 ||
        arg0 == DETECT_CPUID_FLAG_2 ||
        arg0 == DETECT_CPUID_FLAG_3 ||
        arg0 == DETECT_CPUID_FLAG_4) {
        return 2; // 标记为CPU特征检测
    }

    // 检测虚拟化检测标志
    if (arg0 == DETECT_VM_FLAG_1 ||
        arg0 == DETECT_VM_FLAG_2 ||
        arg0 == DETECT_VM_FLAG_3) {
        return 3; // 标记为虚拟化检测
    }

    return 0;
}

// 检查文件路径是否应该被隐藏
int should_hide_file(const char *path)
{
    if (!enable_vm_anti_detection || !path)
        return 0;

    // 检查是否为虚拟环境相关路径
    if (is_virtual_environment_path(path)) {
        return 1;
    }

    return 0;
}

// 反向重定向：从重定向路径获取原始路径
char *get_original_path_from_redirect(const char *redirect_path)
{
    if (!enable_vm_anti_detection || !redirect_path)
        return NULL;

    // 使用prop_redirect模块的函数查找重定向条目
    struct prop_redirect_entry *entry = find_redirect_entry_by_redirect_path(redirect_path);
    if (entry && entry->original_path) {
        return (char *)entry->original_path;
    }

    return NULL;
}

// dlopen 系统调用隐藏 - 防护Native库检测
int hide_vm_in_dlopen(const char *filename)
{
    kuid_t uid;

    if (!enable_vm_anti_detection || !filename)
        return 0;

    uid = current_uid();
    if (!should_hide_from_current_process())
        return 0;

    // 检查虚拟机相关库文件
    if (strstr(filename, "frida") ||
        strstr(filename, "libhook") ||
        strstr(filename, "libinject")) {
        printk("VM_ANTI_DETECTION Hiding dlopen for UID %u: %s\n",
                          from_kuid(&init_user_ns, uid), filename);
        return 1; // 隐藏此库
    }

    // 检查常见的Hook框架库
    if (strstr(filename, "libdobby") ||
        strstr(filename, "libwhale") ||
        strstr(filename, "libmshook")) {
        // 只在检测到可疑进程时隐藏
        if (strstr(current->comm, "detect") ||
            strstr(current->comm, "check") ||
            strstr(current->comm, "scan") ||
            strstr(current->comm, "anti")) {
            printk("VM_ANTI_DETECTION Hiding system lib dlopen for suspicious process %s, UID %u: %s\n",
                              current->comm, from_kuid(&init_user_ns, uid), filename);
            return 1;
        }
    }

    return 0;
}

// SELinux上下文伪造 - 防护权限检测
int fake_selinux_context_for_user(char **context)
{
    kuid_t uid;

    if (!enable_vm_anti_detection || !context)
        return 0;

    uid = current_uid();
    if (!should_hide_from_current_process())
        return 0;

    // 检查是否为系统关键进程 - 不影响系统稳定性
    if (strstr(current->comm, "system_server") ||
        strstr(current->comm, "zygote") ||
        strstr(current->comm, "init") ||
        strstr(current->comm, "kernel") ||
        strstr(current->comm, "surfaceflinger")) {
        return 0; // 不伪造系统关键进程的上下文
    }

    // 检查是否为SystemUI相关进程 - 特别处理
    if (is_systemui_process(current->comm)) {
        return 0; // 不伪造SystemUI进程的上下文
    }

    // 为普通用户应用伪造标准的untrusted_app上下文
    static char fake_context[] = "u:r:untrusted_app:s0:c123,c456";
    *context = fake_context;

    printk("VM_ANTI_DETECTION Faking SELinux context for UID %u, process %s\n",
                      from_kuid(&init_user_ns, uid), current->comm);
    return 1; // 返回伪造的上下文
}

// 环境变量清理通知 - 通知用户空间需要清理环境变量
int should_clean_environment_variables(void)
{
    kuid_t uid;

    if (!enable_vm_anti_detection)
        return 0;

    uid = current_uid();
    if (!should_hide_from_current_process())
        return 0;

    // 检查是否为系统关键进程 - 不影响系统稳定性
    if (strstr(current->comm, "system_server") ||
        strstr(current->comm, "zygote") ||
        strstr(current->comm, "init") ||
        strstr(current->comm, "surfaceflinger")) {
        return 0; // 不清理系统关键进程的环境变量
    }

    // 检查是否为SystemUI相关进程 - 特别处理
    if (is_systemui_process(current->comm)) {
        return 0; // 不清理SystemUI进程的环境变量
    }

    printk("VM_ANTI_DETECTION Should clean environment variables for UID %u, process %s\n",
                      from_kuid(&init_user_ns, uid), current->comm);

    // 返回1表示用户空间应该清理环境变量
    return 1;
}

// 内存扫描防护 - 防护内存中的虚拟机相关字符串检测
int hide_vm_memory_strings(const char *search_string)
{
    kuid_t uid;

    if (!enable_vm_anti_detection || !search_string)
        return 0;

    uid = current_uid();
    if (!should_hide_from_current_process())
        return 0;

    // 检查是否为系统关键进程
    if (strstr(current->comm, "system_server") ||
        strstr(current->comm, "zygote") ||
        strstr(current->comm, "init")) {
        return 0; // 不影响系统关键进程
    }

    // 检查是否为SystemUI相关进程 - 特别处理
    if (is_systemui_process(current->comm)) {
        return 0; // 不影响SystemUI进程的内存扫描
    }

    // 检查是否为可疑的内存扫描字符串
    if (strstr(search_string, "frida") ||
        strstr(search_string, "qemu") ||
        strstr(search_string, "vbox") ||
        strstr(search_string, "vmware") ||
        strstr(search_string, "goldfish") ||
        strstr(search_string, "ranchu")) {
        printk("VM_ANTI_DETECTION Blocking memory scan for suspicious string: %s, UID %u, process %s\n",
                          search_string, from_kuid(&init_user_ns, uid), current->comm);
        return 1; // 阻止内存扫描
    }

    return 0;
}

// 时序攻击防护 - 添加随机延迟防止时序分析
void add_anti_timing_delay(void)
{
    kuid_t uid;
    struct timespec64 ts;
    unsigned int delay_us;

    if (!enable_vm_anti_detection)
        return;

    uid = current_uid();
    if (!should_hide_from_current_process())
        return;

    // 检查是否为系统关键进程 - 不影响系统性能
    if (strstr(current->comm, "system_server") ||
        strstr(current->comm, "zygote") ||
        strstr(current->comm, "init") ||
        strstr(current->comm, "surfaceflinger")) {
        return; // 不对系统关键进程添加延迟
    }

    // 检查是否为SystemUI相关进程 - 特别处理，不影响UI性能
    if (is_systemui_process(current->comm)) {
        return; // 不对SystemUI进程添加延迟
    }

    // 获取当前时间用于生成随机延迟
    ktime_get_real_ts64(&ts);

    // 基于时间戳生成100-500微秒的随机延迟
    delay_us = (ts.tv_nsec % 400) + 100;

    // 只对可疑的检测进程添加延迟
    if (strstr(current->comm, "detect") ||
        strstr(current->comm, "check") ||
        strstr(current->comm, "scan") ||
        strstr(current->comm, "anti") ||
        strstr(current->comm, "security")) {
        usleep_range(delay_us, delay_us + 50);
        printk("VM_ANTI_DETECTION Added anti-timing delay %u us for process %s, UID %u\n",
                          delay_us, current->comm, from_kuid(&init_user_ns, uid));
    }
}

#ifdef MODULE
EXPORT_SYMBOL(hide_vm_in_openat);
EXPORT_SYMBOL(hide_vm_in_openat2);
EXPORT_SYMBOL(hide_vm_in_getdents);
EXPORT_SYMBOL(hide_vm_in_stat);
EXPORT_SYMBOL(hide_vm_in_stat_user);
EXPORT_SYMBOL(hide_vm_in_access);
EXPORT_SYMBOL(hide_vm_in_faccessat);
EXPORT_SYMBOL(needs_attribute_spoofing);
EXPORT_SYMBOL(spoof_file_attributes);
EXPORT_SYMBOL(is_seccomp_arch_detection);
EXPORT_SYMBOL(should_hide_file);
EXPORT_SYMBOL(get_original_path_from_redirect);
EXPORT_SYMBOL(hide_vm_in_dlopen);
EXPORT_SYMBOL(fake_selinux_context_for_user);
EXPORT_SYMBOL(should_clean_environment_variables);
EXPORT_SYMBOL(hide_vm_memory_strings);
EXPORT_SYMBOL(add_anti_timing_delay);
#endif