// SPDX-License-Identifier: GPL-2.0+ OR MIT
//
// Device Tree Source for UniPhier PXs2 Gentil Board
//
// Copyright (C) 2015-2016 Socionext Inc.
//   Author: <PERSON><PERSON><PERSON> <<EMAIL>>

/dts-v1/;
#include "uniphier-pxs2.dtsi"

/ {
	model = "UniPhier PXs2 Gentil Board";
	compatible = "socionext,uniphier-pxs2-gentil",
		     "socionext,uniphier-pxs2";

	chosen {
		stdout-path = "serial0:115200n8";
	};

	aliases {
		serial0 = &serial2;
		serial1 = &serial0;
		serial2 = &serial1;
		i2c0 = &i2c0;
		i2c2 = &i2c2;
		i2c4 = &i2c4;
		i2c5 = &i2c5;
		i2c6 = &i2c6;
		ethernet0 = &eth;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};

	sound {
		compatible = "audio-graph-card";
		label = "UniPhier PXs2";
		dais = <&i2s_port2>;
	};
};

&serial2 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	eeprom@54 {
		compatible = "st,24c64", "atmel,24c64";
		reg = <0x54>;
		pagesize = <32>;
	};
};

&i2s_aux {
	dai-format = "i2s";
	remote-endpoint = <&wm_speaker>;
};

&i2c2 {
	status = "okay";

	wm8960@1a {
		compatible = "wlf,wm8960";
		reg = <0x1a>;
		#sound-dai-cells = <0>;

		port@0 {
			wm_speaker: endpoint {
				dai-format = "i2s";
				remote-endpoint = <&i2s_aux>;
			};
		};
	};
};

&emmc {
	status = "okay";
};

&eth {
	status = "okay";
	phy-handle = <&ethphy>;
};

&mdio {
	ethphy: ethernet-phy@1 {
		reg = <1>;
	};
};

&usb0 {
	status = "okay";
};

&usb1 {
	status = "okay";
};

&ahci {
	status = "okay";
};
