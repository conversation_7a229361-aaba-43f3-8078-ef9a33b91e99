// SPDX-License-Identifier: GPL-2.0+ OR MIT
//
// Device Tree Source for UniPhier Pro4 Reference Board
//
// Copyright (C) 2015-2016 Socionext Inc.
//   Author: <PERSON><PERSON><PERSON> <<EMAIL>>

/dts-v1/;
#include "uniphier-pro4.dtsi"
#include "uniphier-ref-daughter.dtsi"
#include "uniphier-support-card.dtsi"

/ {
	model = "UniPhier Pro4 Reference Board";
	compatible = "socionext,uniphier-pro4-ref", "socionext,uniphier-pro4";

	chosen {
		stdout-path = "serial0:115200n8";
	};

	aliases {
		serial0 = &serial0;
		serial1 = &serial1;
		serial2 = &serial2;
		serial3 = &serialsc;
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		i2c3 = &i2c3;
		i2c5 = &i2c5;
		i2c6 = &i2c6;
		ethernet0 = &eth;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>;
	};
};

&ethsc {
	interrupts = <2 IRQ_TYPE_LEVEL_LOW>;
};

&serialsc {
	interrupts = <2 IRQ_TYPE_LEVEL_LOW>;
};

&serial0 {
	status = "okay";
};

&serial1 {
	status = "okay";
};

&serial2 {
	status = "okay";
};

&gpio {
	xirq2-hog {
		gpio-hog;
		gpios = <UNIPHIER_GPIO_IRQ(2) 0>;
		input;
	};
};

&i2c0 {
	status = "okay";
};

&sd {
	status = "okay";
};

&usb2 {
	status = "okay";
};

&usb3 {
	status = "okay";
};

&eth {
	status = "okay";
	phy-handle = <&ethphy>;
};

&mdio {
	ethphy: ethernet-phy@0 {
		reg = <0>;
	};
};

&usb0 {
	status = "okay";
};

&usb1 {
	status = "okay";
};

&nand {
	status = "okay";

	nand@0 {
		reg = <0>;
	};
};

&ahci0 {
	status = "okay";
};

&ahci1 {
	status = "okay";
};
