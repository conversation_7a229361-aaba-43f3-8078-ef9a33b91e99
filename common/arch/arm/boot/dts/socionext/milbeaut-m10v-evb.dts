// SPDX-License-Identifier: GPL-2.0
/* Socionext Milbeaut M10V Evaluation Board */
/dts-v1/;
#include "milbeaut-m10v.dtsi"

/ {
	model = "Socionext M10V EVB";
	compatible = "socionext,milbeaut-m10v-evb", "socionext,sc2000a";

	aliases {
		serial0 = &uart1;
	};

	chosen {
		bootargs = "rootwait earlycon";
		stdout-path = "serial0:115200n8";
	};

	clocks {
		uclk40xi: uclk40xi {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <40000000>;
		};
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000  0x80000000>;
	};

};
