// SPDX-License-Identifier: GPL-2.0
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	compatible = "socionext,sc2000a";
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		enable-method = "socionext,milbeaut-m10v-smp";
		cpu@f00 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf00>;
		};
		cpu@f01 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf01>;
		};
		cpu@f02 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf02>;
		};
		cpu@f03 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf03>;
		};
	};

	timer { /* The Generic Timer */
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			<GIC_PPI 14
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			<GIC_PPI 11
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			<GIC_PPI 10
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		clock-frequency = <40000000>;
		always-on;
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		interrupt-parent = <&gic>;

		gic: interrupt-controller@1d000000 {
			compatible = "arm,cortex-a7-gic";
			interrupt-controller;
			#interrupt-cells = <3>;
			reg = <0x1d001000 0x1000>,
			      <0x1d002000 0x1000>; /* CPU I/f base and size */
		};

		clk: clock-ctrl@1d021000 {
			compatible = "socionext,milbeaut-m10v-ccu";
			#clock-cells = <1>;
			reg = <0x1d021000 0x1000>;
			clocks = <&uclk40xi>;
		};

		timer@1e000050 { /* 32-bit Reload Timers */
			compatible = "socionext,milbeaut-timer";
			reg = <0x1e000050 0x20>;
			interrupts = <0 91 4>;
			clocks = <&clk 4>;
		};

		uart1: serial@1e700010 { /* PE4, PE5 */
			/* Enable this as ttyUSI0 */
			compatible = "socionext,milbeaut-usio-uart";
			reg = <0x1e700010 0x10>;
			interrupts = <0 141 0x4>, <0 149 0x4>;
			interrupt-names = "rx", "tx";
			clocks = <&clk 2>;
		};

	};

	sram@0 {
		compatible = "mmio-sram";
		reg = <0x0 0x10000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x0 0x10000>;
		smp-sram@f100 {
			compatible = "socionext,milbeaut-smp-sram";
			reg = <0xf100 0x20>;
		};
	};
};
