// SPDX-License-Identifier: GPL-2.0+ OR MIT
//
// Device Tree Source for UniPhier Pro4 Sanji Board
//
// Copyright (C) 2016 Socionext Inc.
//   Author: <PERSON><PERSON><PERSON> <<EMAIL>>

/dts-v1/;
#include "uniphier-pro4.dtsi"

/ {
	model = "UniPhier Pro4 Sanji Board";
	compatible = "socionext,uniphier-pro4-sanji", "socionext,uniphier-pro4";

	chosen {
		stdout-path = "serial0:115200n8";
	};

	aliases {
		serial0 = &serial0;
		serial1 = &serial1;
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		i2c3 = &i2c3;
		i2c5 = &i2c5;
		i2c6 = &i2c6;
		ethernet0 = &eth;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};
};

&serial0 {
	status = "okay";
};

&serial1 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	eeprom@54 {
		compatible = "st,24c64", "atmel,24c64";
		reg = <0x54>;
		pagesize = <32>;
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&usb2 {
	status = "okay";
};

&usb3 {
	status = "okay";
};

&emmc {
	status = "okay";
};

&eth {
	status = "okay";
	phy-handle = <&ethphy>;
};

&mdio {
	ethphy: ethernet-phy@1 {
		reg = <1>;
	};
};

&usb0 {
	status = "okay";
};

&usb1 {
	status = "okay";
};
