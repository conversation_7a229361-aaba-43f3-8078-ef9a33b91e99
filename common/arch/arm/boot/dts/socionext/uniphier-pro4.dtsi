// SPDX-License-Identifier: GPL-2.0+ OR MIT
//
// Device Tree Source for UniPhier Pro4 SoC
//
// Copyright (C) 2015-2016 Socionext Inc.
//   Author: <PERSON><PERSON><PERSON> <<EMAIL>>

#include <dt-bindings/gpio/uniphier-gpio.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	compatible = "socionext,uniphier-pro4";
	#address-cells = <1>;
	#size-cells = <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0>;
			enable-method = "psci";
			next-level-cache = <&l2>;
		};

		cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <1>;
			enable-method = "psci";
			next-level-cache = <&l2>;
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	clocks {
		refclk: ref {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <25000000>;
		};

		arm_timer_clk: arm-timer {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <50000000>;
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		interrupt-parent = <&intc>;

		l2: cache-controller@500c0000 {
			compatible = "socionext,uniphier-system-cache";
			reg = <0x500c0000 0x2000>, <0x503c0100 0x4>,
			      <0x506c0000 0x400>;
			interrupts = <GIC_SPI 174 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 175 IRQ_TYPE_LEVEL_HIGH>;
			cache-unified;
			cache-size = <(768 * 1024)>;
			cache-sets = <256>;
			cache-line-size = <128>;
			cache-level = <2>;
		};

		spi0: spi@54006000 {
			compatible = "socionext,uniphier-scssi";
			status = "disabled";
			reg = <0x54006000 0x100>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_spi0>;
			clocks = <&peri_clk 11>;
			resets = <&peri_rst 11>;
		};

		serial0: serial@54006800 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006800 0x40>;
			interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart0>;
			clocks = <&peri_clk 0>;
			resets = <&peri_rst 0>;
		};

		serial1: serial@54006900 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006900 0x40>;
			interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart1>;
			clocks = <&peri_clk 1>;
			resets = <&peri_rst 1>;
		};

		serial2: serial@54006a00 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006a00 0x40>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart2>;
			clocks = <&peri_clk 2>;
			resets = <&peri_rst 2>;
		};

		serial3: serial@54006b00 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006b00 0x40>;
			interrupts = <GIC_SPI 177 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart3>;
			clocks = <&peri_clk 3>;
			resets = <&peri_rst 3>;
		};

		gpio: gpio@55000000 {
			compatible = "socionext,uniphier-gpio";
			reg = <0x55000000 0x200>;
			interrupt-parent = <&aidet>;
			interrupt-controller;
			#interrupt-cells = <2>;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl 0 0 0>;
			gpio-ranges-group-names = "gpio_range";
			ngpios = <248>;
			socionext,interrupt-ranges = <0 48 16>, <16 154 5>;
		};

		i2c0: i2c@58780000 {
			compatible = "socionext,uniphier-fi2c";
			status = "disabled";
			reg = <0x58780000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c0>;
			clocks = <&peri_clk 4>;
			resets = <&peri_rst 4>;
			clock-frequency = <100000>;
		};

		i2c1: i2c@58781000 {
			compatible = "socionext,uniphier-fi2c";
			status = "disabled";
			reg = <0x58781000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c1>;
			clocks = <&peri_clk 5>;
			resets = <&peri_rst 5>;
			clock-frequency = <100000>;
		};

		i2c2: i2c@58782000 {
			compatible = "socionext,uniphier-fi2c";
			status = "disabled";
			reg = <0x58782000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c2>;
			clocks = <&peri_clk 6>;
			resets = <&peri_rst 6>;
			clock-frequency = <100000>;
		};

		i2c3: i2c@58783000 {
			compatible = "socionext,uniphier-fi2c";
			status = "disabled";
			reg = <0x58783000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c3>;
			clocks = <&peri_clk 7>;
			resets = <&peri_rst 7>;
			clock-frequency = <100000>;
		};

		/* i2c4 does not exist */

		/* chip-internal connection for DMD */
		i2c5: i2c@58785000 {
			compatible = "socionext,uniphier-fi2c";
			reg = <0x58785000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&peri_clk 9>;
			resets = <&peri_rst 9>;
			clock-frequency = <400000>;
		};

		/* chip-internal connection for HDMI */
		i2c6: i2c@58786000 {
			compatible = "socionext,uniphier-fi2c";
			reg = <0x58786000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&peri_clk 10>;
			resets = <&peri_rst 10>;
			clock-frequency = <400000>;
		};

		system_bus: system-bus@58c00000 {
			compatible = "socionext,uniphier-system-bus";
			status = "disabled";
			reg = <0x58c00000 0x400>;
			#address-cells = <2>;
			#size-cells = <1>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_system_bus>;
		};

		smpctrl@59801000 {
			compatible = "socionext,uniphier-smpctrl";
			reg = <0x59801000 0x400>;
		};

		mioctrl: syscon@59810000 {
			compatible = "socionext,uniphier-pro4-mioctrl",
				     "simple-mfd", "syscon";
			reg = <0x59810000 0x800>;

			mio_clk: clock-controller {
				compatible = "socionext,uniphier-pro4-mio-clock";
				#clock-cells = <1>;
			};

			mio_rst: reset-controller {
				compatible = "socionext,uniphier-pro4-mio-reset";
				#reset-cells = <1>;
			};
		};

		syscon@59820000 {
			compatible = "socionext,uniphier-pro4-perictrl",
				     "simple-mfd", "syscon";
			reg = <0x59820000 0x200>;

			peri_clk: clock-controller {
				compatible = "socionext,uniphier-pro4-peri-clock";
				#clock-cells = <1>;
			};

			peri_rst: reset-controller {
				compatible = "socionext,uniphier-pro4-peri-reset";
				#reset-cells = <1>;
			};
		};

		dmac: dma-controller@5a000000 {
			compatible = "socionext,uniphier-mio-dmac";
			reg = <0x5a000000 0x1000>;
			interrupts = <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&mio_clk 7>;
			resets = <&mio_rst 7>;
			#dma-cells = <1>;
		};

		sd: mmc@5a400000 {
			compatible = "socionext,uniphier-sd-v2.91";
			status = "disabled";
			reg = <0x5a400000 0x200>;
			interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default", "uhs";
			pinctrl-0 = <&pinctrl_sd>;
			pinctrl-1 = <&pinctrl_sd_uhs>;
			clocks = <&mio_clk 0>;
			reset-names = "host", "bridge";
			resets = <&mio_rst 0>, <&mio_rst 3>;
			dma-names = "rx-tx";
			dmas = <&dmac 4>;
			bus-width = <4>;
			cap-sd-highspeed;
			sd-uhs-sdr12;
			sd-uhs-sdr25;
			sd-uhs-sdr50;
			socionext,syscon-uhs-mode = <&mioctrl 0>;
		};

		emmc: mmc@5a500000 {
			compatible = "socionext,uniphier-sd-v2.91";
			status = "disabled";
			reg = <0x5a500000 0x200>;
			interrupts = <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_emmc>;
			clocks = <&mio_clk 1>;
			reset-names = "host", "bridge", "hw";
			resets = <&mio_rst 1>, <&mio_rst 4>, <&mio_rst 6>;
			dma-names = "rx-tx";
			dmas = <&dmac 5>;
			bus-width = <8>;
			cap-mmc-highspeed;
			cap-mmc-hw-reset;
			non-removable;
		};

		sd1: mmc@5a600000 {
			compatible = "socionext,uniphier-sd-v2.91";
			status = "disabled";
			reg = <0x5a600000 0x200>;
			interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_sd1>;
			clocks = <&mio_clk 2>;
			reset-names = "host", "bridge";
			resets = <&mio_rst 2>, <&mio_rst 5>;
			dma-names = "rx-tx";
			dmas = <&dmac 6>;
			bus-width = <4>;
			cap-sd-highspeed;
		};

		usb2: usb@5a800100 {
			compatible = "socionext,uniphier-ehci", "generic-ehci";
			status = "disabled";
			reg = <0x5a800100 0x100>;
			interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb2>;
			clocks = <&sys_clk 8>, <&mio_clk 7>, <&mio_clk 8>,
				 <&mio_clk 12>;
			resets = <&sys_rst 8>, <&mio_rst 7>, <&mio_rst 8>,
				 <&mio_rst 12>;
			phy-names = "usb";
			phys = <&usb_phy0>;
			has-transaction-translator;
		};

		usb3: usb@5a810100 {
			compatible = "socionext,uniphier-ehci", "generic-ehci";
			status = "disabled";
			reg = <0x5a810100 0x100>;
			interrupts = <GIC_SPI 81 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb3>;
			clocks = <&sys_clk 8>, <&mio_clk 7>, <&mio_clk 9>,
				 <&mio_clk 13>;
			resets = <&sys_rst 8>, <&mio_rst 7>, <&mio_rst 9>,
				 <&mio_rst 13>;
			phy-names = "usb";
			phys = <&usb_phy1>;
			has-transaction-translator;
		};

		soc_glue: syscon@5f800000 {
			compatible = "socionext,uniphier-pro4-soc-glue",
				     "simple-mfd", "syscon";
			reg = <0x5f800000 0x2000>;

			pinctrl: pinctrl {
				compatible = "socionext,uniphier-pro4-pinctrl";
			};

			usb-hub {
				compatible = "socionext,uniphier-pro4-usb2-phy";
				#address-cells = <1>;
				#size-cells = <0>;

				usb_phy0: phy@0 {
					reg = <0>;
					#phy-cells = <0>;
				};

				usb_phy1: phy@1 {
					reg = <1>;
					#phy-cells = <0>;
				};

				usb_phy2: phy@2 {
					reg = <2>;
					#phy-cells = <0>;
					vbus-supply = <&usb0_vbus>;
				};

				usb_phy3: phy@3 {
					reg = <3>;
					#phy-cells = <0>;
					vbus-supply = <&usb1_vbus>;
				};
			};

			sg_clk: clock-controller {
				compatible = "socionext,uniphier-pro4-sg-clock";
				#clock-cells = <1>;
			};
		};

		syscon@5f900000 {
			compatible = "socionext,uniphier-pro4-soc-glue-debug",
				     "simple-mfd", "syscon";
			reg = <0x5f900000 0x2000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x5f900000 0x2000>;

			efuse@100 {
				compatible = "socionext,uniphier-efuse";
				reg = <0x100 0x28>;
			};

			efuse@130 {
				compatible = "socionext,uniphier-efuse";
				reg = <0x130 0x8>;
			};

			efuse@200 {
				compatible = "socionext,uniphier-efuse";
				reg = <0x200 0x14>;
			};
		};

		xdmac: dma-controller@5fc10000 {
			compatible = "socionext,uniphier-xdmac";
			reg = <0x5fc10000 0x5300>;
			interrupts = <GIC_SPI 188 IRQ_TYPE_LEVEL_HIGH>;
			dma-channels = <16>;
			#dma-cells = <2>;
		};

		aidet: interrupt-controller@5fc20000 {
			compatible = "socionext,uniphier-pro4-aidet";
			reg = <0x5fc20000 0x200>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		timer@60000200 {
			compatible = "arm,cortex-a9-global-timer";
			reg = <0x60000200 0x20>;
			interrupts = <GIC_PPI 11
				(GIC_CPU_MASK_RAW(3) | IRQ_TYPE_LEVEL_HIGH)>;
			clocks = <&arm_timer_clk>;
		};

		timer@60000600 {
			compatible = "arm,cortex-a9-twd-timer";
			reg = <0x60000600 0x20>;
			interrupts = <GIC_PPI 13
				(GIC_CPU_MASK_RAW(3) | IRQ_TYPE_LEVEL_HIGH)>;
			clocks = <&arm_timer_clk>;
		};

		intc: interrupt-controller@60001000 {
			compatible = "arm,cortex-a9-gic";
			reg = <0x60001000 0x1000>,
			      <0x60000100 0x100>;
			#interrupt-cells = <3>;
			interrupt-controller;
		};

		syscon@61840000 {
			compatible = "socionext,uniphier-pro4-sysctrl",
				     "simple-mfd", "syscon";
			reg = <0x61840000 0x10000>;

			sys_clk: clock-controller {
				compatible = "socionext,uniphier-pro4-clock";
				#clock-cells = <1>;
			};

			sys_rst: reset-controller {
				compatible = "socionext,uniphier-pro4-reset";
				#reset-cells = <1>;
			};
		};

		eth: ethernet@65000000 {
			compatible = "socionext,uniphier-pro4-ave4";
			status = "disabled";
			reg = <0x65000000 0x8500>;
			interrupts = <GIC_SPI 66 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_ether_rgmii>;
			clock-names = "gio", "ether", "ether-gb", "ether-phy";
			clocks = <&sys_clk 12>, <&sys_clk 6>, <&sys_clk 7>,
				 <&sys_clk 10>;
			reset-names = "gio", "ether";
			resets = <&sys_rst 12>, <&sys_rst 6>;
			phy-mode = "rgmii";
			local-mac-address = [00 00 00 00 00 00];
			socionext,syscon-phy-mode = <&soc_glue 0>;

			mdio: mdio {
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};

		ahci0: sata@65600000 {
			compatible = "socionext,uniphier-pro4-ahci",
				     "generic-ahci";
			status = "disabled";
			reg = <0x65600000 0x10000>;
			interrupts = <GIC_SPI 142 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&sys_clk 12>, <&sys_clk 28>;
			resets = <&sys_rst 12>, <&sys_rst 28>, <&ahci0_rst 3>;
			ports-implemented = <1>;
			phys = <&ahci0_phy>;
			assigned-clocks = <&sg_clk 0>;
			assigned-clock-rates = <25000000>;
		};

		sata-controller@65700000 {
			compatible = "socionext,uniphier-pxs2-ahci-glue",
				     "simple-mfd";
			reg = <0x65700000 0x100>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x65700000 0x100>;

			ahci0_rst: reset-controller@0 {
				compatible = "socionext,uniphier-pro4-ahci-reset";
				reg = <0x0 0x4>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 28>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 28>;
				#reset-cells = <1>;
			};

			ahci0_phy: phy@10 {
				compatible = "socionext,uniphier-pro4-ahci-phy";
				reg = <0x10 0x40>;
				clock-names = "link", "gio";
				clocks = <&sys_clk 28>, <&sys_clk 12>;
				reset-names = "link", "gio", "phy",
					      "pm", "tx", "rx";
				resets = <&sys_rst 28>, <&sys_rst 12>,
					 <&sys_rst 30>,
					 <&ahci0_rst 0>, <&ahci0_rst 1>,
					 <&ahci0_rst 2>;
				#phy-cells = <0>;
			};
		};

		ahci1: sata@65800000 {
			compatible = "socionext,uniphier-pro4-ahci",
				     "generic-ahci";
			status = "disabled";
			reg = <0x65800000 0x10000>;
			interrupts = <GIC_SPI 143 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&sys_clk 12>, <&sys_clk 29>;
			resets = <&sys_rst 12>, <&sys_rst 29>, <&ahci1_rst 3>;
			ports-implemented = <1>;
			phys = <&ahci1_phy>;
			assigned-clocks = <&sg_clk 0>;
			assigned-clock-rates = <25000000>;
		};

		sata-controller@65900000 {
			compatible = "socionext,uniphier-pro4-ahci-glue",
				     "simple-mfd";
			reg = <0x65900000 0x100>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x65900000 0x100>;

			ahci1_rst: reset-controller@0 {
				compatible = "socionext,uniphier-pro4-ahci-reset";
				reg = <0x0 0x4>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 29>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 29>;
				#reset-cells = <1>;
			};

			ahci1_phy: phy@10 {
				compatible = "socionext,uniphier-pro4-ahci-phy";
				reg = <0x10 0x40>;
				clock-names = "link", "gio";
				clocks = <&sys_clk 29>, <&sys_clk 12>;
				reset-names = "link", "gio", "phy",
					      "pm", "tx", "rx";
				resets = <&sys_rst 29>, <&sys_rst 12>,
					 <&sys_rst 30>,
					 <&ahci1_rst 0>, <&ahci1_rst 1>,
					 <&ahci1_rst 2>;
				#phy-cells = <0>;
			};
		};

		usb0: usb@65a00000 {
			compatible = "socionext,uniphier-dwc3", "snps,dwc3";
			status = "disabled";
			reg = <0x65a00000 0xcd00>;
			interrupt-names = "host", "peripheral";
			interrupts = <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 135 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb0>;
			clock-names = "ref", "bus_early", "suspend";
			clocks = <&sys_clk 12>, <&sys_clk 12>, <&sys_clk 12>;
			resets = <&usb0_rst 4>;
			phys = <&usb_phy2>, <&usb0_ssphy>;
			dr_mode = "host";
		};

		usb-controller@65b00000 {
			compatible = "socionext,uniphier-pro4-dwc3-glue",
				     "simple-mfd";
			reg = <0x65b00000 0x100>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x65b00000 0x100>;

			usb0_vbus: regulator@0 {
				compatible = "socionext,uniphier-pro4-usb3-regulator";
				reg = <0 0x10>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 14>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 14>;
			};

			usb0_ssphy: phy@10 {
				compatible = "socionext,uniphier-pro4-usb3-ssphy";
				reg = <0x10 0x10>;
				#phy-cells = <0>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 14>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 14>;
				vbus-supply = <&usb0_vbus>;
			};

			usb0_rst: reset-controller@40 {
				compatible = "socionext,uniphier-pro4-usb3-reset";
				reg = <0x40 0x4>;
				#reset-cells = <1>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 14>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 14>;
			};
		};

		usb1: usb@65c00000 {
			compatible = "socionext,uniphier-dwc3", "snps,dwc3";
			status = "disabled";
			reg = <0x65c00000 0xcd00>;
			interrupt-names = "host", "peripheral";
			interrupts = <GIC_SPI 137 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 138 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb1>;
			clock-names = "ref", "bus_early", "suspend";
			clocks = <&sys_clk 12>, <&sys_clk 12>, <&sys_clk 12>;
			resets = <&usb1_rst 4>;
			phys = <&usb_phy3>;
			dr_mode = "host";
		};

		usb-controller@65d00000 {
			compatible = "socionext,uniphier-pro4-dwc3-glue",
				     "simple-mfd";
			reg = <0x65d00000 0x100>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x65d00000 0x100>;

			usb1_vbus: regulator@0 {
				compatible = "socionext,uniphier-pro4-usb3-regulator";
				reg = <0 0x10>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 15>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 15>;
			};

			usb1_rst: reset-controller@40 {
				compatible = "socionext,uniphier-pro4-usb3-reset";
				reg = <0x40 0x4>;
				#reset-cells = <1>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 15>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 15>;
			};
		};

		nand: nand-controller@68000000 {
			compatible = "socionext,uniphier-denali-nand-v5a";
			status = "disabled";
			reg-names = "nand_data", "denali_reg";
			reg = <0x68000000 0x20>, <0x68100000 0x1000>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_nand>;
			clock-names = "nand", "nand_x", "ecc";
			clocks = <&sys_clk 2>, <&sys_clk 3>, <&sys_clk 3>;
			reset-names = "nand", "reg";
			resets = <&sys_rst 2>, <&sys_rst 2>;
		};
	};
};

#include "uniphier-pinctrl.dtsi"
