// SPDX-License-Identifier: GPL-2.0+ OR MIT
//
// Device Tree Source for UniPhier sLD8 Reference Board
//
// Copyright (C) 2015-2016 Socionext Inc.
//   Author: <PERSON><PERSON><PERSON> <<EMAIL>>

/dts-v1/;
#include "uniphier-sld8.dtsi"
#include "uniphier-ref-daughter.dtsi"
#include "uniphier-support-card.dtsi"

/ {
	model = "UniPhier sLD8 Reference Board";
	compatible = "socionext,uniphier-sld8-ref", "socionext,uniphier-sld8";

	chosen {
		stdout-path = "serial0:115200n8";
	};

	aliases {
		serial0 = &serial0;
		serial1 = &serialsc;
		serial2 = &serial2;
		serial3 = &serial3;
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		i2c3 = &i2c3;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x20000000>;
	};
};

&ethsc {
	interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
};

&serialsc {
	interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
};

&serial0 {
	status = "okay";
};

&serial2 {
	status = "okay";
};

&serial3 {
	status = "okay";
};

&gpio {
	xirq0-hog {
		gpio-hog;
		gpios = <UNIPHIER_GPIO_IRQ(0) 0>;
		input;
	};
};

&i2c0 {
	status = "okay";
};

&sd {
	status = "okay";
};

&usb0 {
	status = "okay";
};

&usb1 {
	status = "okay";
};

&usb2 {
	status = "okay";
};

&nand {
	status = "okay";

	nand@0 {
		reg = <0>;
	};
};
