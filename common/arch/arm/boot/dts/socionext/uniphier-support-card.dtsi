// SPDX-License-Identifier: GPL-2.0+ OR MIT
//
// Device Tree Source for UniPhier Support Card (Expansion Board)
//
// Copyright (C) 2015-2017 Socionext Inc.
//   Author: <PERSON><PERSON><PERSON> <<EMAIL>>

&system_bus {
	status = "okay";
	ranges = <1 0x00000000 0x42000000 0x02000000>;

	ethsc: ethernet@1,1f00000 {
		compatible = "smsc,lan9118", "smsc,lan9115";
		reg = <1 0x01f00000 0x1000>;
		phy-mode = "mii";
		reg-io-width = <4>;
		interrupt-parent = <&gpio>;
	};

	serialsc: serial@1,1fb0000 {
		compatible = "ns16550a";
		reg = <1 0x01fb0000 0x20>;
		clock-frequency = <12288000>;
		reg-shift = <1>;
		interrupt-parent = <&gpio>;
	};
};
