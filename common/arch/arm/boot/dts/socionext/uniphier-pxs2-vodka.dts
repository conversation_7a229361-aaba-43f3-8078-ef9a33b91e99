// SPDX-License-Identifier: GPL-2.0+ OR MIT
//
// Device Tree Source for UniPhier PXs2 Vodka Board
//
// Copyright (C) 2015-2016 Socionext Inc.
//   Author: <PERSON><PERSON><PERSON> <<EMAIL>>

/dts-v1/;
#include "uniphier-pxs2.dtsi"

/ {
	model = "UniPhier PXs2 Vodka Board";
	compatible = "socionext,uniphier-pxs2-vodka", "socionext,uniphier-pxs2";

	chosen {
		stdout-path = "serial0:115200n8";
	};

	aliases {
		serial0 = &serial2;
		serial1 = &serial0;
		serial2 = &serial1;
		i2c0 = &i2c0;
		i2c4 = &i2c4;
		i2c5 = &i2c5;
		i2c6 = &i2c6;
		ethernet0 = &eth;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x80000000>;
	};

	sound {
		compatible = "audio-graph-card";
		label = "UniPhier PXs2";
		dais = <&spdif_port0
			&comp_spdif_port0>;
	};

	spdif-out {
		compatible = "linux,spdif-dit";
		#sound-dai-cells = <0>;

		port@0 {
			spdif_tx: endpoint {
				remote-endpoint = <&spdif_hiecout1>;
			};
		};
	};

	comp-spdif-out {
		compatible = "linux,spdif-dit";
		#sound-dai-cells = <0>;

		port@0 {
			comp_spdif_tx: endpoint {
				remote-endpoint = <&comp_spdif_hiecout1>;
			};
		};
	};
};

&serial2 {
	status = "okay";
};

&spdif_hiecout1 {
	remote-endpoint = <&spdif_tx>;
};

&comp_spdif_hiecout1 {
	remote-endpoint = <&comp_spdif_tx>;
};

&i2c0 {
	status = "okay";
};

&emmc {
	status = "okay";
};

&eth {
	status = "okay";
	phy-handle = <&ethphy>;
};

&mdio {
	ethphy: ethernet-phy@1 {
		reg = <1>;
	};
};

&usb0 {
	status = "okay";
};
