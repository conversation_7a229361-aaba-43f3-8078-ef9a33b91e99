// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Device Tree Source for UniPhier Pro5 EP-CORE Board (Pro5-PCIe_EP-CORE)
 *
 * Copyright (C) 2021 Socionext Inc.
 *   Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "uniphier-pro5.dtsi"
#include "uniphier-support-card.dtsi"

/ {
	model = "UniPhier Pro5 EP-CORE Board";
	compatible = "socionext,uniphier-pro5-epcore", "socionext,uniphier-pro5";

	chosen {
		stdout-path = "serial0:115200n8";
	};

	aliases {
		serial0 = &serial0;
		serial1 = &serial1;
		serial2 = &serial2;
		i2c0 = &i2c0;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>;
	};
};

&ethsc {
	interrupts = <1 IRQ_TYPE_LEVEL_LOW>;
};

&serialsc {
	interrupts = <1 IRQ_TYPE_LEVEL_LOW>;
};

&serial0 {
	status = "okay";
};

&serial1 {
	status = "okay";
};

&serial2 {
	status = "okay";
};

&i2c0 {
	status = "okay";
};

&usb0 {
	status = "okay";
};

&usb1 {
	status = "okay";
};

&emmc {
	status = "okay";
};

&sd {
	status = "okay";
};

&pcie_ep {
	status = "okay";
};
