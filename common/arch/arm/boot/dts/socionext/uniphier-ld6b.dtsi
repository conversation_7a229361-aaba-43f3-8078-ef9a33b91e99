// SPDX-License-Identifier: GPL-2.0+ OR MIT
//
// Device Tree Source for UniPhier LD6b SoC
//
// Copyright (C) 2015-2016 Socionext Inc.
//   Author: <PERSON><PERSON><PERSON> <<EMAIL>>

/*
 * LD6b consists of two silicon dies: D-chip and A-chip.
 * The D-chip (digital chip) is the same as the PXs2 die.
 * Reuse the PXs2 device tree with some properties overridden.
 */
#include "uniphier-pxs2.dtsi"

/ {
	compatible = "socionext,uniphier-ld6b";
};

/* UART3 unavailable: the pads are not wired to the package balls */
&serial3 {
	status = "disabled";
};

/*
 * LD6b and PXs2 have completely different packages,
 * which makes the pinctrl driver unshareable.
 */
&pinctrl {
	compatible = "socionext,uniphier-ld6b-pinctrl";
};
