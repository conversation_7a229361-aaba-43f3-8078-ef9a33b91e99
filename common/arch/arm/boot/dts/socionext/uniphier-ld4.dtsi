// SPDX-License-Identifier: GPL-2.0+ OR MIT
//
// Device Tree Source for UniPhier LD4 SoC
//
// Copyright (C) 2015-2016 Socionext Inc.
//   Author: <PERSON><PERSON><PERSON> <<EMAIL>>

#include <dt-bindings/gpio/uniphier-gpio.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	compatible = "socionext,uniphier-ld4";
	#address-cells = <1>;
	#size-cells = <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0>;
			enable-method = "psci";
			next-level-cache = <&l2>;
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	clocks {
		refclk: ref {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <24576000>;
		};

		arm_timer_clk: arm-timer {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <50000000>;
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		interrupt-parent = <&intc>;

		l2: cache-controller@500c0000 {
			compatible = "socionext,uniphier-system-cache";
			reg = <0x500c0000 0x2000>, <0x503c0100 0x4>,
			      <0x506c0000 0x400>;
			interrupts = <GIC_SPI 174 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 175 IRQ_TYPE_LEVEL_HIGH>;
			cache-unified;
			cache-size = <(512 * 1024)>;
			cache-sets = <256>;
			cache-line-size = <128>;
			cache-level = <2>;
		};

		spi: spi@54006000 {
			compatible = "socionext,uniphier-scssi";
			status = "disabled";
			reg = <0x54006000 0x100>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_spi0>;
			clocks = <&peri_clk 11>;
			resets = <&peri_rst 11>;
		};

		serial0: serial@54006800 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006800 0x40>;
			interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart0>;
			clocks = <&peri_clk 0>;
			resets = <&peri_rst 0>;
		};

		serial1: serial@54006900 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006900 0x40>;
			interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart1>;
			clocks = <&peri_clk 1>;
			resets = <&peri_rst 1>;
		};

		serial2: serial@54006a00 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006a00 0x40>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart2>;
			clocks = <&peri_clk 2>;
			resets = <&peri_rst 2>;
		};

		serial3: serial@54006b00 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006b00 0x40>;
			interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart3>;
			clocks = <&peri_clk 3>;
			resets = <&peri_rst 3>;
		};

		gpio: gpio@55000000 {
			compatible = "socionext,uniphier-gpio";
			reg = <0x55000000 0x200>;
			interrupt-parent = <&aidet>;
			interrupt-controller;
			#interrupt-cells = <2>;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl 0 0 0>;
			gpio-ranges-group-names = "gpio_range";
			ngpios = <136>;
			socionext,interrupt-ranges = <0 48 13>, <14 62 2>;
		};

		i2c0: i2c@58400000 {
			compatible = "socionext,uniphier-i2c";
			status = "disabled";
			reg = <0x58400000 0x40>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 41 IRQ_TYPE_EDGE_RISING>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c0>;
			clocks = <&peri_clk 4>;
			resets = <&peri_rst 4>;
			clock-frequency = <100000>;
		};

		i2c1: i2c@58480000 {
			compatible = "socionext,uniphier-i2c";
			status = "disabled";
			reg = <0x58480000 0x40>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 42 IRQ_TYPE_EDGE_RISING>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c1>;
			clocks = <&peri_clk 5>;
			resets = <&peri_rst 5>;
			clock-frequency = <100000>;
		};

		/* chip-internal connection for DMD */
		i2c2: i2c@58500000 {
			compatible = "socionext,uniphier-i2c";
			reg = <0x58500000 0x40>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 43 IRQ_TYPE_EDGE_RISING>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c2>;
			clocks = <&peri_clk 6>;
			resets = <&peri_rst 6>;
			clock-frequency = <400000>;
		};

		i2c3: i2c@58580000 {
			compatible = "socionext,uniphier-i2c";
			status = "disabled";
			reg = <0x58580000 0x40>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 44 IRQ_TYPE_EDGE_RISING>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c3>;
			clocks = <&peri_clk 7>;
			resets = <&peri_rst 7>;
			clock-frequency = <100000>;
		};

		system_bus: system-bus@58c00000 {
			compatible = "socionext,uniphier-system-bus";
			status = "disabled";
			reg = <0x58c00000 0x400>;
			#address-cells = <2>;
			#size-cells = <1>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_system_bus>;
		};

		smpctrl@59801000 {
			compatible = "socionext,uniphier-smpctrl";
			reg = <0x59801000 0x400>;
		};

		syscon@59810000 {
			compatible = "socionext,uniphier-ld4-mioctrl",
				     "simple-mfd", "syscon";
			reg = <0x59810000 0x800>;

			mio_clk: clock-controller {
				compatible = "socionext,uniphier-ld4-mio-clock";
				#clock-cells = <1>;
			};

			mio_rst: reset-controller {
				compatible = "socionext,uniphier-ld4-mio-reset";
				#reset-cells = <1>;
			};
		};

		syscon@59820000 {
			compatible = "socionext,uniphier-ld4-perictrl",
				     "simple-mfd", "syscon";
			reg = <0x59820000 0x200>;

			peri_clk: clock-controller {
				compatible = "socionext,uniphier-ld4-peri-clock";
				#clock-cells = <1>;
			};

			peri_rst: reset-controller {
				compatible = "socionext,uniphier-ld4-peri-reset";
				#reset-cells = <1>;
			};
		};

		dmac: dma-controller@5a000000 {
			compatible = "socionext,uniphier-mio-dmac";
			reg = <0x5a000000 0x1000>;
			interrupts = <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&mio_clk 7>;
			resets = <&mio_rst 7>;
			#dma-cells = <1>;
		};

		sd: mmc@5a400000 {
			compatible = "socionext,uniphier-sd-v2.91";
			status = "disabled";
			reg = <0x5a400000 0x200>;
			interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default", "uhs";
			pinctrl-0 = <&pinctrl_sd>;
			pinctrl-1 = <&pinctrl_sd_uhs>;
			clocks = <&mio_clk 0>;
			reset-names = "host", "bridge";
			resets = <&mio_rst 0>, <&mio_rst 3>;
			dma-names = "rx-tx";
			dmas = <&dmac 4>;
			bus-width = <4>;
			cap-sd-highspeed;
			sd-uhs-sdr12;
			sd-uhs-sdr25;
			sd-uhs-sdr50;
		};

		emmc: mmc@5a500000 {
			compatible = "socionext,uniphier-sd-v2.91";
			status = "disabled";
			reg = <0x5a500000 0x200>;
			interrupts = <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_emmc>;
			clocks = <&mio_clk 1>;
			reset-names = "host", "bridge", "hw";
			resets = <&mio_rst 1>, <&mio_rst 4>, <&mio_rst 6>;
			dma-names = "rx-tx";
			dmas = <&dmac 6>;
			bus-width = <8>;
			cap-mmc-highspeed;
			cap-mmc-hw-reset;
			non-removable;
		};

		usb0: usb@5a800100 {
			compatible = "socionext,uniphier-ehci", "generic-ehci";
			status = "disabled";
			reg = <0x5a800100 0x100>;
			interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb0>;
			clocks = <&sys_clk 8>, <&mio_clk 7>, <&mio_clk 8>,
				 <&mio_clk 12>;
			resets = <&sys_rst 8>, <&mio_rst 7>, <&mio_rst 8>,
				 <&mio_rst 12>;
			has-transaction-translator;
		};

		usb1: usb@5a810100 {
			compatible = "socionext,uniphier-ehci", "generic-ehci";
			status = "disabled";
			reg = <0x5a810100 0x100>;
			interrupts = <GIC_SPI 81 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb1>;
			clocks = <&sys_clk 8>, <&mio_clk 7>, <&mio_clk 9>,
				 <&mio_clk 13>;
			resets = <&sys_rst 8>, <&mio_rst 7>, <&mio_rst 9>,
				 <&mio_rst 13>;
			has-transaction-translator;
		};

		usb2: usb@5a820100 {
			compatible = "socionext,uniphier-ehci", "generic-ehci";
			status = "disabled";
			reg = <0x5a820100 0x100>;
			interrupts = <GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb2>;
			clocks = <&sys_clk 8>, <&mio_clk 7>, <&mio_clk 10>,
				 <&mio_clk 14>;
			resets = <&sys_rst 8>, <&mio_rst 7>, <&mio_rst 10>,
				 <&mio_rst 14>;
			has-transaction-translator;
		};

		syscon@5f800000 {
			compatible = "socionext,uniphier-ld4-soc-glue",
				     "simple-mfd", "syscon";
			reg = <0x5f800000 0x2000>;

			pinctrl: pinctrl {
				compatible = "socionext,uniphier-ld4-pinctrl";
			};
		};

		syscon@5f900000 {
			compatible = "socionext,uniphier-ld4-soc-glue-debug",
				     "simple-mfd", "syscon";
			reg = <0x5f900000 0x2000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x5f900000 0x2000>;

			efuse@100 {
				compatible = "socionext,uniphier-efuse";
				reg = <0x100 0x28>;
			};

			efuse@130 {
				compatible = "socionext,uniphier-efuse";
				reg = <0x130 0x8>;
			};
		};

		timer@60000200 {
			compatible = "arm,cortex-a9-global-timer";
			reg = <0x60000200 0x20>;
			interrupts = <GIC_PPI 11
				(GIC_CPU_MASK_RAW(1) | IRQ_TYPE_LEVEL_HIGH)>;
			clocks = <&arm_timer_clk>;
		};

		timer@60000600 {
			compatible = "arm,cortex-a9-twd-timer";
			reg = <0x60000600 0x20>;
			interrupts = <GIC_PPI 13
				(GIC_CPU_MASK_RAW(1) | IRQ_TYPE_LEVEL_HIGH)>;
			clocks = <&arm_timer_clk>;
		};

		intc: interrupt-controller@60001000 {
			compatible = "arm,cortex-a9-gic";
			reg = <0x60001000 0x1000>,
			      <0x60000100 0x100>;
			#interrupt-cells = <3>;
			interrupt-controller;
		};

		aidet: interrupt-controller@61830000 {
			compatible = "socionext,uniphier-ld4-aidet";
			reg = <0x61830000 0x200>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		syscon@61840000 {
			compatible = "socionext,uniphier-ld4-sysctrl",
				     "simple-mfd", "syscon";
			reg = <0x61840000 0x10000>;

			sys_clk: clock-controller {
				compatible = "socionext,uniphier-ld4-clock";
				#clock-cells = <1>;
			};

			sys_rst: reset-controller {
				compatible = "socionext,uniphier-ld4-reset";
				#reset-cells = <1>;
			};
		};

		nand: nand-controller@68000000 {
			compatible = "socionext,uniphier-denali-nand-v5a";
			status = "disabled";
			reg-names = "nand_data", "denali_reg";
			reg = <0x68000000 0x20>, <0x68100000 0x1000>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_nand>;
			clock-names = "nand", "nand_x", "ecc";
			clocks = <&sys_clk 2>, <&sys_clk 3>, <&sys_clk 3>;
			reset-names = "nand", "reg";
			resets = <&sys_rst 2>, <&sys_rst 2>;
		};
	};
};

#include "uniphier-pinctrl.dtsi"
