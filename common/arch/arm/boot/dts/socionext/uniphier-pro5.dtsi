// SPDX-License-Identifier: GPL-2.0+ OR MIT
//
// Device Tree Source for UniPhier Pro5 SoC
//
// Copyright (C) 2015-2016 Socionext Inc.
//   Author: <PERSON><PERSON><PERSON> <<EMAIL>>

#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	compatible = "socionext,uniphier-pro5";
	#address-cells = <1>;
	#size-cells = <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0>;
			clocks = <&sys_clk 32>;
			enable-method = "psci";
			next-level-cache = <&l2>;
			operating-points-v2 = <&cpu_opp>;
		};

		cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <1>;
			clocks = <&sys_clk 32>;
			enable-method = "psci";
			next-level-cache = <&l2>;
			operating-points-v2 = <&cpu_opp>;
		};
	};

	cpu_opp: opp-table {
		compatible = "operating-points-v2";
		opp-shared;

		opp-100000000 {
			opp-hz = /bits/ 64 <100000000>;
			clock-latency-ns = <300>;
		};
		opp-116667000 {
			opp-hz = /bits/ 64 <116667000>;
			clock-latency-ns = <300>;
		};
		opp-150000000 {
			opp-hz = /bits/ 64 <150000000>;
			clock-latency-ns = <300>;
		};
		opp-175000000 {
			opp-hz = /bits/ 64 <175000000>;
			clock-latency-ns = <300>;
		};
		opp-200000000 {
			opp-hz = /bits/ 64 <200000000>;
			clock-latency-ns = <300>;
		};
		opp-233334000 {
			opp-hz = /bits/ 64 <233334000>;
			clock-latency-ns = <300>;
		};
		opp-300000000 {
			opp-hz = /bits/ 64 <300000000>;
			clock-latency-ns = <300>;
		};
		opp-350000000 {
			opp-hz = /bits/ 64 <350000000>;
			clock-latency-ns = <300>;
		};
		opp-400000000 {
			opp-hz = /bits/ 64 <400000000>;
			clock-latency-ns = <300>;
		};
		opp-466667000 {
			opp-hz = /bits/ 64 <466667000>;
			clock-latency-ns = <300>;
		};
		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			clock-latency-ns = <300>;
		};
		opp-700000000 {
			opp-hz = /bits/ 64 <700000000>;
			clock-latency-ns = <300>;
		};
		opp-800000000 {
			opp-hz = /bits/ 64 <800000000>;
			clock-latency-ns = <300>;
		};
		opp-933334000 {
			opp-hz = /bits/ 64 <933334000>;
			clock-latency-ns = <300>;
		};
		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			clock-latency-ns = <300>;
		};
		opp-1400000000 {
			opp-hz = /bits/ 64 <1400000000>;
			clock-latency-ns = <300>;
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	clocks {
		refclk: ref {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <20000000>;
		};

		arm_timer_clk: arm-timer {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <50000000>;
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		interrupt-parent = <&intc>;

		l2: cache-controller@500c0000 {
			compatible = "socionext,uniphier-system-cache";
			reg = <0x500c0000 0x2000>, <0x503c0100 0x8>,
			      <0x506c0000 0x400>;
			interrupts = <GIC_SPI 190 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 191 IRQ_TYPE_LEVEL_HIGH>;
			cache-unified;
			cache-size = <(2 * 1024 * 1024)>;
			cache-sets = <512>;
			cache-line-size = <128>;
			cache-level = <2>;
			next-level-cache = <&l3>;
		};

		l3: cache-controller@500c8000 {
			compatible = "socionext,uniphier-system-cache";
			reg = <0x500c8000 0x2000>, <0x503c8100 0x8>,
			      <0x506c8000 0x400>;
			interrupts = <GIC_SPI 174 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 175 IRQ_TYPE_LEVEL_HIGH>;
			cache-unified;
			cache-size = <(2 * 1024 * 1024)>;
			cache-sets = <512>;
			cache-line-size = <256>;
			cache-level = <3>;
		};

		spi0: spi@54006000 {
			compatible = "socionext,uniphier-scssi";
			status = "disabled";
			reg = <0x54006000 0x100>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_spi0>;
			clocks = <&peri_clk 11>;
			resets = <&peri_rst 11>;
		};

		spi1: spi@54006100 {
			compatible = "socionext,uniphier-scssi";
			status = "disabled";
			reg = <0x54006100 0x100>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 216 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_spi1>;
			clocks = <&peri_clk 11>;	/* common with spi0 */
			resets = <&peri_rst 12>;
		};

		serial0: serial@54006800 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006800 0x40>;
			interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart0>;
			clocks = <&peri_clk 0>;
			resets = <&peri_rst 0>;
		};

		serial1: serial@54006900 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006900 0x40>;
			interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart1>;
			clocks = <&peri_clk 1>;
			resets = <&peri_rst 1>;
		};

		serial2: serial@54006a00 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006a00 0x40>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart2>;
			clocks = <&peri_clk 2>;
			resets = <&peri_rst 2>;
		};

		serial3: serial@54006b00 {
			compatible = "socionext,uniphier-uart";
			status = "disabled";
			reg = <0x54006b00 0x40>;
			interrupts = <GIC_SPI 177 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_uart3>;
			clocks = <&peri_clk 3>;
			resets = <&peri_rst 3>;
		};

		gpio: gpio@55000000 {
			compatible = "socionext,uniphier-gpio";
			reg = <0x55000000 0x200>;
			interrupt-parent = <&aidet>;
			interrupt-controller;
			#interrupt-cells = <2>;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl 0 0 0>;
			gpio-ranges-group-names = "gpio_range";
			ngpios = <248>;
			socionext,interrupt-ranges = <0 48 16>, <16 154 5>;
		};

		i2c0: i2c@58780000 {
			compatible = "socionext,uniphier-fi2c";
			status = "disabled";
			reg = <0x58780000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c0>;
			clocks = <&peri_clk 4>;
			resets = <&peri_rst 4>;
			clock-frequency = <100000>;
		};

		i2c1: i2c@58781000 {
			compatible = "socionext,uniphier-fi2c";
			status = "disabled";
			reg = <0x58781000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c1>;
			clocks = <&peri_clk 5>;
			resets = <&peri_rst 5>;
			clock-frequency = <100000>;
		};

		i2c2: i2c@58782000 {
			compatible = "socionext,uniphier-fi2c";
			status = "disabled";
			reg = <0x58782000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c2>;
			clocks = <&peri_clk 6>;
			resets = <&peri_rst 6>;
			clock-frequency = <100000>;
		};

		i2c3: i2c@58783000 {
			compatible = "socionext,uniphier-fi2c";
			status = "disabled";
			reg = <0x58783000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c3>;
			clocks = <&peri_clk 7>;
			resets = <&peri_rst 7>;
			clock-frequency = <100000>;
		};

		/* i2c4 does not exist */

		/* chip-internal connection for DMD */
		i2c5: i2c@58785000 {
			compatible = "socionext,uniphier-fi2c";
			reg = <0x58785000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&peri_clk 9>;
			resets = <&peri_rst 9>;
			clock-frequency = <400000>;
		};

		/* chip-internal connection for HDMI */
		i2c6: i2c@58786000 {
			compatible = "socionext,uniphier-fi2c";
			reg = <0x58786000 0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&peri_clk 10>;
			resets = <&peri_rst 10>;
			clock-frequency = <400000>;
		};

		system_bus: system-bus@58c00000 {
			compatible = "socionext,uniphier-system-bus";
			status = "disabled";
			reg = <0x58c00000 0x400>;
			#address-cells = <2>;
			#size-cells = <1>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_system_bus>;
		};

		smpctrl@59801000 {
			compatible = "socionext,uniphier-smpctrl";
			reg = <0x59801000 0x400>;
		};

		sdctrl: syscon@59810000 {
			compatible = "socionext,uniphier-pro5-sdctrl",
				     "simple-mfd", "syscon";
			reg = <0x59810000 0x400>;

			sd_clk: clock-controller {
				compatible = "socionext,uniphier-pro5-sd-clock";
				#clock-cells = <1>;
			};

			sd_rst: reset-controller {
				compatible = "socionext,uniphier-pro5-sd-reset";
				#reset-cells = <1>;
			};
		};

		syscon@59820000 {
			compatible = "socionext,uniphier-pro5-perictrl",
				     "simple-mfd", "syscon";
			reg = <0x59820000 0x200>;

			peri_clk: clock-controller {
				compatible = "socionext,uniphier-pro5-peri-clock";
				#clock-cells = <1>;
			};

			peri_rst: reset-controller {
				compatible = "socionext,uniphier-pro5-peri-reset";
				#reset-cells = <1>;
			};
		};

		syscon@5f800000 {
			compatible = "socionext,uniphier-pro5-soc-glue",
				     "simple-mfd", "syscon";
			reg = <0x5f800000 0x2000>;

			pinctrl: pinctrl {
				compatible = "socionext,uniphier-pro5-pinctrl";
			};
		};

		syscon@5f900000 {
			compatible = "socionext,uniphier-pro5-soc-glue-debug",
				     "simple-mfd", "syscon";
			reg = <0x5f900000 0x2000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x5f900000 0x2000>;

			efuse@100 {
				compatible = "socionext,uniphier-efuse";
				reg = <0x100 0x28>;
			};

			efuse@130 {
				compatible = "socionext,uniphier-efuse";
				reg = <0x130 0x8>;
			};

			efuse@200 {
				compatible = "socionext,uniphier-efuse";
				reg = <0x200 0x28>;
			};

			efuse@300 {
				compatible = "socionext,uniphier-efuse";
				reg = <0x300 0x14>;
			};

			efuse@400 {
				compatible = "socionext,uniphier-efuse";
				reg = <0x400 0x8>;
			};
		};

		xdmac: dma-controller@5fc10000 {
			compatible = "socionext,uniphier-xdmac";
			reg = <0x5fc10000 0x5300>;
			interrupts = <GIC_SPI 188 IRQ_TYPE_LEVEL_HIGH>;
			dma-channels = <16>;
			#dma-cells = <2>;
		};

		aidet: interrupt-controller@5fc20000 {
			compatible = "socionext,uniphier-pro5-aidet";
			reg = <0x5fc20000 0x200>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		timer@60000200 {
			compatible = "arm,cortex-a9-global-timer";
			reg = <0x60000200 0x20>;
			interrupts = <GIC_PPI 11
				(GIC_CPU_MASK_RAW(3) | IRQ_TYPE_LEVEL_HIGH)>;
			clocks = <&arm_timer_clk>;
		};

		timer@60000600 {
			compatible = "arm,cortex-a9-twd-timer";
			reg = <0x60000600 0x20>;
			interrupts = <GIC_PPI 13
				(GIC_CPU_MASK_RAW(3) | IRQ_TYPE_LEVEL_HIGH)>;
			clocks = <&arm_timer_clk>;
		};

		intc: interrupt-controller@60001000 {
			compatible = "arm,cortex-a9-gic";
			reg = <0x60001000 0x1000>,
			      <0x60000100 0x100>;
			#interrupt-cells = <3>;
			interrupt-controller;
		};

		syscon@61840000 {
			compatible = "socionext,uniphier-pro5-sysctrl",
				     "simple-mfd", "syscon";
			reg = <0x61840000 0x10000>;

			sys_clk: clock-controller {
				compatible = "socionext,uniphier-pro5-clock";
				#clock-cells = <1>;
			};

			sys_rst: reset-controller {
				compatible = "socionext,uniphier-pro5-reset";
				#reset-cells = <1>;
			};
		};

		usb0: usb@65a00000 {
			compatible = "socionext,uniphier-dwc3", "snps,dwc3";
			status = "disabled";
			reg = <0x65a00000 0xcd00>;
			interrupt-names = "host";
			interrupts = <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb0>;
			clock-names = "ref", "bus_early", "suspend";
			clocks = <&sys_clk 12>, <&sys_clk 12>, <&sys_clk 12>;
			resets = <&usb0_rst 15>;
			phys = <&usb0_hsphy0>, <&usb0_ssphy0>;
			dr_mode = "host";
		};

		usb-controller@65b00000 {
			compatible = "socionext,uniphier-pro5-dwc3-glue",
				     "simple-mfd";
			reg = <0x65b00000 0x400>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x65b00000 0x400>;

			usb0_rst: reset-controller@0 {
				compatible = "socionext,uniphier-pro5-usb3-reset";
				reg = <0x0 0x4>;
				#reset-cells = <1>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 14>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 14>;
			};

			usb0_vbus0: regulator@100 {
				compatible = "socionext,uniphier-pro5-usb3-regulator";
				reg = <0x100 0x10>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 14>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 14>;
			};

			usb0_hsphy0: phy@280 {
				compatible = "socionext,uniphier-pro5-usb3-hsphy";
				reg = <0x280 0x10>;
				#phy-cells = <0>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 14>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 14>;
				vbus-supply = <&usb0_vbus0>;
			};

			usb0_ssphy0: phy@380 {
				compatible = "socionext,uniphier-pro5-usb3-ssphy";
				reg = <0x380 0x10>;
				#phy-cells = <0>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 14>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 14>;
				vbus-supply = <&usb0_vbus0>;
			};
		};

		usb1: usb@65c00000 {
			compatible = "socionext,uniphier-dwc3", "snps,dwc3";
			status = "disabled";
			reg = <0x65c00000 0xcd00>;
			interrupt-names = "host";
			interrupts = <GIC_SPI 137 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb1>, <&pinctrl_usb2>;
			clock-names = "ref", "bus_early", "suspend";
			clocks = <&sys_clk 12>, <&sys_clk 12>, <&sys_clk 12>;
			resets = <&usb1_rst 15>;
			phys = <&usb1_hsphy0>, <&usb1_hsphy1>, <&usb1_ssphy0>;
			dr_mode = "host";
		};

		usb-controller@65d00000 {
			compatible = "socionext,uniphier-pro5-dwc3-glue",
				     "simple-mfd";
			reg = <0x65d00000 0x400>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x65d00000 0x400>;

			usb1_rst: reset-controller@0 {
				compatible = "socionext,uniphier-pro5-usb3-reset";
				reg = <0x0 0x4>;
				#reset-cells = <1>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 15>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 15>;
			};

			usb1_vbus0: regulator@100 {
				compatible = "socionext,uniphier-pro5-usb3-regulator";
				reg = <0x100 0x10>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 15>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 15>;
			};

			usb1_vbus1: regulator@110 {
				compatible = "socionext,uniphier-pro5-usb3-regulator";
				reg = <0x110 0x10>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 15>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 15>;
			};

			usb1_hsphy0: phy@280 {
				compatible = "socionext,uniphier-pro5-usb3-hsphy";
				reg = <0x280 0x10>;
				#phy-cells = <0>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 15>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 15>;
				vbus-supply = <&usb1_vbus0>;
			};

			usb1_hsphy1: phy@290 {
				compatible = "socionext,uniphier-pro5-usb3-hsphy";
				reg = <0x290 0x10>;
				#phy-cells = <0>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 15>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 15>;
				vbus-supply = <&usb1_vbus1>;
			};

			usb1_ssphy0: phy@380 {
				compatible = "socionext,uniphier-pro5-usb3-ssphy";
				reg = <0x380 0x10>;
				#phy-cells = <0>;
				clock-names = "gio", "link";
				clocks = <&sys_clk 12>, <&sys_clk 15>;
				reset-names = "gio", "link";
				resets = <&sys_rst 12>, <&sys_rst 15>;
				vbus-supply = <&usb1_vbus0>;
			};
		};

		pcie_ep: pcie-ep@66000000 {
			compatible = "socionext,uniphier-pro5-pcie-ep";
			status = "disabled";
			reg-names = "dbi", "dbi2", "link", "addr_space";
			reg = <0x66000000 0x1000>, <0x66001000 0x1000>,
			      <0x66010000 0x10000>, <0x67000000 0x400000>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_pcie>;
			clock-names = "gio", "link";
			clocks = <&sys_clk 12>, <&sys_clk 24>;
			reset-names = "gio", "link";
			resets = <&sys_rst 12>, <&sys_rst 24>;
			num-ib-windows = <16>;
			num-ob-windows = <16>;
			num-lanes = <4>;
			phy-names = "pcie-phy";
			phys = <&pcie_phy>;
		};

		pcie_phy: phy@66038000 {
			compatible = "socionext,uniphier-pro5-pcie-phy";
			reg = <0x66038000 0x4000>;
			#phy-cells = <0>;
			clock-names = "gio", "link";
			clocks = <&sys_clk 12>, <&sys_clk 24>;
			reset-names = "gio", "link";
			resets = <&sys_rst 12>, <&sys_rst 24>;
		};

		nand: nand-controller@68000000 {
			compatible = "socionext,uniphier-denali-nand-v5b";
			status = "disabled";
			reg-names = "nand_data", "denali_reg";
			reg = <0x68000000 0x20>, <0x68100000 0x1000>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_nand>;
			clock-names = "nand", "nand_x", "ecc";
			clocks = <&sys_clk 2>, <&sys_clk 3>, <&sys_clk 3>;
			reset-names = "nand", "reg";
			resets = <&sys_rst 2>, <&sys_rst 2>;
		};

		emmc: mmc@68400000 {
			compatible = "socionext,uniphier-sd-v3.1";
			status = "disabled";
			reg = <0x68400000 0x800>;
			interrupts = <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_emmc>;
			clocks = <&sd_clk 1>;
			reset-names = "host", "hw";
			resets = <&sd_rst 1>, <&sd_rst 6>;
			bus-width = <8>;
			cap-mmc-highspeed;
			cap-mmc-hw-reset;
			non-removable;
		};

		sd: mmc@68800000 {
			compatible = "socionext,uniphier-sd-v3.1";
			status = "disabled";
			reg = <0x68800000 0x800>;
			interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default", "uhs";
			pinctrl-0 = <&pinctrl_sd>;
			pinctrl-1 = <&pinctrl_sd_uhs>;
			clocks = <&sd_clk 0>;
			reset-names = "host";
			resets = <&sd_rst 0>;
			bus-width = <4>;
			cap-sd-highspeed;
			sd-uhs-sdr12;
			sd-uhs-sdr25;
			sd-uhs-sdr50;
			socionext,syscon-uhs-mode = <&sdctrl 0>;
		};
	};
};

#include "uniphier-pinctrl.dtsi"
