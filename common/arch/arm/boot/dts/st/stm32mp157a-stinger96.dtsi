// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2020 Manivannan Sadhasivam
 */

/dts-v1/;

#include "stm32mp157.dtsi"
#include "stm32mp15-pinctrl.dtsi"
#include "stm32mp15xxac-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/mfd/st,stpmic1.h>

/ {
	aliases {
		mmc0 = &sdmmc1;
		serial0 = &uart4;
		serial1 = &uart7;
		serial2 = &usart2;
		spi0 = &spi4;
	};

	chosen {
		stdout-path = "serial1:115200n8";
	};

	memory@c0000000 {
		device_type = "memory";
		reg = <0xc0000000 0x10000000>;
	};

	led {
		compatible = "gpio-leds";

		led1 {
			label = "green:user1";
			gpios = <&gpioa 13 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
			default-state = "off";
		};

		led2 {
			label = "green:user2";
			gpios = <&gpioh 3 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "mmc0";
			default-state = "off";
		};

		led3 {
			label = "green:user3";
			gpios = <&gpioh 2 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "mmc1";
			default-state = "off";
		};

		led4 {
			label = "green:user4";
			gpios = <&gpiof 12 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "none";
			default-state = "off";
			panic-indicator;
		};
	};

	sd_switch: regulator-sd_switch {
		compatible = "regulator-gpio";
		regulator-name = "sd_switch";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <2900000>;
		regulator-type = "voltage";
		regulator-always-on;

		gpios = <&gpioa 8 GPIO_ACTIVE_HIGH>;
		gpios-states = <0>;
		states = <1800000 0x1>,
			 <2900000 0x0>;
	};
};

/* Only headless mode is supported */
&gpu {
	status = "disabled";
};

/* LS-I2C0 */
&i2c2 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_pins_a>;
	i2c-scl-rising-time-ns = <1000>;
	i2c-scl-falling-time-ns = <300>;
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;
};

&i2c4 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4_pins_a>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;

	pmic: stpmic@33 {
		compatible = "st,stpmic1";
		reg = <0x33>;
		interrupts-extended = <&gpioa 0 IRQ_TYPE_EDGE_FALLING>;
		interrupt-controller;
		#interrupt-cells = <2>;
		status = "okay";

		regulators {
			compatible = "st,stpmic1-regulators";

			ldo1-supply = <&v3v3>;
			ldo2-supply = <&v3v3>;
			ldo3-supply = <&vdd_ddr>;
			ldo5-supply = <&v3v3>;
			ldo6-supply = <&v3v3>;
			pwr_sw1-supply = <&bst_out>;
			pwr_sw2-supply = <&bst_out>;

			vddcore: buck1 {
				regulator-name = "vddcore";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd_ddr: buck2 {
				regulator-name = "vdd_ddr";
				regulator-min-microvolt = <1500000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd: buck3 {
				regulator-name = "vdd";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				st,mask-reset;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			v3v3: buck4 {
				regulator-name = "v3v3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-over-current-protection;
				regulator-initial-mode = <0>;
			};

			vdda: ldo1 {
				regulator-name = "vdda";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				interrupts = <IT_CURLIM_LDO1 0>;
			};

			v2v9: ldo2 {
				regulator-name = "v2v9";
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				regulator-always-on;
				interrupts = <IT_CURLIM_LDO2 0>;
			};

			vtt_ddr: ldo3 {
				regulator-name = "vtt_ddr";
				regulator-min-microvolt = <500000>;
				regulator-max-microvolt = <750000>;
				regulator-always-on;
				regulator-over-current-protection;
			};

			vdd_usb: ldo4 {
				regulator-name = "vdd_usb";
				interrupts = <IT_CURLIM_LDO4 0>;
			};

			vdd_sd: ldo5 {
				regulator-name = "vdd_sd";
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				interrupts = <IT_CURLIM_LDO5 0>;
				regulator-boot-on;
			};

			v1v8: ldo6 {
				regulator-name = "v1v8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				interrupts = <IT_CURLIM_LDO6 0>;
			};

			vref_ddr: vref_ddr {
				regulator-name = "vref_ddr";
				regulator-always-on;
			};

			bst_out: boost {
				regulator-name = "bst_out";
				interrupts = <IT_OCP_BOOST 0>;
			};

			vbus_otg: pwr_sw1 {
				regulator-name = "vbus_otg";
				interrupts = <IT_OCP_OTG 0>;
				regulator-active-discharge = <1>;
			};

			vbus_sw: pwr_sw2 {
				regulator-name = "vbus_sw";
				interrupts = <IT_OCP_SWOUT 0>;
				regulator-active-discharge = <1>;
			};
		};

		onkey {
			compatible = "st,stpmic1-onkey";
			interrupts = <IT_PONKEY_F 0>, <IT_PONKEY_R 1>;
			interrupt-names = "onkey-falling", "onkey-rising";
			status = "okay";
		};

		watchdog {
			compatible = "st,stpmic1-wdt";
			status = "disabled";
		};
	};
};

&iwdg2 {
	timeout-sec = <32>;
	status = "okay";
};

&pwr_regulators {
	vdd-supply = <&vdd>;
	vdd_3v3_usbfs-supply = <&vdd_usb>;
};

&rng1 {
	status = "okay";
};

&rtc {
	status = "okay";
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a &sdmmc1_dir_pins_b>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a &sdmmc1_dir_pins_b>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a &sdmmc1_dir_sleep_pins_b>;
	broken-cd;
	disable-wp;
	st,sig-dir;
	st,neg-edge;
	st,use-ckin;
	bus-width = <4>;
	vmmc-supply = <&vdd_sd>;
	vqmmc-supply = <&sd_switch>;
	status = "okay";
};

/* LS-SPI0 */
&spi4 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi4_pins_a>;
	cs-gpios = <&gpioe 11 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

/* BG96 */
&usart2 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&usart2_pins_b>;
	pinctrl-1 = <&usart2_sleep_pins_b>;
	uart-has-rtscts;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

/* LS-UART0 */
&uart4 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pins_c>;
	uart-has-rtscts;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

/* Debug console */
&uart7 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart7_pins_b>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&usbh_ehci {
	phys = <&usbphyc_port0>;
	phy-names = "usb";
	status = "okay";
};

&usbotg_hs {
	dr_mode = "peripheral";
	pinctrl-0 = <&usbotg_hs_pins_a>;
	pinctrl-names = "default";
	phy-names = "usb2-phy";
	phys = <&usbphyc_port1 0>;
	vbus-supply = <&vbus_otg>;
	status = "okay";
};

&usbphyc {
	status = "okay";
};

&usbphyc_port0 {
	phy-supply = <&vdd_usb>;
};

&usbphyc_port1 {
	phy-supply = <&vdd_usb>;
};
