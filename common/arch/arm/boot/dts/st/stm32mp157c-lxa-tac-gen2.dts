// SPDX-License-Identifier: (GPL-2.0-or-later OR BSD-3-Clause)
/*
 * Copyright (C) 2020 STMicroelectronics - All Rights Reserved
 * Copyright (C) 2021 <PERSON><PERSON><PERSON>, Pengutronix
 * Copyright (C) 2023 <PERSON>, Pengutronix
 */

/dts-v1/;

#include "stm32mp157.dtsi"
#include "stm32mp15xc-lxa-tac.dtsi"

/ {
	model = "Linux Automation Test Automation Controller (TAC) Gen 2";
	compatible = "lxa,stm32mp157c-tac-gen2", "oct,stm32mp15xx-osd32", "st,stm32mp157";

	backlight: backlight {
		compatible = "pwm-backlight";
		power-supply = <&v3v3>;

		brightness-levels = <0 31 63 95 127 159 191 223 255>;
		default-brightness-level = <7>;
		pwms = <&led_pwm 3 1000000 0>;
	};

	reg_iobus_12v: regulator-iobus-12v {
		compatible = "regulator-fixed";
		vin-supply = <&reg_12v>;
		gpio = <&gpioh 13 GPIO_ACTIVE_LOW>;
		regulator-max-microvolt = <12000000>;
		regulator-min-microvolt = <12000000>;
		regulator-name = "12V_IOBUS";
	};

	led-controller-1 {
		compatible = "pwm-leds-multicolor";

		multi-led {
			color = <LED_COLOR_ID_RGB>;
			function = LED_FUNCTION_STATUS;
			max-brightness = <65535>;

			led-red {
				active-low;
				color = <LED_COLOR_ID_RED>;
				pwms = <&led_pwm 0 1000000 0>;
			};

			led-green {
				active-low;
				color = <LED_COLOR_ID_GREEN>;
				pwms = <&led_pwm 2 1000000 0>;
			};

			led-blue {
				active-low;
				color = <LED_COLOR_ID_BLUE>;
				pwms = <&led_pwm 1 1000000 0>;
			};
		};
	};

	led-controller-2 {
		compatible = "gpio-leds";

		led-5 {
			label = "tac:green:iobus";
			gpios = <&gpiog 1 GPIO_ACTIVE_HIGH>;
		};

		led-6 {
			label = "tac:green:can";
			gpios = <&gpiof 3 GPIO_ACTIVE_HIGH>;
		};

		led-7 {
			label = "tac:green:out0";
			gpios = <&gpiob 8 GPIO_ACTIVE_HIGH>;
		};

		led-8 {
			label = "tac:green:out1";
			gpios = <&gpiog 3 GPIO_ACTIVE_HIGH>;
		};

		led-9 {
			label = "tac:green:uarttx";
			gpios = <&gpiod 3 GPIO_ACTIVE_HIGH>;
		};

		led-10 {
			label = "tac:green:uartrx";
			gpios = <&gpiof 6 GPIO_ACTIVE_HIGH>;
		};

		led-11 {
			label = "tac:green:usbh1";
			gpios = <&gpioc 8 GPIO_ACTIVE_HIGH>;
		};

		led-12 {
			label = "tac:green:usbh2";
			gpios = <&gpiod 6 GPIO_ACTIVE_HIGH>;
		};

		led-13 {
			label = "tac:green:usbh3";
			gpios = <&gpiob 9 GPIO_ACTIVE_HIGH>;
		};

		led-14 {
			label = "tac:green:usbg";
			gpios = <&gpiod 14 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "usb-gadget";
		};

		led-15 {
			label = "tac:green:dutpwr";
			gpios = <&gpioa 15 GPIO_ACTIVE_HIGH>;
		};
	};
};

&gpioa {
	gpio-line-names = "", "", "DUT_PWR_EN", "", "STACK_CS3", /*  0 */
	"ETH_GPIO1", "ETH_INT", "", "", "",                      /*  5 */
	"", "", "", "BOOTROM_LED", "ETH_LAB_LEDRP",              /* 10 */
	"";                                                      /* 15 */
};

&gpioc {
	gpio-line-names = "", "DUT_PWR_DISCH", "", "", "", /*  0 */
	"", "", "", "", "",                                /*  5 */
	"", "";                                            /* 10 */
};

&gpu {
	status = "disabled";
};

&m_can2 {
	termination-gpios = <&gpioe 4 GPIO_ACTIVE_HIGH>;
	termination-ohms = <120>;
};

&spi2 {
	adc@0 {
		compatible = "ti,lmp92064";
		reg = <0>;

		reset-gpios = <&gpioa 4 GPIO_ACTIVE_HIGH>;
		shunt-resistor-micro-ohms = <15000>;
		spi-max-frequency = <5000000>;
		vdd-supply = <&reg_pb_3v3>;
		vdig-supply = <&reg_pb_3v3>;
	};
};

&timers8 {
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	status = "okay";

	led_pwm: pwm {
		pinctrl-names = "default", "sleep";
		pinctrl-0 = <&pwm8_pins_b>;
		pinctrl-1 = <&pwm8_sleep_pins_b>;
		status = "okay";
	};
};
