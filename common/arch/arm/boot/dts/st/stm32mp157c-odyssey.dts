// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) 2020 <PERSON><PERSON> <marcin.s<PERSON><PERSON><PERSON>@gmail.com>.
 */

/dts-v1/;

#include "stm32mp157c-odyssey-som.dtsi"

/ {
	model = "Seeed Studio Odyssey-STM32MP157C Board";
	compatible = "seeed,stm32mp157c-odyssey",
		     "seeed,stm32mp157c-odyssey-som", "st,stm32mp157";

	aliases {
		ethernet0 = &ethernet0;
		serial0 = &uart4;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};
};

&dcmi {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&dcmi_pins_b>;
	pinctrl-1 = <&dcmi_sleep_pins_b>;
};

&ethernet0 {
	status = "okay";
	pinctrl-0 = <&ethernet0_rgmii_pins_a>;
	pinctrl-1 = <&ethernet0_rgmii_sleep_pins_a>;
	pinctrl-names = "default", "sleep";
	phy-mode = "rgmii-id";
	max-speed = <1000>;
	phy-handle = <&phy0>;
	assigned-clocks = <&rcc ETHCK_K>, <&rcc PLL4_P>;
	assigned-clock-parents = <&rcc PLL4_P>;
	assigned-clock-rates = <125000000>; /* Clock PLL4 to 750Mhz in ATF/U-Boot */
	st,eth-clk-sel;

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "snps,dwmac-mdio";
		phy0: ethernet-phy@7 { /* KSZ9031RN */
			reg = <7>;
			reset-gpios = <&gpiog 0 GPIO_ACTIVE_LOW>; /* ETH_RST# */
			reset-assert-us = <10000>;
			reset-deassert-us = <300>;
		};
	};
};

&i2c1 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c1_pins_a>;
	pinctrl-1 = <&i2c1_sleep_pins_a>;
	i2c-scl-rising-time-ns = <100>;
	i2c-scl-falling-time-ns = <7>;
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a>;
	cd-gpios = <&gpioi 3 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
	disable-wp;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&v3v3>;
	status = "okay";
};

&uart4 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

