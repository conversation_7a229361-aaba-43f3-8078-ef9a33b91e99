// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2021 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/clock/stm32mp13-clks.h>
#include <dt-bindings/reset/stm32mp13-resets.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0>;
		};
	};

	arm-pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 133 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>;
		interrupt-parent = <&intc>;
	};

	firmware {
		optee {
			method = "smc";
			compatible = "linaro,optee-tz";
			interrupt-parent = <&intc>;
			interrupts = <GIC_PPI 15 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>;
		};

		scmi: scmi {
			compatible = "linaro,scmi-optee";
			#address-cells = <1>;
			#size-cells = <0>;
			linaro,optee-channel-id = <0>;

			scmi_clk: protocol@14 {
				reg = <0x14>;
				#clock-cells = <1>;
			};

			scmi_reset: protocol@16 {
				reg = <0x16>;
				#reset-cells = <1>;
			};

			scmi_voltd: protocol@17 {
				reg = <0x17>;

				scmi_regu: regulators {
					#address-cells = <1>;
					#size-cells = <0>;

					scmi_reg11: regulator@0 {
						reg = <VOLTD_SCMI_REG11>;
						regulator-name = "reg11";
					};
					scmi_reg18: regulator@1 {
						reg = <VOLTD_SCMI_REG18>;
						regulator-name = "reg18";
					};
					scmi_usb33: regulator@2 {
						reg = <VOLTD_SCMI_USB33>;
						regulator-name = "usb33";
					};
				};
			};
		};
	};

	intc: interrupt-controller@a0021000 {
		compatible = "arm,cortex-a7-gic";
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0xa0021000 0x1000>,
		      <0xa0022000 0x2000>;
	};

	psci {
		compatible = "arm,psci-1.0";
		method = "smc";
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>;
		interrupt-parent = <&intc>;
		always-on;
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		interrupt-parent = <&intc>;
		ranges;

		timers2: timer@40000000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40000000 0x400>;
			interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM2_K>;
			clock-names = "int";
			dmas = <&dmamux1 18 0x400 0x1>,
			       <&dmamux1 19 0x400 0x1>,
			       <&dmamux1 20 0x400 0x1>,
			       <&dmamux1 21 0x400 0x1>,
			       <&dmamux1 22 0x400 0x1>;
			dma-names = "ch1", "ch2", "ch3", "ch4", "up";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@1 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <1>;
				status = "disabled";
			};

			counter {
				compatible = "st,stm32-timer-counter";
				status = "disabled";
			};
		};

		timers3: timer@40001000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40001000 0x400>;
			interrupts = <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM3_K>;
			clock-names = "int";
			dmas = <&dmamux1 23 0x400 0x1>,
			       <&dmamux1 24 0x400 0x1>,
			       <&dmamux1 25 0x400 0x1>,
			       <&dmamux1 26 0x400 0x1>,
			       <&dmamux1 27 0x400 0x1>,
			       <&dmamux1 28 0x400 0x1>;
			dma-names = "ch1", "ch2", "ch3", "ch4", "up", "trig";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@2 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <2>;
				status = "disabled";
			};

			counter {
				compatible = "st,stm32-timer-counter";
				status = "disabled";
			};
		};

		timers4: timer@40002000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40002000 0x400>;
			interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM4_K>;
			clock-names = "int";
			dmas = <&dmamux1 29 0x400 0x1>,
			       <&dmamux1 30 0x400 0x1>,
			       <&dmamux1 31 0x400 0x1>,
			       <&dmamux1 32 0x400 0x1>;
			dma-names = "ch1", "ch2", "ch3", "up";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@3 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <3>;
				status = "disabled";
			};

			counter {
				compatible = "st,stm32-timer-counter";
				status = "disabled";
			};
		};

		timers5: timer@40003000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40003000 0x400>;
			interrupts = <GIC_SPI 51 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM5_K>;
			clock-names = "int";
			dmas = <&dmamux1 55 0x400 0x1>,
			       <&dmamux1 56 0x400 0x1>,
			       <&dmamux1 57 0x400 0x1>,
			       <&dmamux1 58 0x400 0x1>,
			       <&dmamux1 59 0x400 0x1>,
			       <&dmamux1 60 0x400 0x1>;
			dma-names = "ch1", "ch2", "ch3", "ch4", "up", "trig";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@4 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <4>;
				status = "disabled";
			};

			counter {
				compatible = "st,stm32-timer-counter";
				status = "disabled";
			};
		};

		timers6: timer@40004000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40004000 0x400>;
			interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM6_K>;
			clock-names = "int";
			dmas = <&dmamux1 69 0x400 0x1>;
			dma-names = "up";
			status = "disabled";

			timer@5 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <5>;
				status = "disabled";
			};
		};

		timers7: timer@40005000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40005000 0x400>;
			interrupts = <GIC_SPI 56 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM7_K>;
			clock-names = "int";
			dmas = <&dmamux1 70 0x400 0x1>;
			dma-names = "up";
			status = "disabled";

			timer@6 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <6>;
				status = "disabled";
			};
		};

		lptimer1: timer@40009000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-lptimer";
			reg = <0x40009000 0x400>;
			interrupts-extended = <&exti 47 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc LPTIM1_K>;
			clock-names = "mux";
			wakeup-source;
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm-lp";
				#pwm-cells = <3>;
				status = "disabled";
			};

			trigger@0 {
				compatible = "st,stm32-lptimer-trigger";
				reg = <0>;
				status = "disabled";
			};

			counter {
				compatible = "st,stm32-lptimer-counter";
				status = "disabled";
			};

			timer {
				compatible = "st,stm32-lptimer-timer";
				status = "disabled";
			};
		};

		i2s2: audio-controller@4000b000 {
			compatible = "st,stm32h7-i2s";
			reg = <0x4000b000 0x400>;
			#sound-dai-cells = <0>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&dmamux1 39 0x400 0x01>,
			       <&dmamux1 40 0x400 0x01>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		spi2: spi@4000b000 {
			compatible = "st,stm32h7-spi";
			reg = <0x4000b000 0x400>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc SPI2_K>;
			resets = <&rcc SPI2_R>;
			#address-cells = <1>;
			#size-cells = <0>;
			dmas = <&dmamux1 39 0x400 0x01>,
			       <&dmamux1 40 0x400 0x01>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		i2s3: audio-controller@4000c000 {
			compatible = "st,stm32h7-i2s";
			reg = <0x4000c000 0x400>;
			#sound-dai-cells = <0>;
			interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&dmamux1 61 0x400 0x01>,
			       <&dmamux1 62 0x400 0x01>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		spi3: spi@4000c000 {
			compatible = "st,stm32h7-spi";
			reg = <0x4000c000 0x400>;
			interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc SPI3_K>;
			resets = <&rcc SPI3_R>;
			#address-cells = <1>;
			#size-cells = <0>;
			dmas = <&dmamux1 61 0x400 0x01>,
			       <&dmamux1 62 0x400 0x01>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		spdifrx: audio-controller@4000d000 {
			compatible = "st,stm32h7-spdifrx";
			reg = <0x4000d000 0x400>;
			#sound-dai-cells = <0>;
			clocks = <&rcc SPDIF_K>;
			clock-names = "kclk";
			interrupts = <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&dmamux1 93 0x400 0x01>,
			       <&dmamux1 94 0x400 0x01>;
			dma-names = "rx", "rx-ctrl";
			status = "disabled";
		};

		usart3: serial@4000f000 {
			compatible = "st,stm32h7-uart";
			reg = <0x4000f000 0x400>;
			interrupts-extended = <&exti 28 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc USART3_K>;
			resets = <&rcc USART3_R>;
			wakeup-source;
			dmas = <&dmamux1 45 0x400 0x5>,
			       <&dmamux1 46 0x400 0x1>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		uart4: serial@40010000 {
			compatible = "st,stm32h7-uart";
			reg = <0x40010000 0x400>;
			interrupts-extended = <&exti 30 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc UART4_K>;
			resets = <&rcc UART4_R>;
			wakeup-source;
			dmas = <&dmamux1 63 0x400 0x5>,
			       <&dmamux1 64 0x400 0x1>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		uart5: serial@40011000 {
			compatible = "st,stm32h7-uart";
			reg = <0x40011000 0x400>;
			interrupts-extended = <&exti 31 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc UART5_K>;
			resets = <&rcc UART5_R>;
			wakeup-source;
			dmas = <&dmamux1 65 0x400 0x5>,
			       <&dmamux1 66 0x400 0x1>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		i2c1: i2c@40012000 {
			compatible = "st,stm32mp13-i2c";
			reg = <0x40012000 0x400>;
			interrupt-names = "event", "error";
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc I2C1_K>;
			resets = <&rcc I2C1_R>;
			#address-cells = <1>;
			#size-cells = <0>;
			dmas = <&dmamux1 33 0x400 0x1>,
			       <&dmamux1 34 0x400 0x1>;
			dma-names = "rx", "tx";
			st,syscfg-fmp = <&syscfg 0x4 0x1>;
			i2c-analog-filter;
			status = "disabled";
		};

		i2c2: i2c@40013000 {
			compatible = "st,stm32mp13-i2c";
			reg = <0x40013000 0x400>;
			interrupt-names = "event", "error";
			interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc I2C2_K>;
			resets = <&rcc I2C2_R>;
			#address-cells = <1>;
			#size-cells = <0>;
			dmas = <&dmamux1 35 0x400 0x1>,
			       <&dmamux1 36 0x400 0x1>;
			dma-names = "rx", "tx";
			st,syscfg-fmp = <&syscfg 0x4 0x2>;
			i2c-analog-filter;
			status = "disabled";
		};

		uart7: serial@40018000 {
			compatible = "st,stm32h7-uart";
			reg = <0x40018000 0x400>;
			interrupts-extended = <&exti 32 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc UART7_K>;
			resets = <&rcc UART7_R>;
			wakeup-source;
			dmas = <&dmamux1 79 0x400 0x5>,
			       <&dmamux1 80 0x400 0x1>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		uart8: serial@40019000 {
			compatible = "st,stm32h7-uart";
			reg = <0x40019000 0x400>;
			interrupts-extended = <&exti 33 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc UART8_K>;
			resets = <&rcc UART8_R>;
			wakeup-source;
			dmas = <&dmamux1 81 0x400 0x5>,
			       <&dmamux1 82 0x400 0x1>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		timers1: timer@44000000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x44000000 0x400>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "brk", "up", "trg-com", "cc";
			clocks = <&rcc TIM1_K>;
			clock-names = "int";
			dmas = <&dmamux1 11 0x400 0x1>,
			       <&dmamux1 12 0x400 0x1>,
			       <&dmamux1 13 0x400 0x1>,
			       <&dmamux1 14 0x400 0x1>,
			       <&dmamux1 15 0x400 0x1>,
			       <&dmamux1 16 0x400 0x1>,
			       <&dmamux1 17 0x400 0x1>;
			dma-names = "ch1", "ch2", "ch3", "ch4",
				    "up", "trig", "com";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@0 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <0>;
				status = "disabled";
			};

			counter {
				compatible = "st,stm32-timer-counter";
				status = "disabled";
			};
		};

		timers8: timer@44001000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x44001000 0x400>;
			interrupts = <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 47 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "brk", "up", "trg-com", "cc";
			clocks = <&rcc TIM8_K>;
			clock-names = "int";
			dmas = <&dmamux1 47 0x400 0x1>,
			       <&dmamux1 48 0x400 0x1>,
			       <&dmamux1 49 0x400 0x1>,
			       <&dmamux1 50 0x400 0x1>,
			       <&dmamux1 51 0x400 0x1>,
			       <&dmamux1 52 0x400 0x1>,
			       <&dmamux1 53 0x400 0x1>;
			dma-names = "ch1", "ch2", "ch3", "ch4",
				    "up", "trig", "com";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@7 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <7>;
				status = "disabled";
			};

			counter {
				compatible = "st,stm32-timer-counter";
				status = "disabled";
			};
		};

		usart6: serial@44003000 {
			compatible = "st,stm32h7-uart";
			reg = <0x44003000 0x400>;
			interrupts-extended = <&exti 29 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc USART6_K>;
			resets = <&rcc USART6_R>;
			wakeup-source;
			dmas = <&dmamux1 71 0x400 0x5>,
			       <&dmamux1 72 0x400 0x1>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		i2s1: audio-controller@44004000 {
			compatible = "st,stm32h7-i2s";
			reg = <0x44004000 0x400>;
			#sound-dai-cells = <0>;
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&dmamux1 37 0x400 0x01>,
			       <&dmamux1 38 0x400 0x01>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		spi1: spi@44004000 {
			compatible = "st,stm32h7-spi";
			reg = <0x44004000 0x400>;
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc SPI1_K>;
			resets = <&rcc SPI1_R>;
			#address-cells = <1>;
			#size-cells = <0>;
			dmas = <&dmamux1 37 0x400 0x01>,
			       <&dmamux1 38 0x400 0x01>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		sai1: sai@4400a000 {
			compatible = "st,stm32h7-sai";
			reg = <0x4400a000 0x4>, <0x4400a3f0 0x10>;
			ranges = <0 0x4400a000 0x400>;
			#address-cells = <1>;
			#size-cells = <1>;
			interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&rcc SAI1_R>;
			status = "disabled";

			sai1a: audio-controller@4400a004 {
				compatible = "st,stm32-sai-sub-a";
				reg = <0x4 0x20>;
				#sound-dai-cells = <0>;
				clocks = <&rcc SAI1_K>;
				clock-names = "sai_ck";
				dmas = <&dmamux1 87 0x400 0x01>;
				status = "disabled";
			};

			sai1b: audio-controller@4400a024 {
				compatible = "st,stm32-sai-sub-b";
				reg = <0x24 0x20>;
				#sound-dai-cells = <0>;
				clocks = <&rcc SAI1_K>;
				clock-names = "sai_ck";
				dmas = <&dmamux1 88 0x400 0x01>;
				status = "disabled";
			};
		};

		sai2: sai@4400b000 {
			compatible = "st,stm32h7-sai";
			reg = <0x4400b000 0x4>, <0x4400b3f0 0x10>;
			ranges = <0 0x4400b000 0x400>;
			#address-cells = <1>;
			#size-cells = <1>;
			interrupts = <GIC_SPI 90 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&rcc SAI2_R>;
			status = "disabled";

			sai2a: audio-controller@4400b004 {
				compatible = "st,stm32-sai-sub-a";
				reg = <0x4 0x20>;
				#sound-dai-cells = <0>;
				clocks = <&rcc SAI2_K>;
				clock-names = "sai_ck";
				dmas = <&dmamux1 89 0x400 0x01>;
				status = "disabled";
			};

			sai2b: audio-controller@4400b024 {
				compatible = "st,stm32-sai-sub-b";
				reg = <0x24 0x20>;
				#sound-dai-cells = <0>;
				clocks = <&rcc SAI2_K>;
				clock-names = "sai_ck";
				dmas = <&dmamux1 90 0x400 0x01>;
				status = "disabled";
			};
		};

		dfsdm: dfsdm@4400d000 {
			compatible = "st,stm32mp1-dfsdm";
			reg = <0x4400d000 0x800>;
			clocks = <&rcc DFSDM_K>;
			clock-names = "dfsdm";
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			dfsdm0: filter@0 {
				compatible = "st,stm32-dfsdm-adc";
				reg = <0>;
				#io-channel-cells = <1>;
				interrupts = <GIC_SPI 140 IRQ_TYPE_LEVEL_HIGH>;
				dmas = <&dmamux1 101 0x400 0x01>;
				dma-names = "rx";
				status = "disabled";
			};

			dfsdm1: filter@1 {
				compatible = "st,stm32-dfsdm-adc";
				reg = <1>;
				#io-channel-cells = <1>;
				interrupts = <GIC_SPI 141 IRQ_TYPE_LEVEL_HIGH>;
				dmas = <&dmamux1 102 0x400 0x01>;
				dma-names = "rx";
				status = "disabled";
			};
		};

		dma1: dma-controller@48000000 {
			compatible = "st,stm32-dma";
			reg = <0x48000000 0x400>;
			interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 48 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc DMA1>;
			resets = <&rcc DMA1_R>;
			#dma-cells = <4>;
			st,mem2mem;
			dma-requests = <8>;
		};

		dma2: dma-controller@48001000 {
			compatible = "st,stm32-dma";
			reg = <0x48001000 0x400>;
			interrupts = <GIC_SPI 57 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 58 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 59 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc DMA2>;
			resets = <&rcc DMA2_R>;
			#dma-cells = <4>;
			st,mem2mem;
			dma-requests = <8>;
		};

		dmamux1: dma-router@48002000 {
			compatible = "st,stm32h7-dmamux";
			reg = <0x48002000 0x40>;
			clocks = <&rcc DMAMUX1>;
			resets = <&rcc DMAMUX1_R>;
			#dma-cells = <3>;
			dma-masters = <&dma1 &dma2>;
			dma-requests = <128>;
			dma-channels = <16>;
		};

		adc_2: adc@48004000 {
			compatible = "st,stm32mp13-adc-core";
			reg = <0x48004000 0x400>;
			interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc ADC2>, <&rcc ADC2_K>;
			clock-names = "bus", "adc";
			interrupt-controller;
			#interrupt-cells = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			adc2: adc@0 {
				compatible = "st,stm32mp13-adc";
				#io-channel-cells = <1>;
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x0>;
				interrupt-parent = <&adc_2>;
				interrupts = <0>;
				dmas = <&dmamux1 10 0x400 0x80000001>;
				dma-names = "rx";
				status = "disabled";

				channel@13 {
					reg = <13>;
					label = "vrefint";
				};
				channel@14 {
					reg = <14>;
					label = "vddcore";
				};
				channel@16 {
					reg = <16>;
					label = "vddcpu";
				};
				channel@17 {
					reg = <17>;
					label = "vddq_ddr";
				};
			};
		};

		usbotg_hs: usb@49000000 {
			compatible = "st,stm32mp15-hsotg", "snps,dwc2";
			reg = <0x49000000 0x40000>;
			clocks = <&rcc USBO_K>;
			clock-names = "otg";
			resets = <&rcc USBO_R>;
			reset-names = "dwc2";
			interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
			g-rx-fifo-size = <512>;
			g-np-tx-fifo-size = <32>;
			g-tx-fifo-size = <256 16 16 16 16 16 16 16>;
			dr_mode = "otg";
			otg-rev = <0x200>;
			usb33d-supply = <&scmi_usb33>;
			status = "disabled";
		};

		usart1: serial@4c000000 {
			compatible = "st,stm32h7-uart";
			reg = <0x4c000000 0x400>;
			interrupts-extended = <&exti 26 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc USART1_K>;
			resets = <&rcc USART1_R>;
			wakeup-source;
			dmas = <&dmamux1 41 0x400 0x5>,
			       <&dmamux1 42 0x400 0x1>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		usart2: serial@4c001000 {
			compatible = "st,stm32h7-uart";
			reg = <0x4c001000 0x400>;
			interrupts-extended = <&exti 27 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc USART2_K>;
			resets = <&rcc USART2_R>;
			wakeup-source;
			dmas = <&dmamux1 43 0x400 0x5>,
			       <&dmamux1 44 0x400 0x1>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		i2s4: audio-controller@4c002000 {
			compatible = "st,stm32h7-i2s";
			reg = <0x4c002000 0x400>;
			#sound-dai-cells = <0>;
			interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&dmamux1 83 0x400 0x01>,
			       <&dmamux1 84 0x400 0x01>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		spi4: spi@4c002000 {
			compatible = "st,stm32h7-spi";
			reg = <0x4c002000 0x400>;
			interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc SPI4_K>;
			resets = <&rcc SPI4_R>;
			#address-cells = <1>;
			#size-cells = <0>;
			dmas = <&dmamux1 83 0x400 0x01>,
			       <&dmamux1 84 0x400 0x01>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		spi5: spi@4c003000 {
			compatible = "st,stm32h7-spi";
			reg = <0x4c003000 0x400>;
			interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc SPI5_K>;
			resets = <&rcc SPI5_R>;
			#address-cells = <1>;
			#size-cells = <0>;
			dmas = <&dmamux1 85 0x400 0x01>,
			       <&dmamux1 86 0x400 0x01>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		i2c3: i2c@4c004000 {
			compatible = "st,stm32mp13-i2c";
			reg = <0x4c004000 0x400>;
			interrupt-names = "event", "error";
			interrupts = <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc I2C3_K>;
			resets = <&rcc I2C3_R>;
			#address-cells = <1>;
			#size-cells = <0>;
			dmas = <&dmamux1 73 0x400 0x1>,
			       <&dmamux1 74 0x400 0x1>;
			dma-names = "rx", "tx";
			st,syscfg-fmp = <&syscfg 0x4 0x4>;
			i2c-analog-filter;
			status = "disabled";
		};

		i2c4: i2c@4c005000 {
			compatible = "st,stm32mp13-i2c";
			reg = <0x4c005000 0x400>;
			interrupt-names = "event", "error";
			interrupts = <GIC_SPI 93 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc I2C4_K>;
			resets = <&rcc I2C4_R>;
			#address-cells = <1>;
			#size-cells = <0>;
			dmas = <&dmamux1 75 0x400 0x1>,
			       <&dmamux1 76 0x400 0x1>;
			dma-names = "rx", "tx";
			st,syscfg-fmp = <&syscfg 0x4 0x8>;
			i2c-analog-filter;
			status = "disabled";
		};

		i2c5: i2c@4c006000 {
			compatible = "st,stm32mp13-i2c";
			reg = <0x4c006000 0x400>;
			interrupt-names = "event", "error";
			interrupts = <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc I2C5_K>;
			resets = <&rcc I2C5_R>;
			#address-cells = <1>;
			#size-cells = <0>;
			dmas = <&dmamux1 115 0x400 0x1>,
			       <&dmamux1 116 0x400 0x1>;
			dma-names = "rx", "tx";
			st,syscfg-fmp = <&syscfg 0x4 0x10>;
			i2c-analog-filter;
			status = "disabled";
		};

		timers12: timer@4c007000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x4c007000 0x400>;
			interrupts = <GIC_SPI 104 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM12_K>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@11 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <11>;
				status = "disabled";
			};
		};

		timers13: timer@4c008000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x4c008000 0x400>;
			interrupts = <GIC_SPI 111 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM13_K>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@12 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <12>;
				status = "disabled";
			};
		};

		timers14: timer@4c009000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x4c009000 0x400>;
			interrupts = <GIC_SPI 112 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM14_K>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@13 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <13>;
				status = "disabled";
			};
		};

		timers15: timer@4c00a000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x4c00a000 0x400>;
			interrupts = <GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM15_K>;
			clock-names = "int";
			dmas = <&dmamux1 105 0x400 0x1>,
			       <&dmamux1 106 0x400 0x1>,
			       <&dmamux1 107 0x400 0x1>,
			       <&dmamux1 108 0x400 0x1>;
			dma-names = "ch1", "up", "trig", "com";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@14 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <14>;
				status = "disabled";
			};
		};

		timers16: timer@4c00b000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x4c00b000 0x400>;
			interrupts = <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM16_K>;
			clock-names = "int";
			dmas = <&dmamux1 109 0x400 0x1>,
			       <&dmamux1 110 0x400 0x1>;
			dma-names = "ch1", "up";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@15 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <15>;
				status = "disabled";
			};
		};

		timers17: timer@4c00c000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x4c00c000 0x400>;
			interrupts = <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "global";
			clocks = <&rcc TIM17_K>;
			clock-names = "int";
			dmas = <&dmamux1 111 0x400 0x1>,
			       <&dmamux1 112 0x400 0x1>;
			dma-names = "ch1", "up";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@16 {
				compatible = "st,stm32h7-timer-trigger";
				reg = <16>;
				status = "disabled";
			};
		};

		rcc: rcc@50000000 {
			compatible = "st,stm32mp13-rcc", "syscon";
			reg = <0x50000000 0x1000>;
			#clock-cells = <1>;
			#reset-cells = <1>;
			clock-names = "hse", "hsi", "csi", "lse", "lsi";
			clocks = <&scmi_clk CK_SCMI_HSE>,
				 <&scmi_clk CK_SCMI_HSI>,
				 <&scmi_clk CK_SCMI_CSI>,
				 <&scmi_clk CK_SCMI_LSE>,
				 <&scmi_clk CK_SCMI_LSI>;
		};

		exti: interrupt-controller@5000d000 {
			compatible = "st,stm32mp13-exti", "syscon";
			interrupt-controller;
			#interrupt-cells = <2>;
			reg = <0x5000d000 0x400>;
		};

		syscfg: syscon@50020000 {
			compatible = "st,stm32mp157-syscfg", "syscon";
			reg = <0x50020000 0x400>;
			clocks = <&rcc SYSCFG>;
		};

		lptimer2: timer@50021000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-lptimer";
			reg = <0x50021000 0x400>;
			interrupts-extended = <&exti 48 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc LPTIM2_K>;
			clock-names = "mux";
			wakeup-source;
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm-lp";
				#pwm-cells = <3>;
				status = "disabled";
			};

			trigger@1 {
				compatible = "st,stm32-lptimer-trigger";
				reg = <1>;
				status = "disabled";
			};

			counter {
				compatible = "st,stm32-lptimer-counter";
				status = "disabled";
			};

			timer {
				compatible = "st,stm32-lptimer-timer";
				status = "disabled";
			};
		};

		lptimer3: timer@50022000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-lptimer";
			reg = <0x50022000 0x400>;
			interrupts-extended = <&exti 50 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc LPTIM3_K>;
			clock-names = "mux";
			wakeup-source;
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm-lp";
				#pwm-cells = <3>;
				status = "disabled";
			};

			trigger@2 {
				compatible = "st,stm32-lptimer-trigger";
				reg = <2>;
				status = "disabled";
			};

			timer {
				compatible = "st,stm32-lptimer-timer";
				status = "disabled";
			};
		};

		lptimer4: timer@50023000 {
			compatible = "st,stm32-lptimer";
			reg = <0x50023000 0x400>;
			interrupts-extended = <&exti 52 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc LPTIM4_K>;
			clock-names = "mux";
			wakeup-source;
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm-lp";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer {
				compatible = "st,stm32-lptimer-timer";
				status = "disabled";
			};
		};

		lptimer5: timer@50024000 {
			compatible = "st,stm32-lptimer";
			reg = <0x50024000 0x400>;
			interrupts-extended = <&exti 53 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc LPTIM5_K>;
			clock-names = "mux";
			wakeup-source;
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm-lp";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer {
				compatible = "st,stm32-lptimer-timer";
				status = "disabled";
			};
		};

		mdma: dma-controller@58000000 {
			compatible = "st,stm32h7-mdma";
			reg = <0x58000000 0x1000>;
			interrupts = <GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc MDMA>;
			#dma-cells = <5>;
			dma-channels = <32>;
			dma-requests = <48>;
		};

		fmc: memory-controller@58002000 {
			compatible = "st,stm32mp1-fmc2-ebi";
			reg = <0x58002000 0x1000>;
			ranges = <0 0 0x60000000 0x04000000>, /* EBI CS 1 */
				 <1 0 0x64000000 0x04000000>, /* EBI CS 2 */
				 <2 0 0x68000000 0x04000000>, /* EBI CS 3 */
				 <3 0 0x6c000000 0x04000000>, /* EBI CS 4 */
				 <4 0 0x80000000 0x10000000>; /* NAND */
			#address-cells = <2>;
			#size-cells = <1>;
			clocks = <&rcc FMC_K>;
			resets = <&rcc FMC_R>;
			status = "disabled";

			nand-controller@4,0 {
				compatible = "st,stm32mp1-fmc2-nfc";
				reg = <4 0x00000000 0x1000>,
				      <4 0x08010000 0x1000>,
				      <4 0x08020000 0x1000>,
				      <4 0x01000000 0x1000>,
				      <4 0x09010000 0x1000>,
				      <4 0x09020000 0x1000>;
				#address-cells = <1>;
				#size-cells = <0>;
				interrupts = <GIC_SPI 49 IRQ_TYPE_LEVEL_HIGH>;
				dmas = <&mdma 24 0x2 0x12000a02 0x0 0x0>,
				       <&mdma 24 0x2 0x12000a08 0x0 0x0>,
				       <&mdma 25 0x2 0x12000a0a 0x0 0x0>;
				dma-names = "tx", "rx", "ecc";
				status = "disabled";
			};
		};

		qspi: spi@58003000 {
			compatible = "st,stm32f469-qspi";
			reg = <0x58003000 0x1000>, <0x70000000 0x10000000>;
			reg-names = "qspi", "qspi_mm";
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&mdma 26 0x2 0x10100002 0x0 0x0>,
			       <&mdma 26 0x2 0x10100008 0x0 0x0>;
			dma-names = "tx", "rx";
			clocks = <&rcc QSPI_K>;
			resets = <&rcc QSPI_R>;
			status = "disabled";
		};

		sdmmc1: mmc@58005000 {
			compatible = "st,stm32-sdmmc2", "arm,pl18x", "arm,primecell";
			arm,primecell-periphid = <0x20253180>;
			reg = <0x58005000 0x1000>, <0x58006000 0x1000>;
			interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc SDMMC1_K>;
			clock-names = "apb_pclk";
			resets = <&rcc SDMMC1_R>;
			cap-sd-highspeed;
			cap-mmc-highspeed;
			max-frequency = <130000000>;
			status = "disabled";
		};

		sdmmc2: mmc@58007000 {
			compatible = "st,stm32-sdmmc2", "arm,pl18x", "arm,primecell";
			arm,primecell-periphid = <0x20253180>;
			reg = <0x58007000 0x1000>, <0x58008000 0x1000>;
			interrupts = <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc SDMMC2_K>;
			clock-names = "apb_pclk";
			resets = <&rcc SDMMC2_R>;
			cap-sd-highspeed;
			cap-mmc-highspeed;
			max-frequency = <130000000>;
			status = "disabled";
		};

		usbh_ohci: usb@5800c000 {
			compatible = "generic-ohci";
			reg = <0x5800c000 0x1000>;
			clocks = <&usbphyc>, <&rcc USBH>;
			resets = <&rcc USBH_R>;
			interrupts = <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		usbh_ehci: usb@5800d000 {
			compatible = "generic-ehci";
			reg = <0x5800d000 0x1000>;
			clocks = <&usbphyc>, <&rcc USBH>;
			resets = <&rcc USBH_R>;
			interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
			companion = <&usbh_ohci>;
			status = "disabled";
		};

		iwdg2: watchdog@5a002000 {
			compatible = "st,stm32mp1-iwdg";
			reg = <0x5a002000 0x400>;
			clocks = <&rcc IWDG2>, <&scmi_clk CK_SCMI_LSI>;
			clock-names = "pclk", "lsi";
			status = "disabled";
		};

		usbphyc: usbphyc@5a006000 {
			#address-cells = <1>;
			#size-cells = <0>;
			#clock-cells = <0>;
			compatible = "st,stm32mp1-usbphyc";
			reg = <0x5a006000 0x1000>;
			clocks = <&rcc USBPHY_K>;
			resets = <&rcc USBPHY_R>;
			vdda1v1-supply = <&scmi_reg11>;
			vdda1v8-supply = <&scmi_reg18>;
			status = "disabled";

			usbphyc_port0: usb-phy@0 {
				#phy-cells = <0>;
				reg = <0>;
			};

			usbphyc_port1: usb-phy@1 {
				#phy-cells = <1>;
				reg = <1>;
			};
		};

		rtc: rtc@5c004000 {
			compatible = "st,stm32mp1-rtc";
			reg = <0x5c004000 0x400>;
			interrupts-extended = <&exti 19 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk CK_SCMI_RTCAPB>,
				 <&scmi_clk CK_SCMI_RTC>;
			clock-names = "pclk", "rtc_ck";
			status = "disabled";
		};

		bsec: efuse@5c005000 {
			compatible = "st,stm32mp13-bsec";
			reg = <0x5c005000 0x400>;
			#address-cells = <1>;
			#size-cells = <1>;

			part_number_otp: part_number_otp@4 {
				reg = <0x4 0x2>;
				bits = <0 12>;
			};
			ts_cal1: calib@5c {
				reg = <0x5c 0x2>;
			};
			ts_cal2: calib@5e {
				reg = <0x5e 0x2>;
			};
		};

		/*
		 * Break node order to solve dependency probe issue between
		 * pinctrl and exti.
		 */
		pinctrl: pinctrl@******** {
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "st,stm32mp135-pinctrl";
			ranges = <0 0x******** 0x8400>;
			interrupt-parent = <&exti>;
			st,syscfg = <&exti 0x60 0xff>;

			gpioa: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x0 0x400>;
				clocks = <&rcc GPIOA>;
				st,bank-name = "GPIOA";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 0 16>;
			};

			gpiob: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x1000 0x400>;
				clocks = <&rcc GPIOB>;
				st,bank-name = "GPIOB";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 16 16>;
			};

			gpioc: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x2000 0x400>;
				clocks = <&rcc GPIOC>;
				st,bank-name = "GPIOC";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 32 16>;
			};

			gpiod: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x3000 0x400>;
				clocks = <&rcc GPIOD>;
				st,bank-name = "GPIOD";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 48 16>;
			};

			gpioe: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x4000 0x400>;
				clocks = <&rcc GPIOE>;
				st,bank-name = "GPIOE";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 64 16>;
			};

			gpiof: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x5000 0x400>;
				clocks = <&rcc GPIOF>;
				st,bank-name = "GPIOF";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 80 16>;
			};

			gpiog: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x6000 0x400>;
				clocks = <&rcc GPIOG>;
				st,bank-name = "GPIOG";
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 96 16>;
			};

			gpioh: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x7000 0x400>;
				clocks = <&rcc GPIOH>;
				st,bank-name = "GPIOH";
				ngpios = <15>;
				gpio-ranges = <&pinctrl 0 112 15>;
			};

			gpioi: gpio@5000a000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x8000 0x400>;
				clocks = <&rcc GPIOI>;
				st,bank-name = "GPIOI";
				ngpios = <8>;
				gpio-ranges = <&pinctrl 0 128 8>;
			};
		};
	};
};
