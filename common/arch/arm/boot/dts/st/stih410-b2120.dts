// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 STMicroelectronics (R&D) Limited.
 * Author: <PERSON> <<EMAIL>>
 */
/dts-v1/;
#include "stih410.dtsi"
#include "stihxxx-b2120.dtsi"
/ {
	model = "STiH410 B2120";
	compatible = "st,stih410-b2120", "st,stih410";

	chosen {
		stdout-path = &sbc_serial0;
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x80000000>;
	};

	aliases {
		serial0 = &sbc_serial0;
		ethernet0 = &ethernet0;
	};

	usb2_picophy1: phy2 {
		status = "okay";
	};

	usb2_picophy2: phy3 {
		status = "okay";
	};

	soc {

		mmc0: sdhci@9060000 {
			max-frequency = <200000000>;
			sd-uhs-sdr50;
			sd-uhs-sdr104;
			sd-uhs-ddr50;
		};

		ohci0: usb@9a03c00 {
			status = "okay";
		};

		ehci0: usb@9a03e00 {
			status = "okay";
		};

		ohci1: usb@9a83c00 {
			status = "okay";
		};

		ehci1: usb@9a83e00 {
			status = "okay";
		};

		sti-display-subsystem@0 {
			sti-hda@8d02000 {
				status = "okay";
			};
		};
	};
};
