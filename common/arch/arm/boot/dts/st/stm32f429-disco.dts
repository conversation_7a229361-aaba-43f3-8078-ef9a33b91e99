/*
 * Copyright 2015 - <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 *     You should have received a copy of the GNU General Public
 *     License along with this file; if not, write to the Free
 *     Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston,
 *     MA 02110-1301 USA
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "stm32f429.dtsi"
#include "stm32f429-pinctrl.dtsi"
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "STMicroelectronics STM32F429i-DISCO board";
	compatible = "st,stm32f429i-disco", "st,stm32f429";

	chosen {
		bootargs = "root=/dev/ram";
		stdout-path = "serial0:115200n8";
	};

	memory@90000000 {
		device_type = "memory";
		reg = <0x90000000 0x800000>;
	};

	aliases {
		serial0 = &usart1;
	};

	leds {
		compatible = "gpio-leds";
		led-red {
			gpios = <&gpiog 14 0>;
		};
		led-green {
			gpios = <&gpiog 13 0>;
			linux,default-trigger = "heartbeat";
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;
		button-0 {
			label = "User";
			linux,code = <KEY_HOME>;
			gpios = <&gpioa 0 0>;
		};
	};

	/* This turns on vbus for otg for host mode (dwc2) */
	vcc5v_otg: vcc5v-otg-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpioc 4 0>;
		regulator-name = "vcc5_host1";
		regulator-always-on;
	};
};

&clk_hse {
	clock-frequency = <8000000>;
};

&crc {
	status = "okay";
};

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;
	clock-frequency = <100000>;
	status = "okay";

	stmpe811@41 {
		compatible = "st,stmpe811";
		reg = <0x41>;
		interrupts = <15 IRQ_TYPE_EDGE_FALLING>;
		interrupt-parent = <&gpioa>;
		/* 3.25 MHz ADC clock speed */
		st,adc-freq = <1>;
		/* 12-bit ADC */
		st,mod-12b = <1>;
		/* internal ADC reference */
		st,ref-sel = <0>;
		/* ADC converstion time: 80 clocks */
		st,sample-time = <4>;

		stmpe_touchscreen {
			compatible = "st,stmpe-ts";
			/* 8 sample average control */
			st,ave-ctrl = <3>;
			/* 7 length fractional part in z */
			st,fraction-z = <7>;
			/*
			 * 50 mA typical 80 mA max touchscreen drivers
			 * current limit value
			 */
			st,i-drive = <1>;
			/* 1 ms panel driver settling time */
			st,settling = <3>;
			/* 5 ms touch detect interrupt delay */
			st,touch-det-delay = <5>;
		};

		stmpe_adc {
			compatible = "st,stmpe-adc";
			/* forbid to use ADC channels 3-0 (touch) */
			st,norequest-mask = <0x0F>;
		};
	};
};

&ltdc {
	status = "okay";
	pinctrl-0 = <&ltdc_pins_b>;
	pinctrl-names = "default";

	port {
		ltdc_out_rgb: endpoint {
			remote-endpoint = <&panel_in_rgb>;
		};
	};
};

&rtc {
	assigned-clocks = <&rcc 1 CLK_RTC>;
	assigned-clock-parents = <&rcc 1 CLK_LSI>;
	status = "okay";
};

&spi5 {
	status = "okay";
	pinctrl-0 = <&spi5_pins>;
	pinctrl-names = "default";
	#address-cells = <1>;
	#size-cells = <0>;
	cs-gpios = <&gpioc 1 GPIO_ACTIVE_LOW>, <&gpioc 2 GPIO_ACTIVE_LOW>;

	l3gd20: l3gd20@0 {
		compatible = "st,l3gd20-gyro";
		spi-max-frequency = <10000000>;
		st,drdy-int-pin = <2>;
		interrupt-parent = <&gpioa>;
		interrupts = <1 IRQ_TYPE_EDGE_RISING>,
				<2 IRQ_TYPE_EDGE_RISING>;
		reg = <0>;
		status = "okay";
	};

	display: display@1 {
		/* Connect panel-ilitek-9341 to ltdc */
		compatible = "st,sf-tc240t-9370-t", "ilitek,ili9341";
		reg = <1>;
		spi-3wire;
		spi-max-frequency = <10000000>;
		dc-gpios = <&gpiod 13 0>;
		port {
			panel_in_rgb: endpoint {
			remote-endpoint = <&ltdc_out_rgb>;
			};
		};
	};
};

&timers5 {
	/* Override timer5 to act as clockevent */
	compatible = "st,stm32-timer";
	interrupts = <50>;
	status = "okay";
	/delete-property/#address-cells;
	/delete-property/#size-cells;
	/delete-property/clock-names;
	/delete-node/pwm;
	/delete-node/timer@4;
};

&usart1 {
	pinctrl-0 = <&usart1_pins_a>;
	pinctrl-names = "default";
	status = "okay";
};

&usbotg_hs {
	compatible = "st,stm32f4x9-fsotg";
	dr_mode = "host";
	pinctrl-0 = <&usbotg_fs_pins_b>;
	pinctrl-names = "default";
	status = "okay";
};
