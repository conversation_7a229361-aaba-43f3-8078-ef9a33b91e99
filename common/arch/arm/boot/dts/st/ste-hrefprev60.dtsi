// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright 2012 <PERSON>-<PERSON><PERSON> AB
 *
 * Device Tree for the HREF+ prior to the v60 variant.
 */

#include "ste-href-ab8500.dtsi"
#include "ste-href.dtsi"

/ {
	gpio_keys {
		button@1 {
			gpios = <&tc3589x_gpio 7 GPIO_ACTIVE_HIGH>;
		};
	};

	soc {
		/* Enable UART1 on this board */
		serial@80121000 {
			status = "okay";
		};

		i2c@80004000 {
			tps61052@33 {
				compatible = "ti,tps61052";
				reg = <0x33>;
			};

			tc35892@42 {
				compatible = "toshiba,tc35892";
				reg = <0x42>;
				interrupt-parent = <&gpio6>;
				interrupts = <25 IRQ_TYPE_EDGE_RISING>;
				pinctrl-names = "default";
				pinctrl-0 = <&tc35892_hrefprev60_mode>;

				interrupt-controller;
				#interrupt-cells = <1>;

				tc3589x_gpio: tc3589x_gpio {
					compatible = "tc3589x-gpio";
					interrupts = <0>;

					interrupt-controller;
					#interrupt-cells = <2>;
					gpio-controller;
					#gpio-cells = <2>;
				};
			};
		};

		spi@80002000 {
			/*
			 * On the first generation boards, this SSP/SPI port was connected
			 * to the AB8500.
			 */
			pinctrl-names = "default";
			pinctrl-0 = <&ssp0_hrefprev60_mode>;
			status = "okay";
		};

		// External Micro SD slot
		mmc@80126000 {
			cd-gpios  = <&tc3589x_gpio 3 GPIO_ACTIVE_HIGH>;
		};

		pinctrl {
			/* Set this up using hogs */
			pinctrl-names = "default";
			pinctrl-0 = <&ipgpio_hrefprev60_mode>;

			ssp0 {
				ssp0_hrefprev60_mode: ssp0_hrefprev60_default {
					hrefprev60_mux {
						function = "ssp0";
						groups = "ssp0_a_1";
					};
					hrefprev60_cfg1 {
						pins = "GPIO145_C13"; /* RXD */
						ste,config = <&in_pd>;
					};

				};
			};
			sdi0 {
				/* This additional pin needed on early MOP500 and HREFs previous to v60 */
				sdi0_default_mode: sdi0_default {
					hrefprev60_mux {
						function = "mc0";
						groups = "mc0dat31dir_a_1";
					};
					hrefprev60_cfg1 {
						pins = "GPIO21_AB3"; /* DAT31DIR */
						ste,config = <&out_hi>;
					};

				};
			};
			tc35892 {
				tc35892_hrefprev60_mode: tc35892_hrefprev60 {
					hrefprev60_cfg {
						pins = "GPIO217_AH12";
						ste,config = <&gpio_in_pu>;
					};
				};
			};
			ipgpio {
				 ipgpio_hrefprev60_mode: ipgpio_hrefprev60 {
					hrefprev60_mux {
						function = "ipgpio";
						groups = "ipgpio0_c_1", "ipgpio1_c_1";
					};
					hrefprev60_cfg1 {
						pins = "GPIO6_AF6", "GPIO7_AG5";
						ste,config = <&in_pu>;
					};
				 };
			};
		};
	};
};
