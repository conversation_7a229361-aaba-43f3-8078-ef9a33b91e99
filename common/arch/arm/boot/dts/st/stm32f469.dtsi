/* SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause) */
/* Copyright (C) STMicroelectronics 2017 - All Rights Reserved */

#include "stm32f429.dtsi"

/ {
	soc {
		dsi: dsi@40016c00 {
			compatible = "st,stm32-dsi";
			reg = <0x40016c00 0x800>;
			resets = <&rcc STM32F4_APB2_RESET(DSI)>;
			reset-names = "apb";
			clocks = <&rcc 1 CLK_F469_DSI>, <&clk_hse>;
			clock-names = "pclk", "ref";
			status = "disabled";
		};
	};
};
