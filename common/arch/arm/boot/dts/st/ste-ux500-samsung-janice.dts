// SPDX-License-Identifier: GPL-2.0-only
/*
 * Devicetree for the Samsung Galaxy S Advance GT-I9070 also known as <PERSON>.
 */

/dts-v1/;
#include "ste-db8500.dtsi"
#include "ste-ab8500.dtsi"
#include "ste-dbx5x0-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/leds/common.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "Samsung Galaxy S Advance (GT-I9070)";
	compatible = "samsung,janice", "st-er<PERSON><PERSON>,u8500";

	chosen {
		stdout-path = &serial2;
	};

	battery: battery {
		compatible = "samsung,eb535151vu";
	};

	thermal-zones {
		battery-thermal {
			/* This zone will be polled by the battery temperature code */
			polling-delay = <0>;
			polling-delay-passive = <0>;
			thermal-sensors = <&bat_therm>;

			trips {
				battery-crit-hi {
					temperature = <70000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};
	};

	bat_therm: thermistor {
		compatible = "samsung,1404-001221";
		io-channels = <&gpadc 0x02>; /* BatTemp */
		pullup-uv = <1800000>;
		pullup-ohm = <230000>;
		pulldown-ohm = <0>;
		#thermal-sensor-cells = <0>;
	};

	/* External LDO for eMMC LDO VMEM_3V3 controlled by GPIO6 */
	ldo_3v3_reg: regulator-gpio-ldo-3v3 {
		compatible = "regulator-fixed";
		/* Supplied in turn by VBAT */
		regulator-name = "VMEM_3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio0 6 GPIO_ACTIVE_HIGH>;
		startup-delay-us = <5000>; // FIXME
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&emmc_ldo_en_default_mode>;
	};

	/*
	 * External Ricoh "TSP" regulator for the touchscreen.
	 * One GPIO line controls two voltages of 3.3V and 1.8V
	 * this line is known as "TSP_LDO_ON1" in the schematics.
	 */
	ldo_tsp_3v3_reg: regulator-gpio-tsp-ldo-3v3 {
		compatible = "regulator-fixed";
		/* Supplied in turn by VBAT */
		regulator-name = "LDO_TSP_A3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		/* GPIO94 controls this regulator */
		gpio = <&gpio2 30 GPIO_ACTIVE_HIGH>;
		/* 70 ms power-on delay */
		startup-delay-us = <70000>;
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&tsp_ldo_en_default_mode>;
	};
	ldo_tsp_1v8_reg: regulator-gpio-tsp-ldo-1v8 {
		compatible = "regulator-fixed";
		/* Supplied in turn by VBAT */
		regulator-name = "VREG_TSP_1V8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		/* GPIO94 controls this regulator */
		gpio = <&gpio2 30 GPIO_ACTIVE_HIGH>;
		/* 70 ms power-on delay */
		startup-delay-us = <70000>;
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&tsp_ldo_en_default_mode>;
	};

	/*
	 * External Ricoh "TSP" regulator for the touchkeys.
	 * Two GPIO lines controls two voltages of 3.3V and 1.8V
	 * TSP_LDO_ON2 controls VREG_TOUCHKEY_1V8
	 * EN_LED_LDO controls VREG_KLED_3V3 (key LED)
	 */
	ldo_kled_3v3_reg: regulator-gpio-vreg-kled-3v3 {
		compatible = "regulator-fixed";
		/* Supplied in turn by VBAT */
		regulator-name = "VREG_KLED_3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		/* GPIO68 controls this regulator */
		gpio = <&gpio2 4 GPIO_ACTIVE_HIGH>;
		/* 70 ms power-on delay */
		startup-delay-us = <70000>;
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&en_led_ldo_default_mode>;
	};
	ldo_touchkey_1v8_reg: regulator-gpio-vreg-touchkey-1v8 {
		compatible = "regulator-fixed";
		/* Supplied in turn by VBAT */
		regulator-name = "VREG_TOUCHKEY_1V8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		/* GPIO89 controls this regulator */
		gpio = <&gpio2 25 GPIO_ACTIVE_HIGH>;
		/* 70 ms power-on delay */
		startup-delay-us = <70000>;
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&tsp_ldo_on2_default_mode>;
	};


	/*
	 * External Ricoh RP152L010B-TR LCD LDO regulator for the display.
	 * LCD_PWR_EN controls a 3.0V and 1.8V output.
	 */
	lcd_3v0_reg: regulator-gpio-lcd-3v0 {
		compatible = "regulator-fixed";
		/* Supplied in turn by VBAT */
		regulator-name = "VREG_LCD_3V0";
		regulator-min-microvolt = <3000000>;
		regulator-max-microvolt = <3000000>;
		/* GPIO219 controls this regulator */
		gpio = <&gpio6 27 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&lcd_pwr_en_default_mode>;
	};
	lcd_1v8_reg: regulator-gpio-lcd-1v8 {
		compatible = "regulator-fixed";
		/* Supplied in turn by VBAT */
		regulator-name = "VREG_LCD_1V8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		/* GPIO219 controls this regulator */
		gpio = <&gpio6 27 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&lcd_pwr_en_default_mode>;
	};

	/*
	 * This regulator is a GPIO line that drives the Broadcom WLAN
	 * line WL_REG_ON high and enables the internal regulators
	 * inside the chip. Unfortunatley it is erroneously named
	 * WLAN_RST_N on the schematic but it is not a reset line.
	 *
	 * The voltage specified here is only used to determine the OCR mask,
	 * the for the SDIO connector, the chip is actually connected
	 * directly to VBAT.
	 */
	wl_reg: regulator-gpio-wlan {
		compatible = "regulator-fixed";
		regulator-name = "WL_REG_ON";
		regulator-min-microvolt = <3000000>;
		regulator-max-microvolt = <3000000>;
		startup-delay-us = <100000>;
		/* GPIO215 (WLAN_RST_N to WL_REG_ON) */
		gpio = <&gpio6 23 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&wlan_ldo_en_default>;
	};


	gpio-keys {
		compatible = "gpio-keys";
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_keys_default_mode>;

		button-home {
			linux,code = <KEY_HOME>;
			label = "HOME";
			/* GPIO91 */
			gpios = <&gpio2 27 GPIO_ACTIVE_LOW>;
		};
		button-volup {
			linux,code = <KEY_VOLUMEUP>;
			label = "VOL+";
			/* GPIO67 */
			gpios = <&gpio2 3 GPIO_ACTIVE_LOW>;
		};
		button-voldown {
			linux,code = <KEY_VOLUMEDOWN>;
			label = "VOL-";
			/* GPIO92 */
			gpios = <&gpio2 28 GPIO_ACTIVE_LOW>;
		};
	};

	/* Richtek RT8515GQW Flash LED Driver IC */
	flash {
		compatible = "richtek,rt8515";
		/* GPIO 140 */
		enf-gpios = <&gpio4 12 GPIO_ACTIVE_HIGH>;
		/* GPIO 141 */
		ent-gpios = <&gpio4 13 GPIO_ACTIVE_HIGH>;
		/*
		 * RFS is 16 kOhm and RTS is 100 kOhm giving
		 * the flash max current 343mA and torch max
		 * current 55 mA.
		 */
		richtek,rfs-ohms = <16000>;
		richtek,rts-ohms = <100000>;
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_flash_default_mode>;

		led {
			function = LED_FUNCTION_FLASH;
			color = <LED_COLOR_ID_WHITE>;
			flash-max-timeout-us = <250000>;
			flash-max-microamp = <343750>;
			led-max-microamp = <55000>;
		};
	};

	/* Bit-banged I2C on GPIO143 and GPIO144 also called "SUBPMU I2C" */
	i2c-gpio-0 {
		compatible = "i2c-gpio";
		sda-gpios = <&gpio4 16 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpio4 15 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c_gpio_0_default>;
		#address-cells = <1>;
		#size-cells = <0>;

		/* Yamaha YAS530 magnetometer */
		magnetometer@2e {
			compatible = "yamaha,yas530";
			reg = <0x2e>;
			/* VDD 3V */
			vdd-supply = <&ab8500_ldo_aux1_reg>;
			/* IOVDD 1.8V */
			iovdd-supply = <&ab8500_ldo_aux2_reg>;
			/* GPIO204 COMPASS_RST_N */
			reset-gpios = <&gpio6 12 GPIO_ACTIVE_LOW>;
			pinctrl-names = "default";
			pinctrl-0 = <&yas529_default>;
		};
		/* TODO: this should also be used by the NCP6914 Camera power management unit */
	};

	/*
	 * These pins do have an spi controller, however the controller on
	 * these pins is not the fully featured PL022 SSP/SPI block but the
	 * ST Micro diet "PL023" version. One of the lacking features in
	 * this derivative is 3wire support, so it cannot be used to drive
	 * this panel interface. We have to use GPIO bit-banging instead.
	 */
	spi {
		compatible = "spi-gpio";
		/* Clock on GPIO220 */
		sck-gpios = <&gpio6 28 GPIO_ACTIVE_HIGH>;
		/* MISO/MOSI on GPIO224 (no separate MISO pin) */
		mosi-gpios = <&gpio7 0 GPIO_ACTIVE_HIGH>;
		/* Chip select on GPIO223 */
		cs-gpios = <&gpio6 31 GPIO_ACTIVE_LOW>;
		num-chipselects = <1>;

		pinctrl-names = "default";
		pinctrl-0 = <&spi_gpio_0_default>;
		#address-cells = <1>;
		#size-cells = <0>;

		panel@0 {
			compatible = "samsung,s6e63m0";
			reg = <0>;
			vdd3-supply = <&lcd_3v0_reg>;
			vci-supply = <&lcd_1v8_reg>;
			/* Reset on GPIO139 */
			reset-gpios = <&gpio4 11 GPIO_ACTIVE_LOW>;
			pinctrl-names = "default";
			pinctrl-0 = <&panel_default_mode>;
			spi-3wire;
			/* TYPE 3: inverse clock polarity and phase */
			spi-cpha;
			spi-cpol;

			port {
				panel_in: endpoint {
					remote-endpoint = <&display_out>;
				};
			};
		};
	};

	/*
	 * Current sense amplifier on the light sensor to convert current to
	 * voltage. We do not know if this is the actual configuration. The
	 * sense resistor value was found by calibrating in a room ambient
	 * light with a second mobile phone light sensor as reference. If you
	 * pry a Janice phone apart and inspect it you may figure this out.
	 */
	gp2a_shunt: current-sense-shunt {
		compatible = "current-sense-shunt";
		io-channels = <&gpadc 0x07>;
		shunt-resistor-micro-ohms = <15000000>; /* 15 ohms c:a */
		#io-channel-cells = <0>;
		io-channel-ranges;
	};

	/* Bit-banged I2C on GPIO196 and GPIO197 also called "TOUCHKEY_I2C" */
	i2c-gpio-1 {
		compatible = "i2c-gpio";
		sda-gpios = <&gpio6 5 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpio6 4 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		clock-frequency = <400000>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c_gpio_1_default>;
		#address-cells = <1>;
		#size-cells = <0>;

		touchkey@20 {
			compatible = "coreriver,tc360-touchkey";
			reg = <0x20>;
			vdd-supply = <&ldo_kled_3v3_reg>;
			vcc-supply = <&ldo_touchkey_1v8_reg>;
			vddio-supply = <&ldo_touchkey_1v8_reg>;

			/* Interrupt on GPIO 198 */
			interrupt-parent = <&gpio6>;
			interrupts = <6 IRQ_TYPE_EDGE_RISING>;

			pinctrl-names = "default";
			pinctrl-0 = <&touchkey_default_mode>;
			linux,keycodes = <KEY_MENU KEY_BACK>;
		};
	};

	/* Bit-banged I2C on GPIO201 and GPIO202 also called "MOT_I2C" */
	i2c-gpio-2 {
		compatible = "i2c-gpio";
		sda-gpios = <&gpio6 10 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpio6 9 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c_gpio_2_default>;
		#address-cells = <1>;
		#size-cells = <0>;
		/* TODO: add the Immersion ISA1200 I2C device here */
	};

	/* Bit-banged I2C on GPIO151 and GPIO152 also called "NFC_I2C" */
	i2c-gpio-3 {
		compatible = "i2c-gpio";
		sda-gpios = <&gpio4 24 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpio4 23 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c_gpio_3_default>;
		#address-cells = <1>;
		#size-cells = <0>;

		/* This is only mounted on the GT-I9070P */
		nfc@2b { /* 0x30? */
			/* NXP NFC circuit PN544 C1 marked NXP 44501  */
			compatible = "nxp,pn544-i2c";
			/* IF0, IF1 high, gives I2C address 0x2B */
			reg = <0x2b>;
			clock-frequency = <400000>;
			/* NFC IRQ on GPIO32 */
			interrupt-parent = <&gpio1>;
			interrupts = <0 IRQ_TYPE_EDGE_FALLING>;
			/* GPIO 31 */
			firmware-gpios = <&gpio0 31 GPIO_ACTIVE_HIGH>;
			/* GPIO88 */
			enable-gpios = <&gpio2 24 GPIO_ACTIVE_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pn544_janice_default>;
		};
	};

	soc {
		/* External Micro SD slot */
		mmc@80126000 {
			arm,primecell-periphid = <0x10480180>;
			max-frequency = <50000000>;
			bus-width = <4>;
			cap-sd-highspeed;
			cap-mmc-highspeed;
			st,sig-dir-cmd;
			st,sig-dir-dat0;
			st,sig-dir-dat2;
			st,sig-pin-fbclk;
			full-pwr-cycle;
			/* MMC is powered by AUX3 1.2V .. 2.91V */
			vmmc-supply = <&ab8500_ldo_aux3_reg>;
			/* 2.9 V level translator is using AUX3 at 2.9 V as well */
			vqmmc-supply = <&ab8500_ldo_aux3_reg>;
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&mc0_a_2_default>;
			pinctrl-1 = <&mc0_a_2_sleep>;
			cd-gpios  = <&gpio6 25 GPIO_ACTIVE_LOW>; // GPIO217
			status = "okay";
		};

		/* WLAN SDIO channel */
		mmc@80118000 {
			arm,primecell-periphid = <0x10480180>;
			max-frequency = <50000000>;
			bus-width = <4>;
			non-removable;
			cap-sd-highspeed;
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&mc1_a_2_default>;
			pinctrl-1 = <&mc1_a_2_sleep>;
			/*
			 * GPIO-controlled voltage enablement: this drives
			 * the WL_REG_ON line high when we use this device.
			 * Represented as regulator to fill OCR mask.
			 */
			vmmc-supply = <&wl_reg>;

			#address-cells = <1>;
			#size-cells = <0>;
			status = "okay";

			wifi@1 {
				compatible = "brcm,bcm4330-fmac", "brcm,bcm4329-fmac";
				reg = <1>;
				/* GPIO216 WL_HOST_WAKE */
				interrupt-parent = <&gpio6>;
				interrupts = <24 IRQ_TYPE_EDGE_FALLING>;
				interrupt-names = "host-wake";
				pinctrl-names = "default";
				pinctrl-0 = <&wlan_default_mode>;
			};
		};

		/* eMMC */
		mmc@80005000 {
			arm,primecell-periphid = <0x10480180>;
		        max-frequency = <50000000>;
			bus-width = <8>;
			non-removable;
			cap-mmc-highspeed;
			mmc-ddr-1_8v;
			no-sdio;
			no-sd;
			vmmc-supply = <&ldo_3v3_reg>;
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&mc2_a_1_default>;
			pinctrl-1 = <&mc2_a_1_sleep>;
			status = "okay";
		};

		/* GBF (Bluetooth) UART */
		serial@80120000 {
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&u0_a_1_default>;
			pinctrl-1 = <&u0_a_1_sleep>;
			status = "okay";

			bluetooth {
				/* BCM4330B1 actually */
				compatible = "brcm,bcm4330-bt";
				/* GPIO222 rail BT_VREG_EN to BT_REG_ON */
				shutdown-gpios = <&gpio6 30 GPIO_ACTIVE_HIGH>;
				/* BT_WAKE on GPIO199 */
				device-wakeup-gpios = <&gpio6 7 GPIO_ACTIVE_HIGH>;
				/* BT_HOST_WAKE on GPIO97 */
				/* FIXME: convert to interrupt */
				host-wakeup-gpios = <&gpio3 1 GPIO_ACTIVE_HIGH>;
				/* BT_RST_N on GPIO209 */
				reset-gpios = <&gpio6 17 GPIO_ACTIVE_LOW>;
				pinctrl-names = "default";
				pinctrl-0 = <&bluetooth_default_mode>;
			};
		};

		/* GPS UART */
		serial@80121000 {
			status = "okay";
			pinctrl-names = "default", "sleep";
			/* CTS/RTS is not used, CTS is repurposed as GPIO */
			pinctrl-0 = <&u1rxtx_a_1_default>;
			pinctrl-1 = <&u1rxtx_a_1_sleep>;

			gnss {
				/*
				 * The Low Noise Amplifier (LNA) power and enablement is controlled
				 * autonomously by the GSD4t.
				 * Janice has a SiRFstarIV-based GSD4t
				 * Golden has a SiRFstarV 5t-based CSRG05TA03-ICJE-R.
				 */
				compatible = "csr,gsd4t";
				/* GPS_RSTN on GPIO21 */
				reset-gpios = <&gpio0 21 GPIO_ACTIVE_LOW>;
				/* GPS_ON_OFF on GPIO96 */
				sirf,onoff-gpios = <&gpio3 0 GPIO_ACTIVE_HIGH>;
				/* GPS_1V8 (VSMPS2) */
				vcc-supply = <&db8500_vsmps2_reg>;
				pinctrl-names = "default";
				pinctrl-0 = <&gsd4t_janice_default>;
				/* According to /etc/sirfgps.conf */
				current-speed = <460800>;
			};
		};

		/* Debugging console UART connected to TSU6111RSVR (FSA880) */
		serial@80007000 {
			status = "okay";
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&u2rxtx_c_1_default>;
			pinctrl-1 = <&u2rxtx_c_1_sleep>;
		};

		prcmu@80157000 {
			ab8500 {
				phy {
					pinctrl-names = "default", "sleep";
					pinctrl-0 = <&usb_a_1_default>;
					pinctrl-1 = <&usb_a_1_sleep>;
				};

				ab8500_fg {
					line-impedance-micro-ohms = <15000>;
				};

				regulator {
					ab8500_ldo_aux1 {
						/* Used for VDD for sensors */
						regulator-name = "V-SENSORS-VDD";
						regulator-min-microvolt = <3000000>;
						regulator-max-microvolt = <3000000>;
					};

					ab8500_ldo_aux2 {
						/* Used for VIO for sensors */
						regulator-name = "V-SENSORS-VIO";
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <1800000>;
					};

					ab8500_ldo_aux3 {
						/* Used for voltage for external MMC/SD card */
						regulator-name = "V-MMC-SD";
						regulator-min-microvolt = <1200000>;
						regulator-max-microvolt = <2910000>;
					};
				};
			};
		};

		/* I2C0 */
		i2c@80004000 {
			status = "okay";
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&i2c0_a_1_default>;
			pinctrl-1 = <&i2c0_a_1_sleep>;

			proximity@44 {
				/* Janice has the GP2AP002A00F with light sensor */
				compatible = "sharp,gp2ap002a00f";
				clock-frequency = <400000>;
				reg = <0x44>;

				interrupt-parent = <&gpio4>;
				interrupts = <18 IRQ_TYPE_EDGE_FALLING>;
				vdd-supply = <&ab8500_ldo_aux1_reg>;
				vio-supply = <&ab8500_ldo_aux2_reg>;
				/* ADC channel AUX2 to read ALSOUT ambient light sensor out */
				io-channels = <&gp2a_shunt>;
				io-channel-names = "alsout";
				pinctrl-names = "default";
				pinctrl-0 = <&gp2ap002_janice_default>;
				/* B1 mode (arch/arm/mach-ux500/include/mach/gp2a.h) */
				sharp,proximity-far-hysteresis = /bits/ 8 <0x40>;
				sharp,proximity-close-hysteresis = /bits/ 8 <0x0f>;
			};
		};

		/* I2C1 on GPIO16 and GPIO17 also called "MUS I2C" */
		i2c@80122000 {
			status = "okay";
			pinctrl-names = "default","sleep";
			pinctrl-0 = <&i2c1_b_2_default>;
			pinctrl-1 = <&i2c1_b_2_sleep>;

			/* Texas Instruments TSU6111 micro USB switch */
			usb-switch@25 {
				compatible = "ti,tsu6111";
				reg = <0x25>;
				/* Interrupt JACK_INT_N on GPIO95 */
				interrupt-parent = <&gpio2>;
				interrupts = <31 IRQ_TYPE_EDGE_FALLING>;
				pinctrl-names = "default";
				pinctrl-0 = <&tsu6111_janice_default>;
			};
		};

		/* I2C2 on GPIO10 and GPIO11 also called "SENSORS I2C" */
		i2c@80128000 {
			status = "okay";
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&i2c2_b_2_default>;
			pinctrl-1 = <&i2c2_b_2_sleep>;

			gyroscope@68 {
				compatible = "invensense,mpu3050";
				reg = <0x68>;
				/* GPIO226 interrupt */
				interrupt-parent = <&gpio7>;
				interrupts = <2 IRQ_TYPE_EDGE_FALLING>;
				/* FIXME: no idea about this */
				mount-matrix = "1", "0", "0",
					       "0", "1", "0",
					       "0", "0", "1";
				vlogic-supply = <&ab8500_ldo_aux2_reg>; // 1.8V
				vdd-supply = <&ab8500_ldo_aux1_reg>; // 3V
				pinctrl-names = "default";
				pinctrl-0 = <&mpu3050_janice_default>;

				/*
				 * The MPU-3050 acts as a hub for the
				 * accelerometer.
				 */
				i2c-gate {
					#address-cells = <1>;
					#size-cells = <0>;

					/* Bosch BMA222 accelerometer */
					accelerometer@8 {
						compatible = "bosch,bma222";
						reg = <0x08>;
						mount-matrix = "0", "-1", "0",
							       "1", "0", "0",
							       "0", "0", "1";
						vddio-supply = <&ab8500_ldo_aux2_reg>; // 1.8V
						vdd-supply = <&ab8500_ldo_aux1_reg>; // 3V
					};
				};
			};
		};

		/* I2C3 */
		i2c@80110000 {
			status = "okay";

			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&i2c3_c_2_default>;
			pinctrl-1 = <&i2c3_c_2_sleep>;

			/* Atmel mXT224E touchscreen */
			touchscreen@4a {
				compatible = "atmel,maxtouch";
				reg = <0x4a>;
				/* GPIO218 (TSP_INT_1V8) */
				interrupt-parent = <&gpio6>;
				interrupts = <26 IRQ_TYPE_EDGE_FALLING>;
				/* VDDA is "analog supply", 2.57-3.47 V */
				vdda-supply = <&ldo_tsp_3v3_reg>;
				/* VDD is "digital supply" 1.71-3.47V */
				vdd-supply = <&ldo_tsp_1v8_reg>;
				pinctrl-names = "default";
				pinctrl-0 = <&tsp_default>;
			};
		};

		mcde@a0350000 {
			status = "okay";
			pinctrl-names = "default";
			pinctrl-0 = <&dpi_default_mode>;

			port {
				display_out: endpoint {
					remote-endpoint = <&panel_in>;
				};
			};
		};
	};
};

&pinctrl {
	/*
	 * This extends the MC0_A_2 default config to include
	 * the card detect GPIO217 line.
	 */
	sdi0 {
		mc0_a_2_default {
			default_cfg4 {
				pins = "GPIO217_AH12"; /* card detect */
				ste,config = <&gpio_in_pd>;
			};
		};
	};
	mcde {
		dpi_default_mode: dpi_default {
			default_mux1 {
				/* Mux in all the data lines */
				function = "lcd";
				groups =
					/* Data lines D0-D7 GPIO70..GPIO77 */
					"lcd_d0_d7_a_1",
					/* Data lines D8-D11 GPIO78..GPIO81 */
					"lcd_d8_d11_a_1",
					/* Data lines D12-D15 GPIO82..GPIO85 */
					"lcd_d12_d15_a_1",
					/* Data lines D16-D23 GPIO161..GPIO168 */
					"lcd_d16_d23_b_1";
			};
			default_mux2 {
				function = "lcda";
				/* Clock line on GPIO150, DE, VSO, HSO on GPIO169..GPIO171 */
				groups = "lcdaclk_b_1", "lcda_b_1";
			};
			/* Input, no pull-up is the default state for pins used for an alt function */
			default_cfg1 {
				pins = "GPIO150_C14", "GPIO169_D22", "GPIO170_C23", "GPIO171_D23";
				ste,config = <&in_nopull>;
			};
		};
	};
	/* GPIO for panel reset control */
	panel {
		panel_default_mode: panel_default {
			janice_cfg1 {
				/* Reset line */
				pins = "GPIO139_C9";
				ste,config = <&gpio_out_lo>;
			};
		};
	};
	/* GPIO that enables the LDO regulator for the eMMC */
	emmc-ldo {
		emmc_ldo_en_default_mode: emmc_ldo_default {
			/* LDO enable on GPIO6 */
			janice_cfg1 {
				pins = "GPIO6_AF6";
				ste,config = <&gpio_out_hi>;
			};
		};
	};
	/* GPIO that enables the LDO regulator for the touchscreen */
	tsp-ldo {
		tsp_ldo_en_default_mode: tsp_ldo_default {
			/* LDO enable on GPIO94 */
			janice_cfg1 {
				pins = "GPIO94_D7";
				ste,config = <&gpio_out_hi>;
			};
		};
	};
	/* GPIO that enables the LDO regulator for the key LED */
	key-led {
		en_led_ldo_default_mode: en_led_ldo_default {
			/* EN_LED_LDO on GPIO68 */
			janice_cfg1 {
				pins = "GPIO68_E1";
				ste,config = <&gpio_out_hi>;
			};
		};
	};
	/* GPIO that enables the LDO regulator for the touchkeys */
	touchkey-ldo {
		tsp_ldo_on2_default_mode: tsp_ldo_on2_default {
			/* TSP_LDO_ON2 on GPIO89 */
			janice_cfg1 {
				pins = "GPIO89_E6";
				ste,config = <&gpio_out_lo>;
			};
		};
	};
	touchkey {
		touchkey_default_mode: touchkey_default {
			janice_cfg1 {
				/* Interrupt */
				pins = "GPIO198_AG25";
				ste,config = <&gpio_in_nopull>;
			};
			janice_cfg2 {
				/* Reset, actually completely unused (not routed) */
				pins = "GPIO205_AG23";
				ste,config = <&gpio_in_pd>;
			};
		};
	};
	/* GPIO that enabled the LDO regulator for the LCD display */
	lcd-ldo {
		lcd_pwr_en_default_mode: lcd_pwr_en_default {
			/* LCD_PWR_EN on GPIO219 */
			janice_cfg1 {
				pins = "GPIO219_AG10";
				ste,config = <&gpio_out_hi>;
			};
		};
	};
	/* GPIO that enables the WLAN internal LDO regulators */
	wlan-ldo {
		wlan_ldo_en_default: wlan_ldo_default {
			/* GPIO215 named WLAN_RST_N */
			janice_cfg1 {
				pins = "GPIO215_AH13";
				ste,config = <&gpio_out_lo>;
			};
		};
	};
	/* Flash and torch */
	flash {
		gpio_flash_default_mode: flash_default {
			janice_cfg1 {
				pins = "GPIO140_B11", "GPIO141_C12";
				ste,config = <&gpio_out_lo>;
			};
		};
	};
	/* GPIO keys */
	gpio-keys {
		gpio_keys_default_mode: gpio_keys_default {
			skomer_cfg1 {
				pins = "GPIO67_G2", /* VOL UP */
				       "GPIO91_B6", /* HOME */
				       "GPIO92_D6"; /* VOL DOWN */
				ste,config = <&gpio_in_pu>;
			};
		};
	};
	/* Interrupt line for the Atmel MXT228 touchscreen */
	tsp {
		tsp_default: tsp_default {
			janice_cfg1 {
				pins = "GPIO218_AH11";	/* TSP_INT_1V8 */
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	/* Reset line for the Yamaha YAS529 magnetometer */
	yas529 {
		yas529_default: yas529_janice {
			janice_cfg1 {
				pins = "GPIO204_AF23";
				ste,config = <&gpio_out_hi>;
			};
		};
	};
	/* Interrupt line for light/proximity sensor GP2AP002 */
	gp2ap002 {
		gp2ap002_janice_default: gp2ap002_janice {
			janice_cfg1 {
				pins = "GPIO146_D13";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	/* Interrupt line for Invensense MPU3050 gyroscope */
	mpu3050 {
		mpu3050_janice_default: mpu3050_janice {
			janice_cfg1 {
				/* GPIO226 used for IRQ */
				pins = "GPIO226_AF8";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	/* GPIO-based I2C bus for magnetometer and NCP6914 */
	i2c-gpio-0 {
		i2c_gpio_0_default: i2c_gpio_0 {
			janice_cfg1 {
				pins = "GPIO143_D12", "GPIO144_B13";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	/* GPIO-based I2C bus for the Cypress touchkeys */
	i2c-gpio-1 {
		i2c_gpio_1_default: i2c_gpio_1 {
			janice_cfg1 {
				pins = "GPIO196_AG26", "GPIO197_AH24";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	/* GPIO-based I2C bus for the Immersion ISA1200 */
	i2c-gpio-2 {
		i2c_gpio_2_default: i2c_gpio_2 {
			janice_cfg1 {
				pins = "GPIO201_AF24", "GPIO202_AF25";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	/* GPIO-based I2C bus for the NFC */
	i2c-gpio-3 {
		i2c_gpio_3_default: i2c_gpio_3 {
			janice_cfg1 {
				pins = "GPIO151_D17", "GPIO152_D16";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	/* GPIO-based SPI bus for the display */
	spi-gpio-0 {
		spi_gpio_0_default: spi_gpio_0 {
			janice_cfg1 {
				pins = "GPIO220_AH10", "GPIO223_AH9", "GPIO224_AG9";
				ste,config = <&gpio_out_hi>;
			};
			/* This pin is unused but belongs with this SPI block */
			janice_cfg2 {
				pins = "GPIO225_AG8";
				ste,config = <&in_pd>;
			};
		};
	};
	wlan {
		wlan_default_mode: wlan_default {
			/* GPIO216 for WL_HOST_WAKE */
			janice_cfg2 {
				pins = "GPIO216_AG12";
				ste,config = <&gpio_in_pd>;
			};
		};
	};
	bluetooth {
		bluetooth_default_mode: bluetooth_default {
			/* GPIO199 BT_WAKE and GPIO222 BT_VREG_ON */
			janice_cfg1 {
				pins = "GPIO199_AH23", "GPIO222_AJ9";
				ste,config = <&gpio_out_lo>;
			};
			/* GPIO97 BT_HOST_WAKE */
			janice_cfg2 {
				pins = "GPIO97_D9";
				ste,config = <&gpio_in_nopull>;
			};
			/* GPIO209 BT_RST_N */
			janice_cfg3 {
				pins = "GPIO209_AG15";
				ste,config = <&gpio_out_hi>;
			};
		};
	};
	/* Interrupt line for TI TSU6111 Micro USB switch */
	tsu6111 {
		tsu6111_janice_default: tsu6111_janice {
			janice_cfg1 {
				/* GPIO95 used for IRQ */
				pins = "GPIO95_E8";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	nfc {
		pn544_janice_default: pn544_janice {
			/* Interrupt line */
			janice_cfg1 {
				pins = "GPIO32_V2";
				ste,config = <&gpio_in_nopull>;
			};
			/* Enable and firmware GPIOs */
			janice_cfg2 {
				pins = "GPIO31_V3", "GPIO88_C4";
				ste,config = <&gpio_out_lo>;
			};
		};
	};
	gsd4t {
		gsd4t_janice_default: gsd4t_janice {
			/* Reset line, start out asserted */
			janice_cfg1 {
				pins = "GPIO21_AB3";
				ste,config = <&gpio_out_lo>;
			};
			/* GPS_ON_OFF, start out deasserted (off) */
			janice_cfg2 {
				pins = "GPIO96_D8";
				ste,config = <&gpio_out_lo>;
			};
			/* Unused power enablement line, used in R0.0 and R0.1 boards */
			janice_cfg3 {
				pins = "GPIO86_C6";
				ste,config = <&gpio_in_pd>;
			};
		};
	};
};
