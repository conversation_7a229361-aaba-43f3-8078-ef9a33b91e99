// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 STMicroelectronics Limited.
 * Author: <PERSON> <<EMAIL>>
 */
#include "stih407-pinctrl.dtsi"
#include <dt-bindings/mfd/st-lpc.h>
#include <dt-bindings/phy/phy.h>
#include <dt-bindings/reset/stih407-resets.h>
#include <dt-bindings/interrupt-controller/irq-st.h>
/ {
	#address-cells = <1>;
	#size-cells = <1>;

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		gp0_reserved: rproc@45000000 {
			compatible = "shared-dma-pool";
			reg = <0x45000000 0x00400000>;
			no-map;
		};

		delta_reserved: rproc@44000000 {
			compatible = "shared-dma-pool";
			reg = <0x44000000 0x01000000>;
			no-map;
		};
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0>;

			/* u-boot puts hpen in SBC dmem at 0xa4 offset */
			cpu-release-addr = <0x94100A4>;

					 /* kHz     uV   */
			operating-points = <1500000 0
					    1200000 0
					    800000  0
					    500000  0>;

			clocks = <&clk_m_a9>;
			clock-names = "cpu";
			clock-latency = <100000>;
			cpu0-supply = <&pwm_regulator>;
			st,syscfg = <&syscfg_core 0x8e0>;
		};
		cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <1>;

			/* u-boot puts hpen in SBC dmem at 0xa4 offset */
			cpu-release-addr = <0x94100A4>;

					 /* kHz     uV   */
			operating-points = <1500000 0
					    1200000 0
					    800000  0
					    500000  0>;
		};
	};

	intc: interrupt-controller@8761000 {
		compatible = "arm,cortex-a9-gic";
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0x08761000 0x1000>, <0x08760100 0x100>;
	};

	scu@8760000 {
		compatible = "arm,cortex-a9-scu";
		reg = <0x08760000 0x1000>;
	};

	timer@8760200 {
		interrupt-parent = <&intc>;
		compatible = "arm,cortex-a9-global-timer";
		reg = <0x08760200 0x100>;
		interrupts = <GIC_PPI 11 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&arm_periph_clk>;
	};

	l2: cache-controller@8762000 {
		compatible = "arm,pl310-cache";
		reg = <0x08762000 0x1000>;
		arm,data-latency = <3 3 3>;
		arm,tag-latency = <2 2 2>;
		cache-unified;
		cache-level = <2>;
	};

	arm-pmu {
		interrupt-parent = <&intc>;
		compatible = "arm,cortex-a9-pmu";
		interrupts = <GIC_PPI 15 IRQ_TYPE_LEVEL_HIGH>;
	};

	pwm_regulator: pwm-regulator {
		compatible = "pwm-regulator";
		pwms = <&pwm1 3 8448>;
		regulator-name = "CPU_1V0_AVS";
		regulator-min-microvolt = <784000>;
		regulator-max-microvolt = <1299000>;
		regulator-always-on;
		max-duty-cycle = <255>;
		status = "okay";
	};

	restart: restart-controller {
		compatible = "st,stih407-restart";
		st,syscfg = <&syscfg_sbc_reg>;
		status = "okay";
	};

	powerdown: powerdown-controller {
		compatible = "st,stih407-powerdown";
		#reset-cells = <1>;
	};

	softreset: softreset-controller {
		compatible = "st,stih407-softreset";
		#reset-cells = <1>;
	};

	picophyreset: picophyreset-controller {
		compatible = "st,stih407-picophyreset";
		#reset-cells = <1>;
	};

	irq-syscfg {
		compatible = "st,stih407-irq-syscfg";
		st,syscfg = <&syscfg_core>;
		st,irq-device = <ST_IRQ_SYSCFG_PMU_0>,
				<ST_IRQ_SYSCFG_PMU_1>;
		st,fiq-device = <ST_IRQ_SYSCFG_DISABLED>,
				<ST_IRQ_SYSCFG_DISABLED>;
	};

	usb2_picophy0: phy1 {
		compatible = "st,stih407-usb2-phy";
		#phy-cells = <0>;
		st,syscfg = <&syscfg_core 0x100 0xf4>;
		resets = <&softreset STIH407_PICOPHY_SOFTRESET>,
			 <&picophyreset STIH407_PICOPHY2_RESET>;
		reset-names = "global", "port";
	};

	miphy28lp_phy: miphy28lp {
		compatible = "st,miphy28lp-phy";
		st,syscfg = <&syscfg_core>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		phy_port0: port@9b22000 {
			reg = <0x9b22000 0xff>,
			      <0x9b09000 0xff>,
			      <0x9b04000 0xff>;
			reg-names = "sata-up",
				    "pcie-up",
				    "pipew";

			st,syscfg = <0x114 0x818 0xe0 0xec>;
			#phy-cells = <1>;

			reset-names = "miphy-sw-rst";
			resets = <&softreset STIH407_MIPHY0_SOFTRESET>;
		};

		phy_port1: port@9b2a000 {
			reg = <0x9b2a000 0xff>,
			      <0x9b19000 0xff>,
			      <0x9b14000 0xff>;
			reg-names = "sata-up",
				    "pcie-up",
				    "pipew";

			st,syscfg = <0x118 0x81c 0xe4 0xf0>;

			#phy-cells = <1>;

			reset-names = "miphy-sw-rst";
			resets = <&softreset STIH407_MIPHY1_SOFTRESET>;
		};

		phy_port2: port@8f95000 {
			reg = <0x8f95000 0xff>,
			      <0x8f90000 0xff>;
			reg-names = "pipew",
				    "usb3-up";

			st,syscfg = <0x11c 0x820>;

			#phy-cells = <1>;

			reset-names = "miphy-sw-rst";
			resets = <&softreset STIH407_MIPHY2_SOFTRESET>;
		};
	};

	st231_gp0: st231-gp0 {
		compatible = "st,st231-rproc";
		memory-region = <&gp0_reserved>;
		resets = <&softreset STIH407_ST231_GP0_SOFTRESET>;
		reset-names = "sw_reset";
		clocks = <&clk_s_c0_flexgen CLK_ST231_GP_0>;
		clock-frequency = <600000000>;
		st,syscfg = <&syscfg_core 0x22c>;
		#mbox-cells = <1>;
		mbox-names = "vq0_rx", "vq0_tx", "vq1_rx", "vq1_tx";
		mboxes = <&mailbox0 0 2>, <&mailbox2 0 1>, <&mailbox0 0 3>, <&mailbox2 0 0>;
	};

	st231_delta: st231-delta {
		compatible = "st,st231-rproc";
		memory-region = <&delta_reserved>;
		resets = <&softreset STIH407_ST231_DMU_SOFTRESET>;
		reset-names = "sw_reset";
		clocks = <&clk_s_c0_flexgen CLK_ST231_DMU>;
		clock-frequency = <600000000>;
		st,syscfg = <&syscfg_core 0x224>;
		#mbox-cells = <1>;
		mbox-names = "vq0_rx", "vq0_tx", "vq1_rx", "vq1_tx";
		mboxes = <&mailbox0 0 0>, <&mailbox3 0 1>, <&mailbox0 0 1>, <&mailbox3 0 0>;
	};

	delta0 {
		compatible = "st,st-delta";
		clock-names = "delta",
			      "delta-st231",
			      "delta-flash-promip";
		clocks = <&clk_s_c0_flexgen CLK_VID_DMU>,
			 <&clk_s_c0_flexgen CLK_ST231_DMU>,
			 <&clk_s_c0_flexgen CLK_FLASH_PROMIP>;
	};

	soc {
		#address-cells = <1>;
		#size-cells = <1>;
		interrupt-parent = <&intc>;
		ranges;
		compatible = "simple-bus";

		syscfg_sbc: sbc-syscfg@9620000 {
			compatible = "st,stih407-sbc-syscfg", "syscon";
			reg = <0x9620000 0x1000>;
		};

		syscfg_front: front-syscfg@9280000 {
			compatible = "st,stih407-front-syscfg", "syscon";
			reg = <0x9280000 0x1000>;
		};

		syscfg_rear: rear-syscfg@9290000 {
			compatible = "st,stih407-rear-syscfg", "syscon";
			reg = <0x9290000 0x1000>;
		};

		syscfg_flash: flash-syscfg@92a0000 {
			compatible = "st,stih407-flash-syscfg", "syscon";
			reg = <0x92a0000 0x1000>;
		};

		syscfg_sbc_reg: fvdp-lite-syscfg@9600000 {
			compatible = "st,stih407-sbc-reg-syscfg", "syscon";
			reg = <0x9600000 0x1000>;
		};

		syscfg_core: core-syscfg@92b0000 {
			compatible = "st,stih407-core-syscfg", "syscon";
			reg = <0x92b0000 0x1000>;

			sti_sasg_codec: sti-sasg-codec {
				compatible = "st,stih407-sas-codec";
				#sound-dai-cells = <1>;
				status = "disabled";
				st,syscfg = <&syscfg_core>;
			};
		};

		syscfg_lpm: lpm-syscfg@94b5100 {
			compatible = "st,stih407-lpm-syscfg", "syscon";
			reg = <0x94b5100 0x1000>;
		};

		/* Display */
		vtg_main: sti-vtg-main@8d02800 {
			compatible = "st,vtg";
			reg = <0x8d02800 0x200>;
			interrupts = <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>;
		};

		vtg_aux: sti-vtg-aux@8d00200 {
			compatible = "st,vtg";
			reg = <0x8d00200 0x100>;
			interrupts = <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>;
		};

		serial@9830000 {
			compatible = "st,asc";
			reg = <0x9830000 0x2c>;
			interrupts = <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			/* Pinctrl moved out to a per-board configuration */

			status = "disabled";
		};

		serial@9831000 {
			compatible = "st,asc";
			reg = <0x9831000 0x2c>;
			interrupts = <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_serial1>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;

			status = "disabled";
		};

		serial@9832000 {
			compatible = "st,asc";
			reg = <0x9832000 0x2c>;
			interrupts = <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_serial2>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;

			status = "disabled";
		};

		/* SBC_ASC0 - UART10 */
		sbc_serial0: serial@9530000 {
			compatible = "st,asc";
			reg = <0x9530000 0x2c>;
			interrupts = <GIC_SPI 138 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_sbc_serial0>;
			clocks = <&clk_sysin>;

			status = "disabled";
		};

		serial@9531000 {
			compatible = "st,asc";
			reg = <0x9531000 0x2c>;
			interrupts = <GIC_SPI 139 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_sbc_serial1>;
			clocks = <&clk_sysin>;

			status = "disabled";
		};

		i2c@9840000 {
			compatible = "st,comms-ssc4-i2c";
			interrupts = <GIC_SPI 112 IRQ_TYPE_LEVEL_HIGH>;
			reg = <0x9840000 0x110>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			clock-names = "ssc";
			clock-frequency = <400000>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c0_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		i2c@9841000 {
			compatible = "st,comms-ssc4-i2c";
			reg = <0x9841000 0x110>;
			interrupts = <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			clock-names = "ssc";
			clock-frequency = <400000>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c1_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		i2c@9842000 {
			compatible = "st,comms-ssc4-i2c";
			reg = <0x9842000 0x110>;
			interrupts = <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			clock-names = "ssc";
			clock-frequency = <400000>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c2_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		i2c@9843000 {
			compatible = "st,comms-ssc4-i2c";
			reg = <0x9843000 0x110>;
			interrupts = <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			clock-names = "ssc";
			clock-frequency = <400000>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c3_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		i2c@9844000 {
			compatible = "st,comms-ssc4-i2c";
			reg = <0x9844000 0x110>;
			interrupts = <GIC_SPI 116 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			clock-names = "ssc";
			clock-frequency = <400000>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c4_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		i2c@9845000 {
			compatible = "st,comms-ssc4-i2c";
			reg = <0x9845000 0x110>;
			interrupts = <GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			clock-names = "ssc";
			clock-frequency = <400000>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c5_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};


		/* SSCs on SBC */
		i2c@9540000 {
			compatible = "st,comms-ssc4-i2c";
			reg = <0x9540000 0x110>;
			interrupts = <GIC_SPI 135 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_sysin>;
			clock-names = "ssc";
			clock-frequency = <400000>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c10_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		i2c@9541000 {
			compatible = "st,comms-ssc4-i2c";
			reg = <0x9541000 0x110>;
			interrupts = <GIC_SPI 136 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_sysin>;
			clock-names = "ssc";
			clock-frequency = <400000>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_i2c11_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		spi@9840000 {
			compatible = "st,comms-ssc4-spi";
			reg = <0x9840000 0x110>;
			interrupts = <GIC_SPI 112 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			clock-names = "ssc";
			pinctrl-0 = <&pinctrl_spi0_default>;
			pinctrl-names = "default";
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		spi@9841000 {
			compatible = "st,comms-ssc4-spi";
			reg = <0x9841000 0x110>;
			interrupts = <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			clock-names = "ssc";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_spi1_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		spi@9842000 {
			compatible = "st,comms-ssc4-spi";
			reg = <0x9842000 0x110>;
			interrupts = <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			clock-names = "ssc";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_spi2_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		spi@9843000 {
			compatible = "st,comms-ssc4-spi";
			reg = <0x9843000 0x110>;
			interrupts = <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			clock-names = "ssc";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_spi3_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		spi@9844000 {
			compatible = "st,comms-ssc4-spi";
			reg = <0x9844000 0x110>;
			interrupts = <GIC_SPI 116 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			clock-names = "ssc";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_spi4_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		/* SBC SSC */
		spi@9540000 {
			compatible = "st,comms-ssc4-spi";
			reg = <0x9540000 0x110>;
			interrupts = <GIC_SPI 135 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_sysin>;
			clock-names = "ssc";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_spi10_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		spi@9541000 {
			compatible = "st,comms-ssc4-spi";
			reg = <0x9541000 0x110>;
			interrupts = <GIC_SPI 136 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_sysin>;
			clock-names = "ssc";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_spi11_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		spi@9542000 {
			compatible = "st,comms-ssc4-spi";
			reg = <0x9542000 0x110>;
			interrupts = <GIC_SPI 137 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_sysin>;
			clock-names = "ssc";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_spi12_default>;
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};

		mmc0: sdhci@9060000 {
			compatible = "st,sdhci-stih407", "st,sdhci";
			status = "disabled";
			reg = <0x09060000 0x7ff>, <0x9061008 0x20>;
			reg-names = "mmc", "top-mmc-delay";
			interrupts = <GIC_SPI 92 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "mmcirq";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_mmc0>;
			clock-names = "mmc", "icn";
			clocks = <&clk_s_c0_flexgen CLK_MMC_0>,
				 <&clk_s_c0_flexgen CLK_RX_ICN_HVA>;
			bus-width = <8>;
		};

		mmc1: sdhci@9080000 {
			compatible = "st,sdhci-stih407", "st,sdhci";
			status = "disabled";
			reg = <0x09080000 0x7ff>;
			reg-names = "mmc";
			interrupts = <GIC_SPI 90 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "mmcirq";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_sd1>;
			clock-names = "mmc", "icn";
			clocks = <&clk_s_c0_flexgen CLK_MMC_1>,
				 <&clk_s_c0_flexgen CLK_RX_ICN_HVA>;
			resets = <&softreset STIH407_MMC1_SOFTRESET>;
			bus-width = <4>;
		};

		/* Watchdog and Real-Time Clock */
		lpc@8787000 {
			compatible = "st,stih407-lpc";
			reg = <0x8787000 0x1000>;
			interrupts = <GIC_SPI 129 IRQ_TYPE_EDGE_RISING>;
			clocks = <&clk_s_d3_flexgen CLK_LPC_0>;
			timeout-sec = <120>;
			st,syscfg = <&syscfg_core>;
			st,lpc-mode = <ST_LPC_MODE_WDT>;
		};

		lpc@8788000 {
			compatible = "st,stih407-lpc";
			reg = <0x8788000 0x1000>;
			interrupts = <GIC_SPI 130 IRQ_TYPE_EDGE_RISING>;
			clocks = <&clk_s_d3_flexgen CLK_LPC_1>;
			st,lpc-mode = <ST_LPC_MODE_CLKSRC>;
		};

		spifsm: spifsm@9022000 {
			compatible = "st,spi-fsm";
			reg = <0x9022000 0x1000>;
			reg-names = "spi-fsm";
			clocks = <&clk_s_c0_flexgen CLK_FLASH_PROMIP>;
			clock-names = "emi_clk";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_fsm>;
			st,syscfg = <&syscfg_core>;
			st,boot-device-reg = <0x8c4>;
			st,boot-device-spi = <0x68>;

			status = "disabled";
		};

		sata0: sata@9b20000 {
			compatible = "st,ahci";
			reg = <0x9b20000 0x1000>;

			interrupts = <GIC_SPI 159 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "hostc";

			phys = <&phy_port0 PHY_TYPE_SATA>;
			phy-names = "ahci_phy";

			resets = <&powerdown STIH407_SATA0_POWERDOWN>,
				 <&softreset STIH407_SATA0_SOFTRESET>,
				 <&softreset STIH407_SATA0_PWR_SOFTRESET>;
			reset-names = "pwr-dwn", "sw-rst", "pwr-rst";

			clock-names = "ahci_clk";
			clocks = <&clk_s_c0_flexgen CLK_ICN_REG>;

			ports-implemented = <0x1>;

			status = "disabled";
		};

		sata1: sata@9b28000 {
			compatible = "st,ahci";
			reg = <0x9b28000 0x1000>;

			interrupts = <GIC_SPI 170 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "hostc";

			phys = <&phy_port1 PHY_TYPE_SATA>;
			phy-names = "ahci_phy";

			resets = <&powerdown STIH407_SATA1_POWERDOWN>,
				 <&softreset STIH407_SATA1_SOFTRESET>,
				 <&softreset STIH407_SATA1_PWR_SOFTRESET>;
			reset-names = "pwr-dwn",
				      "sw-rst",
				      "pwr-rst";

			clock-names = "ahci_clk";
			clocks = <&clk_s_c0_flexgen CLK_ICN_REG>;

			ports-implemented = <0x1>;

			status = "disabled";
		};


		st_dwc3: dwc3@8f94000 {
			compatible = "st,stih407-dwc3";
			reg = <0x08f94000 0x1000>, <0x110 0x4>;
			reg-names = "reg-glue", "syscfg-reg";
			st,syscfg = <&syscfg_core>;
			resets = <&powerdown STIH407_USB3_POWERDOWN>,
				 <&softreset STIH407_MIPHY2_SOFTRESET>;
			reset-names = "powerdown", "softreset";
			#address-cells = <1>;
			#size-cells = <1>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb3>;
			ranges;

			status = "disabled";

			dwc3: usb@9900000 {
				compatible = "snps,dwc3";
				reg = <0x09900000 0x100000>;
				interrupts = <GIC_SPI 155 IRQ_TYPE_LEVEL_HIGH>;
				dr_mode = "host";
				phy-names = "usb2-phy", "usb3-phy";
				phys = <&usb2_picophy0>,
				       <&phy_port2 PHY_TYPE_USB3>;
				snps,dis_u3_susphy_quirk;
			};
		};

		/* COMMS PWM Module */
		pwm0: pwm@9810000 {
			compatible = "st,sti-pwm";
			#pwm-cells = <2>;
			reg = <0x9810000 0x68>;
			interrupts = <GIC_SPI 128 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_pwm0_chan0_default>;
			clock-names = "pwm";
			clocks = <&clk_sysin>;
			st,pwm-num-chan = <1>;

			status = "disabled";
		};

		/* SBC PWM Module */
		pwm1: pwm@9510000 {
			compatible = "st,sti-pwm";
			#pwm-cells = <2>;
			reg = <0x9510000 0x68>;
			interrupts = <GIC_SPI 131 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_pwm1_chan0_default
				     &pinctrl_pwm1_chan1_default
				     &pinctrl_pwm1_chan2_default
				     &pinctrl_pwm1_chan3_default>;
			clock-names = "pwm";
			clocks = <&clk_sysin>;
			st,pwm-num-chan = <4>;

			status = "disabled";
		};

		rng10: rng@8a89000 {
			compatible = "st,rng";
			reg = <0x08a89000 0x1000>;
			clocks = <&clk_sysin>;
			status = "okay";
		};

		rng11: rng@8a8a000 {
			compatible = "st,rng";
			reg = <0x08a8a000 0x1000>;
			clocks = <&clk_sysin>;
			status = "okay";
		};

		ethernet0: dwmac@9630000 {
			device_type = "network";
			status = "disabled";
			compatible = "st,stih407-dwmac", "snps,dwmac", "snps,dwmac-3.710";
			reg = <0x9630000 0x8000>, <0x80 0x4>;
			reg-names = "stmmaceth", "sti-ethconf";

			st,syscon = <&syscfg_sbc_reg 0x80>;
			st,gmac_en;
			resets = <&softreset STIH407_ETH1_SOFTRESET>;
			reset-names = "stmmaceth";

			interrupts = <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 99 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "macirq", "eth_wake_irq";

			/* DMA Bus Mode */
			snps,pbl = <8>;

			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_rgmii1>;

			clock-names = "stmmaceth", "sti-ethclk";
			clocks = <&clk_s_c0_flexgen CLK_EXT2F_A9>,
				 <&clk_s_c0_flexgen CLK_ETH_PHY>;
		};

		mailbox0: mailbox@8f00000  {
			compatible = "st,stih407-mailbox";
			reg = <0x8f00000 0x1000>;
			interrupts = <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
			#mbox-cells = <2>;
			mbox-name = "a9";
			status = "okay";
		};

		mailbox1: mailbox@8f01000 {
			compatible = "st,stih407-mailbox";
			reg = <0x8f01000 0x1000>;
			#mbox-cells = <2>;
			mbox-name = "st231_gp_1";
			status = "okay";
		};

		mailbox2: mailbox@8f02000 {
			compatible = "st,stih407-mailbox";
			reg = <0x8f02000 0x1000>;
			#mbox-cells = <2>;
			mbox-name = "st231_gp_0";
			status = "okay";
		};

		mailbox3: mailbox@8f03000 {
			compatible = "st,stih407-mailbox";
			reg = <0x8f03000 0x1000>;
			#mbox-cells = <2>;
			mbox-name = "st231_audio_video";
			status = "okay";
		};

		/* fdma audio */
		fdma0: dma-controller@8e20000 {
			compatible = "st,stih407-fdma-mpe31-11", "st,slim-rproc";
			reg = <0x8e20000 0x8000>,
			      <0x8e30000 0x3000>,
			      <0x8e37000 0x1000>,
			      <0x8e38000 0x8000>;
			reg-names = "slimcore", "dmem", "peripherals", "imem";
			clocks = <&clk_s_c0_flexgen CLK_FDMA>,
				 <&clk_s_c0_flexgen CLK_EXT2F_A9>,
				 <&clk_s_c0_flexgen CLK_EXT2F_A9>,
				 <&clk_s_c0_flexgen CLK_EXT2F_A9>;
			interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
			dma-channels = <16>;
			#dma-cells = <3>;
		};

		/* fdma app */
		fdma1: dma-controller@8e40000 {
			compatible = "st,stih407-fdma-mpe31-12", "st,slim-rproc";
			reg = <0x8e40000 0x8000>,
			      <0x8e50000 0x3000>,
			      <0x8e57000 0x1000>,
			      <0x8e58000 0x8000>;
			reg-names = "slimcore", "dmem", "peripherals", "imem";
			clocks = <&clk_s_c0_flexgen CLK_FDMA>,
				<&clk_s_c0_flexgen CLK_TX_ICN_DMU>,
				<&clk_s_c0_flexgen CLK_TX_ICN_DMU>,
				<&clk_s_c0_flexgen CLK_EXT2F_A9>;

			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
			dma-channels = <16>;
			#dma-cells = <3>;

			status = "disabled";
		};

		/* fdma free running */
		fdma2: dma-controller@8e60000 {
			compatible = "st,stih407-fdma-mpe31-13", "st,slim-rproc";
			reg = <0x8e60000 0x8000>,
			      <0x8e70000 0x3000>,
			      <0x8e77000 0x1000>,
			      <0x8e78000 0x8000>;
			reg-names = "slimcore", "dmem", "peripherals", "imem";
			interrupts = <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>;
			dma-channels = <16>;
			#dma-cells = <3>;
			clocks = <&clk_s_c0_flexgen CLK_FDMA>,
				<&clk_s_c0_flexgen CLK_EXT2F_A9>,
				<&clk_s_c0_flexgen CLK_TX_ICN_DISP_0>,
				<&clk_s_c0_flexgen CLK_EXT2F_A9>;

			status = "disabled";
		};

		sti_uni_player0: sti-uni-player@8d80000 {
			compatible = "st,stih407-uni-player-hdmi";
			#sound-dai-cells = <0>;
			st,syscfg = <&syscfg_core>;
			clocks = <&clk_s_d0_flexgen CLK_PCM_0>;
			assigned-clocks = <&clk_s_d0_quadfs 0>, <&clk_s_d0_flexgen CLK_PCM_0>;
			assigned-clock-parents = <0>, <&clk_s_d0_quadfs 0>;
			assigned-clock-rates = <50000000>;
			reg = <0x8d80000 0x158>;
			interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&fdma0 2 0 1>;
			dma-names = "tx";

			status = "disabled";
		};

		sti_uni_player1: sti-uni-player@8d81000 {
			compatible = "st,stih407-uni-player-pcm-out";
			#sound-dai-cells = <0>;
			st,syscfg = <&syscfg_core>;
			clocks = <&clk_s_d0_flexgen CLK_PCM_1>;
			assigned-clocks = <&clk_s_d0_quadfs 1>, <&clk_s_d0_flexgen CLK_PCM_1>;
			assigned-clock-parents = <0>, <&clk_s_d0_quadfs 1>;
			assigned-clock-rates = <50000000>;
			reg = <0x8d81000 0x158>;
			interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&fdma0 3 0 1>;
			dma-names = "tx";

			status = "disabled";
		};

		sti_uni_player2: sti-uni-player@8d82000 {
			compatible = "st,stih407-uni-player-dac";
			#sound-dai-cells = <0>;
			st,syscfg = <&syscfg_core>;
			clocks = <&clk_s_d0_flexgen CLK_PCM_2>;
			assigned-clocks = <&clk_s_d0_quadfs 2>, <&clk_s_d0_flexgen CLK_PCM_2>;
			assigned-clock-parents = <0>, <&clk_s_d0_quadfs 2>;
			assigned-clock-rates = <50000000>;
			reg = <0x8d82000 0x158>;
			interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&fdma0 4 0 1>;
			dma-names = "tx";

			status = "disabled";
		};

		sti_uni_player3: sti-uni-player@8d85000 {
			compatible = "st,stih407-uni-player-spdif";
			#sound-dai-cells = <0>;
			st,syscfg = <&syscfg_core>;
			clocks = <&clk_s_d0_flexgen CLK_SPDIFF>;
			assigned-clocks = <&clk_s_d0_quadfs 3>, <&clk_s_d0_flexgen CLK_SPDIFF>;
			assigned-clock-parents = <0>, <&clk_s_d0_quadfs 3>;
			assigned-clock-rates = <50000000>;
			reg = <0x8d85000 0x158>;
			interrupts = <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&fdma0 7 0 1>;
			dma-names = "tx";

			status = "disabled";
		};

		sti_uni_reader0: sti-uni-reader@8d83000 {
			compatible = "st,stih407-uni-reader-pcm_in";
			#sound-dai-cells = <0>;
			st,syscfg = <&syscfg_core>;
			reg = <0x8d83000 0x158>;
			interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&fdma0 5 0 1>;
			dma-names = "rx";

			status = "disabled";
		};

		sti_uni_reader1: sti-uni-reader@8d84000 {
			compatible = "st,stih407-uni-reader-hdmi";
			#sound-dai-cells = <0>;
			st,syscfg = <&syscfg_core>;
			reg = <0x8d84000 0x158>;
			interrupts = <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&fdma0 6 0 1>;
			dma-names = "rx";

			status = "disabled";
		};
	};
};
