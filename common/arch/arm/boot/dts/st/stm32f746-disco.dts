/*
 * Copyright 2017 - <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "stm32f746.dtsi"
#include "stm32f746-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "STMicroelectronics STM32F746-DISCO board";
	compatible = "st,stm32f746-disco", "st,stm32f746";

	chosen {
		bootargs = "root=/dev/ram";
		stdout-path = "serial0:115200n8";
	};

	memory@c0000000 {
		device_type = "memory";
		reg = <0xC0000000 0x800000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		linux,cma {
			compatible = "shared-dma-pool";
			no-map;
			size = <0x80000>;
			linux,dma-default;
		};
	};

	aliases {
		serial0 = &usart1;
	};

	usbotg_hs_phy: usb-phy {
		#phy-cells = <0>;
		compatible = "usb-nop-xceiv";
		clocks = <&rcc 0 STM32F7_AHB1_CLOCK(OTGHSULPI)>;
		clock-names = "main_clk";
	};

	/* This turns on vbus for otg fs for host mode (dwc2) */
	vcc5v_otg_fs: vcc5v-otg-fs-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpiod 5 0>;
		regulator-name = "vcc5_host1";
		regulator-always-on;
	};

	vcc_3v3: vcc-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "vcc_3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	backlight: backlight {
		compatible = "gpio-backlight";
		gpios = <&gpiok 3 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	panel_rgb: panel-rgb {
		compatible = "rocktech,rk043fn48h";
		power-supply = <&vcc_3v3>;
		backlight = <&backlight>;
		enable-gpios = <&gpioi 12 GPIO_ACTIVE_HIGH>;
		status = "okay";
		port {
			panel_in_rgb: endpoint {
				remote-endpoint = <&ltdc_out_rgb>;
			};
		};
	};
};

&clk_hse {
	clock-frequency = <25000000>;
};

&i2c1 {
	pinctrl-0 = <&i2c1_pins_b>;
	pinctrl-names = "default";
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
};

&i2c3 {
	pinctrl-0 = <&i2c3_pins_a>;
	pinctrl-names = "default";
	clock-frequency = <400000>;
	status = "okay";

	touchscreen@38 {
		compatible = "edt,edt-ft5306";
		reg = <0x38>;
		interrupt-parent = <&gpioi>;
		interrupts = <13 IRQ_TYPE_EDGE_FALLING>;
		touchscreen-size-x = <480>;
		touchscreen-size-y = <272>;
	};
};

&ltdc {
	pinctrl-0 = <&ltdc_pins_a>;
	pinctrl-names = "default";
	status = "okay";

	port {
		ltdc_out_rgb: endpoint {
			remote-endpoint = <&panel_in_rgb>;
		};
	};
};

&sdio1 {
	status = "okay";
	vmmc-supply = <&vcc_3v3>;
	cd-gpios = <&gpioc 13 GPIO_ACTIVE_LOW>;
	pinctrl-names = "default", "opendrain";
	pinctrl-0 = <&sdio_pins_a>;
	pinctrl-1 = <&sdio_pins_od_a>;
	bus-width = <4>;
};

&timers5 {
	/* Override timer5 to act as clockevent */
	compatible = "st,stm32-timer";
	interrupts = <50>;
	status = "okay";
	/delete-property/#address-cells;
	/delete-property/#size-cells;
	/delete-property/clock-names;
	/delete-node/pwm;
	/delete-node/timer@4;
};

&usart1 {
	pinctrl-0 = <&usart1_pins_b>;
	pinctrl-names = "default";
	status = "okay";
};

&usbotg_fs {
	dr_mode = "host";
	pinctrl-0 = <&usbotg_fs_pins_a>;
	pinctrl-names = "default";
	status = "okay";
};

&usbotg_hs {
	dr_mode = "host";
	phys = <&usbotg_hs_phy>;
	phy-names = "usb2-phy";
	pinctrl-0 = <&usbotg_hs_pins_b>;
	pinctrl-names = "default";
	status = "okay";
};
