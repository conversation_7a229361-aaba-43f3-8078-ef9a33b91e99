// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2021 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>>
 */
#include <dt-bindings/pinctrl/stm32-pinfunc.h>

&pinctrl {
	adc1_usb_cc_pins_a: adc1-usb-cc-pins-0 {
		pins {
			pinmux = <STM32_PINMUX('F', 12, ANALOG)>, /* ADC1 in6 */
				 <STM32_PINMUX('A', 3, ANALOG)>; /* ADC1 in12 */
		};
	};

	i2c1_pins_a: i2c1-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 12, AF5)>, /* I2C1_SCL */
				 <STM32_PINMUX('E', 8, AF5)>; /* I2C1_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c1_sleep_pins_a: i2c1-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 12, ANALOG)>, /* I2C1_SCL */
				 <STM32_PINMUX('E', 8, ANALOG)>; /* I2C1_SDA */
		};
	};

	i2c5_pins_a: i2c5-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 1, AF4)>, /* I2C5_SCL */
				 <STM32_PINMUX('H', 6, AF4)>; /* I2C5_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	i2c5_sleep_pins_a: i2c5-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 1, ANALOG)>, /* I2C5_SCL */
				 <STM32_PINMUX('H', 6, ANALOG)>; /* I2C5_SDA */
		};
	};

	mcp23017_pins_a: mcp23017-0 {
		pins {
			pinmux = <STM32_PINMUX('G', 12, GPIO)>;
			bias-pull-up;
		};
	};

	pwm3_pins_a: pwm3-0 {
		pins {
			pinmux = <STM32_PINMUX('B', 1, AF2)>; /* TIM3_CH4 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm3_sleep_pins_a: pwm3-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('B', 1, ANALOG)>; /* TIM3_CH4 */
		};
	};

	pwm4_pins_a: pwm4-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 13, AF2)>; /* TIM4_CH2 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm4_sleep_pins_a: pwm4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 13, ANALOG)>; /* TIM4_CH2 */
		};
	};

	pwm8_pins_a: pwm8-0 {
		pins {
			pinmux = <STM32_PINMUX('E', 5, AF3)>; /* TIM8_CH3 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm8_sleep_pins_a: pwm8-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('E', 5, ANALOG)>; /* TIM8_CH3 */
		};
	};

	pwm14_pins_a: pwm14-0 {
		pins {
			pinmux = <STM32_PINMUX('F', 9, AF9)>; /* TIM14_CH1 */
			bias-pull-down;
			drive-push-pull;
			slew-rate = <0>;
		};
	};

	pwm14_sleep_pins_a: pwm14-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('F', 9, ANALOG)>; /* TIM14_CH1 */
		};
	};

	sdmmc1_b4_pins_a: sdmmc1-b4-0 {
		pins {
			pinmux = <STM32_PINMUX('C', 8, AF12)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('C', 9, AF12)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('C', 10, AF12)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('C', 11, AF12)>, /* SDMMC1_D3 */
				 <STM32_PINMUX('D', 2, AF12)>; /* SDMMC1_CMD */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
	};

	sdmmc1_b4_od_pins_a: sdmmc1-b4-od-0 {
		pins1 {
			pinmux = <STM32_PINMUX('C', 8, AF12)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('C', 9, AF12)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('C', 10, AF12)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('C', 11, AF12)>; /* SDMMC1_D3 */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 2, AF12)>; /* SDMMC1_CMD */
			slew-rate = <1>;
			drive-open-drain;
			bias-disable;
		};
	};

	sdmmc1_b4_sleep_pins_a: sdmmc1-b4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('C', 8, ANALOG)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('C', 9, ANALOG)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('C', 10, ANALOG)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('C', 11, ANALOG)>, /* SDMMC1_D3 */
				 <STM32_PINMUX('C', 12, ANALOG)>, /* SDMMC1_CK */
				 <STM32_PINMUX('D', 2, ANALOG)>; /* SDMMC1_CMD */
		};
	};

	sdmmc1_clk_pins_a: sdmmc1-clk-0 {
		pins {
			pinmux = <STM32_PINMUX('C', 12, AF12)>; /* SDMMC1_CK */
			slew-rate = <1>;
			drive-push-pull;
			bias-disable;
		};
	};

	sdmmc2_b4_pins_a: sdmmc2-b4-0 {
		pins {
			pinmux = <STM32_PINMUX('B', 14, AF10)>, /* SDMMC2_D0 */
				 <STM32_PINMUX('B', 15, AF10)>, /* SDMMC2_D1 */
				 <STM32_PINMUX('B', 3, AF10)>, /* SDMMC2_D2 */
				 <STM32_PINMUX('B', 4, AF10)>, /* SDMMC2_D3 */
				 <STM32_PINMUX('G', 6, AF10)>; /* SDMMC2_CMD */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
	};

	sdmmc2_b4_od_pins_a: sdmmc2-b4-od-0 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 14, AF10)>, /* SDMMC2_D0 */
				 <STM32_PINMUX('B', 15, AF10)>, /* SDMMC2_D1 */
				 <STM32_PINMUX('B', 3, AF10)>, /* SDMMC2_D2 */
				 <STM32_PINMUX('B', 4, AF10)>; /* SDMMC2_D3 */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
		pins2 {
			pinmux = <STM32_PINMUX('G', 6, AF10)>; /* SDMMC2_CMD */
			slew-rate = <1>;
			drive-open-drain;
			bias-pull-up;
		};
	};

	sdmmc2_b4_sleep_pins_a: sdmmc2-b4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('B', 14, ANALOG)>, /* SDMMC2_D0 */
				 <STM32_PINMUX('B', 15, ANALOG)>, /* SDMMC2_D1 */
				 <STM32_PINMUX('B', 3, ANALOG)>, /* SDMMC2_D2 */
				 <STM32_PINMUX('B', 4, ANALOG)>, /* SDMMC2_D3 */
				 <STM32_PINMUX('E', 3, ANALOG)>, /* SDMMC2_CK */
				 <STM32_PINMUX('G', 6, ANALOG)>; /* SDMMC2_CMD */
		};
	};

	sdmmc2_clk_pins_a: sdmmc2-clk-0 {
		pins {
			pinmux = <STM32_PINMUX('E', 3, AF10)>; /* SDMMC2_CK */
			slew-rate = <1>;
			drive-push-pull;
			bias-pull-up;
		};
	};

	spi5_pins_a: spi5-0 {
		pins1 {
			pinmux = <STM32_PINMUX('H', 7, AF6)>, /* SPI5_SCK */
				 <STM32_PINMUX('H', 3, AF5)>; /* SPI5_MOSI */
			bias-disable;
			drive-push-pull;
			slew-rate = <1>;
		};

		pins2 {
			pinmux = <STM32_PINMUX('A', 8, AF5)>; /* SPI5_MISO */
			bias-disable;
		};
	};

	spi5_sleep_pins_a: spi5-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 7, ANALOG)>, /* SPI5_SCK */
				 <STM32_PINMUX('A', 8, ANALOG)>, /* SPI5_MISO */
				 <STM32_PINMUX('H', 3, ANALOG)>; /* SPI5_MOSI */
		};
	};

	stm32g0_intn_pins_a: stm32g0-intn-0 {
		pins {
			pinmux = <STM32_PINMUX('I', 2, GPIO)>;
			bias-pull-up;
		};
	};

	uart4_pins_a: uart4-0 {
		pins1 {
			pinmux = <STM32_PINMUX('D', 6, AF8)>; /* UART4_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 8, AF8)>; /* UART4_RX */
			bias-disable;
		};
	};

	uart4_idle_pins_a: uart4-idle-0 {
		pins1 {
			pinmux = <STM32_PINMUX('D', 6, ANALOG)>; /* UART4_TX */
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 8, AF8)>; /* UART4_RX */
			bias-disable;
		};
	};

	uart4_sleep_pins_a: uart4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('D', 6, ANALOG)>, /* UART4_TX */
				 <STM32_PINMUX('D', 8, ANALOG)>; /* UART4_RX */
		};
	};

	uart8_pins_a: uart8-0 {
		pins1 {
			pinmux = <STM32_PINMUX('E', 1, AF8)>; /* UART8_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('F', 9, AF8)>; /* UART8_RX */
			bias-pull-up;
		};
	};

	uart8_idle_pins_a: uart8-idle-0 {
		pins1 {
			pinmux = <STM32_PINMUX('E', 1, ANALOG)>; /* UART8_TX */
		};
		pins2 {
			pinmux = <STM32_PINMUX('F', 9, AF8)>; /* UART8_RX */
			bias-pull-up;
		};
	};

	uart8_sleep_pins_a: uart8-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('E', 1, ANALOG)>, /* UART8_TX */
				 <STM32_PINMUX('F', 9, ANALOG)>; /* UART8_RX */
		};
	};

	usart1_pins_a: usart1-0 {
		pins1 {
			pinmux = <STM32_PINMUX('C', 0, AF7)>, /* USART1_TX */
				 <STM32_PINMUX('C', 2, AF7)>; /* USART1_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 0, AF4)>, /* USART1_RX */
				 <STM32_PINMUX('A', 7, AF7)>; /* USART1_CTS_NSS */
			bias-pull-up;
		};
	};

	usart1_idle_pins_a: usart1-idle-0 {
		pins1 {
			pinmux = <STM32_PINMUX('C', 0, ANALOG)>, /* USART1_TX */
				 <STM32_PINMUX('A', 7, ANALOG)>; /* USART1_CTS_NSS */
		};
		pins2 {
			pinmux = <STM32_PINMUX('C', 2, AF7)>; /* USART1_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins3 {
			pinmux = <STM32_PINMUX('B', 0, AF4)>; /* USART1_RX */
			bias-pull-up;
		};
	};

	usart1_sleep_pins_a: usart1-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('C', 0, ANALOG)>, /* USART1_TX */
				 <STM32_PINMUX('C', 2, ANALOG)>, /* USART1_RTS */
				 <STM32_PINMUX('A', 7, ANALOG)>, /* USART1_CTS_NSS */
				 <STM32_PINMUX('B', 0, ANALOG)>; /* USART1_RX */
		};
	};

	usart2_pins_a: usart2-0 {
		pins1 {
			pinmux = <STM32_PINMUX('H', 12, AF1)>, /* USART2_TX */
				 <STM32_PINMUX('D', 4, AF3)>; /* USART2_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 15, AF1)>, /* USART2_RX */
				 <STM32_PINMUX('E', 11, AF2)>; /* USART2_CTS_NSS */
			bias-disable;
		};
	};

	usart2_idle_pins_a: usart2-idle-0 {
		pins1 {
			pinmux = <STM32_PINMUX('H', 12, ANALOG)>, /* USART2_TX */
				 <STM32_PINMUX('E', 11, ANALOG)>; /* USART2_CTS_NSS */
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 4, AF3)>; /* USART2_RTS */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins3 {
			pinmux = <STM32_PINMUX('D', 15, AF1)>; /* USART2_RX */
			bias-disable;
		};
	};

	usart2_sleep_pins_a: usart2-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 12, ANALOG)>, /* USART2_TX */
				 <STM32_PINMUX('D', 4, ANALOG)>, /* USART2_RTS */
				 <STM32_PINMUX('D', 15, ANALOG)>, /* USART2_RX */
				 <STM32_PINMUX('E', 11, ANALOG)>; /* USART2_CTS_NSS */
		};
	};
};
