// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright 2013 Linaro Ltd.
 */

#include "ste-dbx5x0-pinctrl.dtsi"

/ {
	soc {
		pinctrl {
			/* Settings for all SPI default and sleep states */
			spi2 {
				spi2_default_mode: spi_default {
					default_mux {
						function = "spi2";
						groups = "spi2_oc1_2";
					};
					default_cfg1 {
						pins = "GPIO216_AG12"; /* FRM */
						ste,config = <&gpio_out_hi>;
					};
					default_cfg2 {
						pins = "GPIO218_AH11"; /* RXD */
						ste,config = <&in_pd>;
					};
					default_cfg3 {
						pins =
						"GPIO215_AH13", /* TXD */
						"GPIO217_AH12"; /* CLK */
						ste,config = <&out_lo>;
					};
				};

				spi2_idle_mode: spi_idle {
					/*
					 * The idle mode is basically sleep mode sans wakeups. Also
					 * note that we have muxes the pins off the function here
					 * as we do not state any muxing.
					 */
					idle_cfg1 {
						pins = "GPIO218_AH11"; /* RXD */
						ste,config = <&slpm_in_pdis>;
					};
					idle_cfg2 {
						pins = "GPIO215_AH13"; /* TXD */
						ste,config = <&slpm_out_lo_pdis>;
					};
					idle_cfg3 {
						pins = "GPIO217_AH12"; /* CLK */
						ste,config = <&slpm_pdis>;
					};
				};

				spi2_sleep_mode: spi_sleep {
					sleep_cfg1 {
						pins =
						"GPIO216_AG12", /* FRM */
						"GPIO218_AH11"; /* RXD */
						ste,config = <&slpm_in_wkup_pdis>;
					};
					sleep_cfg2 {
						pins = "GPIO215_AH13"; /* TXD */
						ste,config = <&slpm_out_lo_wkup_pdis>;
					};
					sleep_cfg3 {
						pins = "GPIO217_AH12"; /* CLK */
						ste,config = <&slpm_wkup_pdis>;
					};
				};
			};

			mcde {
				lcd_default_mode: lcd_default {
					default_mux1 {
						/* Mux in VSI0 and all the data lines */
						function = "lcd";
						groups =
						"lcdvsi0_a_1", /* VSI0 for LCD */
						"lcd_d0_d7_a_1", /* Data lines */
						"lcdvsi1_a_1"; /* VSI1 for HDMI */
					};
					default_mux2 {
						function = "lcda";
						groups =
						"lcdaclk_b_1"; /* Clock line for TV-out */
					};
					default_cfg1 {
						pins =
						"GPIO68_E1", /* VSI0 */
						"GPIO69_E2"; /* VSI1 */
						ste,config = <&in_pu>;
					};
				};
				lcd_sleep_mode: lcd_sleep {
					sleep_cfg1 {
						pins = "GPIO69_E2"; /* VSI1 */
						ste,config = <&slpm_in_wkup_pdis>;
					};
				};
			};

			ske {
				/* SKE keys on position 2 in an 8x8 matrix */
				ske_kpa2_default_mode: ske_kpa2_default {
					default_mux {
						function = "kp";
						groups = "kp_a_2";
					};
					default_cfg1 {
						pins =
						"GPIO153_B17", /* I7 */
						"GPIO154_C16", /* I6 */
						"GPIO155_C19", /* I5 */
						"GPIO156_C17", /* I4 */
						"GPIO161_D21", /* I3 */
						"GPIO162_D20", /* I2 */
						"GPIO163_C20", /* I1 */
						"GPIO164_B21"; /* I0 */
						ste,config = <&in_pd>;
					};
					default_cfg2 {
						pins =
						"GPIO157_A18", /* O7 */
						"GPIO158_C18", /* O6 */
						"GPIO159_B19", /* O5 */
						"GPIO160_B20", /* O4 */
						"GPIO165_C21", /* O3 */
						"GPIO166_A22", /* O2 */
						"GPIO167_B24", /* O1 */
						"GPIO168_C22"; /* O0 */
						ste,config = <&out_lo>;
					};
				};
				ske_kpa2_sleep_mode: ske_kpa2_sleep {
					sleep_cfg1 {
						pins =
						"GPIO153_B17", /* I7 */
						"GPIO154_C16", /* I6 */
						"GPIO155_C19", /* I5 */
						"GPIO156_C17", /* I4 */
						"GPIO161_D21", /* I3 */
						"GPIO162_D20", /* I2 */
						"GPIO163_C20", /* I1 */
						"GPIO164_B21"; /* I0 */
						ste,config = <&slpm_in_pu_wkup_pdis_en>;
					};
					sleep_cfg2 {
						pins =
						"GPIO157_A18", /* O7 */
						"GPIO158_C18", /* O6 */
						"GPIO159_B19", /* O5 */
						"GPIO160_B20", /* O4 */
						"GPIO165_C21", /* O3 */
						"GPIO166_A22", /* O2 */
						"GPIO167_B24", /* O1 */
						"GPIO168_C22"; /* O0 */
						ste,config = <&slpm_out_lo_pdis>;
					};
				};
				/*
				 * SKE keys on position 1 and "other C1" combi giving
				 * six rows of six keys.
				 */
				ske_kpaoc1_default_mode: ske_kpaoc1_default {
					default_mux {
						function = "kp";
						groups = "kp_a_1", "kp_oc1_1";
					};
					default_cfg1 {
						pins =
						"GPIO91_B6", /* KP_O0 */
						"GPIO90_A3", /* KP_O1 */
						"GPIO87_B3", /* KP_O2 */
						"GPIO86_C6", /* KP_O3 */
						"GPIO96_D8", /* KP_O6 */
						"GPIO94_D7"; /* KP_O7 */
						ste,config = <&out_lo>;
					};
					default_cfg2 {
						pins =
						"GPIO93_B7", /* KP_I0 */
						"GPIO92_D6", /* KP_I1 */
						"GPIO89_E6", /* KP_I2 */
						"GPIO88_C4", /* KP_I3 */
						"GPIO97_D9", /* KP_I6 */
						"GPIO95_E8"; /* KP_I7 */
						ste,config = <&in_pu>;
					};
				};
			};

			wlan {
				wlan_default_mode: wlan_default {
					/*
					 * Activate this mode with the WLAN chip.
					 * These are plain GPIO pins used by WLAN
					 */
					default_cfg1 {
						pins =
						"GPIO226_AF8", /* WLAN_PMU_EN */
						"GPIO85_D5"; /* WLAN_ENA */
						ste,config = <&gpio_out_lo>;
					};
					default_cfg2 {
						pins = "GPIO4_AH6"; /* WLAN_IRQ on UART1 */
						ste,config = <&gpio_in_pu>;
					};
				};
			};
		};
	};
};
