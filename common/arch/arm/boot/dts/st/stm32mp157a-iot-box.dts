// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2020 Manivannan Sadhasivam
 */

/dts-v1/;
#include "stm32mp157a-stinger96.dtsi"

/ {
	model = "Shiratech STM32MP157A IoT Box";
	compatible = "shiratech,stm32mp157a-iot-box", "st,stm32mp157";

	wlan_pwr: regulator-wlan {
		compatible = "regulator-fixed";

		regulator-name = "wl-reg";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;

		gpios = <&gpiog 3 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};
};

&i2c2 {
	ccs811@5b {
		compatible = "ams,ccs811";
		reg = <0x5b>;
		wakeup-gpios = <&gpioa 12 GPIO_ACTIVE_LOW>;
		reset-gpios = <&gpioa 11 GPIO_ACTIVE_LOW>;
	};
};

/* WiFi */
&sdmmc2 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc2_b4_pins_a>;
	pinctrl-1 = <&sdmmc2_b4_od_pins_b>;
	pinctrl-2 = <&sdmmc2_b4_sleep_pins_a>;
	broken-cd;
	non-removable;
	st,neg-edge;
	bus-width = <1>;
	vmmc-supply = <&wlan_pwr>;
	status = "okay";

	#address-cells = <1>;
	#size-cells = <0>;
	brcmf: bcrmf@1 {
		reg = <1>;
		compatible = "brcm,bcm4329-fmac";
	};
};

/* Bluetooth */
&uart4 {
	/* Note: HW flow control is broken, hence using custom CTS/RTS gpios */
	/delete-property/st,hw-flow-ctrl;
	cts-gpios = <&gpioa 15 GPIO_ACTIVE_LOW>;
	rts-gpios = <&gpiob 0 GPIO_ACTIVE_LOW>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";

	bluetooth {
		shutdown-gpios = <&gpiog 2 GPIO_ACTIVE_HIGH>;
		compatible = "brcm,bcm43438-bt";
		max-speed = <115200>;
	};
};
