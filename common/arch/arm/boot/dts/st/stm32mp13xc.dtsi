// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2021 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

/ {
	soc {
		cryp: crypto@54002000 {
			compatible = "st,stm32mp1-cryp";
			reg = <0x54002000 0x400>;
			interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc CRYP1>;
			resets = <&rcc CRYP1_R>;
			status = "disabled";
		};
	};
};
