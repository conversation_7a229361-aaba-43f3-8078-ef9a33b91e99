// SPDX-License-Identifier: GPL-2.0-only
/*
 * Devicetree for the Samsung Galaxy Amp SGH-I407 also known as <PERSON>.
 *
 * The code also refers to "Kyle AT&T" reflecting that this mobile phone
 * was customized for the AT&T subsidiary Aio Wireless (All In One) and
 * offered by the company in 2013.
 */

/dts-v1/;
#include "ste-db8500.dtsi"
#include "ste-ab8505.dtsi"
#include "ste-dbx5x0-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/leds/common.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "Samsung Galaxy Amp (SGH-I407)";
	compatible = "samsung,kyle", "st-er<PERSON>son,u8500";

	chosen {
		stdout-path = &serial2;
	};

	battery: battery {
		compatible = "samsung,eb425161la";
	};

	thermal-zones {
		battery-thermal {
			/* This zone will be polled by the battery temperature code */
			polling-delay = <0>;
			polling-delay-passive = <0>;
			thermal-sensors = <&bat_therm>;

			trips {
				battery-crit-hi {
					temperature = <70000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};
	};

	bat_therm: thermistor {
		compatible = "samsung,1404-001221";
		io-channels = <&gpadc 0x02>; /* BatTemp */
		pullup-uv = <1800000>;
		pullup-ohm = <230000>;
		pulldown-ohm = <0>;
		#thermal-sensor-cells = <0>;
	};

	/* TI TXS0206 level translator for 2.9 V */
	sd_level_translator: regulator-gpio {
		compatible = "regulator-fixed";

		/* GPIO87 EN */
		gpios = <&gpio2 23 GPIO_ACTIVE_HIGH>;
		enable-active-high;

		regulator-name = "sd-level-translator";
		regulator-min-microvolt = <2900000>;
		regulator-max-microvolt = <2900000>;
		regulator-type = "voltage";

		startup-delay-us = <200>;

		pinctrl-names = "default";
		pinctrl-0 = <&sd_level_translator_default>;
	};

	/* External LDO MIC5366-3.3YMT for eMMC */
	ldo_3v3_reg: regulator-gpio-ldo-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "en-3v3-fixed-supply";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio6 31 GPIO_ACTIVE_HIGH>;
		startup-delay-us = <5000>;
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&emmc_ldo_en_default_mode>;
	};

	/*
	 * External Ricoh RP152L010B-TR LCD LDO regulator for the display.
	 * LCD_PWR_EN controls both a 3.0V and 1.8V output.
	 */
	lcd_3v0_reg: regulator-gpio-lcd-3v0 {
		compatible = "regulator-fixed";
		/* Supplied in turn by VBAT */
		regulator-name = "VREG_LCD_3V0";
		regulator-min-microvolt = <3000000>;
		regulator-max-microvolt = <3000000>;
		/* GPIO219 controls this regulator */
		gpio = <&gpio6 27 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&lcd_pwr_en_default_mode>;
	};
	lcd_1v8_reg: regulator-gpio-lcd-1v8 {
		compatible = "regulator-fixed";
		/* Supplied in turn by VBAT */
		regulator-name = "VREG_LCD_1V8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		/* GPIO219 controls this regulator too */
		gpio = <&gpio6 27 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&lcd_pwr_en_default_mode>;
	};

	wlan_en: regulator-gpio-wlan-en {
		compatible = "regulator-fixed";
		regulator-name = "wl-reg-on";
		regulator-min-microvolt = <3000000>;
		regulator-max-microvolt = <3000000>;
		startup-delay-us = <200000>;
		/* GPIO215 WLAN_EN */
		gpio = <&gpio6 23 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		pinctrl-names = "default";
		pinctrl-0 = <&wlan_en_default_mode>;
	};

	vibrator {
		compatible = "gpio-vibrator";
		enable-gpios = <&gpio6 3 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&vibrator_default>;
	};

	gpio-keys {
		compatible = "gpio-keys";
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_keys_default_mode>;

		button-home {
			linux,code = <KEY_HOME>;
			label = "HOME";
			/* GPIO91 */
			gpios = <&gpio2 27 GPIO_ACTIVE_LOW>;
		};
		button-volup {
			linux,code = <KEY_VOLUMEUP>;
			label = "VOL+";
			/* GPIO67 */
			gpios = <&gpio2 3 GPIO_ACTIVE_LOW>;
		};
		button-voldown {
			linux,code = <KEY_VOLUMEDOWN>;
			label = "VOL-";
			/* GPIO92 */
			gpios = <&gpio2 28 GPIO_ACTIVE_LOW>;
		};
	};

	ktd253: backlight {
		compatible = "kinetic,ktd253";
		/* GPIO 69 */
		enable-gpios = <&gpio2 5 GPIO_ACTIVE_HIGH>;
		/* Default to 13/32 brightness */
		default-brightness = <13>;
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_backlight_default_mode>;
	};

	/* Richtek RT8515GQW Flash LED Driver IC */
	flash {
		compatible = "richtek,rt8515";
		/* GPIO 140 */
		enf-gpios = <&gpio4 12 GPIO_ACTIVE_HIGH>;
		/* GPIO 141 */
		ent-gpios = <&gpio4 13 GPIO_ACTIVE_HIGH>;
		/*
		 * RFS is 16 kOhm and RTS is 100 kOhm giving
		 * the flash max current 343mA and torch max
		 * current 55 mA.
		 */
		richtek,rfs-ohms = <16000>;
		richtek,rts-ohms = <100000>;
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_flash_default_mode>;

		led {
			function = LED_FUNCTION_FLASH;
			color = <LED_COLOR_ID_WHITE>;
			flash-max-timeout-us = <250000>;
			flash-max-microamp = <343750>;
			led-max-microamp = <55000>;
		};
	};

	i2c-gpio-0 {
		compatible = "i2c-gpio";
		sda-gpios = <&gpio4 16 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpio4 15 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c_gpio_0_default>;
		#address-cells = <1>;
		#size-cells = <0>;
		/* TODO: this should be used by the NCP6914 Camera power management unit */
	};

	i2c-gpio-1 {
		compatible = "i2c-gpio";
		sda-gpios = <&gpio4 24 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpio4 23 (GPIO_ACTIVE_HIGH|GPIO_OPEN_DRAIN)>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c_gpio_1_default>;
		#address-cells = <1>;
		#size-cells = <0>;
		magnetometer@c {
			compatible = "alps,hscdtd008a";
			reg = <0x0c>;
			avdd-supply = <&ab8500_ldo_aux1_reg>;
			dvdd-supply = <&ab8500_ldo_aux6_reg>;
		};
	};

	soc {
		// External Micro SD slot
		mmc@80126000 {
			arm,primecell-periphid = <0x10480180>;
			max-frequency = <100000000>;
			bus-width = <4>;
			cap-sd-highspeed;
			cap-mmc-highspeed;
			st,sig-pin-fbclk;
			full-pwr-cycle;
			vmmc-supply = <&ab8500_ldo_aux3_reg>;
			vqmmc-supply = <&sd_level_translator>;
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&mc0_a_1_default>;
			pinctrl-1 = <&mc0_a_1_sleep>;
			cd-gpios  = <&gpio6 25 GPIO_ACTIVE_LOW>; // GPIO217
			status = "okay";
		};

		// WLAN SDIO channel
		mmc@80118000 {
			arm,primecell-periphid = <0x10480180>;
			max-frequency = <50000000>;
			bus-width = <4>;
			non-removable;
			cap-sd-highspeed;
			vmmc-supply = <&wlan_en>;
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&mc1_a_2_default>;
			pinctrl-1 = <&mc1_a_2_sleep>;
			status = "okay";
			#address-cells = <1>;
			#size-cells = <0>;

			wifi@1 {
				compatible = "brcm,bcm4334-fmac", "brcm,bcm4329-fmac";
				reg = <1>;
				/* GPIO216 WL_HOST_WAKE */
				interrupt-parent = <&gpio6>;
				interrupts = <24 IRQ_TYPE_EDGE_FALLING>;
				interrupt-names = "host-wake";
				pinctrl-names = "default";
				pinctrl-0 = <&wlan_default_mode>;
			};
		};

		/*
		 * eMMC seems to be mostly Samsung KLM4G1YE4C "4YMD1R"
		 */
		mmc@80005000 {
			arm,primecell-periphid = <0x10480180>;
		        max-frequency = <100000000>;
			bus-width = <8>;
			non-removable;
			cap-mmc-highspeed;
			mmc-ddr-1_8v;
			no-sdio;
			no-sd;
			/* From datasheet page 26 figure 9: 300 ms set-up time for 4GB */
			post-power-on-delay-ms = <300>;
			vmmc-supply = <&ldo_3v3_reg>;
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&mc2_a_1_default>;
			pinctrl-1 = <&mc2_a_1_sleep>;

			status = "okay";
		};

		/* GBF (Bluetooth) UART */
		serial@80120000 {
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&u0_a_1_default>;
			pinctrl-1 = <&u0_a_1_sleep>;
			status = "okay";

			bluetooth {
				/* BCM4334B0 actually */
				compatible = "brcm,bcm4330-bt";
				shutdown-gpios = <&gpio6 30 GPIO_ACTIVE_HIGH>;
				device-wakeup-gpios = <&gpio6 7 GPIO_ACTIVE_HIGH>;
				host-wakeup-gpios = <&gpio3 1 GPIO_ACTIVE_HIGH>;
				pinctrl-names = "default";
				pinctrl-0 = <&bluetooth_default_mode>;
			};
		};

		/* GPF UART */
		serial@80121000 {
			status = "okay";
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&u1rxtx_a_1_default &u1ctsrts_a_1_default>;
			pinctrl-1 = <&u1rxtx_a_1_sleep &u1ctsrts_a_1_sleep>;

			gnss {
				/* The CSRG05TA03-ICJE-R is a SirfStarV 5t chip */
				compatible = "csr,csrg05ta03-icje-r";
				/* GPS_RSTN on GPIO21 */
				reset-gpios = <&gpio0 21 GPIO_ACTIVE_LOW>;
				/* GPS_ON_OFF on GPIO86 */
				sirf,onoff-gpios = <&gpio2 22 GPIO_ACTIVE_HIGH>;
				/* GPS_1V8 (VSMPS2) */
				vcc-supply = <&db8500_vsmps2_reg>;
				pinctrl-names = "default";
				pinctrl-0 = <&g05ta03_kyle_default>;
				/* According to /etc/sirfgps.conf */
				current-speed = <460800>;
			};
		};

		/* Debugging console UART connected to AB8505 USB */
		serial@80007000 {
			status = "okay";
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&u2rxtx_c_1_default>;
			pinctrl-1 = <&u2rxtx_c_1_sleep>;
		};

		prcmu@80157000 {
			ab8505 {
				phy {
					pinctrl-names = "default", "sleep";
					pinctrl-0 = <&usb_a_1_default>;
					pinctrl-1 = <&usb_a_1_sleep>;
				};

				ab8500_fg {
					line-impedance-micro-ohms = <36000>;
				};

				regulator {
					ab8500_ldo_aux1 {
						/* Used for VDD for sensors */
						regulator-name = "AUX1";
						regulator-min-microvolt = <3000000>;
						regulator-max-microvolt = <3300000>;
					};

					ab8500_ldo_aux2 {
						/* Supplies the MMS touchscreen only with 3.3V */
						regulator-name = "AUX2";
						regulator-min-microvolt = <3300000>;
						regulator-max-microvolt = <3300000>;
					};

					ab8500_ldo_aux3 {
						/* Used for voltage for external MMC/SD card */
						regulator-name = "AUX3";
						regulator-min-microvolt = <1100000>;
						regulator-max-microvolt = <3300000>;
					};

					ab8500_ldo_aux4 {
						regulator-name = "AUX4";
						/* Hammer to 3.3V for the touchscreen */
						regulator-min-microvolt = <3300000>;
						regulator-max-microvolt = <3300000>;
					};

					ab8500_ldo_aux5 {
						regulator-name = "AUX5";
						/* 1.8V for the touchscreen */
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <1800000>;
					};

					ab8500_ldo_aux6 {
						regulator-name = "AUX6";
						/* Used by sensors for 1.8 V in R0.1+ */
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <1800000>;
					};

					ab8500_ldo_aux8 {
						/* Unused */
						regulator-name = "AUX8";
					};
				};
			};
		};

		/* I2C0 */
		i2c@80004000 {
			status = "okay";

			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&i2c0_a_1_default>;
			pinctrl-1 = <&i2c0_a_1_sleep>;

			proximity@44 {
				compatible = "sharp,gp2ap002s00f";
				clock-frequency = <400000>;
				reg = <0x44>;

				interrupt-parent = <&gpio4>;
				interrupts = <18 IRQ_TYPE_EDGE_FALLING>;
				vdd-supply = <&ab8500_ldo_aux1_reg>;
				vio-supply = <&ab8500_ldo_aux6_reg>;
				pinctrl-names = "default";
				pinctrl-0 = <&gp2ap002_kyle_default>;
				sharp,proximity-far-hysteresis = /bits/ 8 <0x2f>;
				sharp,proximity-close-hysteresis = /bits/ 8 <0x0f>;
			};
		};

		/* I2C2 */
		i2c@80128000 {
			status = "okay";

			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&i2c2_b_2_default>;
			pinctrl-1 = <&i2c2_b_2_sleep>;

			accel@18 {
				compatible = "bosch,bma254";
				clock-frequency = <400000>;
				reg = <0x18>;

				mount-matrix = "-1", "0", "0",
					       "0", "-1", "0",
					       "0", "0", "-1";
				vdd-supply = <&ab8500_ldo_aux1_reg>;
				vddio-supply = <&ab8500_ldo_aux6_reg>;
			};
		};

		/* I2C3 */
		i2c@80110000 {
			status = "okay";

			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&i2c3_c_2_default>;
			pinctrl-1 = <&i2c3_c_2_sleep>;

			/* Melfas MMS134S touchscreen */
			touchscreen@48 {
				compatible = "melfas,mms134s";
				reg = <0x48>;
				/* GPIO218 for IRQ */
				interrupt-parent = <&gpio6>;
				interrupts = <26 IRQ_TYPE_EDGE_FALLING>;
				/* AVDD is "analog supply", 2.57-3.47 V */
				avdd-supply = <&ab8500_ldo_aux2_reg>;
				/* VDD is "digital supply" 1.71-3.47V */
				vdd-supply = <&ab8500_ldo_aux5_reg>;

				touchscreen-size-x = <480>;
				touchscreen-size-y = <800>;

				pinctrl-names = "default";
				pinctrl-0 = <&mms134s_kyle_default>;
			};
		};

		mcde@a0350000 {
			status = "okay";
			pinctrl-names = "default";
			pinctrl-0 = <&dsi_default_mode>;

			dsi@a0351000 {
				panel {
					/*
					 * NT35510-based Hydis HVA40WV1
					 * Apparently some Kyle models can have a NT35512 fitted
					 * here instead. In that case the boot loader needs to
					 * modify this compatible.
					 */
					compatible = "hydis,hva40wv1", "novatek,nt35510";
					reg = <0>;
					/* v_lcd_3v0 2.3-4.8V */
					vdd-supply = <&lcd_3v0_reg>;
					/* v_lcd_1v8 1.65-3.3V */
					vddi-supply = <&lcd_1v8_reg>;
					/* GPIO 139 */
					reset-gpios = <&gpio4 11 GPIO_ACTIVE_LOW>;
					pinctrl-names = "default";
					pinctrl-0 = <&display_default_mode>;
					backlight = <&ktd253>;
				};
			};
		};
	};
};

&pinctrl {
	/*
	 * This extends the MC0_A_1 default config to include
	 * the card detect GPIO217 line.
	 */
	sdi0 {
		mc0_a_1_default {
			default_cfg1 {
				/* GPIO18, 19 & 20 unused so pull down */
				ste,config = <&gpio_in_pd>;
			};
			default_cfg4 {
				pins = "GPIO217_AH12"; /* card detect */
				ste,config = <&gpio_in_pd>;
			};
		};
	};

	mcde {
		dsi_default_mode: dsi_default {
			default_mux1 {
				/* Mux in VSI0 used for DSI TE */
				function = "lcd";
				groups = "lcdvsi0_a_1"; /* VSI0 for LCD */
			};
			default_cfg1 {
				pins = "GPIO68_E1"; /* VSI0 */
				ste,config = <&in_nopull>;
			};
		};
	};

	/* Two GPIO lines used by the display */
	display {
		display_default_mode: display_default {
			kyle_cfg1 {
				/*
				 * OLED DETECT or check_pba, this appears to be high
				 * on "PBA" which I guess is "prototype board A".
				 */
				pins = "GPIO93_B7";
				ste,config = <&gpio_in_nopull>;
			};
			kyle_cfg2 {
				pins = "GPIO139_C9";
				/*
				 * MIPI_DSI0_RESET_N resets the display, leave high
				 * (de-asserted) so we only assert reset explicitly
				 * from the display driver.
				 */
				ste,config = <&gpio_out_hi>;
			};
		};
	};

	/* GPIO that enables the LDO regulator for the LCD display */
	lcd-ldo {
		lcd_pwr_en_default_mode: lcd_pwr_en_default {
			/* LCD_PWR_EN on GPIO219 */
			kyle_cfg1 {
				pins = "GPIO219_AG10";
				ste,config = <&gpio_out_hi>;
			};
		};
	};

	backlight {
		gpio_backlight_default_mode: backlight_default {
			kyle_cfg1 {
				pins = "GPIO69_E2"; /* LCD_BL_CTRL */
				ste,config = <&gpio_out_lo>;
			};
		};
	};
	flash {
		gpio_flash_default_mode: flash_default {
			kyle_cfg1 {
				pins = "GPIO140_B11", "GPIO141_C12";
				ste,config = <&gpio_out_lo>;
			};
		};
	};
	/* GPIO that enables the 2.9V SD card level translator */
	sd-level-translator {
		sd_level_translator_default: sd_level_translator_default {
			/* level shifter on GPIO87 */
			kyle_cfg1 {
				pins = "GPIO87_B3";
				ste,config = <&gpio_out_hi>;
			};
		};
	};
	/* GPIO that enables the LDO regulator for the eMMC */
	emmc-ldo {
		emmc_ldo_en_default_mode: emmc_ldo_default {
			/* LDO enable on GPIO223 */
			kyle_cfg1 {
				pins = "GPIO223_AH9";
				ste,config = <&gpio_out_hi>;
			};
		};
	};
	/* GPIO keys */
	gpio-keys {
		gpio_keys_default_mode: gpio_keys_default {
			kyle_cfg1 {
				pins = "GPIO67_G2", /* VOL UP */
				       "GPIO91_B6", /* HOME */
				       "GPIO92_D6"; /* VOL DOWN */
					ste,config = <&gpio_in_pu>;
			};
		};
	};
	/* Interrupt line for light/proximity sensor GP2AP002 */
	gp2ap002 {
		gp2ap002_kyle_default: gp2ap002_kyle {
			kyle_cfg1 {
				pins = "GPIO146_D13";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	/* GPIO-based I2C bus for NCP6914 */
	i2c-gpio-0 {
		i2c_gpio_0_default: i2c_gpio_0 {
			kyle_cfg1 {
				pins = "GPIO143_D12", "GPIO144_B13";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	/* GPIO-based I2C bus for ALPS HSCD compass */
	i2c-gpio-1 {
		i2c_gpio_1_default: i2c_gpio_1 {
			kyle_cfg1 {
				pins = "GPIO151_B17", "GPIO152_D16";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	wlan {
		wlan_default_mode: wlan_default {
			kyle_cfg1 {
				pins = "GPIO216_AG12";
				ste,config = <&gpio_in_pd>;
			};
		};
		wlan_en_default_mode: wlan_en_default {
			kyle_cfg2 {
				pins = "GPIO215_AH13";
				ste,config = <&gpio_out_lo>;
			};
		};
	};
	bluetooth {
		bluetooth_default_mode: bluetooth_default {
			kyle_cfg1 {
				pins = "GPIO199_AH23", "GPIO222_AJ9";
				ste,config = <&gpio_out_lo>;
			};
			kyle_cfg2 {
				pins = "GPIO97_D9";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	vibrator {
		vibrator_default: vibrator_default {
			kyle_cfg1 {
				pins = "GPIO195_AG28";	/* MOT_EN */
				ste,config = <&gpio_out_lo>;
			};
		};
	};
	/* Interrupt line for the Melfas MMS134S touchscreen */
	touchscreen {
		mms134s_kyle_default: mms134s_kyle {
			kyle_cfg1 {
				pins = "GPIO218_AH11";
				ste,config = <&gpio_in_nopull>;
			};
		};
	};
	g05ta03 {
		g05ta03_kyle_default: g05ta03 {
			/* Reset line, start out de-asserted */
			kyle_cfg1 {
				pins = "GPIO21_AB3";
				ste,config = <&gpio_out_hi>;
			};
			/* GPS_ON_OFF, start out deasserted (off) */
			kyle_cfg2 {
				pins = "GPIO86_C6";
				ste,config = <&gpio_out_lo>;
			};
		};
	};
};

&ab8505_gpio {
	/* Hog a few default settings */
	pinctrl-names = "default";
	pinctrl-0 = <&gpio_default>;

	gpio {
		gpio_default: gpio_default {
			kyle_mux {
				/* Change unused pins to GPIO mode */
				function = "gpio";
				groups = "gpio3_a_1",	/* default: SysClkReq4 */
					 "gpio14_a_1";	/* default: PWMOut1 */
			};
			kyle_cfg1 {
				pins = "GPIO11_B17", "GPIO13_D17", "GPIO50_L4";
				bias-disable;
			};
		};
	};
};
