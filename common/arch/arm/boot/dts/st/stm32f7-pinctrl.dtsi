// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2017 - All Rights Reserved
 * Author: <PERSON>  <<EMAIL>> for STMicroelectronics.
 */

#include <dt-bindings/pinctrl/stm32-pinfunc.h>
#include <dt-bindings/mfd/stm32f7-rcc.h>

/ {
	soc {
		pinctrl: pinctrl@******** {
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x******** 0x3000>;
			interrupt-parent = <&exti>;
			st,syscfg = <&syscfg 0x8>;

			gpioa: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x0 0x400>;
				clocks = <&rcc 0 STM32F7_AHB1_CLOCK(GPIOA)>;
				st,bank-name = "GPIOA";
			};

			gpiob: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x400 0x400>;
				clocks = <&rcc 0 STM32F7_AHB1_CLOCK(GPIOB)>;
				st,bank-name = "GPIOB";
			};

			gpioc: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x800 0x400>;
				clocks = <&rcc 0 STM32F7_AHB1_CLOCK(GPIOC)>;
				st,bank-name = "GPIOC";
			};

			gpiod: gpio@40020c00 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0xc00 0x400>;
				clocks = <&rcc 0 STM32F7_AHB1_CLOCK(GPIOD)>;
				st,bank-name = "GPIOD";
			};

			gpioe: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x1000 0x400>;
				clocks = <&rcc 0 STM32F7_AHB1_CLOCK(GPIOE)>;
				st,bank-name = "GPIOE";
			};

			gpiof: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x1400 0x400>;
				clocks = <&rcc 0 STM32F7_AHB1_CLOCK(GPIOF)>;
				st,bank-name = "GPIOF";
			};

			gpiog: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x1800 0x400>;
				clocks = <&rcc 0 STM32F7_AHB1_CLOCK(GPIOG)>;
				st,bank-name = "GPIOG";
			};

			gpioh: gpio@40021c00 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x1c00 0x400>;
				clocks = <&rcc 0 STM32F7_AHB1_CLOCK(GPIOH)>;
				st,bank-name = "GPIOH";
			};

			gpioi: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x2000 0x400>;
				clocks = <&rcc 0 STM32F7_AHB1_CLOCK(GPIOI)>;
				st,bank-name = "GPIOI";
			};

			gpioj: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x2400 0x400>;
				clocks = <&rcc 0 STM32F7_AHB1_CLOCK(GPIOJ)>;
				st,bank-name = "GPIOJ";
			};

			gpiok: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x2800 0x400>;
				clocks = <&rcc 0 STM32F7_AHB1_CLOCK(GPIOK)>;
				st,bank-name = "GPIOK";
			};

			cec_pins_a: cec-0 {
				pins {
					pinmux = <STM32_PINMUX('A', 15, AF4)>; /* HDMI CEC */
					slew-rate = <0>;
					drive-open-drain;
					bias-disable;
				};
			};

			usart1_pins_a: usart1-0 {
				pins1 {
					pinmux = <STM32_PINMUX('A', 9, AF7)>; /* USART1_TX */
					bias-disable;
					drive-push-pull;
					slew-rate = <0>;
				};
				pins2 {
					pinmux = <STM32_PINMUX('A', 10, AF7)>; /* USART1_RX */
					bias-disable;
				};
			};

			usart1_pins_b: usart1-1 {
				pins1 {
					pinmux = <STM32_PINMUX('A', 9, AF7)>; /* USART1_TX */
					bias-disable;
					drive-push-pull;
					slew-rate = <0>;
				};
				pins2 {
					pinmux = <STM32_PINMUX('B', 7, AF7)>; /* USART1_RX */
					bias-disable;
				};
			};

			i2c1_pins_b: i2c1-0 {
				pins {
					pinmux = <STM32_PINMUX('B', 9, AF4)>, /* I2C1 SDA */
						 <STM32_PINMUX('B', 8, AF4)>; /* I2C1 SCL */
					bias-disable;
					drive-open-drain;
					slew-rate = <0>;
				};
			};

			i2c3_pins_a: i2c3-0 {
				pins {
					pinmux = <STM32_PINMUX('H', 8, AF4)>, /* I2C3_SDA */
						 <STM32_PINMUX('H', 7, AF4)>; /* I2C3_SCL */
					bias-disable;
					drive-open-drain;
					slew-rate = <0>;
				};
			};

			usbotg_hs_pins_a: usbotg-hs-0 {
				pins {
					pinmux = <STM32_PINMUX('H', 4, AF10)>, /* OTG_HS_ULPI_NXT */
						 <STM32_PINMUX('I', 11, AF10)>, /* OTG_HS_ULPI_DIR */
						 <STM32_PINMUX('C', 0, AF10)>, /* OTG_HS_ULPI_STP */
						 <STM32_PINMUX('A', 5, AF10)>, /* OTG_HS_ULPI_CK */
						 <STM32_PINMUX('A', 3, AF10)>, /* OTG_HS_ULPI_D0 */
						 <STM32_PINMUX('B', 0, AF10)>, /* OTG_HS_ULPI_D1 */
						 <STM32_PINMUX('B', 1, AF10)>, /* OTG_HS_ULPI_D2 */
						 <STM32_PINMUX('B', 10, AF10)>, /* OTG_HS_ULPI_D3 */
						 <STM32_PINMUX('B', 11, AF10)>, /* OTG_HS_ULPI_D4 */
						 <STM32_PINMUX('B', 12, AF10)>, /* OTG_HS_ULPI_D5 */
						 <STM32_PINMUX('B', 13, AF10)>, /* OTG_HS_ULPI_D6 */
						 <STM32_PINMUX('B', 5, AF10)>; /* OTG_HS_ULPI_D7 */
					bias-disable;
					drive-push-pull;
					slew-rate = <2>;
				};
			};

			usbotg_hs_pins_b: usbotg-hs-1 {
				pins {
					pinmux = <STM32_PINMUX('H', 4, AF10)>, /* OTG_HS_ULPI_NXT */
						 <STM32_PINMUX('C', 2, AF10)>, /* OTG_HS_ULPI_DIR */
						 <STM32_PINMUX('C', 0, AF10)>, /* OTG_HS_ULPI_STP */
						 <STM32_PINMUX('A', 5, AF10)>, /* OTG_HS_ULPI_CK */
						 <STM32_PINMUX('A', 3, AF10)>, /* OTG_HS_ULPI_D0 */
						 <STM32_PINMUX('B', 0, AF10)>, /* OTG_HS_ULPI_D1 */
						 <STM32_PINMUX('B', 1, AF10)>, /* OTG_HS_ULPI_D2 */
						 <STM32_PINMUX('B', 10, AF10)>, /* OTG_HS_ULPI_D3 */
						 <STM32_PINMUX('B', 11, AF10)>, /* OTG_HS_ULPI_D4 */
						 <STM32_PINMUX('B', 12, AF10)>, /* OTG_HS_ULPI_D5 */
						 <STM32_PINMUX('B', 13, AF10)>, /* OTG_HS_ULPI_D6 */
						 <STM32_PINMUX('B', 5, AF10)>; /* OTG_HS_ULPI_D7 */
					bias-disable;
					drive-push-pull;
					slew-rate = <2>;
				};
			};

			usbotg_fs_pins_a: usbotg-fs-0 {
				pins {
					pinmux = <STM32_PINMUX('A', 10, AF10)>, /* OTG_FS_ID */
						 <STM32_PINMUX('A', 11, AF10)>, /* OTG_FS_DM */
						 <STM32_PINMUX('A', 12, AF10)>; /* OTG_FS_DP */
					bias-disable;
					drive-push-pull;
					slew-rate = <2>;
				};
			};

			sdio_pins_a: sdio-pins-a-0 {
				pins {
					pinmux = <STM32_PINMUX('C', 8, AF12)>, /* SDMMC1 D0 */
						 <STM32_PINMUX('C', 9, AF12)>, /* SDMMC1 D1 */
						 <STM32_PINMUX('C', 10, AF12)>, /* SDMMC1 D2 */
						 <STM32_PINMUX('C', 11, AF12)>, /* SDMMC1 D3 */
						 <STM32_PINMUX('C', 12, AF12)>, /* SDMMC1 CLK */
						 <STM32_PINMUX('D', 2, AF12)>; /* SDMMC1 CMD */
					drive-push-pull;
					slew-rate = <2>;
				};
			};

			sdio_pins_od_a: sdio-pins-od-a-0 {
				pins1 {
					pinmux = <STM32_PINMUX('C', 8, AF12)>, /* SDMMC1 D0 */
						 <STM32_PINMUX('C', 9, AF12)>, /* SDMMC1 D1 */
						 <STM32_PINMUX('C', 10, AF12)>, /* SDMMC1 D2 */
						 <STM32_PINMUX('C', 11, AF12)>, /* SDMMC1 D3 */
						 <STM32_PINMUX('C', 12, AF12)>; /* SDMMC1 CLK */
					drive-push-pull;
					slew-rate = <2>;
				};

				pins2 {
					pinmux = <STM32_PINMUX('D', 2, AF12)>; /* SDMMC1 CMD */
					drive-open-drain;
					slew-rate = <2>;
				};
			};

			sdio_pins_b: sdio-pins-b-0 {
				pins {
					pinmux = <STM32_PINMUX('G', 9, AF11)>, /* SDMMC2 D0 */
						 <STM32_PINMUX('G', 10, AF11)>, /* SDMMC2 D1 */
						 <STM32_PINMUX('B', 3, AF10)>, /* SDMMC2 D2 */
						 <STM32_PINMUX('B', 4, AF10)>, /* SDMMC2 D3 */
						 <STM32_PINMUX('D', 6, AF11)>, /* SDMMC2 CLK */
						 <STM32_PINMUX('D', 7, AF11)>; /* SDMMC2 CMD */
					drive-push-pull;
					slew-rate = <2>;
				};
			};

			sdio_pins_od_b: sdio-pins-od-b-0 {
				pins1 {
					pinmux = <STM32_PINMUX('G', 9, AF11)>, /* SDMMC2 D0 */
						 <STM32_PINMUX('G', 10, AF11)>, /* SDMMC2 D1 */
						 <STM32_PINMUX('B', 3, AF10)>, /* SDMMC2 D2 */
						 <STM32_PINMUX('B', 4, AF10)>, /* SDMMC2 D3 */
						 <STM32_PINMUX('D', 6, AF11)>; /* SDMMC2 CLK */
					drive-push-pull;
					slew-rate = <2>;
				};

				pins2 {
					pinmux = <STM32_PINMUX('D', 7, AF11)>; /* SDMMC2 CMD */
					drive-open-drain;
					slew-rate = <2>;
				};
			};

			can1_pins_a: can1-0 {
				pins1 {
					pinmux = <STM32_PINMUX('A', 12, AF9)>; /* CAN1_TX */
				};
				pins2 {
					pinmux = <STM32_PINMUX('A', 11, AF9)>; /* CAN1_RX */
					bias-pull-up;
				};
			};

			can1_pins_b: can1-1 {
				pins1 {
					pinmux = <STM32_PINMUX('B', 9, AF9)>; /* CAN1_TX */
				};
				pins2 {
					pinmux = <STM32_PINMUX('B', 8, AF9)>; /* CAN1_RX */
					bias-pull-up;
				};
			};

			can1_pins_c: can1-2 {
				pins1 {
					pinmux = <STM32_PINMUX('D', 1, AF9)>; /* CAN1_TX */
				};
				pins2 {
					pinmux = <STM32_PINMUX('D', 0, AF9)>; /* CAN1_RX */
					bias-pull-up;

				};
			};

			can1_pins_d: can1-3 {
				pins1 {
					pinmux = <STM32_PINMUX('H', 13, AF9)>; /* CAN1_TX */
				};
				pins2 {
					pinmux = <STM32_PINMUX('H', 14, AF9)>; /* CAN1_RX */
					bias-pull-up;

				};
			};

			can2_pins_a: can2-0 {
				pins1 {
					pinmux = <STM32_PINMUX('B', 6, AF9)>; /* CAN2_TX */
				};
				pins2 {
					pinmux = <STM32_PINMUX('B', 5, AF9)>; /* CAN2_RX */
					bias-pull-up;
				};
			};

			can2_pins_b: can2-1 {
				pins1 {
					pinmux = <STM32_PINMUX('B', 13, AF9)>; /* CAN2_TX */
				};
				pins2 {
					pinmux = <STM32_PINMUX('B', 12, AF9)>; /* CAN2_RX */
					bias-pull-up;
				};
			};

			can3_pins_a: can3-0 {
				pins1 {
					pinmux = <STM32_PINMUX('A', 15, AF11)>; /* CAN3_TX */
				};
				pins2 {
					pinmux = <STM32_PINMUX('A', 8, AF11)>; /* CAN3_RX */
					bias-pull-up;
				};
			};

			can3_pins_b: can3-1 {
				pins1 {
					pinmux = <STM32_PINMUX('B', 4, AF11)>;  /* CAN3_TX */
				};
				pins2 {
					pinmux = <STM32_PINMUX('B', 3, AF11)>; /* CAN3_RX */
					bias-pull-up;
				};
			};

			ltdc_pins_a: ltdc-0 {
				pins {
					pinmux = <STM32_PINMUX('E', 4, AF14)>, /* LCD_B0 */
						 <STM32_PINMUX('G',12, AF9)>,  /* LCD_B4 */
						 <STM32_PINMUX('I', 9, AF14)>, /* LCD_VSYNC */
						 <STM32_PINMUX('I',10, AF14)>, /* LCD_HSYNC */
						 <STM32_PINMUX('I',14, AF14)>, /* LCD_CLK */
						 <STM32_PINMUX('I',15, AF14)>, /* LCD_R0 */
						 <STM32_PINMUX('J', 0, AF14)>, /* LCD_R1 */
						 <STM32_PINMUX('J', 1, AF14)>, /* LCD_R2 */
						 <STM32_PINMUX('J', 2, AF14)>, /* LCD_R3 */
						 <STM32_PINMUX('J', 3, AF14)>, /* LCD_R4 */
						 <STM32_PINMUX('J', 4, AF14)>, /* LCD_R5 */
						 <STM32_PINMUX('J', 5, AF14)>, /* LCD_R6 */
						 <STM32_PINMUX('J', 6, AF14)>, /* LCD_R7 */
						 <STM32_PINMUX('J', 7, AF14)>, /* LCD_G0 */
						 <STM32_PINMUX('J', 8, AF14)>, /* LCD_G1 */
						 <STM32_PINMUX('J', 9, AF14)>, /* LCD_G2 */
						 <STM32_PINMUX('J',10, AF14)>, /* LCD_G3 */
						 <STM32_PINMUX('J',11, AF14)>, /* LCD_G4 */
						 <STM32_PINMUX('J',13, AF14)>, /* LCD_B1 */
						 <STM32_PINMUX('J',14, AF14)>, /* LCD_B2 */
						 <STM32_PINMUX('J',15, AF14)>, /* LCD_B3 */
						 <STM32_PINMUX('K', 0, AF14)>, /* LCD_G5 */
						 <STM32_PINMUX('K', 1, AF14)>, /* LCD_G6 */
						 <STM32_PINMUX('K', 2, AF14)>, /* LCD_G7 */
						 <STM32_PINMUX('K', 4, AF14)>, /* LCD_B5 */
						 <STM32_PINMUX('K', 5, AF14)>, /* LCD_B6 */
						 <STM32_PINMUX('K', 6, AF14)>, /* LCD_B7 */
						 <STM32_PINMUX('K', 7, AF14)>; /* LCD_DE */
					slew-rate = <2>;
				};
			};
		};
	};
};
