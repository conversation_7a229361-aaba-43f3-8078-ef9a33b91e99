// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * DTS file for all SPEAr3xx SoCs
 *
 * Copyright 2012 V<PERSON><PERSON> <<EMAIL>>
 */

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&vic>;

	cpus {
		#address-cells = <0>;
		#size-cells = <0>;

		cpu {
			compatible = "arm,arm926ej-s";
			device_type = "cpu";
		};
	};

	memory {
		device_type = "memory";
		reg = <0 0x40000000>;
	};

	ahb {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "simple-bus";
		ranges = <0xd0000000 0xd0000000 0x30000000>;

		vic: interrupt-controller@f1100000 {
			compatible = "arm,pl190-vic";
			interrupt-controller;
			reg = <0xf1100000 0x1000>;
			#interrupt-cells = <1>;
		};

		dma@fc400000 {
			compatible = "arm,pl080", "arm,primecell";
			reg = <0xfc400000 0x1000>;
			interrupt-parent = <&vic>;
			interrupts = <8>;
			status = "disabled";
		};

		gmac: eth@e0800000 {
			compatible = "snps,dwmac-3.40a";
			reg = <0xe0800000 0x8000>;
			interrupts = <23 22>;
			interrupt-names = "macirq", "eth_wake_irq";
			phy-mode = "mii";
			status = "disabled";
		};

		smi: flash@fc000000 {
			compatible = "st,spear600-smi";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0xfc000000 0x1000>;
			interrupts = <9>;
			status = "disabled";
		};

		spi0: spi@d0100000 {
			compatible = "arm,pl022", "arm,primecell";
			reg = <0xd0100000 0x1000>;
			interrupts = <20>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		ehci@e1800000 {
			compatible = "st,spear600-ehci", "usb-ehci";
			reg = <0xe1800000 0x1000>;
			interrupts = <26>;
			status = "disabled";
		};

		ohci@e1900000 {
			compatible = "st,spear600-ohci", "usb-ohci";
			reg = <0xe1900000 0x1000>;
			interrupts = <25>;
			status = "disabled";
		};

		ohci@e2100000 {
			compatible = "st,spear600-ohci", "usb-ohci";
			reg = <0xe2100000 0x1000>;
			interrupts = <27>;
			status = "disabled";
		};

		apb {
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "simple-bus";
			ranges = <0xd0000000 0xd0000000 0x30000000>;

			gpio0: gpio@fc980000 {
				compatible = "arm,pl061", "arm,primecell";
				reg = <0xfc980000 0x1000>;
				interrupts = <11>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				status = "disabled";
			};

			i2c0: i2c@d0180000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,designware-i2c";
				reg = <0xd0180000 0x1000>;
				interrupts = <21>;
				status = "disabled";
			};

			rtc@fc900000 {
				compatible = "st,spear600-rtc";
				reg = <0xfc900000 0x1000>;
				interrupts = <10>;
				status = "disabled";
			};

			serial@d0000000 {
				compatible = "arm,pl011", "arm,primecell";
				reg = <0xd0000000 0x1000>;
				interrupts = <19>;
				status = "disabled";
			};

			wdt@fc880000 {
				compatible = "arm,sp805", "arm,primecell";
				reg = <0xfc880000 0x1000>;
				interrupts = <12>;
				status = "disabled";
			};

			timer@f0000000 {
				compatible = "st,spear-timer";
				reg = <0xf0000000 0x400>;
				interrupts = <2>;
			};
		};
	};
};
