/* SPDX-License-Identifier: (GPL-2.0-or-later OR BSD-3-Clause) */
/*
 * Copyright (C) 2020 STMicroelectronics - All Rights Reserved
 * Copyright (C) 2020 <PERSON>, Pengutronix
 */

/dts-v1/;

#include "stm32mp157.dtsi"
#include "stm32mp15xx-osd32.dtsi"
#include "stm32mp15xxac-pinctrl.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pwm/pwm.h>

/ {
	model = "Linux Automation MC-1 board";
	compatible = "lxa,stm32mp157c-mc1", "oct,stm32mp15xx-osd32", "st,stm32mp157";

	aliases {
		ethernet0 = &ethernet0;
		mmc0 = &sdmmc1;
		mmc1 = &sdmmc2;
		serial0 = &uart4;
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		pwms = <&backlight_pwm 1 100000 PWM_POLARITY_INVERTED>;
		brightness-levels = <0 31 63 95 127 159 191 223 255>;
		default-brightness-level = <7>;
		power-supply = <&reg_5v2>; /* 3V3_BACKLIGHT */
	};

	chosen {
		stdout-path = &uart4;
	};

	led-controller-0 {
		compatible = "gpio-leds";

		led-0 {
			label = "mc1:green:act";
			gpios = <&gpioa 13 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "heartbeat";
		};
	};

	led-controller-1 {
		compatible = "pwm-leds";

		/* led-1 to led-3 are part of a single RGB led */
		led-1 {
			label = "mc1:red:rgb";
			pwms = <&leds_pwm 1 1000000 0>;
			max-brightness = <255>;
			active-low;
		};

		led-2 {
			label = "mc1:green:rgb";
			pwms = <&leds_pwm 2 1000000 0>;
			max-brightness = <255>;
			active-low;
		};

		led-3 {
			label = "mc1:blue:rgb";
			pwms = <&leds_pwm 3 1000000 0>;
			max-brightness = <255>;
			active-low;
		};
	};

	panel: panel {
		compatible = "edt,etm0700g0edh6";
		backlight = <&backlight>;
		enable-gpios = <&gpiod 4 GPIO_ACTIVE_HIGH>;
		power-supply = <&reg_3v3>;

		port {
			panel_input: endpoint {
				remote-endpoint = <&ltdc_ep0_out>;
			};
		};
	};

	reg_3v3: regulator_3v3 {
		compatible = "regulator-fixed";
		regulator-name = "3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		vin-supply = <&v3v3>;
	};

	/* supplied by either debug board or PoE */
	reg_5v2: regulator_5v2 {
		compatible = "regulator-fixed";
		regulator-name = "5V2";
		regulator-min-microvolt = <5200000>;
		regulator-max-microvolt = <5200000>;
		regulator-always-on;
	};
};

&ethernet0 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&ethernet0_rgmii_pins_b>;
	pinctrl-1 = <&ethernet0_rgmii_sleep_pins_b>;
	phy-mode = "rgmii-id";
	phy-handle = <&ethphy>;
	status = "okay";

	mdio {
		compatible = "snps,dwmac-mdio";
		#address-cells = <1>;
		#size-cells = <0>;

		ethphy: ethernet-phy@3 { /* KSZ9031RN */
			reg = <3>;
			reset-gpios = <&gpiog 0 GPIO_ACTIVE_LOW>; /* ETH_RST# */
			interrupt-parent = <&gpioa>;
			interrupts = <6 IRQ_TYPE_EDGE_FALLING>; /* ETH_MDINT# */
			reset-assert-us = <10000>;
			reset-deassert-us = <300>;
			micrel,force-master;
		};
	};
};

&gpioz {
	gpio-line-names = "HWID0", "HWID1", "HWID2", "HWID3", "", "",
			  "HWID4", "HWID5";
};

&i2c5 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c5_pins_b>;
	pinctrl-1 = <&i2c5_sleep_pins_b>;
	clock-frequency = <400000>;
	status = "okay";

	touchscreen@38 {
		compatible = "edt,edt-ft5x06";
		interrupt-parent = <&gpiod>;
		interrupts = <11 IRQ_TYPE_EDGE_FALLING>; /* TOUCH_INT# */
		vcc-supply = <&reg_3v3>;
		reg = <0x38>;
		reset-gpios = <&gpiof 8 GPIO_ACTIVE_LOW>; /* TOUCH_RESET# */
		touchscreen-size-x = <1792>;
		touchscreen-size-y = <1024>;
		wakeup-source;
	};
};

&ltdc {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&ltdc_pins_c>;
	pinctrl-1 = <&ltdc_sleep_pins_c>;
	status = "okay";

	port {
		ltdc_ep0_out: endpoint {
			remote-endpoint = <&panel_input>;
		};
	};
};

&pmic {
	regulators {
		buck4-supply = <&reg_5v2>;	/* VIN */
		ldo2-supply = <&reg_5v2>;	/* PMIC_LDO25IN */
		ldo5-supply = <&reg_5v2>;	/* PMIC_LDO25IN */
		boost-supply = <&reg_5v2>;	/* PMIC_BSTIN */
		pwr_sw2-supply = <&bst_out>;    /* PMIC_SWIN */
	};
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a>;
	bus-width = <4>;
	cd-gpios = <&gpioh 3 GPIO_ACTIVE_LOW>;
	disable-wp;
	no-1-8-v;
	st,neg-edge;
	vmmc-supply = <&reg_3v3>;
	status = "okay";
};

&sdmmc1_b4_pins_a {
	/*
	 * board lacks external pull-ups on SDMMC lines. Class 10 SD refuses to
	 * work, thus enable internal pull-ups.
	 */
	pins1 {
		/delete-property/ bias-disable;
		bias-pull-up;
	};
	pins2 {
		/delete-property/ bias-disable;
		bias-pull-up;
	};
};

&sdmmc2 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc2_b4_pins_a &sdmmc2_d47_pins_b>;
	pinctrl-1 = <&sdmmc2_b4_od_pins_a &sdmmc2_d47_pins_b>;
	pinctrl-2 = <&sdmmc2_b4_sleep_pins_a &sdmmc2_d47_sleep_pins_b>;
	bus-width = <8>;
	mmc-ddr-3_3v;
	no-1-8-v;
	no-sd;
	no-sdio;
	non-removable;
	st,neg-edge;
	vmmc-supply = <&reg_3v3>;
	status = "okay";
};

&timers3 {
	status = "okay";

	backlight_pwm: pwm {
		pinctrl-names = "default", "sleep";
		pinctrl-0 = <&pwm3_pins_b>;
		pinctrl-1 = <&pwm3_sleep_pins_b>;
		status = "okay";
	};
};

&timers5 {
	status = "okay";

	leds_pwm: pwm {
		pinctrl-names = "default", "sleep";
		pinctrl-0 = <&pwm5_pins_b>;
		pinctrl-1 = <&pwm5_sleep_pins_b>;
		status = "okay";
	};
};

&uart4 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};
