// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Device Tree for the HREF520 version with the TVK1281618 R3 UIB
 */

/dts-v1/;
#include "ste-db8520.dtsi"
#include "ste-hrefv60plus.dtsi"
#include "ste-href-tvk1281618-r3.dtsi"

/ {
	model = "ST-Ericsson HREF520 and TVK1281618 R3 UIB";
	compatible = "st-er<PERSON>son,href520", "st-er<PERSON><PERSON>,u8500";


	/* ST6G3244ME level translator for 1.8/2.9 V */
	vmmci: regulator-gpio {
		compatible = "regulator-gpio";

		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <2900000>;
		regulator-name = "mmci-reg";
		regulator-type = "voltage";

		startup-delay-us = <100>;

		states = <1800000 0x1
			  2900000 0x0>;

		gpios = <&gpio0 5 GPIO_ACTIVE_HIGH>;
		enable-gpios = <&gpio2 14 GPIO_ACTIVE_HIGH>;
		enable-active-high;

		pinctrl-names = "default";
		pinctrl-0 = <&vmmci_default_mode>;
	};
};

&pinctrl {
	vmmci {
		vmmci_default_mode: vmmc_default {
			/* VMMCI level-shifter enable */
			default_href520_cfg1 {
				pins = "GPIO78_F4";
				ste,config = <&gpio_out_hi>;
			};
			/* VMMCI level-shifter voltage select */
			default_href520_cfg2 {
				pins = "GPIO5_AG6";
				ste,config = <&gpio_out_hi>;
			};
		};
	};
};
