// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (c) STMicroelectronics 2019 - All Rights Reserved
 * Copyright (c) 2020 Engicam srl
 * Copyright (c) 2020 Amarula Solutions(India)
 */

/dts-v1/;
#include "stm32mp157.dtsi"
#include "stm32mp157a-icore-stm32mp1.dtsi"
#include "stm32mp15-pinctrl.dtsi"
#include "stm32mp15xxaa-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Engicam i.Core STM32MP1 C.TOUCH 2.0 10.1\" Open Frame";
	compatible = "engicam,icore-stm32mp1-ctouch2-of10",
		     "engicam,icore-stm32mp1", "st,stm32mp157";

	aliases {
		serial0 = &uart4;
	};

	backlight: backlight {
		compatible = "gpio-backlight";
		gpios = <&gpiod 13 GPIO_ACTIVE_HIGH>;
		default-on;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	panel {
		compatible = "ampire,am-1280800n3tzqw-t00h";
		backlight = <&backlight>;
		power-supply = <&v3v3>;

		port {
			panel_in_lvds: endpoint {
				remote-endpoint = <&bridge_out>;
			};
		};
	};
};

&dsi {
	status = "okay";
};

&dsi_in {
	remote-endpoint = <&ltdc_ep0_out>;
};

&dsi_out {
	remote-endpoint = <&bridge_in>;
};

&i2c6 {
	i2c-scl-falling-time-ns = <20>;
	i2c-scl-rising-time-ns = <185>;
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c6_pins_a>;
	pinctrl-1 = <&i2c6_sleep_pins_a>;
	status = "okay";

	bridge@2c {
		compatible = "ti,sn65dsi84";
		reg = <0x2c>;
		enable-gpios = <&gpiof 15 GPIO_ACTIVE_HIGH>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				bridge_in: endpoint {
					remote-endpoint = <&dsi_out>;
					data-lanes = <1 2>;
				};
			};

			port@2 {
				reg = <2>;
				bridge_out: endpoint {
					remote-endpoint = <&panel_in_lvds>;
				};
			};
		};
	};
};

&ltdc {
	status = "okay";

	port {
		ltdc_ep0_out: endpoint {
			remote-endpoint = <&dsi_in>;
		};
	};
};

&sdmmc1 {
	bus-width = <4>;
	disable-wp;
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a>;
	st,neg-edge;
	vmmc-supply = <&v3v3>;
	status = "okay";
};

&uart4 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&uart4_pins_a>;
	pinctrl-1 = <&uart4_sleep_pins_a>;
	pinctrl-2 = <&uart4_idle_pins_a>;
	status = "okay";
};
