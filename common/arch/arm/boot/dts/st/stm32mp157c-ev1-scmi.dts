// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2022 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

/dts-v1/;

#include "stm32mp157c-ev1.dts"
#include "stm32mp15-scmi.dtsi"

/ {
	model = "STMicroelectronics STM32MP157C-EV1 SCMI eval daughter on eval mother";
	compatible = "st,stm32mp157c-ev1-scmi", "st,stm32mp157c-ed1", "st,stm32mp157";

	reserved-memory {
		optee@fe000000 {
			reg = <0xfe000000 0x2000000>;
			no-map;
		};
	};
};

&cpu0 {
	clocks = <&scmi_clk CK_SCMI_MPU>;
};

&cpu1 {
	clocks = <&scmi_clk CK_SCMI_MPU>;
};

&cryp1 {
	clocks = <&scmi_clk CK_SCMI_CRYP1>;
	resets = <&scmi_reset RST_SCMI_CRYP1>;
};

&dsi {
	phy-dsi-supply = <&scmi_reg18>;
	clocks = <&rcc DSI_K>, <&scmi_clk CK_SCMI_HSE>, <&rcc DSI_PX>;
};

&gpioz {
	clocks = <&scmi_clk CK_SCMI_GPIOZ>;
};

&hash1 {
	clocks = <&scmi_clk CK_SCMI_HASH1>;
	resets = <&scmi_reset RST_SCMI_HASH1>;
};

&i2c4 {
	clocks = <&scmi_clk CK_SCMI_I2C4>;
	resets = <&scmi_reset RST_SCMI_I2C4>;
};

&iwdg2 {
	clocks = <&rcc IWDG2>, <&scmi_clk CK_SCMI_LSI>;
};

&m_can1 {
	clocks = <&scmi_clk CK_SCMI_HSE>, <&rcc FDCAN_K>;
};

&mdma1 {
	resets = <&scmi_reset RST_SCMI_MDMA>;
};

&m4_rproc {
	/delete-property/ st,syscfg-holdboot;
	resets = <&scmi_reset RST_SCMI_MCU>,
		 <&scmi_reset RST_SCMI_MCU_HOLD_BOOT>;
	reset-names =  "mcu_rst", "hold_boot";
};

&rcc {
	compatible = "st,stm32mp1-rcc-secure", "syscon";
	clock-names = "hse", "hsi", "csi", "lse", "lsi";
	clocks = <&scmi_clk CK_SCMI_HSE>,
		 <&scmi_clk CK_SCMI_HSI>,
		 <&scmi_clk CK_SCMI_CSI>,
		 <&scmi_clk CK_SCMI_LSE>,
		 <&scmi_clk CK_SCMI_LSI>;
};

&rng1 {
	clocks = <&scmi_clk CK_SCMI_RNG1>;
	resets = <&scmi_reset RST_SCMI_RNG1>;
};

&rtc {
	clocks = <&scmi_clk CK_SCMI_RTCAPB>, <&scmi_clk CK_SCMI_RTC>;
};
