// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2019 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/mfd/st,stpmic1.h>

/ {
	aliases {
		serial0 = &uart4;
		serial1 = &usart3;
		serial2 = &uart7;
	};

	memory@c0000000 {
		device_type = "memory";
		reg = <0xc0000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		mcuram2: mcuram2@10000000 {
			compatible = "shared-dma-pool";
			reg = <0x10000000 0x40000>;
			no-map;
		};

		vdev0vring0: vdev0vring0@10040000 {
			compatible = "shared-dma-pool";
			reg = <0x10040000 0x1000>;
			no-map;
		};

		vdev0vring1: vdev0vring1@10041000 {
			compatible = "shared-dma-pool";
			reg = <0x10041000 0x1000>;
			no-map;
		};

		vdev0buffer: vdev0buffer@10042000 {
			compatible = "shared-dma-pool";
			reg = <0x10042000 0x4000>;
			no-map;
		};

		mcuram: mcuram@******** {
			compatible = "shared-dma-pool";
			reg = <0x******** 0x40000>;
			no-map;
		};

		retram: retram@******** {
			compatible = "shared-dma-pool";
			reg = <0x******** 0x10000>;
			no-map;
		};
	};

	led {
		compatible = "gpio-leds";
		led-blue {
			label = "heartbeat";
			gpios = <&gpiod 11 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
			default-state = "off";
		};
	};

	sound {
		compatible = "audio-graph-card";
		label = "STM32MP15-DK";
		routing =
			"Playback" , "MCLK",
			"Capture" , "MCLK",
			"MICL" , "Mic Bias";
		dais = <&sai2a_port &sai2b_port &i2s2_port>;
		status = "okay";
	};

	vin: vin {
		compatible = "regulator-fixed";
		regulator-name = "vin";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};
};

&adc {
	pinctrl-names = "default";
	pinctrl-0 = <&adc12_usb_cc_pins_a>;
	vdd-supply = <&vdd>;
	vdda-supply = <&vdd>;
	vref-supply = <&vrefbuf>;
	status = "okay";
	adc1: adc@0 {
		status = "okay";
		/*
		 * Type-C USB_PWR_CC1 & USB_PWR_CC2 on in18 & in19.
		 * Use at least 5 * RC time, e.g. 5 * (Rp + Rd) * C:
		 * 5 * (56 + 47kOhms) * 5pF => 2.5us.
		 * Use arbitrary margin here (e.g. 5us).
		 */
		channel@18 {
			reg = <18>;
			st,min-sample-time-ns = <5000>;
		};
		channel@19 {
			reg = <19>;
			st,min-sample-time-ns = <5000>;
		};
	};
	adc2: adc@100 {
		status = "okay";
		/* USB Type-C CC1 & CC2 */
		channel@18 {
			reg = <18>;
			st,min-sample-time-ns = <5000>;
		};
		channel@19 {
			reg = <19>;
			st,min-sample-time-ns = <5000>;
		};
	};
};

&cec {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&cec_pins_b>;
	pinctrl-1 = <&cec_sleep_pins_b>;
	status = "okay";
};

&crc1 {
	status = "okay";
};

&dts {
	status = "okay";
};

&ethernet0 {
	status = "okay";
	pinctrl-0 = <&ethernet0_rgmii_pins_a>;
	pinctrl-1 = <&ethernet0_rgmii_sleep_pins_a>;
	pinctrl-names = "default", "sleep";
	phy-mode = "rgmii-id";
	max-speed = <1000>;
	phy-handle = <&phy0>;

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "snps,dwmac-mdio";
		phy0: ethernet-phy@0 {
			reg = <0>;
		};
	};
};

&hash1 {
	status = "okay";
};

&i2c1 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c1_pins_a>;
	pinctrl-1 = <&i2c1_sleep_pins_a>;
	i2c-scl-rising-time-ns = <100>;
	i2c-scl-falling-time-ns = <7>;
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;

	hdmi-transmitter@39 {
		compatible = "sil,sii9022";
		reg = <0x39>;
		iovcc-supply = <&v3v3_hdmi>;
		cvcc12-supply = <&v1v2_hdmi>;
		reset-gpios = <&gpioa 10 GPIO_ACTIVE_LOW>;
		interrupts = <1 IRQ_TYPE_EDGE_FALLING>;
		interrupt-parent = <&gpiog>;
		#sound-dai-cells = <0>;
		status = "okay";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				sii9022_in: endpoint {
					remote-endpoint = <&ltdc_ep0_out>;
				};
			};

			port@3 {
				reg = <3>;
				sii9022_tx_endpoint: endpoint {
					remote-endpoint = <&i2s2_endpoint>;
				};
			};
		};
	};

	cs42l51: cs42l51@4a {
		compatible = "cirrus,cs42l51";
		reg = <0x4a>;
		#sound-dai-cells = <0>;
		VL-supply = <&v3v3>;
		VD-supply = <&v1v8_audio>;
		VA-supply = <&v1v8_audio>;
		VAHP-supply = <&v1v8_audio>;
		reset-gpios = <&gpiog 9 GPIO_ACTIVE_LOW>;
		clocks = <&sai2a>;
		clock-names = "MCLK";
		status = "okay";

		cs42l51_port: port {
			#address-cells = <1>;
			#size-cells = <0>;

			cs42l51_tx_endpoint: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&sai2a_endpoint>;
				frame-master = <&cs42l51_tx_endpoint>;
				bitclock-master = <&cs42l51_tx_endpoint>;
			};

			cs42l51_rx_endpoint: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&sai2b_endpoint>;
				frame-master = <&cs42l51_rx_endpoint>;
				bitclock-master = <&cs42l51_rx_endpoint>;
			};
		};
	};
};

&i2c4 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c4_pins_a>;
	pinctrl-1 = <&i2c4_sleep_pins_a>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	clock-frequency = <400000>;
	status = "okay";
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	stusb1600@28 {
		compatible = "st,stusb1600";
		reg = <0x28>;
		interrupts = <11 IRQ_TYPE_LEVEL_LOW>;
		interrupt-parent = <&gpioi>;
		pinctrl-names = "default";
		pinctrl-0 = <&stusb1600_pins_a>;
		status = "okay";
		vdd-supply = <&vin>;

		connector {
			compatible = "usb-c-connector";
			label = "USB-C";
			power-role = "dual";
			typec-power-opmode = "default";

			port {
				con_usbotg_hs_ep: endpoint {
					remote-endpoint = <&usbotg_hs_ep>;
				};
			};
		};
	};

	pmic: stpmic@33 {
		compatible = "st,stpmic1";
		reg = <0x33>;
		interrupts-extended = <&gpioa 0 IRQ_TYPE_EDGE_FALLING>;
		interrupt-controller;
		#interrupt-cells = <2>;
		status = "okay";

		regulators {
			compatible = "st,stpmic1-regulators";
			buck1-supply = <&vin>;
			buck2-supply = <&vin>;
			buck3-supply = <&vin>;
			buck4-supply = <&vin>;
			ldo1-supply = <&v3v3>;
			ldo2-supply = <&vin>;
			ldo3-supply = <&vdd_ddr>;
			ldo4-supply = <&vin>;
			ldo5-supply = <&vin>;
			ldo6-supply = <&v3v3>;
			vref_ddr-supply = <&vin>;
			boost-supply = <&vin>;
			pwr_sw1-supply = <&bst_out>;
			pwr_sw2-supply = <&bst_out>;

			vddcore: buck1 {
				regulator-name = "vddcore";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd_ddr: buck2 {
				regulator-name = "vdd_ddr";
				regulator-min-microvolt = <1350000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd: buck3 {
				regulator-name = "vdd";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				st,mask-reset;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			v3v3: buck4 {
				regulator-name = "v3v3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-over-current-protection;
				regulator-initial-mode = <0>;
			};

			v1v8_audio: ldo1 {
				regulator-name = "v1v8_audio";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				interrupts = <IT_CURLIM_LDO1 0>;
			};

			v3v3_hdmi: ldo2 {
				regulator-name = "v3v3_hdmi";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				interrupts = <IT_CURLIM_LDO2 0>;
			};

			vtt_ddr: ldo3 {
				regulator-name = "vtt_ddr";
				regulator-min-microvolt = <500000>;
				regulator-max-microvolt = <750000>;
				regulator-always-on;
				regulator-over-current-protection;
			};

			vdd_usb: ldo4 {
				regulator-name = "vdd_usb";
				interrupts = <IT_CURLIM_LDO4 0>;
			};

			vdda: ldo5 {
				regulator-name = "vdda";
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				interrupts = <IT_CURLIM_LDO5 0>;
				regulator-boot-on;
			};

			v1v2_hdmi: ldo6 {
				regulator-name = "v1v2_hdmi";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				interrupts = <IT_CURLIM_LDO6 0>;
			};

			vref_ddr: vref_ddr {
				regulator-name = "vref_ddr";
				regulator-always-on;
			};

			bst_out: boost {
				regulator-name = "bst_out";
				interrupts = <IT_OCP_BOOST 0>;
			};

			vbus_otg: pwr_sw1 {
				regulator-name = "vbus_otg";
				interrupts = <IT_OCP_OTG 0>;
			};

			vbus_sw: pwr_sw2 {
				regulator-name = "vbus_sw";
				interrupts = <IT_OCP_SWOUT 0>;
				regulator-active-discharge = <1>;
			};
		};

		onkey {
			compatible = "st,stpmic1-onkey";
			interrupts = <IT_PONKEY_F 0>, <IT_PONKEY_R 0>;
			interrupt-names = "onkey-falling", "onkey-rising";
			power-off-time-sec = <10>;
			status = "okay";
		};

		watchdog {
			compatible = "st,stpmic1-wdt";
			status = "disabled";
		};
	};
};

&i2c5 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c5_pins_a>;
	pinctrl-1 = <&i2c5_sleep_pins_a>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	clock-frequency = <400000>;
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
};

&i2s2 {
	clocks = <&rcc SPI2>, <&rcc SPI2_K>, <&rcc PLL3_Q>, <&rcc PLL3_R>;
	clock-names = "pclk", "i2sclk", "x8k", "x11k";
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2s2_pins_a>;
	pinctrl-1 = <&i2s2_sleep_pins_a>;
	status = "okay";

	i2s2_port: port {
		i2s2_endpoint: endpoint {
			remote-endpoint = <&sii9022_tx_endpoint>;
			dai-format = "i2s";
			mclk-fs = <256>;
		};
	};
};

&ipcc {
	status = "okay";
};

&iwdg2 {
	timeout-sec = <32>;
	status = "okay";
};

&ltdc {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&ltdc_pins_a>;
	pinctrl-1 = <&ltdc_sleep_pins_a>;
	status = "okay";

	port {
		ltdc_ep0_out: endpoint {
			remote-endpoint = <&sii9022_in>;
		};
	};
};

&m4_rproc {
	memory-region = <&retram>, <&mcuram>, <&mcuram2>, <&vdev0vring0>,
			<&vdev0vring1>, <&vdev0buffer>;
	mboxes = <&ipcc 0>, <&ipcc 1>, <&ipcc 2>, <&ipcc 3>;
	mbox-names = "vq0", "vq1", "shutdown", "detach";
	interrupt-parent = <&exti>;
	interrupts = <68 1>;
	status = "okay";
};

&pwr_regulators {
	vdd-supply = <&vdd>;
	vdd_3v3_usbfs-supply = <&vdd_usb>;
};

&rng1 {
	status = "okay";
};

&rtc {
	status = "okay";
};

&sai2 {
	clocks = <&rcc SAI2>, <&rcc PLL3_Q>, <&rcc PLL3_R>;
	clock-names = "pclk", "x8k", "x11k";
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&sai2a_pins_a>, <&sai2b_pins_b>;
	pinctrl-1 = <&sai2a_sleep_pins_a>, <&sai2b_sleep_pins_b>;
	status = "okay";

	sai2a: audio-controller@4400b004 {
		#clock-cells = <0>;
		dma-names = "tx";
		status = "okay";

		sai2a_port: port {
			sai2a_endpoint: endpoint {
				remote-endpoint = <&cs42l51_tx_endpoint>;
				dai-format = "i2s";
				mclk-fs = <256>;
				dai-tdm-slot-num = <2>;
				dai-tdm-slot-width = <32>;
			};
		};
	};

	sai2b: audio-controller@4400b024 {
		dma-names = "rx";
		st,sync = <&sai2a 2>;
		clocks = <&rcc SAI2_K>, <&sai2a>;
		clock-names = "sai_ck", "MCLK";
		status = "okay";

		sai2b_port: port {
			sai2b_endpoint: endpoint {
				remote-endpoint = <&cs42l51_rx_endpoint>;
				dai-format = "i2s";
				mclk-fs = <256>;
				dai-tdm-slot-num = <2>;
				dai-tdm-slot-width = <32>;
			};
		};
	};
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a>;
	cd-gpios = <&gpiob 7 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
	disable-wp;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&v3v3>;
	status = "okay";
};

&sdmmc3 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc3_b4_pins_a>;
	pinctrl-1 = <&sdmmc3_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc3_b4_sleep_pins_a>;
	broken-cd;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&v3v3>;
	status = "disabled";
};

&timers1 {
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm1_pins_a>;
		pinctrl-1 = <&pwm1_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@0 {
		status = "okay";
	};
};

&timers3 {
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm3_pins_a>;
		pinctrl-1 = <&pwm3_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@2 {
		status = "okay";
	};
};

&timers4 {
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm4_pins_a &pwm4_pins_b>;
		pinctrl-1 = <&pwm4_sleep_pins_a &pwm4_sleep_pins_b>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@3 {
		status = "okay";
	};
};

&timers5 {
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm5_pins_a>;
		pinctrl-1 = <&pwm5_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@4 {
		status = "okay";
	};
};

&timers6 {
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	timer@5 {
		status = "okay";
	};
};

&timers12 {
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm12_pins_a>;
		pinctrl-1 = <&pwm12_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@11 {
		status = "okay";
	};
};

&uart4 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&uart4_pins_a>;
	pinctrl-1 = <&uart4_sleep_pins_a>;
	pinctrl-2 = <&uart4_idle_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&uart7 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&uart7_pins_c>;
	pinctrl-1 = <&uart7_sleep_pins_c>;
	pinctrl-2 = <&uart7_idle_pins_c>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
};

&usart3 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&usart3_pins_c>;
	pinctrl-1 = <&usart3_sleep_pins_c>;
	pinctrl-2 = <&usart3_idle_pins_c>;
	uart-has-rtscts;
	status = "disabled";
};

&usbh_ehci {
	phys = <&usbphyc_port0>;
	status = "okay";
	#address-cells = <1>;
	#size-cells = <0>;
	/* onboard HUB */
	hub@1 {
		compatible = "usb424,2514";
		reg = <1>;
		vdd-supply = <&v3v3>;
	};
};

&usbotg_hs {
	phys = <&usbphyc_port1 0>;
	phy-names = "usb2-phy";
	usb-role-switch;
	status = "okay";

	port {
		usbotg_hs_ep: endpoint {
			remote-endpoint = <&con_usbotg_hs_ep>;
		};
	};
};

&usbphyc {
	status = "okay";
};

&usbphyc_port0 {
	phy-supply = <&vdd_usb>;
	st,tune-hs-dc-level = <2>;
	st,enable-fs-rftime-tuning;
	st,enable-hs-rftime-reduction;
	st,trim-hs-current = <15>;
	st,trim-hs-impedance = <1>;
	st,tune-squelch-level = <3>;
	st,tune-hs-rx-offset = <2>;
	st,no-lsfs-sc;
};

&usbphyc_port1 {
	phy-supply = <&vdd_usb>;
	st,tune-hs-dc-level = <2>;
	st,enable-fs-rftime-tuning;
	st,enable-hs-rftime-reduction;
	st,trim-hs-current = <15>;
	st,trim-hs-impedance = <1>;
	st,tune-squelch-level = <3>;
	st,tune-hs-rx-offset = <2>;
	st,no-lsfs-sc;
};

&vrefbuf {
	regulator-min-microvolt = <2500000>;
	regulator-max-microvolt = <2500000>;
	vdda-supply = <&vdd>;
	status = "okay";
};
