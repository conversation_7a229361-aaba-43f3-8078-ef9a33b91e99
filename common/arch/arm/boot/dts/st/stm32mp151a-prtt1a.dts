// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) Protonic Holland
 * Author: <PERSON> <<EMAIL>>
 */
/dts-v1/;

#include "stm32mp151a-prtt1l.dtsi"

/ {
	model = "Protonic PRTT1A";
	compatible = "prt,prtt1a", "st,stm32mp151";
};

&ethernet0 {
	phy-handle = <&phy0>;
};

&mdio0 {
	/* TI DP83TD510E */
	phy0: ethernet-phy@0 {
		compatible = "ethernet-phy-id2000.0181";
		reg = <0>;
		interrupts-extended = <&gpioa 4 IRQ_TYPE_LEVEL_LOW>;
		reset-gpios = <&gpioa 3 GPIO_ACTIVE_LOW>;
		reset-assert-us = <10>;
		reset-deassert-us = <35>;
	};
};

&pwm5_pins_a {
	pins {
		pinmux = <STM32_PINMUX('A', 0, AF2)>; /* TIM5_CH1 */
	};
};

&pwm5_sleep_pins_a {
	pins {
		pinmux = <STM32_PINMUX('A', 0, ANALOG)>; /* TIM5_CH1 */
	};
};

&timers5 {
	status = "okay";

	pwm {
		pinctrl-0 = <&pwm5_pins_a>;
		pinctrl-1 = <&pwm5_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
};
