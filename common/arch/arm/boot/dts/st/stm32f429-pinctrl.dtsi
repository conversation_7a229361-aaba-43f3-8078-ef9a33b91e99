/*
 * Copyright 2017 - <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include "stm32f4-pinctrl.dtsi"

&pinctrl {
	compatible = "st,stm32f429-pinctrl";

	gpioa: gpio@40020000 {
		gpio-ranges = <&pinctrl 0 0 16>;
	};

	gpiob: gpio@40020400 {
		gpio-ranges = <&pinctrl 0 16 16>;
	};

	gpioc: gpio@40020800 {
		gpio-ranges = <&pinctrl 0 32 16>;
	};

	gpiod: gpio@40020c00 {
		gpio-ranges = <&pinctrl 0 48 16>;
	};

	gpioe: gpio@40021000 {
		gpio-ranges = <&pinctrl 0 64 16>;
	};

	gpiof: gpio@40021400 {
		gpio-ranges = <&pinctrl 0 80 16>;
	};

	gpiog: gpio@40021800 {
		gpio-ranges = <&pinctrl 0 96 16>;
	};

	gpioh: gpio@40021c00 {
		gpio-ranges = <&pinctrl 0 112 16>;
	};

	gpioi: gpio@40022000 {
		gpio-ranges = <&pinctrl 0 128 16>;
	};

	gpioj: gpio@40022400 {
		gpio-ranges = <&pinctrl 0 144 16>;
	};

	gpiok: gpio@40022800 {
		gpio-ranges = <&pinctrl 0 160 8>;
	};
};
