// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * DTS file for all SPEAr1310 SoCs
 *
 * Copyright 2012 V<PERSON><PERSON> <<EMAIL>>
 */

/include/ "spear13xx.dtsi"

/ {
	compatible = "st,spear1310";

	ahb {
		spics: spics@e0700000 {
			compatible = "st,spear-spics-gpio";
			reg = <0xe0700000 0x1000>;
			st-spics,peripcfg-reg = <0x3b0>;
			st-spics,sw-enable-bit = <12>;
			st-spics,cs-value-bit = <11>;
			st-spics,cs-enable-mask = <3>;
			st-spics,cs-enable-shift = <8>;
			gpio-controller;
			#gpio-cells = <2>;
		};

		miphy0: miphy@eb800000 {
			compatible = "st,spear1310-miphy";
			reg = <0xeb800000 0x4000>;
			misc = <&misc>;
			phy-id = <0>;
			#phy-cells = <1>;
			status = "disabled";
		};

		miphy1: miphy@eb804000 {
			compatible = "st,spear1310-miphy";
			reg = <0xeb804000 0x4000>;
			misc = <&misc>;
			phy-id = <1>;
			#phy-cells = <1>;
			status = "disabled";
		};

		miphy2: miphy@eb808000 {
			compatible = "st,spear1310-miphy";
			reg = <0xeb808000 0x4000>;
			misc = <&misc>;
			phy-id = <2>;
			#phy-cells = <1>;
			status = "disabled";
		};

		ahci0: ahci@b1000000 {
			compatible = "snps,spear-ahci";
			reg = <0xb1000000 0x10000>;
			interrupts = <0 68 0x4>;
			phys = <&miphy0 0>;
			phy-names = "sata-phy";
			status = "disabled";
		};

		ahci1: ahci@b1800000 {
			compatible = "snps,spear-ahci";
			reg = <0xb1800000 0x10000>;
			interrupts = <0 69 0x4>;
			phys = <&miphy1 0>;
			phy-names = "sata-phy";
			status = "disabled";
		};

		ahci2: ahci@b4000000 {
			compatible = "snps,spear-ahci";
			reg = <0xb4000000 0x10000>;
			interrupts = <0 70 0x4>;
			phys = <&miphy2 0>;
			phy-names = "sata-phy";
			status = "disabled";
		};

		pcie0: pcie@b1000000 {
			compatible = "st,spear1340-pcie", "snps,dw-pcie";
			reg = <0xb1000000 0x4000>, <0x80000000 0x20000>;
			reg-names = "dbi", "config";
			interrupts = <0 68 0x4>;
			num-lanes = <1>;
			phys = <&miphy0 1>;
			phy-names = "pcie-phy";
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			ranges = <0x81000000 0 0	 0x80020000 0 0x00010000   /* downstream I/O */
				0x82000000 0 0x80030000 0xc0030000 0 0x0ffd0000>; /* non-prefetchable memory */
			bus-range = <0x00 0xff>;
			status = "disabled";
		};

		pcie1: pcie@b1800000 {
			compatible = "st,spear1340-pcie", "snps,dw-pcie";
			reg = <0xb1800000 0x4000>, <0x90000000 0x20000>;
			reg-names = "dbi", "config";
			interrupts = <0 69 0x4>;
			num-lanes = <1>;
			phys = <&miphy1 1>;
			phy-names = "pcie-phy";
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			ranges = <0x81000000 0 0  0x90020000 0 0x00010000   /* downstream I/O */
				0x82000000 0 0x90030000 0x90030000 0 0x0ffd0000>; /* non-prefetchable memory */
			bus-range = <0x00 0xff>;
			status = "disabled";
		};

		pcie2: pcie@b4000000 {
			compatible = "st,spear1340-pcie", "snps,dw-pcie";
			reg = <0xb4000000 0x4000>, <0xc0000000 0x20000>;
			reg-names = "dbi", "config";
			interrupts = <0 70 0x4>;
			num-lanes = <1>;
			phys = <&miphy2 1>;
			phy-names = "pcie-phy";
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			ranges = <0x81000000 0 0	 0xc0020000 0 0x00010000   /* downstream I/O */
				0x82000000 0 0xc0030000 0xc0030000 0 0x0ffd0000>; /* non-prefetchable memory */
			bus-range = <0x00 0xff>;
			status = "disabled";
		};

		gmac1: eth@5c400000 {
			compatible = "st,spear600-gmac";
			reg = <0x5c400000 0x8000>;
			interrupts = <0 95 0x4>;
			interrupt-names = "macirq";
			phy-mode = "mii";
			status = "disabled";
		};

		gmac2: eth@5c500000 {
			compatible = "st,spear600-gmac";
			reg = <0x5c500000 0x8000>;
			interrupts = <0 96 0x4>;
			interrupt-names = "macirq";
			phy-mode = "mii";
			status = "disabled";
		};

		gmac3: eth@5c600000 {
			compatible = "st,spear600-gmac";
			reg = <0x5c600000 0x8000>;
			interrupts = <0 97 0x4>;
			interrupt-names = "macirq";
			phy-mode = "rmii";
			status = "disabled";
		};

		gmac4: eth@5c700000 {
			compatible = "st,spear600-gmac";
			reg = <0x5c700000 0x8000>;
			interrupts = <0 98 0x4>;
			interrupt-names = "macirq";
			phy-mode = "rgmii";
			status = "disabled";
		};

		pinmux: pinmux@e0700000 {
			compatible = "st,spear1310-pinmux";
			reg = <0xe0700000 0x1000>;
			#gpio-range-cells = <3>;
		};

		apb {
			i2c1: i2c@5cd00000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,designware-i2c";
				reg = <0x5cd00000 0x1000>;
				interrupts = <0 87 0x4>;
				status = "disabled";
			};

			i2c2: i2c@5ce00000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,designware-i2c";
				reg = <0x5ce00000 0x1000>;
				interrupts = <0 88 0x4>;
				status = "disabled";
			};

			i2c3: i2c@5cf00000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,designware-i2c";
				reg = <0x5cf00000 0x1000>;
				interrupts = <0 89 0x4>;
				status = "disabled";
			};

			i2c4: i2c@5d000000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,designware-i2c";
				reg = <0x5d000000 0x1000>;
				interrupts = <0 90 0x4>;
				status = "disabled";
			};

			i2c5: i2c@5d100000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,designware-i2c";
				reg = <0x5d100000 0x1000>;
				interrupts = <0 91 0x4>;
				status = "disabled";
			};

			i2c6: i2c@5d200000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,designware-i2c";
				reg = <0x5d200000 0x1000>;
				interrupts = <0 92 0x4>;
				status = "disabled";
			};

			i2c7: i2c@5d300000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,designware-i2c";
				reg = <0x5d300000 0x1000>;
				interrupts = <0 93 0x4>;
				status = "disabled";
			};

			spi1: spi@5d400000 {
				compatible = "arm,pl022", "arm,primecell";
				reg = <0x5d400000 0x1000>;
				interrupts = <0 99 0x4>;
				#address-cells = <1>;
				#size-cells = <0>;
				status = "disabled";
			};

			serial@5c800000 {
				compatible = "arm,pl011", "arm,primecell";
				reg = <0x5c800000 0x1000>;
				interrupts = <0 82 0x4>;
				status = "disabled";
			};

			serial@5c900000 {
				compatible = "arm,pl011", "arm,primecell";
				reg = <0x5c900000 0x1000>;
				interrupts = <0 83 0x4>;
				status = "disabled";
			};

			serial@5ca00000 {
				compatible = "arm,pl011", "arm,primecell";
				reg = <0x5ca00000 0x1000>;
				interrupts = <0 84 0x4>;
				status = "disabled";
			};

			serial@5cb00000 {
				compatible = "arm,pl011", "arm,primecell";
				reg = <0x5cb00000 0x1000>;
				interrupts = <0 85 0x4>;
				status = "disabled";
			};

			serial@5cc00000 {
				compatible = "arm,pl011", "arm,primecell";
				reg = <0x5cc00000 0x1000>;
				interrupts = <0 86 0x4>;
				status = "disabled";
			};

			thermal@e07008c4 {
				st,thermal-flags = <0x7000>;
			};

			gpiopinctrl: gpio@d8400000 {
				compatible = "st,spear-plgpio";
				reg = <0xd8400000 0x1000>;
				interrupts = <0 100 0x4>;
				#interrupt-cells = <1>;
				interrupt-controller;
				gpio-controller;
				#gpio-cells = <2>;
				gpio-ranges = <&pinmux 0 0 246>;
				status = "disabled";

				st-plgpio,ngpio = <246>;
				st-plgpio,enb-reg = <0xd0>;
				st-plgpio,wdata-reg = <0x90>;
				st-plgpio,dir-reg = <0xb0>;
				st-plgpio,ie-reg = <0x30>;
				st-plgpio,rdata-reg = <0x70>;
				st-plgpio,mis-reg = <0x10>;
				st-plgpio,eit-reg = <0x50>;
			};
		};
	};
};
