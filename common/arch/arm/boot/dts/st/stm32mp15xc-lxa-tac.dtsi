// SPDX-License-Identifier: (GPL-2.0-or-later OR BSD-3-Clause)
/*
 * Copyright (C) 2020 STMicroelectronics - All Rights Reserved
 * Copyright (C) 2021 <PERSON><PERSON><PERSON>, Pengutronix
 * Copyright (C) 2023 <PERSON>, Pengutronix
 */

#include "stm32mp15xc.dtsi"
#include "stm32mp15xx-osd32.dtsi"
#include "stm32mp15xxac-pinctrl.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/leds/common.h>
#include <dt-bindings/pwm/pwm.h>

/ {
	aliases {
		ethernet0 = &ethernet0;
		ethernet1 = &port_uplink;
		ethernet2 = &port_dut;
		mmc1 = &sdmmc2;
		serial0 = &uart4;
		serial1 = &usart3;
	};

	chosen {
		stdout-path = &uart4;
	};

	led-controller-0 {
		compatible = "gpio-leds";

		led-0 {
			label = "tac:green:user1";
			gpios = <&gpiof 10 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
		};

		led-1 {
			label = "tac:green:user2";
			gpios = <&gpiog 7 GPIO_ACTIVE_HIGH>;
		};

		led-2 {
			label = "tac:green:statusdut";
			gpios = <&gpioa 13 GPIO_ACTIVE_LOW>;
		};

		/* led-3 and led-4 are internally connected antiparallel to one
		 * another inside the ethernet jack like this:
		 * GPIOA14 ---+---|led-3|>--+--- GPIOD15
		 *            +--<|led-4|---+
		 * E.g. only one of the LEDs can be illuminated at a time while
		 * the other output must be driven low.
		 * This should likely be implemented using a multi color LED
		 * driver for antiparallel LEDs.
		 */
		led-3 {
			label = "tac:green:statuslab";
			gpios = <&gpioa 14 GPIO_ACTIVE_HIGH>;
		};

		led-4 {
			label = "tac:orange:statuslab";
			gpios = <&gpiod 15 GPIO_ACTIVE_HIGH>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		button-lower {
			label = "USER_BTN2";
			linux,code = <KEY_ESC>;
			gpios = <&gpioe 7 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
		};

		button-upper {
			label = "USER_BTN";
			linux,code = <KEY_HOME>;
			gpios = <&gpioi 11 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
		};
	};

	/* supplied by either barrel connector or PoE */
	reg_12v: regulator-12v {
		compatible = "regulator-fixed";
		regulator-name = "12V";
		regulator-min-microvolt = <12000000>;
		regulator-max-microvolt = <12000000>;
		regulator-always-on;
	};

	reg_5v: regulator-5v {
		compatible = "regulator-fixed";
		regulator-name = "5V";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		vin-supply = <&reg_12v>;
	};

	reg_1v2: regulator-1v2 {
		compatible = "regulator-fixed";
		regulator-name = "1V2";
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		regulator-always-on;
		vin-supply = <&reg_5v>;
	};

	reg_pb_5v: regulator-pb-5v {
		compatible = "regulator-fixed";
		regulator-name = "5V_POWERBOARD";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		vin-supply = <&reg_5v>;
	};

	reg_pb_3v3: regulator-pb-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "3V3_POWERBOARD";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		vin-supply = <&reg_pb_5v>;
	};

	output-iobus-12v {
		compatible = "regulator-output";
		vout-supply = <&reg_iobus_12v>;
	};

	output-vuart {
		compatible = "regulator-output";
		vout-supply = <&v3v3_hdmi>;
	};
};

baseboard_eeprom: &sip_eeprom {
};

&adc {
	pinctrl-names = "default";
	pinctrl-0 = <&adc1_ain_pins_a>;
	vdd-supply = <&vdd>;
	vdda-supply = <&vdda>;
	vref-supply = <&vrefbuf>;
	status = "okay";

	adc1: adc@0 {
		st,adc-channels = <0 1 2 5 9 10 13 15>;
		st,min-sample-time-nsecs = <5000>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";

		channel@0 {
			reg = <0>;
			label = "HOST_2_CURR_FB";
		};

		channel@1 {
			reg = <1>;
			label = "HOST_3_CURR_FB";
		};

		channel@2 {
			reg = <2>;
			label = "OUT_0_FB";
		};

		channel@5 {
			reg = <5>;
			label = "IOBUS_CURR_FB";
		};

		channel@9 {
			reg = <9>;
			label = "IOBUS_VOLT_FB";
		};

		channel@10 {
			reg = <10>;
			label = "OUT_1_FB";
		};

		channel@13 {
			reg = <13>;
			label = "HOST_CURR_FB";
		};

		channel@15 {
			reg = <15>;
			label = "HOST_1_CURR_FB";
		};
	};

	adc2: adc@100 {
		st,adc-channels = <12>;
		st,min-sample-time-nsecs = <500000>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";

		channel@12 {
			reg = <12>;
			label = "TEMP_INTERNAL";
		};
	};
};

&crc1 {
	status = "okay";
};

&cryp1 {
	status = "okay";
};

&dts {
	status = "okay";
};

&ethernet0 {
	assigned-clocks = <&rcc ETHCK_K>, <&rcc PLL4_P>;
	assigned-clock-parents = <&rcc PLL4_P>;
	assigned-clock-rates = <125000000>; /* Clock PLL4 to 750Mhz in ATF */

	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&ethernet0_rgmii_pins_e>;
	pinctrl-1 = <&ethernet0_rgmii_sleep_pins_e>;

	st,eth-clk-sel;
	phy-mode = "rgmii-id";

	status = "okay";

	fixed-link {
		speed = <1000>;
		full-duplex;
	};
};

&ethernet0_rgmii_pins_e {
	pins1 {
		/* Reduce EMI emission by reducing RGMII drive strength */
		slew-rate = <1>;
	};
};

&gpiob {
	gpio-line-names = "", "", "", "", "", /*  0 */
	"", "USB_RESET", "", "", "",          /*  5 */
	"", "", "", "", "",                   /* 10 */
	"";                                   /* 15 */
};

&gpiod {
	gpio-line-names = "", "", "", "", "TP38", /*  0 */
	"TP39", "", "", "TP41", "TP42",           /*  5 */
	"OLED_DC", "", "", "ETH_CS", "",          /* 10 */
	"ETH_LAB_LEDRN";                          /* 15 */
};

&gpioe {
	gpio-line-names = "TP35", "", "", "", "CAN_1_120R", /*  0 */
	"", "", "USER_BTN2", "TP48", "UART_TX_EN",          /*  5 */
	"UART_RX_EN", "TP24", "", "TP25", "TP26",           /* 10 */
	"TP27";                                             /* 15 */
};

&gpiof {
	gpio-line-names = "TP36", "TP37", "", "", "OLED_CS", /*  0 */
	"", "", "", "", "",                                  /*  5 */
	"USER_LED1", "", "STACK_CS0", "", "",                /* 10 */
	"";                                                  /* 15 */
};

&gpiog {
	gpio-line-names = "ETH_RESET", "", "", "", "",               /*  0 */
	"IOBUS_FLT_FB", "", "USER_LED2", "ETH1_PPS_A", "CAN_0_120R", /*  5 */
	"TP49", "", "", "", "",                                      /* 10 */
	"";                                                          /* 15 */
};

&gpioh {
	gpio-line-names = "", "", "OUT_1", "OUT_0", "OLED_RESET", /*  0 */
	"", "", "", "", "",                                       /*  5 */
	"ETH1_PPS_B", "ETH_GPIO2", "", "IOBUS_PWR_EN", "",        /* 10 */
	"TP33";                                                   /* 15 */
};

&gpioi {
	gpio-line-names = "TIM_RTS", "", "", "", "DEVICE_DATA_EN", /*  0 */
	"", "", "", "ETH_WOL", "TP43",                             /*  5 */
	"", "USER_BTN";                                            /* 10 */
};

&gpioz {
	gpio-line-names = "HWID0", "HWID1", "HWID2", "HWID3", "", /*  0 */
	"", "HWID4", "HWID5";                                     /*  5 */
};

&hash1 {
	status = "okay";
};

&i2c1 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c1_pins_b>;
	pinctrl-1 = <&i2c1_sleep_pins_b>;
	status = "okay";

	powerboard_eeprom: eeprom@50 {
		compatible = "atmel,24c02";
		reg = <0x50>;
		vcc-supply = <&v3v3>;
	};

	temperature-sensor@48 {
		compatible = "national,lm75a";
		reg = <0x48>;
		status = "disabled";
	};
};

&i2c5 {
	/delete-property/dmas;
	/delete-property/dma-names;

	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c5_pins_b>;
	pinctrl-1 = <&i2c5_sleep_pins_b>;

	status = "okay";

	usbhub: usbhub@2c {
		compatible ="microchip,usb2514b";
		reg = <0x2c>;
		vdd-supply = <&v3v3>;
		reset-gpios = <&gpiob 6 GPIO_ACTIVE_LOW>;
	};
};

&iwdg2 {
	timeout-sec = <8>;
	status = "okay";
};

&m_can1 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&m_can1_pins_b>;
	pinctrl-1 = <&m_can1_sleep_pins_b>;
	status = "okay";
};

&m_can2 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&m_can2_pins_a>;
	pinctrl-1 = <&m_can2_sleep_pins_a>;
	status = "okay";
};

&pmic {
	regulators {
		buck1-supply = <&reg_5v>;	/* VIN */
		buck2-supply = <&reg_5v>;	/* VIN */
		buck3-supply = <&reg_5v>;	/* VIN */
		buck4-supply = <&reg_5v>;	/* VIN */
		ldo2-supply = <&reg_5v>;	/* PMIC_LDO25IN */
		ldo4-supply = <&reg_5v>;	/* VIN */
		ldo5-supply = <&reg_5v>;	/* PMIC_LDO25IN */
		vref_ddr-supply = <&reg_5v>;	/* VIN */
		boost-supply = <&reg_5v>;	/* PMIC_BSTIN */
		pwr_sw2-supply = <&bst_out>;	/* PMIC_SWIN */
	};
};

&pwr_regulators {
	vdd-supply = <&vdd>;
	vdd_3v3_usbfs-supply = <&vdd_usb>;
};

&rtc {
	status = "okay";
};

&sdmmc2 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc2_b4_pins_a &sdmmc2_d47_pins_b>;
	pinctrl-1 = <&sdmmc2_b4_od_pins_a &sdmmc2_d47_pins_b>;
	pinctrl-2 = <&sdmmc2_b4_sleep_pins_a &sdmmc2_d47_sleep_pins_b>;
	vmmc-supply = <&v3v3>;

	bus-width = <8>;
	mmc-ddr-3_3v;
	no-1-8-v;
	non-removable;
	no-sd;
	no-sdio;
	st,neg-edge;

	status = "okay";
};

&spi2 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi2_pins_c>;
	cs-gpios = <&gpiof 12 GPIO_ACTIVE_LOW>;
	status = "okay";
};

&spi4 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi4_pins_a>;
	cs-gpios = <&gpiof 4 GPIO_ACTIVE_LOW>;
	status = "okay";

	lcd: display@0 {
		compatible = "shineworld,lh133k", "panel-mipi-dbi-spi";
		reg = <0>;
		power-supply = <&v3v3>;
		io-supply = <&v3v3>;
		backlight = <&backlight>;
		dc-gpios = <&gpiod 10 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpioh 4 GPIO_ACTIVE_HIGH>;
		spi-3wire;
		spi-max-frequency = <32000000>;

		width-mm = <23>;
		height-mm = <23>;
		rotation = <180>;

		panel-timing {
			hactive = <240>;
			vactive = <240>;
			hback-porch = <0>;
			vback-porch = <0>;

			clock-frequency = <0>;
			hfront-porch = <0>;
			hsync-len = <0>;
			vfront-porch = <0>;
			vsync-len = <0>;
		};
	};
};

&spi5 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi5_pins_a>;

	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	cs-gpios = <&gpiod 13 GPIO_ACTIVE_LOW>;

	status = "okay";

	switch: switch@0 {
		compatible = "microchip,ksz9563";
		reg = <0>;

		reset-gpios = <&gpiog 0 GPIO_ACTIVE_LOW>;
		spi-max-frequency = <44000000>;

		interrupt-parent = <&gpioa>;
		interrupts = <6 IRQ_TYPE_EDGE_RISING>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;
			port_dut: port@0 {
				reg = <0>;
				label = "dut";
			};

			port_uplink: port@1 {
				reg = <1>;
				label = "uplink";
			};

			port_cpu: port@2 {
				reg = <2>;
				label = "cpu";

				ethernet = <&ethernet0>;

				phy-mode = "rgmii-id";
				rx-internal-delay-ps = <2000>;
				tx-internal-delay-ps = <2000>;

				fixed-link {
					speed = <1000>;
					full-duplex;
				};
			};
		};
	};
};

&timers2 {
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	status = "okay";

	timer@1 {
		status = "okay";
	};
};

&timers3 {
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	status = "okay";

	timer@2 {
		status = "okay";
	};
};

&timers4 {
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	status = "okay";

	timer@3 {
		status = "okay";
	};
};

&uart4 {
	label = "debug";

	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pins_a>;

	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	status = "okay";
};

&usart3 {
	label = "dut";
	uart-has-rtscts;

	pinctrl-names = "default";
	pinctrl-0 = <&usart3_pins_f>;

	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	status = "okay";
};

&usbh_ehci {
	phys = <&usbphyc_port0>;
	phy-names = "usb";

	status = "okay";
};

&usbotg_hs {
	phys = <&usbphyc_port1 0>;
	phy-names = "usb2-phy";

	vusb_d-supply = <&vdd_usb>;
	vusb_a-supply = <&reg18>;

	dr_mode = "peripheral";

	status = "okay";
};

&usbphyc {
	status = "okay";
};

&usbphyc_port0 {
	phy-supply = <&vdd_usb>;
};

&usbphyc_port1 {
	phy-supply = <&vdd_usb>;
};

&v3v3_hdmi {
	/delete-property/regulator-always-on;
};

&vrefbuf {
	regulator-min-microvolt = <2500000>;
	regulator-max-microvolt = <2500000>;
	vdda-supply = <&vdda>;

	status = "okay";
};
