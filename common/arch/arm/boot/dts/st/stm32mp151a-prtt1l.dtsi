// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) Protonic Holland
 * Author: <PERSON> <<EMAIL>>
 */
/dts-v1/;

#include "stm32mp151.dtsi"
#include "stm32mp15-pinctrl.dtsi"
#include "stm32mp15xxad-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/leds/common.h>

/ {
	aliases {
		ethernet0 = &ethernet0;
		mdio-gpio0 = &mdio0;
		serial0 = &uart4;
	};

	led-controller-0 {
		compatible = "gpio-leds";

		led-0 {
			color = <LED_COLOR_ID_RED>;
			function = LED_FUNCTION_INDICATOR;
			gpios = <&gpioa 13 GPIO_ACTIVE_LOW>;
		};

		led-1 {
			color = <LED_COLOR_ID_GREEN>;
			function = LED_FUNCTION_INDICATOR;
			gpios = <&gpioa 14 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "heartbeat";
		};
	};


	/* DP83TD510E PHYs have max MDC rate of 1.75MHz. Since we can't reduce
	 * stmmac MDC clock without reducing system bus rate, we need to use
	 * gpio based MDIO bus.
	 */
	mdio0: mdio {
		compatible = "virtual,mdio-gpio";
		#address-cells = <1>;
		#size-cells = <0>;
		gpios = <&gpioc 1 GPIO_ACTIVE_HIGH
			 &gpioa 2 GPIO_ACTIVE_HIGH>;
	};

	reg_3v3: regulator-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};
};

&dts {
	status = "okay";
};

&ethernet0 {
	pinctrl-0 = <&ethernet0_rmii_pins_a>;
	pinctrl-1 = <&ethernet0_rmii_sleep_pins_a>;
	pinctrl-names = "default", "sleep";
	phy-mode = "rmii";
	status = "okay";
};

&ethernet0_rmii_pins_a {
	pins1 {
		pinmux = <STM32_PINMUX('B', 12, AF11)>, /* ETH1_RMII_TXD0 */
			 <STM32_PINMUX('B', 13, AF11)>, /* ETH1_RMII_TXD1 */
			 <STM32_PINMUX('B', 11, AF11)>; /* ETH1_RMII_TX_EN */
	};
	pins2 {
		pinmux = <STM32_PINMUX('C', 4, AF11)>,  /* ETH1_RMII_RXD0 */
			 <STM32_PINMUX('C', 5, AF11)>,  /* ETH1_RMII_RXD1 */
			 <STM32_PINMUX('A', 1, AF11)>,  /* ETH1_RMII_REF_CLK input */
			 <STM32_PINMUX('A', 7, AF11)>;  /* ETH1_RMII_CRS_DV */
	};
};

&ethernet0_rmii_sleep_pins_a {
	pins1 {
		pinmux = <STM32_PINMUX('B', 12, ANALOG)>, /* ETH1_RMII_TXD0 */
			 <STM32_PINMUX('B', 13, ANALOG)>, /* ETH1_RMII_TXD1 */
			 <STM32_PINMUX('B', 11, ANALOG)>, /* ETH1_RMII_TX_EN */
			 <STM32_PINMUX('C', 4, ANALOG)>,  /* ETH1_RMII_RXD0 */
			 <STM32_PINMUX('C', 5, ANALOG)>,  /* ETH1_RMII_RXD1 */
			 <STM32_PINMUX('A', 1, ANALOG)>,  /* ETH1_RMII_REF_CLK */
			 <STM32_PINMUX('A', 7, ANALOG)>;  /* ETH1_RMII_CRS_DV */
	};
};

&iwdg2 {
	status = "okay";
};

&qspi {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&qspi_clk_pins_a
		     &qspi_bk1_pins_a
		     &qspi_cs1_pins_a>;
	pinctrl-1 = <&qspi_clk_sleep_pins_a
		     &qspi_bk1_sleep_pins_a
		     &qspi_cs1_sleep_pins_a>;
	reg = <0x58003000 0x1000>, <0x70000000 0x4000000>;
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	flash@0 {
		compatible = "spi-nand";
		reg = <0>;
		spi-rx-bus-width = <4>;
		spi-max-frequency = <104000000>;
		#address-cells = <1>;
		#size-cells = <1>;
	};
};

&qspi_bk1_pins_a {
	pins1 {
		bias-pull-up;
		drive-push-pull;
		slew-rate = <1>;
	};
};

&rng1 {
	status = "okay";
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a>;
	broken-cd;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&reg_3v3>;
	vqmmc-supply = <&reg_3v3>;
	status = "okay";
};

&sdmmc1_b4_od_pins_a {
	pins1 {
		bias-pull-up;
	};
	pins2 {
		bias-pull-up;
	};
};

&sdmmc1_b4_pins_a {
	pins1 {
		bias-pull-up;
	};
	pins2 {
		bias-pull-up;
	};
};

&uart4 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&uart4_pins_a>;
	pinctrl-1 = <&uart4_sleep_pins_a>;
	pinctrl-2 = <&uart4_idle_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&uart4_idle_pins_a {
	pins1 {
		pinmux = <STM32_PINMUX('B', 9, ANALOG)>; /* UART4_TX */
	};
	pins2 {
		pinmux = <STM32_PINMUX('B', 2, AF8)>; /* UART4_RX */
		bias-pull-up;
	};
};

&uart4_pins_a {
	pins1 {
		pinmux = <STM32_PINMUX('B', 9, AF8)>; /* UART4_TX */
		bias-disable;
		drive-push-pull;
		slew-rate = <0>;
	};
	pins2 {
		pinmux = <STM32_PINMUX('B', 2, AF8)>; /* UART4_RX */
		bias-pull-up;
	};
};

&uart4_sleep_pins_a {
	pins {
		pinmux = <STM32_PINMUX('B', 9, ANALOG)>, /* UART4_TX */
			<STM32_PINMUX('B', 2, ANALOG)>; /* UART4_RX */
	};
};

&usbh_ehci {
	phys = <&usbphyc_port0>;
	phy-names = "usb";
	status = "okay";
};

&usbotg_hs {
	dr_mode = "host";
	pinctrl-0 = <&usbotg_hs_pins_a>;
	pinctrl-names = "default";
	phys = <&usbphyc_port1 0>;
	phy-names = "usb2-phy";
	status = "okay";
};

&usbphyc {
	status = "okay";
};

&usbphyc_port0 {
	phy-supply = <&reg_3v3>;
};

&usbphyc_port1 {
	phy-supply = <&reg_3v3>;
};
