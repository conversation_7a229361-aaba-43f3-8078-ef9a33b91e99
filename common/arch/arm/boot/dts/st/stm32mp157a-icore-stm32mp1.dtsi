// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (c) STMicroelectronics 2019 - All Rights Reserved
 * Copyright (c) 2020 Engicam srl
 * Copyright (c) 2020 Amarula Solutions(India)
 */

/ {
	compatible = "engicam,icore-stm32mp1", "st,stm32mp157";

	memory@c0000000 {
		device_type = "memory";
		reg = <0xc0000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		mcuram2: mcuram2@10000000 {
			compatible = "shared-dma-pool";
			reg = <0x10000000 0x40000>;
			no-map;
		};

		vdev0vring0: vdev0vring0@10040000 {
			compatible = "shared-dma-pool";
			reg = <0x10040000 0x1000>;
			no-map;
		};

		vdev0vring1: vdev0vring1@10041000 {
			compatible = "shared-dma-pool";
			reg = <0x10041000 0x1000>;
			no-map;
		};

		vdev0buffer: vdev0buffer@10042000 {
			compatible = "shared-dma-pool";
			reg = <0x10042000 0x4000>;
			no-map;
		};

		mcuram: mcuram@30000000 {
			compatible = "shared-dma-pool";
			reg = <0x30000000 0x40000>;
			no-map;
		};

		retram: retram@38000000 {
			compatible = "shared-dma-pool";
			reg = <0x38000000 0x10000>;
			no-map;
		};
	};

	vddcore: regulator-vddcore {
		compatible = "regulator-fixed";
		regulator-name = "vddcore";
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		regulator-always-on;
	};

	vdd: regulator-vdd {
		compatible = "regulator-fixed";
		regulator-name = "vdd";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	vdd_usb: regulator-vdd-usb {
		compatible = "regulator-fixed";
		regulator-name = "vdd_usb";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	vdda: regulator-vdda {
		compatible = "regulator-fixed";
		regulator-name = "vdda";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	vdd_ddr: regulator-vdd-ddr {
		compatible = "regulator-fixed";
		regulator-name = "vdd_ddr";
		regulator-min-microvolt = <1350000>;
		regulator-max-microvolt = <1350000>;
		regulator-always-on;
	};

	vtt_ddr: regulator-vtt-ddr {
		compatible = "regulator-fixed";
		regulator-name = "vtt_ddr";
		regulator-min-microvolt = <675000>;
		regulator-max-microvolt = <675000>;
		regulator-always-on;
		vin-supply = <&vdd>;
	};

	vref_ddr: regulator-vref-ddr {
		compatible = "regulator-fixed";
		regulator-name = "vref_ddr";
		regulator-min-microvolt = <675000>;
		regulator-max-microvolt = <675000>;
		regulator-always-on;
		vin-supply = <&vdd>;
	};

	vdd_sd: regulator-vdd-sd {
		compatible = "regulator-fixed";
		regulator-name = "vdd_sd";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	v3v3: regulator-v3v3 {
		compatible = "regulator-fixed";
		regulator-name = "v3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	v2v8: regulator-v2v8 {
		compatible = "regulator-fixed";
		regulator-name = "v2v8";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		regulator-always-on;
		vin-supply = <&v3v3>;
	};

	v1v8: regulator-v1v8 {
		compatible = "regulator-fixed";
		regulator-name = "v1v8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
		vin-supply = <&v3v3>;
	};
};

&dts {
	status = "okay";
};

&i2c2 {
	i2c-scl-falling-time-ns = <20>;
	i2c-scl-rising-time-ns = <185>;
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c2_pins_a>;
	pinctrl-1 = <&i2c2_sleep_pins_a>;
	status = "okay";
};

&ipcc {
	status = "okay";
};

&iwdg2 {
	timeout-sec = <32>;
	status = "okay";
};

&m4_rproc {
	memory-region = <&retram>, <&mcuram>, <&mcuram2>, <&vdev0vring0>,
			<&vdev0vring1>, <&vdev0buffer>;
	mboxes = <&ipcc 0>, <&ipcc 1>, <&ipcc 2>;
	mbox-names = "vq0", "vq1", "shutdown";
	interrupt-parent = <&exti>;
	interrupts = <68 1>;
	status = "okay";
};

&rng1 {
	status = "okay";
};

&rtc {
	status = "okay";
};

&vrefbuf {
	regulator-min-microvolt = <2500000>;
	regulator-max-microvolt = <2500000>;
	vdda-supply = <&vdd>;
	status = "okay";
};
