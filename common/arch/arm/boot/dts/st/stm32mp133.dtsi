// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2021 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

#include "stm32mp131.dtsi"

/ {
	soc {
		m_can1: can@4400e000 {
			compatible = "bosch,m_can";
			reg = <0x4400e000 0x400>, <0x44011000 0x1400>;
			reg-names = "m_can", "message_ram";
			interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "int0", "int1";
			clocks = <&scmi_clk CK_SCMI_HSE>, <&rcc FDCAN_K>;
			clock-names = "hclk", "cclk";
			bosch,mram-cfg = <0x0 0 0 32 0 0 2 2>;
			status = "disabled";
		};

		m_can2: can@4400f000 {
			compatible = "bosch,m_can";
			reg = <0x4400f000 0x400>, <0x44011000 0x2800>;
			reg-names = "m_can", "message_ram";
			interrupts = <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "int0", "int1";
			clocks = <&scmi_clk CK_SCMI_HSE>, <&rcc FDCAN_K>;
			clock-names = "hclk", "cclk";
			bosch,mram-cfg = <0x1400 0 0 32 0 0 2 2>;
			status = "disabled";
		};

		adc_1: adc@48003000 {
			compatible = "st,stm32mp13-adc-core";
			reg = <0x48003000 0x400>;
			interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&rcc ADC1>, <&rcc ADC1_K>;
			clock-names = "bus", "adc";
			interrupt-controller;
			#interrupt-cells = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			adc1: adc@0 {
				compatible = "st,stm32mp13-adc";
				#io-channel-cells = <1>;
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x0>;
				interrupt-parent = <&adc_1>;
				interrupts = <0>;
				dmas = <&dmamux1 9 0x400 0x80000001>;
				dma-names = "rx";
				status = "disabled";

				channel@18 {
					reg = <18>;
					label = "vrefint";
				};
			};
		};
	};
};
