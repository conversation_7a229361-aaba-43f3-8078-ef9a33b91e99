// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * DTS file for SPEAr320 Evaluation Baord
 *
 * Copyright 2012 V<PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
/include/ "spear320.dtsi"

/ {
	model = "ST SPEAr320 Evaluation Board";
	compatible = "st,spear320-evb", "st,spear320";
	#address-cells = <1>;
	#size-cells = <1>;

	memory {
		reg = <0 0x40000000>;
	};

	ahb {
		pinmux@b3000000 {
			st,pinmux-mode = <4>;
			pinctrl-names = "default";
			pinctrl-0 = <&state_default>;

			state_default: pinmux {
				i2c0 {
					st,pins = "i2c0_grp";
					st,function = "i2c0";
				};
				mii0 {
					st,pins = "mii0_grp";
					st,function = "mii0";
				};
				ssp0 {
					st,pins = "ssp0_grp";
					st,function = "ssp0";
				};
				uart0 {
					st,pins = "uart0_grp";
					st,function = "uart0";
				};
				sdhci {
					st,pins = "sdhci_cd_51_grp";
					st,function = "sdhci";
				};
				i2s {
					st,pins = "i2s_grp";
					st,function = "i2s";
				};
				uart1 {
					st,pins = "uart1_grp";
					st,function = "uart1";
				};
				uart2 {
					st,pins = "uart2_grp";
					st,function = "uart2";
				};
				can0 {
					st,pins = "can0_grp";
					st,function = "can0";
				};
				can1 {
					st,pins = "can1_grp";
					st,function = "can1";
				};
				mii2 {
					st,pins = "mii2_grp";
					st,function = "mii2";
				};
				pwm0_1 {
					st,pins = "pwm0_1_pin_37_38_grp";
					st,function = "pwm0_1";
				};
			};
		};

		dma@fc400000 {
			status = "okay";
		};

		fsmc: flash@4c000000 {
			status = "okay";
		};

		gmac: eth@e0800000 {
			status = "okay";
		};

		sdhci@70000000 {
			power-gpio = <&gpiopinctrl 61 1>;
			status = "okay";
		};

		smi: flash@fc000000 {
			status = "okay";
			clock-rate = <50000000>;

			flash@f8000000 {
				#address-cells = <1>;
				#size-cells = <1>;
				reg = <0xf8000000 0x800000>;
				st,smi-fast-mode;

				partition@0 {
					label = "xloader";
					reg = <0x0 0x10000>;
				};
				partition@10000 {
					label = "u-boot";
					reg = <0x10000 0x50000>;
				};
				partition@60000 {
					label = "environment";
					reg = <0x60000 0x10000>;
				};
				partition@70000 {
					label = "dtb";
					reg = <0x70000 0x10000>;
				};
				partition@80000 {
					label = "linux";
					reg = <0x80000 0x310000>;
				};
				partition@390000 {
					label = "rootfs";
					reg = <0x390000 0x0>;
				};
			};
		};

		spi0: spi@d0100000 {
			status = "okay";
		};

		spi1: spi@a5000000 {
			status = "okay";
		};

		spi2: spi@a6000000 {
			status = "okay";
		};

		ehci@e1800000 {
			status = "okay";
		};

		ohci@e1900000 {
			status = "okay";
		};

		ohci@e2100000 {
			status = "okay";
		};

		apb {
			gpio0: gpio@fc980000 {
			       status = "okay";
			};

			gpio@b3000000 {
			       status = "okay";
			};

			i2c0: i2c@d0180000 {
			       status = "okay";
			};

			i2c1: i2c@a7000000 {
			       status = "okay";
			};

			rtc@fc900000 {
			       status = "okay";
			};

			serial@d0000000 {
			       status = "okay";
				pinctrl-names = "default";
				pinctrl-0 = <>;
			};

			serial@a3000000 {
			       status = "okay";
				pinctrl-names = "default";
				pinctrl-0 = <>;
			};

			serial@a4000000 {
			       status = "okay";
				pinctrl-names = "default";
				pinctrl-0 = <>;
			};

			wdt@fc880000 {
			       status = "okay";
			};
		};
	};
};
