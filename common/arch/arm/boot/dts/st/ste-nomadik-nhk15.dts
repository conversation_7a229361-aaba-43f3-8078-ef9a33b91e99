// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree for the ST Microelectronics Nomadik NHK8815 board
 */

/dts-v1/;
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>
#include "ste-nomadik-stn8815.dtsi"

/ {
	model = "Nomadik STN8815NHK";
	compatible = "st,nomadik-nhk-15";

	chosen {
		bootargs = "root=/dev/ram0 console=ttyAMA1,115200n8 earlyprintk";
	};

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
		stmpe-i2c0 = &stmpe0;
		stmpe-i2c1 = &stmpe1;
	};

	pinctrl {
		uart0 {
			uart0_nhk_mode: uart0_mux {
				u0_default_mux {
					function = "u0";
					groups = "u0txrx_a_1", "u0ctsrts_a_1";
				};
			};
		};

		stmpe2401_1 {
			stmpe2401_1_nhk_mode: stmpe2401_1_nhk {
				nhk_cfg1 {
					pins = "GPIO76_B20"; // IRQ line
					ste,input = <0>;
				};
				nhk_cfg2 {
					pins = "GPIO77_B8"; // reset line
					ste,output = <1>;
				};
			};
		};
		stmpe2401_2 {
			stmpe2401_2_nhk_mode: stmpe2401_2_nhk {
				nhk_cfg1 {
					pins = "GPIO78_A8"; // IRQ line
					ste,input = <0>;
				};
				nhk_cfg2 {
					pins = "GPIO79_C9"; // reset line
					ste,output = <1>;
				};
			};
		};
		lis3lv02dl {
			lis3lv02dl_nhk_mode: lis3lv02dl_nhk {
				nhk_cfg1 {
					pins = "GPIO82_C10"; // IRQ line
					ste,input = <0>;
				};
			};
		};
	};
	src@101e0000 {
		/* These chrystal outputs are not used on this board */
		disable-sxtalo;
		disable-mxtalo;
	};

	/* This is where the interrupt is routed on the NHK-15 debug board */
	external-bus@34000000 {
		compatible = "simple-bus";
		reg = <0x34000000 0x1000000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x34000000 0x1000000>;
		ethernet@300 {
			compatible = "smsc,lan91c111";
			reg = <0x300 0x0fd00>;
			reg-io-width = <2>;
			reset-gpios = <&stmpe_gpio44 10 GPIO_ACTIVE_HIGH>;
			interrupt-parent = <&stmpe_gpio44>;
			interrupts = <11 IRQ_TYPE_EDGE_RISING>;
		};
	};

	i2c0 {
		lis3lv02dl@1d {
			/* Accelerometer */
			compatible = "st,lis3lv02dl-accel";
			interrupt-parent = <&gpio2>;
			interrupts = <18 IRQ_TYPE_EDGE_RISING>; // GPIO 82
			pinctrl-0 = <&lis3lv02dl_nhk_mode>;
			pinctrl-names = "default";
			reg = <0x1d>;
		};
		stmpe0: port-expander@43 {
			compatible = "st,stmpe2401";
			reg = <0x43>;
			reset-gpios = <&gpio2 13 GPIO_ACTIVE_LOW>; // GPIO77
			interrupts = <12 IRQ_TYPE_EDGE_FALLING>; // GPIO76
			interrupt-parent = <&gpio2>;
			wakeup-source;
			pinctrl-names = "default";
			pinctrl-0 = <&stmpe2401_1_nhk_mode>;
			stmpe_gpio43: gpio {
				compatible = "st,stmpe-gpio";
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				/* Some pins in alternate functions */
				st,norequest-mask = <0xf0f002>;
			};
			keyboard-controller {
				compatible = "st,stmpe-keypad";
				debounce-interval = <64>;
				st,scan-count = <8>;
				st,no-autorepeat;
				keypad,num-rows = <8>;
				keypad,num-columns = <8>;
				linux,keymap = <0x00020072 // Vol down
						0x00030073 // Vol up
						0x0100009e // Back
						0x010100e3 // TV out
						0x01020098 // Lock
						0x0103013b // Start
						0x020000a3 // Next
						0x020100a4 // Play
						0x020200a5 // Prev
						0x02030160 // OK
						0x03000069 // Left
						0x0301006a // Right
						0x03020067 // Up
						0x0303006c>; // Down
			};
			stmpe0_pwm: pwm {
				compatible = "st,stmpe-pwm";
				#pwm-cells = <2>;
			};
		};
		stmpe1: port-expander@44 {
			compatible = "st,stmpe2401";
			reg = <0x44>;
			reset-gpios = <&gpio2 15 GPIO_ACTIVE_LOW>; // GPIO79
			interrupts = <14 IRQ_TYPE_EDGE_FALLING>; // GPIO78
			interrupt-parent = <&gpio2>;
			wakeup-source;
			pinctrl-names = "default";
			pinctrl-0 = <&stmpe2401_2_nhk_mode>;
			stmpe_gpio44: gpio {
				compatible = "st,stmpe-gpio";
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				/*
				 * This will turn off SATA so that MMC/SD
				 * can thrive
				 */
				mmcsd-hog {
					gpio-hog;
					gpios = <2 0x0>;
					output-low;
					line-name = "SATA EN";
				};
			};
		};
	};

	amba {
		clcd@10120000 {
			status = "okay";
			pinctrl-names = "default";
			pinctrl-0 = <&clcd_24bit_mux>;
			port {
				nomadik_clcd: endpoint {
					remote-endpoint = <&nomadik_clcd_panel>;
					arm,pl11x,tft-r0g0b0-pads = <16 8 0>;
				};
			};

		};

		/* Activate RX/TX and CTS/RTS on UART 0 */
		uart0: serial@101fd000 {
			pinctrl-names = "default";
			pinctrl-0 = <&uart0_nhk_mode>;
			status = "okay";
		};
		mmcsd: mmc@101f6000 {
			cd-gpios = <&stmpe_gpio44 7 GPIO_ACTIVE_LOW>;
			wp-gpios = <&stmpe_gpio44 18 GPIO_ACTIVE_HIGH>;
		};
	};

	spi {
		compatible = "spi-gpio";
		#address-cells = <1>;
		#size-cells = <0>;

		/*
		 * As we're dealing with 3wire SPI, we only define SCK
		 * and MOSI (in the spec MOSI is called "SDA").
		 */
		sck-gpios = <&gpio0 5 GPIO_ACTIVE_HIGH>;
		mosi-gpios = <&gpio0 4 GPIO_ACTIVE_HIGH>;
		cs-gpios = <&gpio0 6 GPIO_ACTIVE_LOW>;
		num-chipselects = <1>;

		/*
		 * WVGA connector 21
		 * WVGA (800x480): 4.3" TPG110 TDO43MTEA2 24-bit RGB
		 * with TPO touch screen.
		  */
		panel: display@0 {
			/*
			 * The TPO display driver is connected to a
			 * 5.7" OSD OSD057VA01CT TFT display.
			 */
			compatible = "tpo,tpg110";
			reg = <0>;
			spi-3wire;
			/* 320 ns min period ~= 3 MHz */
			spi-max-frequency = <3000000>;
			/* Width and height from the OSD data sheet */
			width-mm = <116>;
			height-mm = <87>;
			grestb-gpios = <&stmpe_gpio44 5 GPIO_ACTIVE_LOW>;
			backlight = <&bl>;

			port {
				nomadik_clcd_panel: endpoint {
					remote-endpoint = <&nomadik_clcd>;
				};
			};
		};
	};

	bl: backlight {
		compatible = "pwm-backlight";
		pwms = <&stmpe0_pwm 0 500000>;
		pwm-names = "backlight";
		brightness-levels = <
			0  1  2  3  4  5  6  7  8  9
			10 11 12 13 14 15 16 17 18 19
			20 21 22 23 24 25 26 27 28 29
			30 31 32 33 34 35 36 37 38 39
			40 41 42 43 44 45 46 47 48 49
			50 51 52 53 54 55 56 57 58 59
			60 61 62 63 64 65 66 67 68 69
			70 71 72 73 74 75 76 77 78 79
			80 81 82 83 84 85 86 87 88 89
			90 91 92 93 94 95 96 97 98 99
			100
		>;
		default-brightness-level = <100>;
	};
};
