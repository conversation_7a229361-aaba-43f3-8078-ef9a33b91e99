/*
 * Copyright 2021 - <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 *
 * For art-pi board resources, you can refer to link:
 * 	https://art-pi.gitee.io/website/
 */

/dts-v1/;
#include "stm32h750.dtsi"
#include "stm32h7-pinctrl.dtsi"
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "RT-Thread STM32H750i-ART-PI board";
	compatible = "st,stm32h750i-art-pi", "st,stm32h750";

	chosen {
		bootargs = "root=/dev/ram";
		stdout-path = "serial0:2000000n8";
	};

	memory@c0000000 {
		device_type = "memory";
		reg = <0xc0000000 0x2000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		linux,cma {
			compatible = "shared-dma-pool";
			no-map;
			size = <0x100000>;
			linux,dma-default;
		};
	};

	aliases {
		serial0 = &uart4;
		serial1 = &usart3;
	};

	leds {
		compatible = "gpio-leds";
		led-red {
			gpios = <&gpioi 8 0>;
		};
		led-green {
			gpios = <&gpioc 15 0>;
			linux,default-trigger = "heartbeat";
		};
	};

	v3v3: regulator-v3v3 {
		compatible = "regulator-fixed";
		regulator-name = "v3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	wlan_pwr: regulator-wlan {
		compatible = "regulator-fixed";

		regulator-name = "wl-reg";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;

		gpios = <&gpioc 13 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};
};

&clk_hse {
	clock-frequency = <25000000>;
};

&dma1 {
	status = "okay";
};

&dma2 {
	status = "okay";
};

&mac {
	status = "disabled";
	pinctrl-0 = <&ethernet_rmii>;
	pinctrl-names = "default";
	phy-mode = "rmii";
	phy-handle = <&phy0>;

	mdio0 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "snps,dwmac-mdio";
		phy0: ethernet-phy@0 {
			reg = <0>;
		};
	};
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a>;
	broken-cd;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&v3v3>;
	status = "okay";
};

&sdmmc2 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc2_b4_pins_a>;
	pinctrl-1 = <&sdmmc2_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc2_b4_sleep_pins_a>;
	broken-cd;
	non-removable;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&wlan_pwr>;
	status = "okay";

	#address-cells = <1>;
	#size-cells = <0>;
	brcmf: bcrmf@1 {
		reg = <1>;
		compatible = "brcm,bcm4329-fmac";
	};
};

&spi1 {
	status = "okay";
	pinctrl-0 = <&spi1_pins>;
	pinctrl-names = "default";
	cs-gpios = <&gpioa 4 GPIO_ACTIVE_LOW>;
	dmas = <&dmamux1 37 0x400 0x05>,
	       <&dmamux1 38 0x400 0x05>;
	dma-names = "rx", "tx";

	flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "winbond,w25q128", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <80000000>;

		partition@0 {
			label = "root filesystem";
			reg = <0 0x1000000>;
		};
	};
};

&usart2 {
	pinctrl-0 = <&usart2_pins>;
	pinctrl-names = "default";
	status = "disabled";
};

&usart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&usart3_pins>;
	dmas = <&dmamux1 45 0x400 0x05>,
	       <&dmamux1 46 0x400 0x05>;
	dma-names = "rx", "tx";
	uart-has-rtscts;
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm43438-bt";
		host-wakeup-gpios = <&gpioc 0 GPIO_ACTIVE_HIGH>;
		device-wakeup-gpios = <&gpioi 10 GPIO_ACTIVE_HIGH>;
		shutdown-gpios = <&gpioi 11 GPIO_ACTIVE_HIGH>;
		max-speed = <115200>;
	};
};

&uart4 {
	pinctrl-0 = <&uart4_pins>;
	pinctrl-names = "default";
	status = "okay";
};


