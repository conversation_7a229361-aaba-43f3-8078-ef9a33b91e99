// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2021 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

/dts-v1/;

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/leds/common.h>
#include <dt-bindings/regulator/st,stm32mp13-regulator.h>
#include "stm32mp135.dtsi"
#include "stm32mp13xf.dtsi"
#include "stm32mp13-pinctrl.dtsi"

/ {
	model = "STMicroelectronics STM32MP135F-DK Discovery Board";
	compatible = "st,stm32mp135f-dk", "st,stm32mp135";

	aliases {
		serial0 = &uart4;
		serial1 = &usart1;
		serial2 = &uart8;
		serial3 = &usart2;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	memory@c0000000 {
		device_type = "memory";
		reg = <0xc0000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		optee@dd000000 {
			reg = <0xdd000000 0x3000000>;
			no-map;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		button-user {
			label = "User-PA13";
			linux,code = <BTN_1>;
			gpios = <&gpioa 13 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
		};
	};

	leds {
		compatible = "gpio-leds";

		led-blue {
			function = LED_FUNCTION_HEARTBEAT;
			color = <LED_COLOR_ID_BLUE>;
			gpios = <&gpioa 14 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "heartbeat";
			default-state = "off";
		};
	};
};

&adc_1 {
	pinctrl-names = "default";
	pinctrl-0 = <&adc1_usb_cc_pins_a>;
	vdda-supply = <&scmi_vdd_adc>;
	vref-supply = <&scmi_vdd_adc>;
	status = "okay";
	adc1: adc@0 {
		status = "okay";
		/*
		 * Type-C USB_PWR_CC1 & USB_PWR_CC2 on in6 & in12.
		 * Use at least 5 * RC time, e.g. 5 * (Rp + Rd) * C:
		 * 5 * (5.1 + 47kOhms) * 5pF => 1.3us.
		 * Use arbitrary margin here (e.g. 5us).
		 */
		channel@6 {
			reg = <6>;
			st,min-sample-time-ns = <5000>;
		};
		channel@12 {
			reg = <12>;
			st,min-sample-time-ns = <5000>;
		};
	};
};

&i2c1 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c1_pins_a>;
	pinctrl-1 = <&i2c1_sleep_pins_a>;
	i2c-scl-rising-time-ns = <96>;
	i2c-scl-falling-time-ns = <3>;
	clock-frequency = <1000000>;
	status = "okay";
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	mcp23017: pinctrl@21 {
		compatible = "microchip,mcp23017";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupts = <12 IRQ_TYPE_LEVEL_LOW>;
		interrupt-parent = <&gpiog>;
		pinctrl-names = "default";
		pinctrl-0 = <&mcp23017_pins_a>;
		interrupt-controller;
		#interrupt-cells = <2>;
		microchip,irq-mirror;
	};

	typec@53 {
		compatible = "st,stm32g0-typec";
		reg = <0x53>;
		/* Alert pin on PI2 */
		interrupts = <2 IRQ_TYPE_EDGE_FALLING>;
		interrupt-parent = <&gpioi>;
		/* Internal pull-up on PI2 */
		pinctrl-names = "default";
		pinctrl-0 = <&stm32g0_intn_pins_a>;
		firmware-name = "stm32g0-ucsi.mp135f-dk.fw";
		connector {
			compatible = "usb-c-connector";
			label = "USB-C";

			port {
				con_usb_c_g0_ep: endpoint {
					remote-endpoint = <&usbotg_hs_ep>;
				};
			};
		};
	};
};

&i2c5 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c5_pins_a>;
	pinctrl-1 = <&i2c5_sleep_pins_a>;
	i2c-scl-rising-time-ns = <170>;
	i2c-scl-falling-time-ns = <5>;
	clock-frequency = <400000>;
	status = "okay";
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;
};

&iwdg2 {
	timeout-sec = <32>;
	status = "okay";
};

&rtc {
	status = "okay";
};

&scmi_regu {
	scmi_vdd_adc: regulator@10 {
		reg = <VOLTD_SCMI_STPMIC1_LDO1>;
		regulator-name = "vdd_adc";
	};
	scmi_vdd_usb: regulator@13 {
		reg = <VOLTD_SCMI_STPMIC1_LDO4>;
		regulator-name = "vdd_usb";
	};
	scmi_vdd_sd: regulator@14 {
		reg = <VOLTD_SCMI_STPMIC1_LDO5>;
		regulator-name = "vdd_sd";
	};
	scmi_v1v8_periph: regulator@15 {
		reg = <VOLTD_SCMI_STPMIC1_LDO6>;
		regulator-name = "v1v8_periph";
	};
	scmi_v3v3_sw: regulator@19 {
		reg = <VOLTD_SCMI_STPMIC1_PWR_SW2>;
		regulator-name = "v3v3_sw";
	};
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a &sdmmc1_clk_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a &sdmmc1_clk_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a>;
	cd-gpios = <&gpioh 4 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
	disable-wp;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&scmi_vdd_sd>;
	status = "okay";
};

&spi5 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&spi5_pins_a>;
	pinctrl-1 = <&spi5_sleep_pins_a>;
	status = "disabled";
};

&timers3 {
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm3_pins_a>;
		pinctrl-1 = <&pwm3_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@2 {
		status = "okay";
	};
};

&timers4 {
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm4_pins_a>;
		pinctrl-1 = <&pwm4_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@3 {
		status = "okay";
	};
};

&timers8 {
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm8_pins_a>;
		pinctrl-1 = <&pwm8_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@7 {
		status = "okay";
	};
};

&timers14 {
	status = "disabled";
	pwm {
		pinctrl-0 = <&pwm14_pins_a>;
		pinctrl-1 = <&pwm14_sleep_pins_a>;
		pinctrl-names = "default", "sleep";
		status = "okay";
	};
	timer@13 {
		status = "okay";
	};
};

&uart4 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&uart4_pins_a>;
	pinctrl-1 = <&uart4_sleep_pins_a>;
	pinctrl-2 = <&uart4_idle_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&uart8 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&uart8_pins_a>;
	pinctrl-1 = <&uart8_sleep_pins_a>;
	pinctrl-2 = <&uart8_idle_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "disabled";
};

&usart1 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&usart1_pins_a>;
	pinctrl-1 = <&usart1_sleep_pins_a>;
	pinctrl-2 = <&usart1_idle_pins_a>;
	uart-has-rtscts;
	status = "disabled";
};

/* Bluetooth */
&usart2 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&usart2_pins_a>;
	pinctrl-1 = <&usart2_sleep_pins_a>;
	pinctrl-2 = <&usart2_idle_pins_a>;
	uart-has-rtscts;
	status = "okay";
};

&usbh_ehci {
	phys = <&usbphyc_port0>;
	status = "okay";
	#address-cells = <1>;
	#size-cells = <0>;
	/* onboard HUB */
	hub@1 {
		compatible = "usb424,2514";
		reg = <1>;
		vdd-supply = <&scmi_v3v3_sw>;
	};
};

&usbotg_hs {
	phys = <&usbphyc_port1 0>;
	phy-names = "usb2-phy";
	usb-role-switch;
	status = "okay";
	port {
		usbotg_hs_ep: endpoint {
			remote-endpoint = <&con_usb_c_g0_ep>;
		};
	};
};

&usbphyc {
	status = "okay";
};

&usbphyc_port0 {
	phy-supply = <&scmi_vdd_usb>;
	st,current-boost-microamp = <1000>;
	st,decrease-hs-slew-rate;
	st,tune-hs-dc-level = <2>;
	st,enable-hs-rftime-reduction;
	st,trim-hs-current = <11>;
	st,trim-hs-impedance = <2>;
	st,tune-squelch-level = <1>;
	st,enable-hs-rx-gain-eq;
	st,no-hs-ftime-ctrl;
	st,no-lsfs-sc;
};

&usbphyc_port1 {
	phy-supply = <&scmi_vdd_usb>;
	st,current-boost-microamp = <1000>;
	st,decrease-hs-slew-rate;
	st,tune-hs-dc-level = <2>;
	st,enable-hs-rftime-reduction;
	st,trim-hs-current = <11>;
	st,trim-hs-impedance = <2>;
	st,tune-squelch-level = <1>;
	st,enable-hs-rx-gain-eq;
	st,no-hs-ftime-ctrl;
	st,no-lsfs-sc;
};
