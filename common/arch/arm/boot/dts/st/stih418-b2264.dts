// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2021 STMicroelectronics
 * Author: <PERSON> <<EMAIL>>
 */
/dts-v1/;
#include "stih418.dtsi"
#include <dt-bindings/gpio/gpio.h>
/ {
	model = "STiH418 B2264";
	compatible = "st,stih418-b2264", "st,stih418";

	chosen {
		stdout-path = &sbc_serial0;
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0xc0000000>;
	};

	cpus {
		cpu@0 {
			operating-points-v2 = <&cpu_opp_table>;
			/* u-boot puts hpen in SBC dmem at 0xb8 offset */
			cpu-release-addr = <0x94100b8>;
		};
		cpu@1 {
			operating-points-v2 = <&cpu_opp_table>;
			/* u-boot puts hpen in SBC dmem at 0xb8 offset */
			cpu-release-addr = <0x94100b8>;
		};
		cpu@2 {
			operating-points-v2 = <&cpu_opp_table>;
			/* u-boot puts hpen in SBC dmem at 0xb8 offset */
			cpu-release-addr = <0x94100b8>;
		};
		cpu@3 {
			operating-points-v2 = <&cpu_opp_table>;
			/* u-boot puts hpen in SBC dmem at 0xb8 offset */
			cpu-release-addr = <0x94100b8>;
		};
	};

	cpu_opp_table: opp-table {
		compatible = "operating-points-v2";
		opp-shared;

		opp00 {
			opp-hz = /bits/ 64 <300000000>;
			opp-microvolt = <784000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <500000000>;
			opp-microvolt = <784000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt = <784000>;
		};
		opp03 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <784000>;
		};
		opp04 {
			opp-hz = /bits/ 64 <1500000000>;
			opp-microvolt = <784000>;
		};
	};

	aliases {
		ttyAS0 = &sbc_serial0;
		ethernet0 = &ethernet0;
	};

	soc {
		leds {
			compatible = "gpio-leds";
			led-green {
				gpios = <&pio1 3 GPIO_ACTIVE_HIGH>;
				default-state = "off";
			};
		};

		pin-controller-sbc@961f080 {
			gmac1 {
				rgmii1-0 {
					st,pins {
						rxd0 = <&pio1 4 ALT1 IN DE_IO 300 CLK_A>;
						rxd1 = <&pio1 5 ALT1 IN DE_IO 300 CLK_A>;
						rxd2 = <&pio1 6 ALT1 IN DE_IO 300 CLK_A>;
						rxd3 = <&pio1 7 ALT1 IN DE_IO 300 CLK_A>;
						rxdv = <&pio2 0 ALT1 IN DE_IO 300 CLK_A>;
					};
				};
			};
		};

	};
};

&ehci0 {
	status = "okay";
};

&ethernet0 {
	phy-mode = "rgmii";
	pinctrl-0 = <&pinctrl_rgmii1 &pinctrl_rgmii1_mdio_1>;
	st,tx-retime-src = "clkgen";

	snps,reset-gpio = <&pio0 7 0>;
	snps,reset-active-low;
	snps,reset-delays-us = <0 10000 1000000>;

	status = "okay";
};

&miphy28lp_phy {
	phy_port0: port@9b22000 {
		st,sata-gen = <2>; /* SATA GEN3 */
		st,osc-rdy;
	};
};

&mmc0 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&pwm1 {
	status = "okay";
};

&sata0 {
	status = "okay";
};

&sbc_serial0 {
	status = "okay";
};

&spifsm {
	status = "okay";
};

&st_dwc3 {
	status = "okay";
};
