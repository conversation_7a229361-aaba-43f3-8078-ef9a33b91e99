// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright 2012 ST-<PERSON><PERSON> AB
 */

#include <dt-bindings/interrupt-controller/irq.h>

/ {
	gpio_keys {
		compatible = "gpio-keys";
		#address-cells = <1>;
		#size-cells = <0>;
		vdd-supply = <&ab8500_ldo_aux1_reg>;
		pinctrl-names = "default";
		pinctrl-0 = <&prox_stuib_mode>, <&hall_stuib_mode>;

		button@139 {
			/* Proximity sensor */
			gpios = <&gpio6 25 GPIO_ACTIVE_HIGH>;
			linux,code = <11>; /* SW_FRONT_PROXIMITY */
			label = "SFH7741 Proximity Sensor";
		};
		button@145 {
			/* Hall sensor */
			gpios = <&gpio4 17 GPIO_ACTIVE_HIGH>;
			linux,code = <0>; /* SW_LID */
			label = "HED54XXU11 Hall Effect Sensor";
		};
	};

	soc {
		i2c@80004000 {
			stmpe1601: port-expander@40 {
				compatible = "st,stmpe1601";
				reg = <0x40>;
				interrupts = <26 IRQ_TYPE_EDGE_FALLING>;
				interrupt-parent = <&gpio6>;
				vcc-supply = <&db8500_vsmps2_reg>;
				vio-supply = <&db8500_vsmps2_reg>;
				pinctrl-names = "default";
				pinctrl-0 = <&stmpe_stuib_mode>;

				wakeup-source;
				st,autosleep-timeout = <1024>;

				keyboard-controller {
					compatible = "st,stmpe-keypad";

					debounce-interval = <64>;
					st,scan-count = <8>;
					st,no-autorepeat;

					linux,keymap = <0x205006b
							0x4010074
							0x3050072
							0x1030004
							0x502006a
							0x500000a
							0x5008b
							0x706001c
							0x405000b
							0x6070003
							0x3040067
							0x303006c
							0x60400e7
							0x602009e
							0x4020073
							0x5050002
							0x4030069
							0x3020008>;
				};
			};
		};

		/* Sensors mounted on this board variant */
		i2c@80128000 {
			lis331dl@1c {
				/* Accelerometer */
				compatible = "st,lis331dl-accel";
				st,drdy-int-pin = <1>;
				reg = <0x1c>;
				vdd-supply = <&ab8500_ldo_aux1_reg>;
				vddio-supply = <&db8500_vsmps2_reg>;
				pinctrl-names = "default";
				pinctrl-0 = <&accel_stuib_mode>;
				interrupt-parent = <&gpio2>;
				interrupts = <18 IRQ_TYPE_EDGE_RISING>,
					     <19 IRQ_TYPE_EDGE_RISING>;
			};
			ak8974@f {
				/* Magnetometer */
				compatible = "asahi-kasei,ak8974";
				reg = <0x0f>;
				vdd-supply = <&ab8500_ldo_aux1_reg>;
				vddio-supply = <&db8500_vsmps2_reg>;
				pinctrl-names = "default";
				pinctrl-0 = <&magneto_stuib_mode>;
				interrupt-parent = <&gpio1>;
				interrupts = <0 IRQ_TYPE_EDGE_RISING>;
			};
		};

		i2c@80110000 {
			bu21013_tp@5c {
				compatible = "rohm,bu21013_tp";
				reg = <0x5c>;
				avdd-supply = <&ab8500_ldo_aux1_reg>;

				rohm,touch-max-x = <384>;
				rohm,touch-max-y = <704>;
				rohm,flip-y;
				pinctrl-names = "default";
				pinctrl-0 = <&touch_rohm_mode>;
			};

			bu21013_tp@5d {
				compatible = "rohm,bu21013_tp";
				reg = <0x5d>;
				avdd-supply = <&ab8500_ldo_aux1_reg>;

				rohm,touch-max-x = <384>;
				rohm,touch-max-y = <704>;
				rohm,flip-y;
				pinctrl-names = "default";
				pinctrl-0 = <&touch_rohm_mode>;
			};
		};

		pinctrl {
			/* Pull up this GPIO pin */
			stmpe {
				stmpe_stuib_mode: stmpe_stuib {
					stuib_cfg {
						ste,pins = "GPIO218_AH11";
						ste,config = <&gpio_in_pu>;
					};
				};
			};
			prox {
				prox_stuib_mode: prox_stuib {
					stuib_cfg {
						pins = "GPIO217_AH12";
						ste,config = <&gpio_in_pu>;
					};
				};
			};
			hall {
				hall_stuib_mode: stuib_tvk {
					stuib_cfg {
						pins = "GPIO145_C13";
						ste,config = <&gpio_in_pu>;
					};
				};
			};
			accelerometer {
				accel_stuib_mode: accel_stuib {
					/* Accelerometer interrupt lines 1 & 2 */
					stuib_cfg {
						pins = "GPIO82_C1", "GPIO83_D3";
						ste,config = <&gpio_in_pu>;
					};
				};
			};
			magnetometer {
				magneto_stuib_mode: magneto_stuib {
					/* Magnetometer uses GPIO 31 and 32, pull these up/down respectively */
					stuib_cfg1 {
						pins = "GPIO31_V3";
						ste,config = <&gpio_in_pu>;
					};
					stuib_cfg2 {
						pins = "GPIO32_V2";
						ste,config = <&gpio_in_pd>;
					};
				};
			};
			touch {
				touch_rohm_mode: touch_rohm {
					/*
					 * ROHM touch screen uses GPIO 143 for
					 * RST1, GPIO 146 for RST2 and
					 * GPIO 67 for interrupts. Pull-up
					 * the IRQ line and drive both
					 * reset signals low.
					 */
					stuib_cfg1 {
						pins = "GPIO143_D12", "GPIO146_D13";
						ste,config = <&gpio_out_lo>;
					};
					stuib_cfg2 {
						pins = "GPIO67_G2";
						ste,config = <&gpio_in_pu>;
					};
				};
			};
		};

		mcde@a0350000 {
			status = "okay";

			dsi@a0351000 {
				panel {
					compatible = "samsung,s6d16d0";
					reg = <0>;
					vdd1-supply = <&ab8500_ldo_aux1_reg>;
					reset-gpios = <&gpio2 1 GPIO_ACTIVE_LOW>;
				};
			};
		};
	};
};
