// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2015 STMicroelectronics Limited.
 * Author: <PERSON> <gab<PERSON>.<EMAIL>>
 */
#include "stih407-clock.dtsi"
#include "stih407-family.dtsi"
#include <dt-bindings/gpio/gpio.h>
/ {
	soc {
		sti-display-subsystem@0 {
			compatible = "st,sti-display-subsystem";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0 0>;
			assigned-clocks = <&clk_s_d2_quadfs 0>,
					  <&clk_s_d2_quadfs 1>,
					  <&clk_s_c0_pll1 0>,
					  <&clk_s_c0_flexgen CLK_COMPO_DVP>,
					  <&clk_s_c0_flexgen CLK_MAIN_DISP>,
					  <&clk_s_d2_flexgen CLK_PIX_MAIN_DISP>,
					  <&clk_s_d2_flexgen CLK_PIX_AUX_DISP>,
					  <&clk_s_d2_flexgen CLK_PIX_GDP1>,
					  <&clk_s_d2_flexgen CLK_PIX_GDP2>,
					  <&clk_s_d2_flexgen CLK_PIX_GDP3>,
					  <&clk_s_d2_flexgen CLK_PIX_GDP4>;

			assigned-clock-parents = <0>,
						 <0>,
						 <0>,
						 <&clk_s_c0_pll1 0>,
						 <&clk_s_c0_pll1 0>,
						 <&clk_s_d2_quadfs 0>,
						 <&clk_s_d2_quadfs 1>,
						 <&clk_s_d2_quadfs 0>,
						 <&clk_s_d2_quadfs 0>,
						 <&clk_s_d2_quadfs 0>,
						 <&clk_s_d2_quadfs 0>;

			assigned-clock-rates = <297000000>,
					       <108000000>,
					       <0>,
					       <400000000>,
					       <400000000>;

			ranges;

			sti-compositor@9d11000 {
				compatible = "st,stih407-compositor";
				reg = <0x9d11000 0x1000>;

				clock-names = "compo_main",
					      "compo_aux",
					      "pix_main",
					      "pix_aux",
					      "pix_gdp1",
					      "pix_gdp2",
					      "pix_gdp3",
					      "pix_gdp4",
					      "main_parent",
					      "aux_parent";

				clocks = <&clk_s_c0_flexgen CLK_COMPO_DVP>,
					 <&clk_s_c0_flexgen CLK_COMPO_DVP>,
					 <&clk_s_d2_flexgen CLK_PIX_MAIN_DISP>,
					 <&clk_s_d2_flexgen CLK_PIX_AUX_DISP>,
					 <&clk_s_d2_flexgen CLK_PIX_GDP1>,
					 <&clk_s_d2_flexgen CLK_PIX_GDP2>,
					 <&clk_s_d2_flexgen CLK_PIX_GDP3>,
					 <&clk_s_d2_flexgen CLK_PIX_GDP4>,
					 <&clk_s_d2_quadfs 0>,
					 <&clk_s_d2_quadfs 1>;

				reset-names = "compo-main", "compo-aux";
				resets = <&softreset STIH407_COMPO_SOFTRESET>,
					 <&softreset STIH407_COMPO_SOFTRESET>;
				st,vtg = <&vtg_main>, <&vtg_aux>;
			};

			sti-tvout@8d08000 {
				compatible = "st,stih407-tvout";
				reg = <0x8d08000 0x1000>;
				reg-names = "tvout-reg";
				reset-names = "tvout";
				resets = <&softreset STIH407_HDTVOUT_SOFTRESET>;
				#address-cells = <1>;
				#size-cells = <1>;
				assigned-clocks = <&clk_s_d2_flexgen CLK_PIX_HDMI>,
						  <&clk_s_d2_flexgen CLK_TMDS_HDMI>,
						  <&clk_s_d2_flexgen CLK_REF_HDMIPHY>,
						  <&clk_s_d0_flexgen CLK_PCM_0>,
						  <&clk_s_d2_flexgen CLK_PIX_HDDAC>,
						  <&clk_s_d2_flexgen CLK_HDDAC>;

				assigned-clock-parents = <&clk_s_d2_quadfs 0>,
							 <&clk_tmdsout_hdmi>,
							 <&clk_s_d2_quadfs 0>,
							 <&clk_s_d0_quadfs 0>,
							 <&clk_s_d2_quadfs 0>,
							 <&clk_s_d2_quadfs 0>;
			};

			sti_hdmi: sti-hdmi@8d04000 {
				compatible = "st,stih407-hdmi";
				reg = <0x8d04000 0x1000>;
				reg-names = "hdmi-reg";
				#sound-dai-cells = <0>;
				interrupts = <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names = "irq";
				clock-names = "pix",
					      "tmds",
					      "phy",
					      "audio",
					      "main_parent",
					      "aux_parent";

				clocks = <&clk_s_d2_flexgen CLK_PIX_HDMI>,
					 <&clk_s_d2_flexgen CLK_TMDS_HDMI>,
					 <&clk_s_d2_flexgen CLK_REF_HDMIPHY>,
					 <&clk_s_d0_flexgen CLK_PCM_0>,
					 <&clk_s_d2_quadfs 0>,
					 <&clk_s_d2_quadfs 1>;

				hdmi,hpd-gpio = <&pio5 3 GPIO_ACTIVE_LOW>;
				reset-names = "hdmi";
				resets = <&softreset STIH407_HDMI_TX_PHY_SOFTRESET>;
				ddc = <&hdmiddc>;
			};

			sti-hda@8d02000 {
				compatible = "st,stih407-hda";
				reg = <0x8d02000 0x400>, <0x92b0120 0x4>;
				reg-names = "hda-reg", "video-dacs-ctrl";
				clock-names = "pix",
					      "hddac",
					      "main_parent",
					      "aux_parent";
				clocks = <&clk_s_d2_flexgen CLK_PIX_HDDAC>,
					 <&clk_s_d2_flexgen CLK_HDDAC>,
					 <&clk_s_d2_quadfs 0>,
					 <&clk_s_d2_quadfs 1>;
			};
		};
	};
};
