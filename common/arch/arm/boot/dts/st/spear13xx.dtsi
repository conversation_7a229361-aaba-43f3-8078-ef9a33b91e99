// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * DTS file for all SPEAr13xx SoCs
 *
 * Copyright 2012 V<PERSON><PERSON> <<EMAIL>>
 */

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&gic>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			compatible = "arm,cortex-a9";
			device_type = "cpu";
			reg = <0>;
			next-level-cache = <&L2>;
		};

		cpu@1 {
			compatible = "arm,cortex-a9";
			device_type = "cpu";
			reg = <1>;
			next-level-cache = <&L2>;
		};
	};

	gic: interrupt-controller@ec801000 {
		compatible = "arm,cortex-a9-gic";
		interrupt-controller;
		#interrupt-cells = <3>;
		reg = < 0xec801000 0x1000 >,
		      < 0xec800100 0x0100 >;
	};

	pmu {
		compatible = "arm,cortex-a9-pmu";
		interrupts = <0 6 0x04>,
			     <0 7 0x04>;
	};

	L2: cache-controller {
		    compatible = "arm,pl310-cache";
		    reg = <0xed000000 0x1000>;
		    cache-unified;
		    cache-level = <2>;
	};

	memory {
		name = "memory";
		device_type = "memory";
		reg = <0 0x40000000>;
	};

	chosen {
		bootargs = "console=ttyAMA0,115200";
	};

	cpufreq {
		compatible = "st,cpufreq-spear";
		cpufreq_tbl = < 166000
				200000
				250000
				300000
				400000
				500000
				600000 >;
		status = "disabled";
	};

	ahb {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "simple-bus";
		ranges = <0x50000000 0x50000000 0x10000000
			  0x80000000 0x80000000 0x20000000
			  0xb0000000 0xb0000000 0x22000000
			  0xd8000000 0xd8000000 0x01000000
			  0xe0000000 0xe0000000 0x10000000>;

		sdhci@b3000000 {
			compatible = "st,sdhci-spear";
			reg = <0xb3000000 0x100>;
			interrupts = <0 28 0x4>;
			status = "disabled";
		};

		cf@b2800000 {
			compatible = "arasan,cf-spear1340";
			reg = <0xb2800000 0x1000>;
			interrupts = <0 29 0x4>;
			status = "disabled";
			dmas = <&dwdma0 0 0 0>;
			dma-names = "data";
		};

		dwdma0: dma@ea800000 {
			compatible = "snps,dma-spear1340";
			reg = <0xea800000 0x1000>;
			interrupts = <0 19 0x4>;
			status = "disabled";

			dma-channels = <8>;
			#dma-cells = <3>;
			dma-requests = <32>;
			chan_allocation_order = <1>;
			chan_priority = <1>;
			block_size = <0xfff>;
			dma-masters = <2>;
			data-width = <8 8>;
			multi-block = <1 1 1 1 1 1 1 1>;
		};

		dma@eb000000 {
			compatible = "snps,dma-spear1340";
			reg = <0xeb000000 0x1000>;
			interrupts = <0 59 0x4>;
			status = "disabled";

			dma-requests = <32>;
			dma-channels = <8>;
			dma-masters = <2>;
			#dma-cells = <3>;
			chan_allocation_order = <1>;
			chan_priority = <1>;
			block_size = <0xfff>;
			data-width = <8 8>;
			multi-block = <1 1 1 1 1 1 1 1>;
		};

		fsmc: flash@b0000000 {
			compatible = "st,spear600-fsmc-nand";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0xb0000000 0x1000	/* FSMC Register*/
			       0xb0800000 0x0010	/* NAND Base DATA */
			       0xb0820000 0x0010	/* NAND Base ADDR */
			       0xb0810000 0x0010>;	/* NAND Base CMD */
			reg-names = "fsmc_regs", "nand_data", "nand_addr", "nand_cmd";
			interrupts = <0 20 0x4>,
				     <0 21 0x4>,
				     <0 22 0x4>,
				     <0 23 0x4>;
			st,mode = <2>;
			status = "disabled";
		};

		gmac0: eth@e2000000 {
			compatible = "st,spear600-gmac";
			reg = <0xe2000000 0x8000>;
			interrupts = <0 33 0x4>,
				     <0 34 0x4>;
			interrupt-names = "macirq", "eth_wake_irq";
			status = "disabled";
		};

		pcm {
			compatible = "st,pcm-audio";
			#address-cells = <0>;
			#size-cells = <0>;
			status = "disabled";
		};

		smi: flash@ea000000 {
			compatible = "st,spear600-smi";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0xea000000 0x1000>;
			interrupts = <0 30 0x4>;
			status = "disabled";
		};

		ehci@e4800000 {
			compatible = "st,spear600-ehci", "usb-ehci";
			reg = <0xe4800000 0x1000>;
			interrupts = <0 64 0x4>;
			usbh0_id = <0>;
			status = "disabled";
		};

		ehci@e5800000 {
			compatible = "st,spear600-ehci", "usb-ehci";
			reg = <0xe5800000 0x1000>;
			interrupts = <0 66 0x4>;
			usbh1_id = <1>;
			status = "disabled";
		};

		ohci@e4000000 {
			compatible = "st,spear600-ohci", "usb-ohci";
			reg = <0xe4000000 0x1000>;
			interrupts = <0 65 0x4>;
			usbh0_id = <0>;
			status = "disabled";
		};

		ohci@e5000000 {
			compatible = "st,spear600-ohci", "usb-ohci";
			reg = <0xe5000000 0x1000>;
			interrupts = <0 67 0x4>;
			usbh1_id = <1>;
			status = "disabled";
		};

		apb {
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "simple-bus";
			ranges = <0x50000000 0x50000000 0x10000000
				  0xb0000000 0xb0000000 0x10000000
				  0xd0000000 0xd0000000 0x02000000
				  0xd8000000 0xd8000000 0x01000000
				  0xe0000000 0xe0000000 0x10000000>;

			misc: syscon@e0700000 {
				compatible = "st,spear1340-misc", "syscon";
				reg = <0xe0700000 0x1000>;
			};

			gpio0: gpio@e0600000 {
				compatible = "arm,pl061", "arm,primecell";
				reg = <0xe0600000 0x1000>;
				interrupts = <0 24 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				status = "disabled";
			};

			gpio1: gpio@e0680000 {
				compatible = "arm,pl061", "arm,primecell";
				reg = <0xe0680000 0x1000>;
				interrupts = <0 25 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				status = "disabled";
			};

			kbd@e0300000 {
				compatible = "st,spear300-kbd";
				reg = <0xe0300000 0x1000>;
				interrupts = <0 52 0x4>;
				status = "disabled";
			};

			i2c0: i2c@e0280000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,designware-i2c";
				reg = <0xe0280000 0x1000>;
				interrupts = <0 41 0x4>;
				status = "disabled";
			};

			i2s@e0180000 {
				compatible = "st,designware-i2s";
				reg = <0xe0180000 0x1000>;
				interrupt-names = "play_irq", "record_irq";
				interrupts = <0 10 0x4>,
					     <0 11 0x4>;
				status = "disabled";
			};

			i2s@e0200000 {
				compatible = "st,designware-i2s";
				reg = <0xe0200000 0x1000>;
				interrupt-names = "play_irq", "record_irq";
				interrupts = <0 26 0x4>,
					     <0 53 0x4>;
				status = "disabled";
			};

			spi0: spi@e0100000 {
				compatible = "arm,pl022", "arm,primecell";
				reg = <0xe0100000 0x1000>;
				#address-cells = <1>;
				#size-cells = <0>;
				interrupts = <0 31 0x4>;
				status = "disabled";
				dmas = <&dwdma0 5 0 0>,
					<&dwdma0 4 0 0>;
				dma-names = "rx", "tx";
			};

			rtc@e0580000 {
				compatible = "st,spear600-rtc";
				reg = <0xe0580000 0x1000>;
				interrupts = <0 36 0x4>;
				status = "disabled";
			};

			serial@e0000000 {
				compatible = "arm,pl011", "arm,primecell";
				reg = <0xe0000000 0x1000>;
				interrupts = <0 35 0x4>;
				status = "disabled";
			};

			adc@e0080000 {
				compatible = "st,spear600-adc";
				reg = <0xe0080000 0x1000>;
				interrupts = <0 12 0x4>;
				status = "disabled";
			};

			timer@e0380000 {
				compatible = "st,spear-timer";
				reg = <0xe0380000 0x400>;
				interrupts = <0 37 0x4>;
			};

			timer@ec800600 {
				compatible = "arm,cortex-a9-twd-timer";
				reg = <0xec800600 0x20>;
				interrupts = <1 13 0x4>;
				status = "disabled";
			};

			wdt@ec800620 {
				compatible = "arm,cortex-a9-twd-wdt";
				reg = <0xec800620 0x20>;
				status = "disabled";
			};

			thermal@e07008c4 {
				compatible = "st,thermal-spear1340";
				reg = <0xe07008c4 0x4>;
				thermal_flags = <0x7000>;
			};
		};
	};
};
