/*
 * Copyright 2015 - <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 *     You should have received a copy of the GNU General Public
 *     License along with this file; if not, write to the Free
 *     Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston,
 *     MA 02110-1301 USA
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include "../armv7-m.dtsi"
#include <dt-bindings/clock/stm32fx-clock.h>
#include <dt-bindings/mfd/stm32f4-rcc.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	clocks {
		clk_hse: clk-hse {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <0>;
		};

		clk_lse: clk-lse {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
		};

		clk_lsi: clk-lsi {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32000>;
		};

		clk_i2s_ckin: i2s-ckin {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <0>;
		};
	};

	soc {
		romem: efuse@1fff7800 {
			compatible = "st,stm32f4-otp";
			reg = <0x1fff7800 0x400>;
			#address-cells = <1>;
			#size-cells = <1>;
			ts_cal1: calib@22c {
				reg = <0x22c 0x2>;
			};
			ts_cal2: calib@22e {
				reg = <0x22e 0x2>;
			};
		};

		timers2: timers@40000000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40000000 0x400>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(TIM2)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@1 {
				compatible = "st,stm32-timer-trigger";
				reg = <1>;
				status = "disabled";
			};
		};

		timers3: timers@40000400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40000400 0x400>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(TIM3)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@2 {
				compatible = "st,stm32-timer-trigger";
				reg = <2>;
				status = "disabled";
			};
		};

		timers4: timers@40000800 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40000800 0x400>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(TIM4)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@3 {
				compatible = "st,stm32-timer-trigger";
				reg = <3>;
				status = "disabled";
			};
		};

		timers5: timers@40000c00 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40000C00 0x400>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(TIM5)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@4 {
				compatible = "st,stm32-timer-trigger";
				reg = <4>;
				status = "disabled";
			};
		};

		timers6: timers@40001000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40001000 0x400>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(TIM6)>;
			clock-names = "int";
			status = "disabled";

			timer@5 {
				compatible = "st,stm32-timer-trigger";
				reg = <5>;
				status = "disabled";
			};
		};

		timers7: timers@40001400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40001400 0x400>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(TIM7)>;
			clock-names = "int";
			status = "disabled";

			timer@6 {
				compatible = "st,stm32-timer-trigger";
				reg = <6>;
				status = "disabled";
			};
		};

		timers12: timers@40001800 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40001800 0x400>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(TIM12)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@11 {
				compatible = "st,stm32-timer-trigger";
				reg = <11>;
				status = "disabled";
			};
		};

		timers13: timers@40001c00 {
			compatible = "st,stm32-timers";
			reg = <0x40001C00 0x400>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(TIM13)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		timers14: timers@40002000 {
			compatible = "st,stm32-timers";
			reg = <0x40002000 0x400>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(TIM14)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		rtc: rtc@40002800 {
			compatible = "st,stm32-rtc";
			reg = <0x40002800 0x400>;
			clocks = <&rcc 1 CLK_RTC>;
			assigned-clocks = <&rcc 1 CLK_RTC>;
			assigned-clock-parents = <&rcc 1 CLK_LSE>;
			interrupt-parent = <&exti>;
			interrupts = <17 1>;
			st,syscfg = <&pwrcfg 0x00 0x100>;
			status = "disabled";
		};

		iwdg: watchdog@40003000 {
			compatible = "st,stm32-iwdg";
			reg = <0x40003000 0x400>;
			clocks = <&clk_lsi>;
			clock-names = "lsi";
			status = "disabled";
		};

		spi2: spi@40003800 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32f4-spi";
			reg = <0x40003800 0x400>;
			interrupts = <36>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(SPI2)>;
			status = "disabled";
		};

		spi3: spi@40003c00 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32f4-spi";
			reg = <0x40003c00 0x400>;
			interrupts = <51>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(SPI3)>;
			status = "disabled";
		};

		usart2: serial@40004400 {
			compatible = "st,stm32-uart";
			reg = <0x40004400 0x400>;
			interrupts = <38>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(UART2)>;
			status = "disabled";
		};

		usart3: serial@40004800 {
			compatible = "st,stm32-uart";
			reg = <0x40004800 0x400>;
			interrupts = <39>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(UART3)>;
			status = "disabled";
			dmas = <&dma1 1 4 0x400 0x0>,
			       <&dma1 3 4 0x400 0x0>;
			dma-names = "rx", "tx";
		};

		usart4: serial@40004c00 {
			compatible = "st,stm32-uart";
			reg = <0x40004c00 0x400>;
			interrupts = <52>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(UART4)>;
			status = "disabled";
		};

		usart5: serial@40005000 {
			compatible = "st,stm32-uart";
			reg = <0x40005000 0x400>;
			interrupts = <53>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(UART5)>;
			status = "disabled";
		};

		i2c1: i2c@40005400 {
			compatible = "st,stm32f4-i2c";
			reg = <0x40005400 0x400>;
			interrupts = <31>,
				     <32>;
			resets = <&rcc STM32F4_APB1_RESET(I2C1)>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(I2C1)>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		i2c3: i2c@40005c00 {
			compatible = "st,stm32f4-i2c";
			reg = <0x40005c00 0x400>;
			interrupts = <72>,
				     <73>;
			resets = <&rcc STM32F4_APB1_RESET(I2C3)>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(I2C3)>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		can1: can@40006400 {
			compatible = "st,stm32f4-bxcan";
			reg = <0x40006400 0x200>;
			interrupts = <19>, <20>, <21>, <22>;
			interrupt-names = "tx", "rx0", "rx1", "sce";
			resets = <&rcc STM32F4_APB1_RESET(CAN1)>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(CAN1)>;
			st,can-primary;
			st,gcan = <&gcan>;
			status = "disabled";
		};

		gcan: gcan@40006600 {
			compatible = "st,stm32f4-gcan", "syscon";
			reg = <0x40006600 0x200>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(CAN1)>;
		};

		can2: can@40006800 {
			compatible = "st,stm32f4-bxcan";
			reg = <0x40006800 0x200>;
			interrupts = <63>, <64>, <65>, <66>;
			interrupt-names = "tx", "rx0", "rx1", "sce";
			resets = <&rcc STM32F4_APB1_RESET(CAN2)>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(CAN2)>;
			st,can-secondary;
			st,gcan = <&gcan>;
			status = "disabled";
		};

		dac: dac@40007400 {
			compatible = "st,stm32f4-dac-core";
			reg = <0x40007400 0x400>;
			resets = <&rcc STM32F4_APB1_RESET(DAC)>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(DAC)>;
			clock-names = "pclk";
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			dac1: dac@1 {
				compatible = "st,stm32-dac";
				#io-channel-cells = <1>;
				reg = <1>;
				status = "disabled";
			};

			dac2: dac@2 {
				compatible = "st,stm32-dac";
				#io-channel-cells = <1>;
				reg = <2>;
				status = "disabled";
			};
		};

		usart7: serial@40007800 {
			compatible = "st,stm32-uart";
			reg = <0x40007800 0x400>;
			interrupts = <82>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(UART7)>;
			status = "disabled";
		};

		usart8: serial@40007c00 {
			compatible = "st,stm32-uart";
			reg = <0x40007c00 0x400>;
			interrupts = <83>;
			clocks = <&rcc 0 STM32F4_APB1_CLOCK(UART8)>;
			status = "disabled";
		};

		timers1: timers@40010000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40010000 0x400>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(TIM1)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@0 {
				compatible = "st,stm32-timer-trigger";
				reg = <0>;
				status = "disabled";
			};
		};

		timers8: timers@40010400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40010400 0x400>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(TIM8)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@7 {
				compatible = "st,stm32-timer-trigger";
				reg = <7>;
				status = "disabled";
			};
		};

		usart1: serial@40011000 {
			compatible = "st,stm32-uart";
			reg = <0x40011000 0x400>;
			interrupts = <37>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(USART1)>;
			status = "disabled";
			dmas = <&dma2 2 4 0x400 0x0>,
			       <&dma2 7 4 0x400 0x0>;
			dma-names = "rx", "tx";
		};

		usart6: serial@40011400 {
			compatible = "st,stm32-uart";
			reg = <0x40011400 0x400>;
			interrupts = <71>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(USART6)>;
			status = "disabled";
		};

		adc: adc@40012000 {
			compatible = "st,stm32f4-adc-core";
			reg = <0x40012000 0x400>;
			interrupts = <18>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(ADC1)>;
			clock-names = "adc";
			interrupt-controller;
			#interrupt-cells = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			adc1: adc@0 {
				compatible = "st,stm32f4-adc";
				#io-channel-cells = <1>;
				reg = <0x0>;
				clocks = <&rcc 0 STM32F4_APB2_CLOCK(ADC1)>;
				interrupt-parent = <&adc>;
				interrupts = <0>;
				dmas = <&dma2 0 0 0x400 0x0>;
				dma-names = "rx";
				status = "disabled";
			};

			adc2: adc@100 {
				compatible = "st,stm32f4-adc";
				#io-channel-cells = <1>;
				reg = <0x100>;
				clocks = <&rcc 0 STM32F4_APB2_CLOCK(ADC2)>;
				interrupt-parent = <&adc>;
				interrupts = <1>;
				dmas = <&dma2 3 1 0x400 0x0>;
				dma-names = "rx";
				status = "disabled";
			};

			adc3: adc@200 {
				compatible = "st,stm32f4-adc";
				#io-channel-cells = <1>;
				reg = <0x200>;
				clocks = <&rcc 0 STM32F4_APB2_CLOCK(ADC3)>;
				interrupt-parent = <&adc>;
				interrupts = <2>;
				dmas = <&dma2 1 2 0x400 0x0>;
				dma-names = "rx";
				status = "disabled";
			};
		};

		sdio: mmc@40012c00 {
			compatible = "arm,pl180", "arm,primecell";
			arm,primecell-periphid = <0x00880180>;
			reg = <0x40012c00 0x400>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(SDIO)>;
			clock-names = "apb_pclk";
			interrupts = <49>;
			max-frequency = <48000000>;
			status = "disabled";
		};

		spi1: spi@40013000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32f4-spi";
			reg = <0x40013000 0x400>;
			interrupts = <35>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(SPI1)>;
			status = "disabled";
		};

		spi4: spi@40013400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32f4-spi";
			reg = <0x40013400 0x400>;
			interrupts = <84>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(SPI4)>;
			status = "disabled";
		};

		syscfg: syscon@40013800 {
			compatible = "st,stm32-syscfg", "syscon";
			reg = <0x40013800 0x400>;
		};

		exti: interrupt-controller@40013c00 {
			compatible = "st,stm32-exti";
			interrupt-controller;
			#interrupt-cells = <2>;
			reg = <0x40013C00 0x400>;
			interrupts = <1>, <2>, <3>, <6>, <7>, <8>, <9>, <10>, <23>, <40>, <41>, <42>, <62>, <76>;
		};

		timers9: timers@40014000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40014000 0x400>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(TIM9)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@8 {
				compatible = "st,stm32-timer-trigger";
				reg = <8>;
				status = "disabled";
			};
		};

		timers10: timers@40014400 {
			compatible = "st,stm32-timers";
			reg = <0x40014400 0x400>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(TIM10)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		timers11: timers@40014800 {
			compatible = "st,stm32-timers";
			reg = <0x40014800 0x400>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(TIM11)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		spi5: spi@40015000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32f4-spi";
			reg = <0x40015000 0x400>;
			interrupts = <85>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(SPI5)>;
			dmas = <&dma2 3 2 0x400 0x0>,
				<&dma2 4 2 0x400 0x0>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		spi6: spi@40015400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32f4-spi";
			reg = <0x40015400 0x400>;
			interrupts = <86>;
			clocks = <&rcc 0 STM32F4_APB2_CLOCK(SPI6)>;
			status = "disabled";
		};

		pwrcfg: power-config@40007000 {
			compatible = "st,stm32-power-config", "syscon";
			reg = <0x40007000 0x400>;
		};

		ltdc: display-controller@40016800 {
			compatible = "st,stm32-ltdc";
			reg = <0x40016800 0x200>;
			interrupts = <88>, <89>;
			resets = <&rcc STM32F4_APB2_RESET(LTDC)>;
			clocks = <&rcc 1 CLK_LCD>;
			clock-names = "lcd";
			status = "disabled";
		};

		crc: crc@40023000 {
			compatible = "st,stm32f4-crc";
			reg = <0x40023000 0x400>;
			clocks = <&rcc 0 STM32F4_AHB1_CLOCK(CRC)>;
			status = "disabled";
		};

		rcc: rcc@40023800 {
			#reset-cells = <1>;
			#clock-cells = <2>;
			compatible = "st,stm32f42xx-rcc", "st,stm32-rcc";
			reg = <0x40023800 0x400>;
			clocks = <&clk_hse>, <&clk_i2s_ckin>;
			st,syscfg = <&pwrcfg>;
			assigned-clocks = <&rcc 1 CLK_HSE_RTC>;
			assigned-clock-rates = <1000000>;
		};

		dma1: dma-controller@40026000 {
			compatible = "st,stm32-dma";
			reg = <0x40026000 0x400>;
			interrupts = <11>,
				     <12>,
				     <13>,
				     <14>,
				     <15>,
				     <16>,
				     <17>,
				     <47>;
			clocks = <&rcc 0 STM32F4_AHB1_CLOCK(DMA1)>;
			#dma-cells = <4>;
		};

		dma2: dma-controller@40026400 {
			compatible = "st,stm32-dma";
			reg = <0x40026400 0x400>;
			interrupts = <56>,
				     <57>,
				     <58>,
				     <59>,
				     <60>,
				     <68>,
				     <69>,
				     <70>;
			clocks = <&rcc 0 STM32F4_AHB1_CLOCK(DMA2)>;
			#dma-cells = <4>;
			st,mem2mem;
		};

		mac: ethernet@40028000 {
			compatible = "st,stm32-dwmac", "snps,dwmac-3.50a";
			reg = <0x40028000 0x8000>;
			reg-names = "stmmaceth";
			interrupts = <61>;
			interrupt-names = "macirq";
			clock-names = "stmmaceth", "mac-clk-tx", "mac-clk-rx";
			clocks = <&rcc 0 STM32F4_AHB1_CLOCK(ETHMAC)>,
					<&rcc 0 STM32F4_AHB1_CLOCK(ETHMACTX)>,
					<&rcc 0 STM32F4_AHB1_CLOCK(ETHMACRX)>;
			st,syscon = <&syscfg 0x4>;
			snps,pbl = <8>;
			snps,mixed-burst;
			status = "disabled";
		};

		dma2d: dma2d@4002b000 {
			compatible = "st,stm32-dma2d";
			reg = <0x4002b000 0xc00>;
			interrupts = <90>;
			resets = <&rcc STM32F4_AHB1_RESET(DMA2D)>;
			clocks = <&rcc 0 STM32F4_AHB1_CLOCK(DMA2D)>;
			clock-names = "dma2d";
			status = "disabled";
		};

		usbotg_hs: usb@40040000 {
			compatible = "snps,dwc2";
			reg = <0x40040000 0x40000>;
			interrupts = <77>;
			clocks = <&rcc 0 STM32F4_AHB1_CLOCK(OTGHS)>;
			clock-names = "otg";
			status = "disabled";
		};

		usbotg_fs: usb@50000000 {
			compatible = "st,stm32f4x9-fsotg";
			reg = <0x50000000 0x40000>;
			interrupts = <67>;
			clocks = <&rcc 0 39>;
			clock-names = "otg";
			status = "disabled";
		};

		dcmi: dcmi@50050000 {
			compatible = "st,stm32-dcmi";
			reg = <0x50050000 0x400>;
			interrupts = <78>;
			resets = <&rcc STM32F4_AHB2_RESET(DCMI)>;
			clocks = <&rcc 0 STM32F4_AHB2_CLOCK(DCMI)>;
			clock-names = "mclk";
			pinctrl-names = "default";
			pinctrl-0 = <&dcmi_pins>;
			dmas = <&dma2 1 1 0x414 0x3>;
			dma-names = "tx";
			status = "disabled";
		};

		rng: rng@50060800 {
			compatible = "st,stm32-rng";
			reg = <0x50060800 0x400>;
			clocks = <&rcc 0 STM32F4_AHB2_CLOCK(RNG)>;

		};
	};
};

&systick {
	clocks = <&rcc 1 SYSTICK>;
	status = "okay";
};
