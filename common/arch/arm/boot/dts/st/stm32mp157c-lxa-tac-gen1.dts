// SPDX-License-Identifier: (GPL-2.0-or-later OR BSD-3-Clause)
/*
 * Copyright (C) 2020 STMicroelectronics - All Rights Reserved
 * Copyright (C) 2021 <PERSON><PERSON><PERSON>, Pengutronix
 * Copyright (C) 2023 <PERSON>, Pengutronix
 */

/dts-v1/;

#include "stm32mp157.dtsi"
#include "stm32mp15xc-lxa-tac.dtsi"

/ {
	model = "Linux Automation Test Automation Controller (TAC) Gen 1";
	compatible = "lxa,stm32mp157c-tac-gen1", "oct,stm32mp15xx-osd32", "st,stm32mp157";

	backlight: backlight {
		compatible = "pwm-backlight";
		power-supply = <&v3v3>;

		brightness-levels = <0 31 63 95 127 159 191 223 255>;
		default-brightness-level = <7>;
		pwms = <&backlight_pwm 1 1000000 0>;
	};

	reg_iobus_12v: regulator-iobus-12v {
		compatible = "regulator-fixed";
		vin-supply = <&reg_12v>;

		gpio = <&gpioh 13 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		regulator-max-microvolt = <12000000>;
		regulator-min-microvolt = <12000000>;
		regulator-name = "12V_IOBUS";
	};
};

&gpioa {
	gpio-line-names = "", "", "STACK_CS2", "", "STACK_CS3", /*  0 */
	"ETH_GPIO1", "ETH_INT", "", "", "",                     /*  5 */
	"", "", "", "BOOTROM_LED", "ETH_LAB_LEDRP",             /* 10 */
	"";                                                     /* 15 */
};

&gpioc {
	gpio-line-names = "", "STACK_CS1", "", "", "", /*  0 */
	"", "", "", "", "",                            /*  5 */
	"", "";                                        /* 10 */
};

&gpu {
	status = "disabled";
};

&i2c1 {
	powerboard_gpio: gpio@24 {
		compatible = "nxp,pca9570";
		reg = <0x24>;

		#gpio-cells = <2>;
		gpio-controller;
		gpio-line-names = "DUT_PWR_EN", "DUT_PWR_DISCH", "DUT_PWR_ADCRST", "";
	};
};

&spi2 {
	adc@0 {
		compatible = "ti,lmp92064";
		reg = <0>;
		spi-max-frequency = <5000000>;
		vdd-supply = <&reg_pb_3v3>;
		vdig-supply = <&reg_pb_3v3>;
		reset-gpios = <&powerboard_gpio 2 GPIO_ACTIVE_HIGH>;

		shunt-resistor-micro-ohms = <15000>;
	};
};

&timers1 {
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	status = "okay";

	backlight_pwm: pwm {
		pinctrl-names = "default", "sleep";
		pinctrl-0 = <&pwm1_pins_c>;
		pinctrl-1 = <&pwm1_sleep_pins_c>;

		status = "okay";
	};
};
