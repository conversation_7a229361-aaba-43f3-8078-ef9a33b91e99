/*
 * Copyright 2017 - <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "stm32f746.dtsi"
#include "stm32f769-pinctrl.dtsi"
#include <dt-bindings/input/input.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "STMicroelectronics STM32F769-DISCO board";
	compatible = "st,stm32f769-disco", "st,stm32f769";

	chosen {
		bootargs = "root=/dev/ram";
		stdout-path = "serial0:115200n8";
	};

	memory@c0000000 {
		device_type = "memory";
		reg = <0xC0000000 0x1000000>;
	};

	aliases {
		serial0 = &usart1;
	};

	leds {
		compatible = "gpio-leds";
		led-green {
			gpios = <&gpioj 5 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
		};
		led-red {
			gpios = <&gpioj 13 GPIO_ACTIVE_HIGH>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;
		button-0 {
			label = "User";
			linux,code = <KEY_HOME>;
			gpios = <&gpioa 0 GPIO_ACTIVE_HIGH>;
		};
	};

	usbotg_hs_phy: usb-phy {
		#phy-cells = <0>;
		compatible = "usb-nop-xceiv";
		clocks = <&rcc 0 STM32F7_AHB1_CLOCK(OTGHSULPI)>;
		clock-names = "main_clk";
	};

	mmc_vcard: mmc_vcard {
		compatible = "regulator-fixed";
		regulator-name = "mmc_vcard";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};
};

&rcc {
	compatible = "st,stm32f769-rcc", "st,stm32f746-rcc", "st,stm32-rcc";
};

&cec {
	pinctrl-0 = <&cec_pins_a>;
	pinctrl-names = "default";
	status = "okay";
};

&clk_hse {
	clock-frequency = <25000000>;
};

&i2c1 {
	pinctrl-0 = <&i2c1_pins_b>;
	pinctrl-names = "default";
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
};

&rtc {
	status = "okay";
};

&sdio2 {
	status = "okay";
	vmmc-supply = <&mmc_vcard>;
	cd-gpios = <&gpioi 15 GPIO_ACTIVE_LOW>;
	broken-cd;
	pinctrl-names = "default", "opendrain";
	pinctrl-0 = <&sdio_pins_b>;
	pinctrl-1 = <&sdio_pins_od_b>;
	bus-width = <4>;
};

&timers5 {
	/* Override timer5 to act as clockevent */
	compatible = "st,stm32-timer";
	interrupts = <50>;
	status = "okay";
	/delete-property/#address-cells;
	/delete-property/#size-cells;
	/delete-property/clock-names;
	/delete-node/pwm;
	/delete-node/timer@4;
};

&usart1 {
	pinctrl-0 = <&usart1_pins_a>;
	pinctrl-names = "default";
	status = "okay";
};

&usbotg_hs {
	dr_mode = "otg";
	phys = <&usbotg_hs_phy>;
	phy-names = "usb2-phy";
	pinctrl-0 = <&usbotg_hs_pins_a>;
	pinctrl-names = "default";
	status = "okay";
};
