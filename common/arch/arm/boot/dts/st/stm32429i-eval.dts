/*
 * Copyright 2015 - <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 *     You should have received a copy of the GNU General Public
 *     License along with this file; if not, write to the Free
 *     Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston,
 *     MA 02110-1301 USA
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "stm32f429.dtsi"
#include "stm32f429-pinctrl.dtsi"
#include <dt-bindings/input/input.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/media/video-interfaces.h>

/ {
	model = "STMicroelectronics STM32429i-EVAL board";
	compatible = "st,stm32429i-eval", "st,stm32f429";

	chosen {
		bootargs = "root=/dev/ram";
		stdout-path = "serial0:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x2000000>;
	};

	aliases {
		serial0 = &usart1;
	};

	clocks {
		clk_ext_camera: clk-ext-camera {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
		};
	};

	soc {
		dma-ranges = <0xc0000000 0x0 0x10000000>;
	};

	vdda: regulator-vdda {
		compatible = "regulator-fixed";
		regulator-name = "vdda";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	vref: regulator-vref {
		compatible = "regulator-fixed";
		regulator-name = "vref";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	vdd_panel: vdd-panel {
		compatible = "regulator-fixed";
		regulator-name = "vdd_panel";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	leds {
		compatible = "gpio-leds";
		led-green {
			gpios = <&gpiog 6 1>;
			linux,default-trigger = "heartbeat";
		};
		led-orange {
			gpios = <&gpiog 7 1>;
		};
		led-red {
			gpios = <&gpiog 10 1>;
		};
		led-blue {
			gpios = <&gpiog 12 1>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;
		button-0 {
			label = "Wake up";
			linux,code = <KEY_WAKEUP>;
			gpios = <&gpioa 0 0>;
		};
		button-1 {
			label = "Tamper";
			linux,code = <KEY_RESTART>;
			gpios = <&gpioc 13 0>;
		};
	};

	usbotg_hs_phy: usbphy {
		#phy-cells = <0>;
		compatible = "usb-nop-xceiv";
		clocks = <&rcc 0 STM32F4_AHB1_CLOCK(OTGHSULPI)>;
		clock-names = "main_clk";
	};

	panel_rgb: panel-rgb {
		compatible = "ampire,am-480272h3tmqw-t01h";
		power-supply = <&vdd_panel>;
		status = "okay";
		port {
			panel_in_rgb: endpoint {
				remote-endpoint = <&ltdc_out_rgb>;
			};
		};
	};

	mmc_vcard: mmc_vcard {
		compatible = "regulator-fixed";
		regulator-name = "mmc_vcard";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};
};

&adc {
	pinctrl-names = "default";
	pinctrl-0 = <&adc3_in8_pin>;
	vdda-supply = <&vdda>;
	vref-supply = <&vref>;
	status = "okay";
	adc3: adc@200 {
		st,adc-channels = <8>;
		status = "okay";
	};
};

&clk_hse {
	clock-frequency = <25000000>;
};

&crc {
	status = "okay";
};

&dcmi {
	status = "okay";

	port {
		dcmi_0: endpoint {
			remote-endpoint = <&ov2640_0>;
			bus-type = <MEDIA_BUS_TYPE_PARALLEL>;
			bus-width = <8>;
			hsync-active = <0>;
			vsync-active = <0>;
			pclk-sample = <1>;
		};
	};
};

&i2c1 {
	pinctrl-0 = <&i2c1_pins>;
	pinctrl-names = "default";
	status = "okay";

	ov2640: camera@30 {
		compatible = "ovti,ov2640";
		reg = <0x30>;
		resetb-gpios = <&stmpegpio 2 GPIO_ACTIVE_HIGH>;
		pwdn-gpios = <&stmpegpio 0 GPIO_ACTIVE_LOW>;
		clocks = <&clk_ext_camera>;
		clock-names = "xvclk";
		status = "okay";

		port {
			ov2640_0: endpoint {
				remote-endpoint = <&dcmi_0>;
			};
		};
	};

	stmpe1600: stmpe1600@42 {
		compatible = "st,stmpe1600";
		reg = <0x42>;
		interrupts = <8 3>;
		interrupt-parent = <&gpioi>;
		wakeup-source;

		stmpegpio: stmpe_gpio {
			compatible = "st,stmpe-gpio";
			gpio-controller;
			#gpio-cells = <2>;
		};
	};
};

&iwdg {
	status = "okay";
	timeout-sec = <32>;
};

&ltdc {
	status = "okay";
	pinctrl-0 = <&ltdc_pins_a>;
	pinctrl-names = "default";

	port {
		ltdc_out_rgb: endpoint {
			remote-endpoint = <&panel_in_rgb>;
		};
	};
};

&mac {
	status = "okay";
	pinctrl-0 = <&ethernet_mii>;
	pinctrl-names = "default";
	phy-mode = "mii";
	phy-handle = <&phy1>;
	mdio0 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "snps,dwmac-mdio";
		phy1: ethernet-phy@1 {
			reg = <1>;
		};
	};
};

&rtc {
	status = "okay";
};

&sdio {
	status = "okay";
	vmmc-supply = <&mmc_vcard>;
	cd-gpios = <&stmpegpio 15 GPIO_ACTIVE_LOW>;
	pinctrl-names = "default", "opendrain";
	pinctrl-0 = <&sdio_pins>;
	pinctrl-1 = <&sdio_pins_od>;
	bus-width = <4>;
	max-frequency = <12500000>;
};

&timers1 {
	status = "okay";

	pwm {
		pinctrl-0 = <&pwm1_pins>;
		pinctrl-names = "default";
		status = "okay";
	};

	timer@0 {
		status = "okay";
	};
};

&timers3 {
	status = "okay";

	pwm {
		pinctrl-0 = <&pwm3_pins>;
		pinctrl-names = "default";
		status = "okay";
	};

	timer@2 {
		status = "okay";
	};
};

&timers5 {
	/* Override timer5 to act as clockevent */
	compatible = "st,stm32-timer";
	interrupts = <50>;
	status = "okay";
	/delete-property/#address-cells;
	/delete-property/#size-cells;
	/delete-property/clock-names;
	/delete-node/pwm;
	/delete-node/timer@4;
};

&usart1 {
	pinctrl-0 = <&usart1_pins_a>;
	pinctrl-names = "default";
	status = "okay";
};

&usbotg_hs {
	dr_mode = "host";
	phys = <&usbotg_hs_phy>;
	phy-names = "usb2-phy";
	pinctrl-0 = <&usbotg_hs_pins_a>;
	pinctrl-names = "default";
	status = "okay";
};
