// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * DTS file for SPEAr300 SoC
 *
 * Copyright 2012 V<PERSON><PERSON> <<EMAIL>>
 */

/include/ "spear3xx.dtsi"

/ {
	ahb {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "simple-bus";
		ranges = <0x60000000 0x60000000 0x50000000
			  0xd0000000 0xd0000000 0x30000000>;

		pinmux@99000000 {
			compatible = "st,spear300-pinmux";
			reg = <0x99000000 0x1000>;
		};

		clcd@60000000 {
			compatible = "arm,pl110", "arm,primecell";
			reg = <0x60000000 0x1000>;
			interrupts = <30>;
			status = "disabled";
		};

		fsmc: flash@94000000 {
			compatible = "st,spear600-fsmc-nand";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x94000000 0x1000	/* FSMC Register */
			       0x80000000 0x0010	/* NAND Base DATA */
			       0x80020000 0x0010	/* NAND Base ADDR */
			       0x80010000 0x0010>;	/* NAND Base CMD */
			reg-names = "fsmc_regs", "nand_data", "nand_addr", "nand_cmd";
			status = "disabled";
		};

		sdhci@70000000 {
			compatible = "st,sdhci-spear";
			reg = <0x70000000 0x100>;
			interrupts = <1>;
			status = "disabled";
		};

		shirq: interrupt-controller@50000000 {
			compatible = "st,spear300-shirq";
			reg = <0x50000000 0x1000>;
			interrupts = <28>;
			#interrupt-cells = <1>;
			interrupt-controller;
		};

		apb {
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "simple-bus";
			ranges = <0xa0000000 0xa0000000 0x10000000
				  0xd0000000 0xd0000000 0x30000000>;

			gpio1: gpio@a9000000 {
				#gpio-cells = <2>;
				compatible = "arm,pl061", "arm,primecell";
				gpio-controller;
				reg = <0xa9000000 0x1000>;
				interrupts = <8>;
				interrupt-parent = <&shirq>;
				status = "disabled";
			};

			kbd@a0000000 {
				compatible = "st,spear300-kbd";
				reg = <0xa0000000 0x1000>;
				interrupts = <7>;
				interrupt-parent = <&shirq>;
				status = "disabled";
			};
		};
	};
};
