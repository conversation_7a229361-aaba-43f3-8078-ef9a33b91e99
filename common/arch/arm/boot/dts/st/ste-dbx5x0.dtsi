// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright 2012 Linaro Ltd
 */

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/clock/ste-db8500-clkout.h>
#include <dt-bindings/reset/stericsson,db8500-prcc-reset.h>
#include <dt-bindings/mfd/dbx500-prcmu.h>
#include <dt-bindings/arm/ux500_pm_domains.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/thermal/thermal.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	/* This stablilizes the device enumeration */
	aliases {
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		i2c3 = &i2c3;
		i2c4 = &i2c4;
		spi0 = &spi0;
		spi1 = &spi1;
		spi2 = &spi2;
		spi3 = &spi3;
		serial0 = &serial0;
		serial1 = &serial1;
		serial2 = &serial2;
	};

	chosen {
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		enable-method = "ste,dbx500-smp";

		cpu-map {
			cluster0 {
				core0 {
					cpu = <&CPU0>;
				};
				core1 {
					cpu = <&CPU1>;
				};
			};
		};
		CPU0: cpu@300 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0x300>;
			clocks = <&prcmu_clk PRCMU_ARMSS>;
			clock-names = "cpu";
			clock-latency = <20000>;
			#cooling-cells = <2>;
		};
		CPU1: cpu@301 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0x301>;
		};
	};

	thermal-zones {
		/*
		 * Thermal zone for the SoC, using the thermal sensor in the
		 * PRCMU for temperature and the cpufreq driver for passive
		 * cooling.
		 */
		cpu_thermal: cpu-thermal {
			polling-delay-passive = <250>;
			/*
			 * This sensor fires interrupts to update the thermal
			 * zone, so no polling is needed.
			 */
			polling-delay = <0>;

			thermal-sensors = <&thermal>;

			trips {
				cpu_alert: cpu-alert {
					temperature = <70000>;
					hysteresis = <2000>;
					type = "passive";
				};
				cpu-crit {
					temperature = <85000>;
					hysteresis = <0>;
					type = "critical";
				};
			};

			cooling-maps {
				trip = <&cpu_alert>;
				cooling-device = <&CPU0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				contribution = <100>;
			};
		};
	};

	soc {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "stericsson,db8500", "simple-bus";
		interrupt-parent = <&intc>;
		ranges;

		/*
		 * 640KB ESRAM (embedded static random access memory), divided
		 * into 5 banks of 128 KB each. This is a fast memory usually
		 * used by different accelerators. We group these according to
		 * their power domains: ESRAM0 (always on) ESRAM 1+2 and
		 * ESRAM 3+4.
		 */
		sram@******** {
			/* The first (always on) ESRAM 0, 128 KB */
			compatible = "mmio-sram";
			reg = <0x******** 0x20000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x******** 0x20000>;

			sram@0 {
				compatible = "stericsson,u8500-esram";
				reg = <0x0 0x10000>;
				pool;
			};
			lcpa: sram@10000 {
				/*
				 * This eSRAM is used by the DMA40 DMA controller
				 * for Logical Channel Paramers (LCP), the address
				 * where these parameters are stored is called "LCPA".
				 * This is addressed directly by the driver so no
				 * pool is used.
				 */
				compatible = "stericsson,u8500-esram";
				label = "DMA40-LCPA";
				reg = <0x10000 0x800>;
			};
			sram@10800 {
				compatible = "stericsson,u8500-esram";
				reg = <0x10800 0xf800>;
				pool;
			};
		};
		sram@40020000 {
			/* ESRAM 1+2, 256 KB */
			compatible = "mmio-sram";
			reg = <0x40020000 0x40000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x40020000 0x40000>;
		};
		sram@40060000 {
			/* ESRAM 3+4, 256 KB */
			compatible = "mmio-sram";
			reg = <0x40060000 0x40000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x40060000 0x40000>;

			lcla: sram@20000 {
				/*
				 * This eSRAM is used by the DMA40 DMA controller
				 * for Logical Channel Logical Addresses (LCLA), the address
				 * where these parameters are stored is called "LCLA".
				 * This is addressed directly by the driver so no
				 * pool is used.
				 */
				compatible = "stericsson,u8500-esram";
				label = "DMA40-LCLA";
				reg = <0x20000 0x2000>;
			};
		};

		ptm@801ae000 {
			compatible = "arm,coresight-etm3x", "arm,primecell";
			reg = <0x801ae000 0x1000>;

			clocks = <&prcmu_clk PRCMU_APETRACECLK>, <&prcmu_clk PRCMU_APEATCLK>;
			clock-names = "apb_pclk", "atclk";
			cpu = <&CPU0>;
			out-ports {
				port {
					ptm0_out_port: endpoint {
						remote-endpoint = <&funnel_in_port0>;
					};
				};
			};
		};

		ptm@801af000 {
			compatible = "arm,coresight-etm3x", "arm,primecell";
			reg = <0x801af000 0x1000>;

			clocks = <&prcmu_clk PRCMU_APETRACECLK>, <&prcmu_clk PRCMU_APEATCLK>;
			clock-names = "apb_pclk", "atclk";
			cpu = <&CPU1>;
			out-ports {
				port {
					ptm1_out_port: endpoint {
						remote-endpoint = <&funnel_in_port1>;
					};
				};
			};
		};

		funnel@801a6000 {
			compatible = "arm,coresight-dynamic-funnel", "arm,primecell";
			reg = <0x801a6000 0x1000>;

			clocks = <&prcmu_clk PRCMU_APETRACECLK>, <&prcmu_clk PRCMU_APEATCLK>;
			clock-names = "apb_pclk", "atclk";
			out-ports {
				port {
					funnel_out_port: endpoint {
						remote-endpoint =
							<&replicator_in_port0>;
					};
				};
			};

			in-ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;
					funnel_in_port0: endpoint {
						remote-endpoint = <&ptm0_out_port>;
					};
				};

				port@1 {
					reg = <1>;
					funnel_in_port1: endpoint {
						remote-endpoint = <&ptm1_out_port>;
					};
				};
			};
		};

		replicator {
			compatible = "arm,coresight-static-replicator";
			clocks = <&prcmu_clk PRCMU_APEATCLK>;
			clock-names = "atclk";

			out-ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;
					replicator_out_port0: endpoint {
						remote-endpoint = <&tpiu_in_port>;
					};
				};
				port@1 {
					reg = <1>;
					replicator_out_port1: endpoint {
						remote-endpoint = <&etb_in_port>;
					};
				};
			};

			in-ports {
				port {
					replicator_in_port0: endpoint {
						remote-endpoint = <&funnel_out_port>;
					};
				};
			};
		};

		tpiu@80190000 {
			compatible = "arm,coresight-tpiu", "arm,primecell";
			reg = <0x80190000 0x1000>;

			clocks = <&prcmu_clk PRCMU_APETRACECLK>, <&prcmu_clk PRCMU_APEATCLK>;
			clock-names = "apb_pclk", "atclk";
			in-ports {
				port {
					tpiu_in_port: endpoint {
						remote-endpoint = <&replicator_out_port0>;
					};
				};
			};
		};

		etb@801a4000 {
			compatible = "arm,coresight-etb10", "arm,primecell";
			reg = <0x801a4000 0x1000>;

			clocks = <&prcmu_clk PRCMU_APETRACECLK>, <&prcmu_clk PRCMU_APEATCLK>;
			clock-names = "apb_pclk", "atclk";
			in-ports {
				port {
					etb_in_port: endpoint {
						remote-endpoint = <&replicator_out_port1>;
					};
				};
			};
		};

		intc: interrupt-controller@a0411000 {
			compatible = "arm,cortex-a9-gic";
			#interrupt-cells = <3>;
			#address-cells = <1>;
			interrupt-controller;
			reg = <0xa0411000 0x1000>,
			      <0xa0410100 0x100>;
		};

		scu@a0410000 {
			compatible = "arm,cortex-a9-scu";
			reg = <0xa0410000 0x100>;
		};

		/*
		 * The backup RAM is used for retention during sleep
		 * and various things like spin tables
		 */
		backupram@80150000 {
			compatible = "ste,dbx500-backupram";
			reg = <0x80150000 0x2000>;
		};

		L2: cache-controller {
			compatible = "arm,pl310-cache";
			reg = <0xa0412000 0x1000>;
			interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
			cache-unified;
			cache-level = <2>;
		};

		pmu {
			compatible = "arm,cortex-a9-pmu";
			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
		};

		pm_domains: pm_domains0 {
			compatible = "stericsson,ux500-pm-domains";
			#power-domain-cells = <1>;
		};

		clocks {
			compatible = "stericsson,u8500-clks";
			/*
			 * Registers for the CLKRST block on peripheral
			 * groups 1, 2, 3, 5, 6,
			 */
			reg = <0x8012f000 0x1000>, <0x8011f000 0x1000>,
			    <0x8000f000 0x1000>, <0xa03ff000 0x1000>,
			    <0xa03cf000 0x1000>;

			prcmu_clk: prcmu-clock {
				#clock-cells = <1>;
			};

			prcc_pclk: prcc-periph-clock {
				#clock-cells = <2>;
			};

			prcc_kclk: prcc-kernel-clock {
				#clock-cells = <2>;
			};

			prcc_reset: prcc-reset-controller {
				#reset-cells = <2>;
			};

			rtc_clk: rtc32k-clock {
				#clock-cells = <0>;
			};

			smp_twd_clk: smp-twd-clock {
				#clock-cells = <0>;
			};

			clkout_clk: clkout-clock {
				/* Cell 1 id, cell 2 source, cell 3 div */
				#clock-cells = <3>;
			};
		};

		mtu@a03c6000 {
			/* Nomadik System Timer */
			compatible = "st,nomadik-mtu";
			reg = <0xa03c6000 0x1000>;
			interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;

			clocks = <&prcmu_clk PRCMU_TIMCLK>, <&prcc_pclk 6 6>;
			clock-names = "timclk", "apb_pclk";
		};

		timer@a0410600 {
			compatible = "arm,cortex-a9-twd-timer";
			reg = <0xa0410600 0x20>;
			interrupts = <GIC_PPI 13 (GIC_CPU_MASK_RAW(3) | IRQ_TYPE_LEVEL_HIGH)>;

			clocks = <&smp_twd_clk>;
		};

		watchdog@a0410620 {
			compatible = "arm,cortex-a9-twd-wdt";
			reg = <0xa0410620 0x20>;
			interrupts = <GIC_PPI 14 (GIC_CPU_MASK_RAW(3) | IRQ_TYPE_LEVEL_HIGH)>;
			clocks = <&smp_twd_clk>;
		};

		rtc@******** {
			compatible = "arm,pl031", "arm,primecell";
			reg = <0x******** 0x1000>;
			interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>;

			clocks = <&rtc_clk>;
			clock-names = "apb_pclk";
		};

		gpio0: gpio@8012e000 {
			compatible = "stericsson,db8500-gpio",
				"st,nomadik-gpio";
			reg =  <0x8012e000 0x80>;
			interrupts = <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			st,supports-sleepmode;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-bank = <0>;
			gpio-ranges = <&pinctrl 0 0 32>;
			clocks = <&prcc_pclk 1 9>;
		};

		gpio1: gpio@8012e080 {
			compatible = "stericsson,db8500-gpio",
				"st,nomadik-gpio";
			reg =  <0x8012e080 0x80>;
			interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			st,supports-sleepmode;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-bank = <1>;
			gpio-ranges = <&pinctrl 0 32 5>;
			clocks = <&prcc_pclk 1 9>;
		};

		gpio2: gpio@8000e000 {
			compatible = "stericsson,db8500-gpio",
				"st,nomadik-gpio";
			reg =  <0x8000e000 0x80>;
			interrupts = <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			st,supports-sleepmode;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-bank = <2>;
			gpio-ranges = <&pinctrl 0 64 32>;
			clocks = <&prcc_pclk 3 8>;
		};

		gpio3: gpio@8000e080 {
			compatible = "stericsson,db8500-gpio",
				"st,nomadik-gpio";
			reg =  <0x8000e080 0x80>;
			interrupts = <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			st,supports-sleepmode;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-bank = <3>;
			gpio-ranges = <&pinctrl 0 96 2>;
			clocks = <&prcc_pclk 3 8>;
		};

		gpio4: gpio@8000e100 {
			compatible = "stericsson,db8500-gpio",
				"st,nomadik-gpio";
			reg =  <0x8000e100 0x80>;
			interrupts = <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			st,supports-sleepmode;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-bank = <4>;
			gpio-ranges = <&pinctrl 0 128 32>;
			clocks = <&prcc_pclk 3 8>;
		};

		gpio5: gpio@8000e180 {
			compatible = "stericsson,db8500-gpio",
				"st,nomadik-gpio";
			reg =  <0x8000e180 0x80>;
			interrupts = <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			st,supports-sleepmode;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-bank = <5>;
			gpio-ranges = <&pinctrl 0 160 12>;
			clocks = <&prcc_pclk 3 8>;
		};

		gpio6: gpio@8011e000 {
			compatible = "stericsson,db8500-gpio",
				"st,nomadik-gpio";
			reg =  <0x8011e000 0x80>;
			interrupts = <GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			st,supports-sleepmode;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-bank = <6>;
			gpio-ranges = <&pinctrl 0 192 32>;
			clocks = <&prcc_pclk 2 11>;
		};

		gpio7: gpio@8011e080 {
			compatible = "stericsson,db8500-gpio",
				"st,nomadik-gpio";
			reg =  <0x8011e080 0x80>;
			interrupts = <GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			st,supports-sleepmode;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-bank = <7>;
			gpio-ranges = <&pinctrl 0 224 7>;
			clocks = <&prcc_pclk 2 11>;
		};

		gpio8: gpio@a03fe000 {
			compatible = "stericsson,db8500-gpio",
				"st,nomadik-gpio";
			reg =  <0xa03fe000 0x80>;
			interrupts = <GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			st,supports-sleepmode;
			gpio-controller;
			#gpio-cells = <2>;
			gpio-bank = <8>;
			gpio-ranges = <&pinctrl 0 256 12>;
			clocks = <&prcc_pclk 5 1>;
		};

		pinctrl: pinctrl {
			compatible = "stericsson,db8500-pinctrl";
			nomadik-gpio-chips = <&gpio0>, <&gpio1>, <&gpio2>, <&gpio3>,
						<&gpio4>, <&gpio5>, <&gpio6>, <&gpio7>,
						<&gpio8>;
			prcm = <&prcmu>;
		};

		usb_per5@a03e0000 {
			compatible = "stericsson,db8500-musb";
			reg = <0xa03e0000 0x10000>;
			interrupts = <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "mc";

			dr_mode = "otg";

			dmas = <&dma 38 0 0x2>, /* Logical - DevToMem */
			       <&dma 38 0 0x0>, /* Logical - MemToDev */
			       <&dma 37 0 0x2>, /* Logical - DevToMem */
			       <&dma 37 0 0x0>, /* Logical - MemToDev */
			       <&dma 36 0 0x2>, /* Logical - DevToMem */
			       <&dma 36 0 0x0>, /* Logical - MemToDev */
			       <&dma 19 0 0x2>, /* Logical - DevToMem */
			       <&dma 19 0 0x0>, /* Logical - MemToDev */
			       <&dma 18 0 0x2>, /* Logical - DevToMem */
			       <&dma 18 0 0x0>, /* Logical - MemToDev */
			       <&dma 17 0 0x2>, /* Logical - DevToMem */
			       <&dma 17 0 0x0>, /* Logical - MemToDev */
			       <&dma 16 0 0x2>, /* Logical - DevToMem */
			       <&dma 16 0 0x0>, /* Logical - MemToDev */
			       <&dma 39 0 0x2>, /* Logical - DevToMem */
			       <&dma 39 0 0x0>; /* Logical - MemToDev */

			dma-names = "iep_1_9",  "oep_1_9",
				    "iep_2_10", "oep_2_10",
				    "iep_3_11", "oep_3_11",
				    "iep_4_12", "oep_4_12",
				    "iep_5_13", "oep_5_13",
				    "iep_6_14", "oep_6_14",
				    "iep_7_15", "oep_7_15",
				    "iep_8",    "oep_8";

			clocks = <&prcc_pclk 5 0>;
		};

		dma: dma-controller@801C0000 {
			compatible = "stericsson,db8500-dma40", "stericsson,dma40";
			reg = <0x801C0000 0x1000>;
			reg-names = "base";
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			sram = <&lcpa>, <&lcla>;

			#dma-cells = <3>;
			memcpy-channels = <56 57 58 59 60>;

			clocks = <&prcmu_clk PRCMU_DMACLK>;
		};

		prcmu: prcmu@80157000 {
			compatible = "stericsson,db8500-prcmu", "syscon";
			reg = <0x80157000 0x2000>, <0x801b0000 0x8000>, <0x801b8000 0x1000>;
			reg-names = "prcmu", "prcmu-tcpm", "prcmu-tcdm";
			interrupts = <GIC_SPI 47 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <1>;
			interrupt-controller;
			#interrupt-cells = <2>;
			ranges;

			prcmu-timer-4@80157450 {
				compatible = "stericsson,db8500-prcmu-timer-4";
				reg = <0x80157450 0xC>;
			};

			thermal: thermal@801573c0 {
				compatible = "stericsson,db8500-thermal";
				reg = <0x801573c0 0x40>;
				interrupt-parent = <&prcmu>;
				interrupts = <21 IRQ_TYPE_LEVEL_HIGH>,
					     <22 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names = "IRQ_HOTMON_LOW", "IRQ_HOTMON_HIGH";
				#thermal-sensor-cells = <0>;
			};

			db8500-prcmu-regulators {
				compatible = "stericsson,db8500-prcmu-regulator";

				// DB8500_REGULATOR_VAPE
				db8500_vape_reg: db8500_vape {
					regulator-always-on;
				};

				// DB8500_REGULATOR_VARM
				db8500_varm_reg: db8500_varm {
				};

				// DB8500_REGULATOR_VMODEM
				db8500_vmodem_reg: db8500_vmodem {
				};

				// DB8500_REGULATOR_VPLL
				db8500_vpll_reg: db8500_vpll {
				};

				// DB8500_REGULATOR_VSMPS1
				db8500_vsmps1_reg: db8500_vsmps1 {
				};

				// DB8500_REGULATOR_VSMPS2
				db8500_vsmps2_reg: db8500_vsmps2 {
				};

				// DB8500_REGULATOR_VSMPS3
				db8500_vsmps3_reg: db8500_vsmps3 {
				};

				// DB8500_REGULATOR_VRF1
				db8500_vrf1_reg: db8500_vrf1 {
				};

				// DB8500_REGULATOR_SWITCH_SVAMMDSP
				db8500_sva_mmdsp_reg: db8500_sva_mmdsp {
				};

				// DB8500_REGULATOR_SWITCH_SVAMMDSPRET
				db8500_sva_mmdsp_ret_reg: db8500_sva_mmdsp_ret {
				};

				// DB8500_REGULATOR_SWITCH_SVAPIPE
				db8500_sva_pipe_reg: db8500_sva_pipe {
				};

				// DB8500_REGULATOR_SWITCH_SIAMMDSP
				db8500_sia_mmdsp_reg: db8500_sia_mmdsp {
				};

				// DB8500_REGULATOR_SWITCH_SIAMMDSPRET
				db8500_sia_mmdsp_ret_reg: db8500_sia_mmdsp_ret {
				};

				// DB8500_REGULATOR_SWITCH_SIAPIPE
				db8500_sia_pipe_reg: db8500_sia_pipe {
				};

				// DB8500_REGULATOR_SWITCH_SGA
				db8500_sga_reg: db8500_sga {
					vin-supply = <&db8500_vape_reg>;
				};

				// DB8500_REGULATOR_SWITCH_B2R2_MCDE
				db8500_b2r2_mcde_reg: db8500_b2r2_mcde {
					vin-supply = <&db8500_vape_reg>;
				};

				// DB8500_REGULATOR_SWITCH_ESRAM12
				db8500_esram12_reg: db8500_esram12 {
				};

				// DB8500_REGULATOR_SWITCH_ESRAM12RET
				db8500_esram12_ret_reg: db8500_esram12_ret {
				};

				// DB8500_REGULATOR_SWITCH_ESRAM34
				db8500_esram34_reg: db8500_esram34 {
				};

				// DB8500_REGULATOR_SWITCH_ESRAM34RET
				db8500_esram34_ret_reg: db8500_esram34_ret {
				};
			};
		};

		i2c0: i2c@80004000 {
			compatible = "stericsson,db8500-i2c", "st,nomadik-i2c", "arm,primecell";
			reg = <0x80004000 0x1000>;
			interrupts = <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>;

			#address-cells = <1>;
			#size-cells = <0>;

			clock-frequency = <400000>;
			clocks = <&prcc_kclk 3 3>, <&prcc_pclk 3 3>;
			clock-names = "i2cclk", "apb_pclk";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_3 DB8500_PRCC_3_RESET_I2C0>;

			status = "disabled";
		};

		i2c1: i2c@80122000 {
			compatible = "stericsson,db8500-i2c", "st,nomadik-i2c", "arm,primecell";
			reg = <0x80122000 0x1000>;
			interrupts = <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>;

			#address-cells = <1>;
			#size-cells = <0>;

			clock-frequency = <400000>;

			clocks = <&prcc_kclk 1 2>, <&prcc_pclk 1 2>;
			clock-names = "i2cclk", "apb_pclk";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_1 DB8500_PRCC_1_RESET_I2C1>;

			status = "disabled";
		};

		i2c2: i2c@80128000 {
			compatible = "stericsson,db8500-i2c", "st,nomadik-i2c", "arm,primecell";
			reg = <0x80128000 0x1000>;
			interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;

			#address-cells = <1>;
			#size-cells = <0>;

			clock-frequency = <400000>;

			clocks = <&prcc_kclk 1 6>, <&prcc_pclk 1 6>;
			clock-names = "i2cclk", "apb_pclk";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_1 DB8500_PRCC_1_RESET_I2C2>;

			status = "disabled";
		};

		i2c3: i2c@80110000 {
			compatible = "stericsson,db8500-i2c", "st,nomadik-i2c", "arm,primecell";
			reg = <0x80110000 0x1000>;
			interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;

			#address-cells = <1>;
			#size-cells = <0>;

			clock-frequency = <400000>;

			clocks = <&prcc_kclk 2 0>, <&prcc_pclk 2 0>;
			clock-names = "i2cclk", "apb_pclk";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_2 DB8500_PRCC_2_RESET_I2C3>;

			status = "disabled";
		};

		i2c4: i2c@8012a000 {
			compatible = "stericsson,db8500-i2c", "st,nomadik-i2c", "arm,primecell";
			reg = <0x8012a000 0x1000>;
			interrupts = <GIC_SPI 51 IRQ_TYPE_LEVEL_HIGH>;

			#address-cells = <1>;
			#size-cells = <0>;

			clock-frequency = <400000>;

			clocks = <&prcc_kclk 1 9>, <&prcc_pclk 1 10>;
			clock-names = "i2cclk", "apb_pclk";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_1 DB8500_PRCC_1_RESET_I2C4>;

			status = "disabled";
		};

		ssp0: spi@80002000 {
			compatible = "arm,pl022", "arm,primecell";
			reg = <0x80002000 0x1000>;
			interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&prcc_kclk 3 1>, <&prcc_pclk 3 1>;
			clock-names = "sspclk", "apb_pclk";
			dmas = <&dma 8 0 0x2>, /* Logical - DevToMem */
			       <&dma 8 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_3 DB8500_PRCC_3_RESET_SSP0>;

			status = "disabled";
		};

		ssp1: spi@80003000 {
			compatible = "arm,pl022", "arm,primecell";
			reg = <0x80003000 0x1000>;
			interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&prcc_kclk 3 2>, <&prcc_pclk 3 2>;
			clock-names = "sspclk", "apb_pclk";
			dmas = <&dma 9 0 0x2>, /* Logical - DevToMem */
			       <&dma 9 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_3 DB8500_PRCC_3_RESET_SSP1>;

			status = "disabled";
		};

		spi0: spi@8011a000 {
			compatible = "arm,pl022", "arm,primecell";
			reg = <0x8011a000 0x1000>;
			interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			/* Same clock wired to kernel and pclk */
			clocks = <&prcc_pclk 2 8>, <&prcc_pclk 2 8>;
			clock-names = "sspclk", "apb_pclk";
			dmas = <&dma 0 0 0x2>, /* Logical - DevToMem */
			       <&dma 0 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";
			power-domains = <&pm_domains DOMAIN_VAPE>;

			status = "disabled";
		};

		spi1: spi@80112000 {
			compatible = "arm,pl022", "arm,primecell";
			reg = <0x80112000 0x1000>;
			interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			/* Same clock wired to kernel and pclk */
			clocks = <&prcc_pclk 2 2>, <&prcc_pclk 2 2>;
			clock-names = "sspclk", "apb_pclk";
			dmas = <&dma 35 0 0x2>, /* Logical - DevToMem */
			       <&dma 35 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";
			power-domains = <&pm_domains DOMAIN_VAPE>;

			status = "disabled";
		};

		spi2: spi@80111000 {
			compatible = "arm,pl022", "arm,primecell";
			reg = <0x80111000 0x1000>;
			interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			/* Same clock wired to kernel and pclk */
			clocks = <&prcc_pclk 2 1>, <&prcc_pclk 2 1>;
			clock-names = "sspclk", "apb_pclk";
			dmas = <&dma 33 0 0x2>, /* Logical - DevToMem */
			       <&dma 33 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";
			power-domains = <&pm_domains DOMAIN_VAPE>;

			status = "disabled";
		};

		spi3: spi@80129000 {
			compatible = "arm,pl022", "arm,primecell";
			reg = <0x80129000 0x1000>;
			interrupts = <GIC_SPI 49 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			/* Same clock wired to kernel and pclk */
			clocks = <&prcc_pclk 1 7>, <&prcc_pclk 1 7>;
			clock-names = "sspclk", "apb_pclk";
			dmas = <&dma 40 0 0x2>, /* Logical - DevToMem */
			       <&dma 40 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_1 DB8500_PRCC_1_RESET_SPI3>;

			status = "disabled";
		};

		serial0: serial@80120000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x80120000 0x1000>;
			interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;

			dmas = <&dma 13 0 0x2>, /* Logical - DevToMem */
			       <&dma 13 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";

			clocks = <&prcc_kclk 1 0>, <&prcc_pclk 1 0>;
			clock-names = "uart", "apb_pclk";
			resets = <&prcc_reset DB8500_PRCC_1 DB8500_PRCC_1_RESET_UART0>;

			status = "disabled";
		};

		serial1: serial@80121000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x80121000 0x1000>;
			interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;

			dmas = <&dma 12 0 0x2>, /* Logical - DevToMem */
			       <&dma 12 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";

			clocks = <&prcc_kclk 1 1>, <&prcc_pclk 1 1>;
			clock-names = "uart", "apb_pclk";
			resets = <&prcc_reset DB8500_PRCC_1 DB8500_PRCC_1_RESET_UART1>;

			status = "disabled";
		};

		serial2: serial@80007000 {
			compatible = "arm,pl011", "arm,primecell";
			reg = <0x80007000 0x1000>;
			interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;

			dmas = <&dma 11 0 0x2>, /* Logical - DevToMem */
			       <&dma 11 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";

			clocks = <&prcc_kclk 3 6>, <&prcc_pclk 3 6>;
			clock-names = "uart", "apb_pclk";
			resets = <&prcc_reset DB8500_PRCC_3 DB8500_PRCC_3_RESET_UART2>;

			status = "disabled";
		};

		mmc@80126000 {
			compatible = "arm,pl18x", "arm,primecell";
			reg = <0x80126000 0x1000>;
			interrupts = <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>;

			dmas = <&dma 29 0 0x2>, /* Logical - DevToMem */
			       <&dma 29 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";

			clocks = <&prcc_kclk 1 5>, <&prcc_pclk 1 5>;
			clock-names = "sdi", "apb_pclk";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_1 DB8500_PRCC_1_RESET_SDI0>;

			status = "disabled";
		};

		mmc@80118000 {
			compatible = "arm,pl18x", "arm,primecell";
			reg = <0x80118000 0x1000>;
			interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;

			dmas = <&dma 32 0 0x2>, /* Logical - DevToMem */
			       <&dma 32 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";

			clocks = <&prcc_kclk 2 4>, <&prcc_pclk 2 6>;
			clock-names = "sdi", "apb_pclk";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_2 DB8500_PRCC_2_RESET_SDI1>;

			status = "disabled";
		};

		mmc@80005000 {
			compatible = "arm,pl18x", "arm,primecell";
			reg = <0x80005000 0x1000>;
			interrupts = <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>;

			dmas = <&dma 28 0 0x2>, /* Logical - DevToMem */
			       <&dma 28 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";

			clocks = <&prcc_kclk 3 4>, <&prcc_pclk 3 4>;
			clock-names = "sdi", "apb_pclk";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_3 DB8500_PRCC_3_RESET_SDI2>;

			status = "disabled";
		};

		mmc@80119000 {
			compatible = "arm,pl18x", "arm,primecell";
			reg = <0x80119000 0x1000>;
			interrupts = <GIC_SPI 59 IRQ_TYPE_LEVEL_HIGH>;

			dmas = <&dma 41 0 0x2>, /* Logical - DevToMem */
			       <&dma 41 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";

			clocks = <&prcc_kclk 2 5>, <&prcc_pclk 2 7>;
			clock-names = "sdi", "apb_pclk";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_2 DB8500_PRCC_2_RESET_SDI3>;

			status = "disabled";
		};

		mmc@80114000 {
			compatible = "arm,pl18x", "arm,primecell";
			reg = <0x80114000 0x1000>;
			interrupts = <GIC_SPI 99 IRQ_TYPE_LEVEL_HIGH>;

			dmas = <&dma 42 0 0x2>, /* Logical - DevToMem */
			       <&dma 42 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";

			clocks = <&prcc_kclk 2 2>, <&prcc_pclk 2 4>;
			clock-names = "sdi", "apb_pclk";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_2 DB8500_PRCC_2_RESET_SDI4>;

			status = "disabled";
		};

		mmc@80008000 {
			compatible = "arm,pl18x", "arm,primecell";
			reg = <0x80008000 0x1000>;
			interrupts = <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>;

			dmas = <&dma 43 0 0x2>, /* Logical - DevToMem */
			       <&dma 43 0 0x0>; /* Logical - MemToDev */
			dma-names = "rx", "tx";

			clocks = <&prcc_kclk 3 7>, <&prcc_pclk 3 7>;
			clock-names = "sdi", "apb_pclk";
			power-domains = <&pm_domains DOMAIN_VAPE>;
			resets = <&prcc_reset DB8500_PRCC_3 DB8500_PRCC_3_RESET_SDI5>;

			status = "disabled";
		};

		sound {
			compatible = "stericsson,snd-soc-mop500";
			stericsson,cpu-dai = <&msp1 &msp3>;
		};

		msp0: msp@80123000 {
			compatible = "stericsson,ux500-msp-i2s";
			reg = <0x80123000 0x1000>;
			interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
			v-ape-supply = <&db8500_vape_reg>;

			dmas = <&dma 31 0 0x12>, /* Logical - DevToMem - HighPrio */
			       <&dma 31 0 0x10>; /* Logical - MemToDev - HighPrio */
			dma-names = "rx", "tx";

			clocks = <&prcc_kclk 1 3>, <&prcc_pclk 1 3>;
			clock-names = "msp", "apb_pclk";
			resets = <&prcc_reset DB8500_PRCC_1 DB8500_PRCC_1_RESET_MSP0>;

			status = "disabled";
		};

		msp1: msp@80124000 {
			compatible = "stericsson,ux500-msp-i2s";
			reg = <0x80124000 0x1000>;
			interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
			v-ape-supply = <&db8500_vape_reg>;

			/* This DMA channel only exist on DB8500 v1 */
			dmas = <&dma 30 0 0x10>; /* Logical - MemToDev - HighPrio */
			dma-names = "tx";

			clocks = <&prcc_kclk 1 4>, <&prcc_pclk 1 4>;
			clock-names = "msp", "apb_pclk";
			resets = <&prcc_reset DB8500_PRCC_1 DB8500_PRCC_1_RESET_MSP1>;

			status = "disabled";
		};

		// HDMI sound
		msp2: msp@80117000 {
			compatible = "stericsson,ux500-msp-i2s";
			reg = <0x80117000 0x1000>;
			interrupts = <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>;
			v-ape-supply = <&db8500_vape_reg>;

			dmas = <&dma 14 0 0x12>, /* Logical  - DevToMem - HighPrio */
			       <&dma 14 1 0x19>; /* Physical Chan 1 - MemToDev
                                                    HighPrio - Fixed */
			dma-names = "rx", "tx";

			clocks = <&prcc_kclk 2 3>, <&prcc_pclk 2 5>;
			clock-names = "msp", "apb_pclk";
			resets = <&prcc_reset DB8500_PRCC_2 DB8500_PRCC_2_RESET_MSP2>;

			status = "disabled";
		};

		msp3: msp@80125000 {
			compatible = "stericsson,ux500-msp-i2s";
			reg = <0x80125000 0x1000>;
			interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
			v-ape-supply = <&db8500_vape_reg>;

			/* This DMA channel only exist on DB8500 v2 */
			dmas = <&dma 30 0 0x12>; /* Logical - DevToMem - HighPrio */
			dma-names = "rx";

			clocks = <&prcc_kclk 1 10>, <&prcc_pclk 1 11>;
			clock-names = "msp", "apb_pclk";
			resets = <&prcc_reset DB8500_PRCC_1 DB8500_PRCC_1_RESET_MSP3>;

			status = "disabled";
		};

		external-bus@50000000 {
			compatible = "simple-bus";
			reg = <0x50000000 0x4000000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x50000000 0x4000000>;
			status = "disabled";
		};

		gpu@a0300000 {
			/*
			 * This block is referred to as "Smart Graphics Adapter SGA500"
			 * in documentation but is in practice a pretty straight-forward
			 * MALI-400 GPU block.
			 */
			compatible = "stericsson,db8500-mali", "arm,mali-400";
			reg = <0xa0300000 0x10000>;
			interrupts = <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 112 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 116 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "gp",
					  "gpmmu",
					  "pp0",
					  "ppmmu0",
					  "combined";
			clocks = <&prcmu_clk PRCMU_ACLK>, <&prcmu_clk PRCMU_SGACLK>;
			clock-names = "bus", "core";
			mali-supply = <&db8500_sga_reg>;
			power-domains = <&pm_domains DOMAIN_VAPE>;
		};

		mcde@a0350000 {
			compatible = "ste,mcde";
			reg = <0xa0350000 0x1000>;
			interrupts = <GIC_SPI 48 IRQ_TYPE_LEVEL_HIGH>;
			epod-supply = <&db8500_b2r2_mcde_reg>;
			clocks = <&prcmu_clk PRCMU_MCDECLK>, /* Main MCDE clock */
				 <&prcmu_clk PRCMU_LCDCLK>, /* LCD clock */
				 <&prcmu_clk PRCMU_PLLDSI>; /* HDMI clock */
			clock-names = "mcde", "lcd", "hdmi";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;
			status = "disabled";

			dsi0: dsi@a0351000 {
				compatible = "ste,mcde-dsi";
				reg = <0xa0351000 0x1000>;
				clocks = <&prcmu_clk PRCMU_DSI0CLK>, <&prcmu_clk PRCMU_DSI0ESCCLK>;
				clock-names = "hs", "lp";
				#address-cells = <1>;
				#size-cells = <0>;
			};
			dsi1: dsi@a0352000 {
				compatible = "ste,mcde-dsi";
				reg = <0xa0352000 0x1000>;
				clocks = <&prcmu_clk PRCMU_DSI1CLK>, <&prcmu_clk PRCMU_DSI1ESCCLK>;
				clock-names = "hs", "lp";
				#address-cells = <1>;
				#size-cells = <0>;
			};
			dsi2: dsi@a0353000 {
				compatible = "ste,mcde-dsi";
				reg = <0xa0353000 0x1000>;
				/* This DSI port only has the Low Power / Energy Save clock */
				clocks = <&prcmu_clk PRCMU_DSI2ESCCLK>;
				clock-names = "lp";
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};

		cryp@a03cb000 {
			compatible = "stericsson,ux500-cryp";
			reg = <0xa03cb000 0x1000>;
			interrupts = <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&prcc_pclk 6 1>;
			power-domains = <&pm_domains DOMAIN_VAPE>;
		};

		hash@a03c2000 {
			compatible = "stericsson,ux500-hash";
			reg = <0xa03c2000 0x1000>;
			clocks = <&prcc_pclk 6 2>;
			power-domains = <&pm_domains DOMAIN_VAPE>;
		};
	};
};
