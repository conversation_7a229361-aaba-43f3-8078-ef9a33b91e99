// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (c) STMicroelectronics 2019 - All Rights Reserved
 * Copyright (c) 2020 Engicam srl
 * Copyright (c) 2020 Amarula Solutions(India)
 */

/dts-v1/;
#include "stm32mp157.dtsi"
#include "stm32mp157a-icore-stm32mp1.dtsi"
#include "stm32mp15-pinctrl.dtsi"
#include "stm32mp15xxaa-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Engicam i.Core STM32MP1 C.TOUCH 2.0";
	compatible = "engicam,icore-stm32mp1-ctouch2",
		     "engicam,icore-stm32mp1", "st,stm32mp157";

	aliases {
		serial0 = &uart4;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};
};

&sdmmc1 {
	bus-width = <4>;
	disable-wp;
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a>;
	st,neg-edge;
	vmmc-supply = <&v3v3>;
	status = "okay";
};

&uart4 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&uart4_pins_a>;
	pinctrl-1 = <&uart4_sleep_pins_a>;
	pinctrl-2 = <&uart4_idle_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};
