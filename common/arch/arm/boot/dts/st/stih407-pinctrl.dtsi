// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 STMicroelectronics Limited.
 * Author: <PERSON> <<EMAIL>>
 */
#include "st-pincfg.h"
#include <dt-bindings/interrupt-controller/arm-gic.h>
/ {

	aliases {
		/* 0-5: PIO_SBC */
		gpio0 = &pio0;
		gpio1 = &pio1;
		gpio2 = &pio2;
		gpio3 = &pio3;
		gpio4 = &pio4;
		gpio5 = &pio5;
		/* 10-19: PIO_FRONT0 */
		gpio6 = &pio10;
		gpio7 = &pio11;
		gpio8 = &pio12;
		gpio9 = &pio13;
		gpio10 = &pio14;
		gpio11 = &pio15;
		gpio12 = &pio16;
		gpio13 = &pio17;
		gpio14 = &pio18;
		gpio15 = &pio19;
		/* 20: PIO_FRONT1 */
		gpio16 = &pio20;
		/* 30-35: PIO_REAR */
		gpio17 = &pio30;
		gpio18 = &pio31;
		gpio19 = &pio32;
		gpio20 = &pio33;
		gpio21 = &pio34;
		gpio22 = &pio35;
		/* 40-42: PIO_FLASH */
		gpio23 = &pio40;
		gpio24 = &pio41;
		gpio25 = &pio42;
	};

	soc {
		pin-controller-sbc@961f080 {
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "st,stih407-sbc-pinctrl";
			st,syscfg = <&syscfg_sbc>;
			reg = <0x0961f080 0x4>;
			reg-names = "irqmux";
			interrupts = <GIC_SPI 188 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "irqmux";
			ranges = <0 0x09610000 0x6000>;

			pio0: gpio@9610000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x0 0x100>;
				st,bank-name = "PIO0";
			};
			pio1: gpio@9611000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x1000 0x100>;
				st,bank-name = "PIO1";
			};
			pio2: gpio@9612000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x2000 0x100>;
				st,bank-name = "PIO2";
			};
			pio3: gpio@9613000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x3000 0x100>;
				st,bank-name = "PIO3";
			};
			pio4: gpio@9614000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x4000 0x100>;
				st,bank-name = "PIO4";
			};

			pio5: gpio@9615000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x5000 0x100>;
				st,bank-name = "PIO5";
				st,retime-pin-mask = <0x3f>;
			};

			cec0 {
				pinctrl_cec0_default: cec0-default {
					st,pins {
						hdmi_cec = <&pio2 4 ALT1 BIDIR>;
					};
				};
			};

			rc {
				pinctrl_ir: ir0 {
					st,pins {
						ir = <&pio4 0 ALT2 IN>;
					};
				};

				pinctrl_uhf: uhf0 {
					st,pins {
						ir = <&pio4 1 ALT2 IN>;
					};
				};

				pinctrl_tx: tx0 {
					st,pins {
						tx = <&pio4 2 ALT2 OUT>;
					};
				};

				pinctrl_tx_od: tx_od0 {
					st,pins {
						tx_od = <&pio4 3 ALT2 OUT>;
					};
				};
			};

			/* SBC_ASC0 - UART10 */
			sbc_serial0 {
				pinctrl_sbc_serial0: sbc_serial0-0 {
					st,pins {
						tx = <&pio3 4 ALT1 OUT>;
						rx = <&pio3 5 ALT1 IN>;
					};
				};
			};
			/* SBC_ASC1 - UART11 */
			sbc_serial1 {
				pinctrl_sbc_serial1: sbc_serial1-0 {
					st,pins {
						tx = <&pio2 6 ALT3 OUT>;
						rx = <&pio2 7 ALT3 IN>;
					};
				};
			};

			i2c10 {
				pinctrl_i2c10_default: i2c10-default {
					st,pins {
						sda = <&pio4 6 ALT1 BIDIR>;
						scl = <&pio4 5 ALT1 BIDIR>;
					};
				};
			};

			i2c11 {
				pinctrl_i2c11_default: i2c11-default {
					st,pins {
						sda = <&pio5 1 ALT1 BIDIR>;
						scl = <&pio5 0 ALT1 BIDIR>;
					};
				};
			};

			keyscan {
				pinctrl_keyscan: keyscan {
					st,pins {
						keyin0 = <&pio4 0 ALT6 IN>;
						keyin1 = <&pio4 5 ALT4 IN>;
						keyin2 = <&pio0 4 ALT2 IN>;
						keyin3 = <&pio2 6 ALT2 IN>;

						keyout0 = <&pio4 6 ALT4 OUT>;
						keyout1 = <&pio1 7 ALT2 OUT>;
						keyout2 = <&pio0 6 ALT2 OUT>;
						keyout3 = <&pio2 7 ALT2 OUT>;
					};
				};
			};

			gmac1 {
				/*
				 * Almost all the boards based on STiH407 SoC have an embedded
				 * switch where the mdio/mdc have been used for managing the SMI
				 * iface via I2C. For this reason these lines can be allocated
				 * by using dedicated configuration (in case of there will be a
				 * standard PHY transceiver on-board).
				 */
				pinctrl_rgmii1: rgmii1-0 {
					st,pins {

						txd0 = <&pio0 0 ALT1 OUT DE_IO 0 CLK_A>;
						txd1 = <&pio0 1 ALT1 OUT DE_IO 0 CLK_A>;
						txd2 = <&pio0 2 ALT1 OUT DE_IO 0 CLK_A>;
						txd3 = <&pio0 3 ALT1 OUT DE_IO 0 CLK_A>;
						txen = <&pio0 5 ALT1 OUT DE_IO 0 CLK_A>;
						txclk = <&pio0 6 ALT1 IN NICLK 0 CLK_A>;
						rxd0 = <&pio1 4 ALT1 IN DE_IO 0 CLK_A>;
						rxd1 = <&pio1 5 ALT1 IN DE_IO 0 CLK_A>;
						rxd2 = <&pio1 6 ALT1 IN DE_IO 0 CLK_A>;
						rxd3 = <&pio1 7 ALT1 IN DE_IO 0 CLK_A>;
						rxdv = <&pio2 0 ALT1 IN DE_IO 0 CLK_A>;
						rxclk = <&pio2 2 ALT1 IN NICLK 0 CLK_A>;
						clk125 = <&pio3 7 ALT4 IN NICLK 0 CLK_A>;
						phyclk = <&pio2 3 ALT4 OUT NICLK 1250 CLK_B>;
					};
				};

				pinctrl_rgmii1_mdio: rgmii1-mdio {
					st,pins {
						mdio = <&pio1 0 ALT1 OUT BYPASS 0>;
						mdc = <&pio1 1 ALT1 OUT NICLK 0 CLK_A>;
						mdint = <&pio1 3 ALT1 IN BYPASS 0>;
					};
				};

				pinctrl_rgmii1_mdio_1: rgmii1-mdio-1 {
					st,pins {
						mdio = <&pio1 0 ALT1 OUT BYPASS 0>;
						mdc = <&pio1 1 ALT1 OUT NICLK 0 CLK_A>;
					};
				};

				pinctrl_mii1: mii1 {
					st,pins {
						txd0 = <&pio0 0 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						txd1 = <&pio0 1 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						txd2 = <&pio0 2 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						txd3 = <&pio0 3 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						txer = <&pio0 4 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						txen = <&pio0 5 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						txclk = <&pio0 6 ALT1 IN NICLK 0 CLK_A>;
						col = <&pio0 7 ALT1 IN BYPASS 1000>;

						mdio = <&pio1 0 ALT1 OUT BYPASS 1500>;
						mdc = <&pio1 1 ALT1 OUT NICLK 0 CLK_A>;
						crs = <&pio1 2 ALT1 IN BYPASS 1000>;
						mdint = <&pio1 3 ALT1 IN BYPASS 0>;
						rxd0 = <&pio1 4 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						rxd1 = <&pio1 5 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						rxd2 = <&pio1 6 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						rxd3 = <&pio1 7 ALT1 IN SE_NICLK_IO 0 CLK_A>;

						rxdv = <&pio2 0 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						rx_er = <&pio2 1 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						rxclk = <&pio2 2 ALT1 IN NICLK 0 CLK_A>;
						phyclk = <&pio2 3 ALT1 OUT NICLK 0 CLK_A>;
					};
				};

				pinctrl_rmii1: rmii1-0 {
					st,pins {
						txd0 = <&pio0 0 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						txd1 = <&pio0 1 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						txen = <&pio0 5 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						mdio = <&pio1 0 ALT1 OUT BYPASS 0>;
						mdc = <&pio1 1 ALT1 OUT NICLK 0 CLK_A>;
						mdint = <&pio1 3 ALT1 IN BYPASS 0>;
						rxd0 = <&pio1 4 ALT1 IN SE_NICLK_IO 0 CLK_B>;
						rxd1 = <&pio1 5 ALT1 IN SE_NICLK_IO 0 CLK_B>;
						rxdv = <&pio2 0 ALT1 IN SE_NICLK_IO 0 CLK_B>;
						rx_er = <&pio2 1 ALT1 IN SE_NICLK_IO 0 CLK_A>;
					};
				};

				pinctrl_rmii1_phyclk: rmii1_phyclk {
					st,pins {
						phyclk = <&pio2 3 ALT1 OUT NICLK 0 CLK_A>;
					};
				};

				pinctrl_rmii1_phyclk_ext: rmii1_phyclk_ext {
					st,pins {
						phyclk = <&pio2 3 ALT2 IN NICLK 0 CLK_A>;
					};
				};
			};

			pwm1 {
				pinctrl_pwm1_chan0_default: pwm1-0-default {
					st,pins {
						pwm-out = <&pio3 0 ALT1 OUT>;
						pwm-capturein = <&pio3 2 ALT1 IN>;
					};
				};
				pinctrl_pwm1_chan1_default: pwm1-1-default {
					st,pins {
						pwm-capturein = <&pio4 3 ALT1 IN>;
						pwm-out = <&pio4 4 ALT1 OUT>;
					};
				};
				pinctrl_pwm1_chan2_default: pwm1-2-default {
					st,pins {
						pwm-out = <&pio4 6 ALT3 OUT>;
					};
				};
				pinctrl_pwm1_chan3_default: pwm1-3-default {
					st,pins {
						pwm-out = <&pio4 7 ALT3 OUT>;
					};
				};
			};

			spi10 {
				pinctrl_spi10_default: spi10-4w-alt1-0 {
					st,pins {
						mtsr = <&pio4 6 ALT1 OUT>;
						mrst = <&pio4 7 ALT1 IN>;
						scl = <&pio4 5 ALT1 OUT>;
					};
				};

				pinctrl_spi10_3w_alt1_0: spi10-3w-alt1-0 {
					st,pins {
						mtsr = <&pio4 6 ALT1 BIDIR_PU>;
						scl = <&pio4 5 ALT1 OUT>;
					};
				};
			};

			spi11 {
				pinctrl_spi11_default: spi11-4w-alt2-0 {
					st,pins {
						mtsr = <&pio3 1 ALT2 OUT>;
						mrst = <&pio3 0 ALT2 IN>;
						scl = <&pio3 2 ALT2 OUT>;
					};
				};

				pinctrl_spi11_3w_alt2_0: spi11-3w-alt2-0 {
					st,pins {
						mtsr = <&pio3 1 ALT2 BIDIR_PU>;
						scl = <&pio3 2 ALT2 OUT>;
					};
				};
			};

			spi12 {
				pinctrl_spi12_default: spi12-4w-alt2-0 {
					st,pins {
						mtsr = <&pio3 6 ALT2 OUT>;
						mrst = <&pio3 4 ALT2 IN>;
						scl = <&pio3 7 ALT2 OUT>;
					};
				};

				pinctrl_spi12_3w_alt2_0: spi12-3w-alt2-0 {
					st,pins {
						mtsr = <&pio3 6 ALT2 BIDIR_PU>;
						scl = <&pio3 7 ALT2 OUT>;
					};
				};
			};
		};

		pin-controller-front0@920f080 {
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "st,stih407-front-pinctrl";
			st,syscfg = <&syscfg_front>;
			reg = <0x0920f080 0x4>;
			reg-names = "irqmux";
			interrupts = <GIC_SPI 189 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "irqmux";
			ranges = <0 0x09200000 0x10000>;

			pio10: pio@9200000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x0 0x100>;
				st,bank-name = "PIO10";
			};
			pio11: pio@9201000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x1000 0x100>;
				st,bank-name = "PIO11";
			};
			pio12: pio@9202000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x2000 0x100>;
				st,bank-name = "PIO12";
			};
			pio13: pio@9203000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x3000 0x100>;
				st,bank-name = "PIO13";
			};
			pio14: pio@9204000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x4000 0x100>;
				st,bank-name = "PIO14";
			};
			pio15: pio@9205000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x5000 0x100>;
				st,bank-name = "PIO15";
			};
			pio16: pio@9206000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x6000 0x100>;
				st,bank-name = "PIO16";
			};
			pio17: pio@9207000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x7000 0x100>;
				st,bank-name = "PIO17";
			};
			pio18: pio@9208000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x8000 0x100>;
				st,bank-name = "PIO18";
			};
			pio19: pio@9209000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x9000 0x100>;
				st,bank-name = "PIO19";
			};

			/* Comms */
			serial0 {
				pinctrl_serial0: serial0-0 {
					st,pins {
						tx =  <&pio17 0 ALT1 OUT>;
						rx =  <&pio17 1 ALT1 IN>;
					};
				};
				pinctrl_serial0_hw_flowctrl: serial0-0_hw_flowctrl {
					st,pins {
						tx =  <&pio17 0 ALT1 OUT>;
						rx =  <&pio17 1 ALT1 IN>;
						cts = <&pio17 2 ALT1 IN>;
						rts = <&pio17 3 ALT1 OUT>;
					};
				};
			};

			serial1 {
				pinctrl_serial1: serial1-0 {
					st,pins {
						tx = <&pio16 0 ALT1 OUT>;
						rx = <&pio16 1 ALT1 IN>;
					};
				};
			};

			serial2 {
				pinctrl_serial2: serial2-0 {
					st,pins {
						tx = <&pio15 0 ALT1 OUT>;
						rx = <&pio15 1 ALT1 IN>;
					};
				};
			};

			mmc1 {
				pinctrl_sd1: sd1-0 {
					st,pins {
						sd_clk = <&pio19 3 ALT5 BIDIR NICLK 0 CLK_B>;
						sd_cmd = <&pio19 2 ALT5 BIDIR_PU BYPASS 0>;
						sd_dat0 = <&pio19 4 ALT5 BIDIR_PU BYPASS 0>;
						sd_dat1 = <&pio19 5 ALT5 BIDIR_PU BYPASS 0>;
						sd_dat2 = <&pio19 6 ALT5 BIDIR_PU BYPASS 0>;
						sd_dat3 = <&pio19 7 ALT5 BIDIR_PU BYPASS 0>;
						sd_led = <&pio16 6 ALT6 OUT>;
						sd_pwren = <&pio16 7 ALT6 OUT>;
						sd_cd = <&pio19 0 ALT6 IN>;
						sd_wp = <&pio19 1 ALT6 IN>;
					};
				};
			};


			i2c0 {
				pinctrl_i2c0_default: i2c0-default {
					st,pins {
						sda = <&pio10 6 ALT2 BIDIR>;
						scl = <&pio10 5 ALT2 BIDIR>;
					};
				};
			};

			i2c1 {
				pinctrl_i2c1_default: i2c1-default {
					st,pins {
						sda = <&pio11 1 ALT2 BIDIR>;
						scl = <&pio11 0 ALT2 BIDIR>;
					};
				};
			};

			i2c2 {
				pinctrl_i2c2_default: i2c2-default {
					st,pins {
						sda = <&pio15 6 ALT2 BIDIR>;
						scl = <&pio15 5 ALT2 BIDIR>;
					};
				};

				pinctrl_i2c2_alt2_1: i2c2-alt2-1 {
					st,pins {
						sda = <&pio12 6 ALT2 BIDIR>;
						scl = <&pio12 5 ALT2 BIDIR>;
					};
				};
			};

			i2c3 {
				pinctrl_i2c3_default: i2c3-alt1-0 {
					st,pins {
						sda = <&pio18 6 ALT1 BIDIR>;
						scl = <&pio18 5 ALT1 BIDIR>;
					};
				};
				pinctrl_i2c3_alt1_1: i2c3-alt1-1 {
					st,pins {
						sda = <&pio17 7 ALT1 BIDIR>;
						scl = <&pio17 6 ALT1 BIDIR>;
					};
				};
				pinctrl_i2c3_alt3_0: i2c3-alt3-0 {
					st,pins {
						sda = <&pio13 6 ALT3 BIDIR>;
						scl = <&pio13 5 ALT3 BIDIR>;
					};
				};
			};

			spi0 {
				pinctrl_spi0_default: spi0-4w-alt2-0 {
					st,pins {
						mtsr = <&pio10 6 ALT2 OUT>;
						mrst = <&pio10 7 ALT2 IN>;
						scl = <&pio10 5 ALT2 OUT>;
					};
				};

				pinctrl_spi0_3w_alt2_0: spi0-3w-alt2-0 {
					st,pins {
						mtsr = <&pio10 6 ALT2 BIDIR_PU>;
						scl = <&pio10 5 ALT2 OUT>;
					};
				};

				pinctrl_spi0_4w_alt1_0: spi0-4w-alt1-0 {
					st,pins {
						mtsr = <&pio19 7 ALT1 OUT>;
						mrst = <&pio19 5 ALT1 IN>;
						scl = <&pio19 6 ALT1 OUT>;
					};
				};

				pinctrl_spi0_3w_alt1_0: spi0-3w-alt1-0 {
					st,pins {
						mtsr = <&pio19 7 ALT1 BIDIR_PU>;
						scl = <&pio19 6 ALT1 OUT>;
					};
				};
			};

			spi1 {
				pinctrl_spi1_default: spi1-4w-alt2-0 {
					st,pins {
						mtsr = <&pio11 1 ALT2 OUT>;
						mrst = <&pio11 2 ALT2 IN>;
						scl = <&pio11 0 ALT2 OUT>;
					};
				};

				pinctrl_spi1_3w_alt2_0: spi1-3w-alt2-0 {
					st,pins {
						mtsr = <&pio11 1 ALT2 BIDIR_PU>;
						scl = <&pio11 0 ALT2 OUT>;
					};
				};

				pinctrl_spi1_4w_alt1_0: spi1-4w-alt1-0 {
					st,pins {
						mtsr = <&pio14 3 ALT1 OUT>;
						mrst = <&pio14 4 ALT1 IN>;
						scl = <&pio14 2 ALT1 OUT>;
					};
				};

				pinctrl_spi1_3w_alt1_0: spi1-3w-alt1-0 {
					st,pins {
						mtsr = <&pio14 3 ALT1 BIDIR_PU>;
						scl = <&pio14 2 ALT1 OUT>;
					};
				};
			};

			spi2 {
				pinctrl_spi2_default: spi2-4w-alt2-0 {
					st,pins {
						mtsr = <&pio12 6 ALT2 OUT>;
						mrst = <&pio12 7 ALT2 IN>;
						scl = <&pio12 5 ALT2 OUT>;
					};
				};

				pinctrl_spi2_3w_alt2_0: spi2-3w-alt2-0 {
					st,pins {
						mtsr = <&pio12 6 ALT2 BIDIR_PU>;
						scl = <&pio12 5 ALT2 OUT>;
					};
				};

				pinctrl_spi2_4w_alt1_0: spi2-4w-alt1-0 {
					st,pins {
						mtsr = <&pio14 6 ALT1 OUT>;
						mrst = <&pio14 7 ALT1 IN>;
						scl = <&pio14 5 ALT1 OUT>;
					};
				};

				pinctrl_spi2_3w_alt1_0: spi2-3w-alt1-0 {
					st,pins {
						mtsr = <&pio14 6 ALT1 BIDIR_PU>;
						scl = <&pio14 5 ALT1 OUT>;
					};
				};

				pinctrl_spi2_4w_alt2_1: spi2-4w-alt2-1 {
					st,pins {
						mtsr = <&pio15 6 ALT2 OUT>;
						mrst = <&pio15 7 ALT2 IN>;
						scl = <&pio15 5 ALT2 OUT>;
					};
				};

				pinctrl_spi2_3w_alt2_1: spi2-3w-alt2-1 {
					st,pins {
						mtsr = <&pio15 6 ALT2 BIDIR_PU>;
						scl = <&pio15 5 ALT2 OUT>;
					};
				};
			};

			spi3 {
				pinctrl_spi3_default: spi3-4w-alt3-0 {
					st,pins {
						mtsr = <&pio13 6 ALT3 OUT>;
						mrst = <&pio13 7 ALT3 IN>;
						scl = <&pio13 5 ALT3 OUT>;
					};
				};

				pinctrl_spi3_3w_alt3_0: spi3-3w-alt3-0 {
					st,pins {
						mtsr = <&pio13 6 ALT3 BIDIR_PU>;
						scl = <&pio13 5 ALT3 OUT>;
					};
				};

				pinctrl_spi3_4w_alt1_0: spi3-4w-alt1-0 {
					st,pins {
						mtsr = <&pio17 7 ALT1 OUT>;
						mrst = <&pio17 5 ALT1 IN>;
						scl = <&pio17 6 ALT1 OUT>;
					};
				};

				pinctrl_spi3_3w_alt1_0: spi3-3w-alt1-0 {
					st,pins {
						mtsr = <&pio17 7 ALT1 BIDIR_PU>;
						scl = <&pio17 6 ALT1 OUT>;
					};
				};

				pinctrl_spi3_4w_alt1_1: spi3-4w-alt1-1 {
					st,pins {
						mtsr = <&pio18 6 ALT1 OUT>;
						mrst = <&pio18 7 ALT1 IN>;
						scl = <&pio18 5 ALT1 OUT>;
					};
				};

				pinctrl_spi3_3w_alt1_1: spi3-3w-alt1-1 {
					st,pins {
						mtsr = <&pio18 6 ALT1 BIDIR_PU>;
						scl = <&pio18 5 ALT1 OUT>;
					};
				};
			};

			tsin0 {
				pinctrl_tsin0_parallel: tsin0_parallel {
					st,pins {
						DATA7 = <&pio10 4 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA6 = <&pio10 5 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA5 = <&pio10 6 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA4 = <&pio10 7 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA3 = <&pio11 0 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA2 = <&pio11 1 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA1 = <&pio11 2 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA0 = <&pio11 3 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio10 3 ALT1 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio10 1 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio10 0 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio10 2 ALT1 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
				pinctrl_tsin0_serial: tsin0_serial {
					st,pins {
						DATA7 = <&pio10 4 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio10 3 ALT1 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio10 1 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio10 0 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio10 2 ALT1 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
			};

			tsin1 {
				pinctrl_tsin1_parallel: tsin1_parallel {
					st,pins {
						DATA7 = <&pio12 0 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA6 = <&pio12 1 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA5 = <&pio12 2 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA4 = <&pio12 3 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA3 = <&pio12 4 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA2 = <&pio12 5 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA1 = <&pio12 6 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA0 = <&pio12 7 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio11 7 ALT1 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio11 5 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio11 4 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio11 6 ALT1 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
				pinctrl_tsin1_serial: tsin1_serial {
					st,pins {
						DATA7 = <&pio12 0 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio11 7 ALT1 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio11 5 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio11 4 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio11 6 ALT1 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
			};

			tsin2 {
				pinctrl_tsin2_parallel: tsin2_parallel {
					st,pins {
						DATA7 = <&pio13 4 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						DATA6 = <&pio13 5 ALT2 IN SE_NICLK_IO 0 CLK_B>;
						DATA5 = <&pio13 6 ALT2 IN SE_NICLK_IO 0 CLK_B>;
						DATA4 = <&pio13 7 ALT2 IN SE_NICLK_IO 0 CLK_B>;
						DATA3 = <&pio14 0 ALT2 IN SE_NICLK_IO 0 CLK_A>;
						DATA2 = <&pio14 1 ALT2 IN SE_NICLK_IO 0 CLK_B>;
						DATA1 = <&pio14 2 ALT2 IN SE_NICLK_IO 0 CLK_A>;
						DATA0 = <&pio14 3 ALT2 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio13 3 ALT1 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio13 1 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio13 0 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio13 2 ALT1 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
				pinctrl_tsin2_serial: tsin2_serial {
					st,pins {
						DATA7 = <&pio13 4 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio13 3 ALT1 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio13 1 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio13 0 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio13 2 ALT1 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
			};

			tsin3 {
				pinctrl_tsin3_serial: tsin3_serial {
					st,pins {
						DATA7 = <&pio14 1 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio14 0 ALT1 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio13 6 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio13 5 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio13 7 ALT1 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
			};

			tsin4 {
				pinctrl_tsin4_serial_alt3: tsin4_serial_alt3 {
					st,pins {
						DATA7 = <&pio14 6 ALT3 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio14 5 ALT3 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio14 3 ALT3 IN SE_NICLK_IO 0 CLK_B>;
						ERROR = <&pio14 2 ALT3 IN SE_NICLK_IO 0 CLK_B>;
						PKCLK = <&pio14 4 ALT3 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
			};

			tsin5 {
				pinctrl_tsin5_serial_alt1: tsin5_serial_alt1 {
					st,pins {
						DATA7 = <&pio18 4 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio18 3 ALT1 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio18 1 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio18 0 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio18 2 ALT1 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
				pinctrl_tsin5_serial_alt2: tsin5_serial_alt2 {
					st,pins {
						DATA7 = <&pio19 4 ALT2 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio19 3 ALT2 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio19 1 ALT2 IN SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio19 0 ALT2 IN SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio19 2 ALT2 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
			};

			tsout0 {
				pinctrl_tsout0_parallel: tsout0_parallel {
					st,pins {
						DATA7 = <&pio12 0 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						DATA6 = <&pio12 1 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						DATA5 = <&pio12 2 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						DATA4 = <&pio12 3 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						DATA3 = <&pio12 4 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						DATA2 = <&pio12 5 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						DATA1 = <&pio12 6 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						DATA0 = <&pio12 7 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio11 7 ALT3 OUT NICLK 0 CLK_A>;
						VALID = <&pio11 5 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio11 4 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio11 6 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
					};
				};
				pinctrl_tsout0_serial: tsout0_serial {
					st,pins {
						DATA7 = <&pio12 0 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio11 7 ALT3 OUT NICLK 0 CLK_A>;
						VALID = <&pio11 5 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio11 4 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio11 6 ALT3 OUT SE_NICLK_IO 0 CLK_A>;
					};
				};
			};

			tsout1 {
				pinctrl_tsout1_serial: tsout1_serial {
					st,pins {
						DATA7 = <&pio19 4 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio19 3 ALT1 OUT NICLK 0 CLK_A>;
						VALID = <&pio19 1 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio19 0 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio19 2 ALT1 OUT SE_NICLK_IO 0 CLK_A>;
					};
				};
			};

			mtsin0 {
				pinctrl_mtsin0_parallel: mtsin0_parallel {
					st,pins {
						DATA7 = <&pio10 4 ALT3 IN SE_NICLK_IO 0 CLK_A>;
						DATA6 = <&pio10 5 ALT3 IN SE_NICLK_IO 0 CLK_A>;
						DATA5 = <&pio10 6 ALT3 IN SE_NICLK_IO 0 CLK_A>;
						DATA4 = <&pio10 7 ALT3 IN SE_NICLK_IO 0 CLK_A>;
						DATA3 = <&pio11 0 ALT3 IN SE_NICLK_IO 0 CLK_A>;
						DATA2 = <&pio11 1 ALT3 IN SE_NICLK_IO 0 CLK_A>;
						DATA1 = <&pio11 2 ALT3 IN SE_NICLK_IO 0 CLK_A>;
						DATA0 = <&pio11 3 ALT3 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio10 3 ALT3 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio10 1 ALT3 IN SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio10 0 ALT3 IN SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio10 2 ALT3 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
			};

			systrace {
				pinctrl_systrace_default: systrace-default {
					st,pins {
						trc_data0 = <&pio11 3 ALT5 OUT>;
						trc_data1 = <&pio11 4 ALT5 OUT>;
						trc_data2 = <&pio11 5 ALT5 OUT>;
						trc_data3 = <&pio11 6 ALT5 OUT>;
						trc_clk   = <&pio11 7 ALT5 OUT>;
					};
				};
			};
		};

		pin-controller-front1@921f080 {
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "st,stih407-front-pinctrl";
			st,syscfg = <&syscfg_front>;
			reg = <0x0921f080 0x4>;
			reg-names = "irqmux";
			interrupts = <GIC_SPI 190 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "irqmux";
			ranges = <0 0x09210000 0x10000>;

			pio20: pio@9210000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x0 0x100>;
				st,bank-name = "PIO20";
			};

			tsin4 {
				pinctrl_tsin4_serial_alt1: tsin4_serial_alt1 {
					st,pins {
						DATA7 = <&pio20 4 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						CLKIN = <&pio20 3 ALT1 IN CLKNOTDATA 0 CLK_A>;
						VALID = <&pio20 1 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						ERROR = <&pio20 0 ALT1 IN SE_NICLK_IO 0 CLK_A>;
						PKCLK = <&pio20 2 ALT1 IN SE_NICLK_IO 0 CLK_A>;
					};
				};
			};
		};

		pin-controller-rear@922f080 {
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "st,stih407-rear-pinctrl";
			st,syscfg = <&syscfg_rear>;
			reg = <0x0922f080 0x4>;
			reg-names = "irqmux";
			interrupts = <GIC_SPI 191 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "irqmux";
			ranges = <0 0x09220000 0x6000>;

			pio30: gpio@9220000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x0 0x100>;
				st,bank-name = "PIO30";
			};
			pio31: gpio@9221000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x1000 0x100>;
				st,bank-name = "PIO31";
			};
			pio32: gpio@9222000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x2000 0x100>;
				st,bank-name = "PIO32";
			};
			pio33: gpio@9223000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x3000 0x100>;
				st,bank-name = "PIO33";
			};
			pio34: gpio@9224000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x4000 0x100>;
				st,bank-name = "PIO34";
			};
			pio35: gpio@9225000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x5000 0x100>;
				st,bank-name = "PIO35";
				st,retime-pin-mask = <0x7f>;
			};

			i2c4 {
				pinctrl_i2c4_default: i2c4-default {
					st,pins {
						sda = <&pio30 1 ALT1 BIDIR>;
						scl = <&pio30 0 ALT1 BIDIR>;
					};
				};
			};

			i2c5 {
				pinctrl_i2c5_default: i2c5-default {
					st,pins {
						sda = <&pio34 4 ALT1 BIDIR>;
						scl = <&pio34 3 ALT1 BIDIR>;
					};
				};
			};

			usb3 {
				pinctrl_usb3: usb3-2 {
					st,pins {
						usb-oc-detect = <&pio35 4 ALT1 IN>;
						usb-pwr-enable = <&pio35 5 ALT1 OUT>;
						usb-vbus-valid = <&pio35 6 ALT1 IN>;
					};
				};
			};

			pwm0 {
				pinctrl_pwm0_chan0_default: pwm0-0-default {
					st,pins {
						pwm-capturein = <&pio31 0 ALT1 IN>;
						pwm-out = <&pio31 1 ALT1 OUT>;
					};
				};
			};

			spi4 {
				pinctrl_spi4_default: spi4-4w-alt1-0 {
					st,pins {
						mtsr = <&pio30 1 ALT1 OUT>;
						mrst = <&pio30 2 ALT1 IN>;
						scl = <&pio30 0 ALT1 OUT>;
					};
				};

				pinctrl_spi4_3w_alt1_0: spi4-3w-alt1-0 {
					st,pins {
						mtsr = <&pio30 1 ALT1 BIDIR_PU>;
						scl = <&pio30 0 ALT1 OUT>;
					};
				};

				pinctrl_spi4_4w_alt3_0: spi4-4w-alt3-0 {
					st,pins {
						mtsr = <&pio34 1 ALT3 OUT>;
						mrst = <&pio34 2 ALT3 IN>;
						scl = <&pio34 0 ALT3 OUT>;
					};
				};

				pinctrl_spi4_3w_alt3_0: spi4-3w-alt3-0 {
					st,pins {
						mtsr = <&pio34 1 ALT3 BIDIR_PU>;
						scl = <&pio34 0 ALT3 OUT>;
					};
				};
			};

			i2s_out {
				pinctrl_i2s_8ch_out: i2s_8ch_out {
					st,pins {
						mclk = <&pio33 5 ALT1 OUT>;
						lrclk = <&pio33 7 ALT1 OUT>;
						sclk = <&pio33 6 ALT1 OUT>;
						data0 = <&pio33 4 ALT1 OUT>;
						data1 = <&pio34 0 ALT1 OUT>;
						data2 = <&pio34 1 ALT1 OUT>;
						data3 = <&pio34 2 ALT1 OUT>;
					};
				};

				pinctrl_i2s_2ch_out: i2s_2ch_out {
					st,pins {
						mclk = <&pio33 5 ALT1 OUT>;
						lrclk = <&pio33 7 ALT1 OUT>;
						sclk = <&pio33 6 ALT1 OUT>;
						data0 = <&pio33 4 ALT1 OUT>;
					};
				};
			};

			i2s_in {
				pinctrl_i2s_8ch_in: i2s_8ch_in {
					st,pins {
						mclk = <&pio32 5 ALT1 IN>;
						lrclk = <&pio32 7 ALT1 IN>;
						sclk = <&pio32 6 ALT1 IN>;
						data0 = <&pio32 4 ALT1 IN>;
						data1 = <&pio33 0 ALT1 IN>;
						data2 = <&pio33 1 ALT1 IN>;
						data3 = <&pio33 2 ALT1 IN>;
						data4 = <&pio33 3 ALT1 IN>;
					};
				};

				pinctrl_i2s_2ch_in: i2s_2ch_in {
					st,pins {
						mclk = <&pio32 5 ALT1 IN>;
						lrclk = <&pio32 7 ALT1 IN>;
						sclk = <&pio32 6 ALT1 IN>;
						data0 = <&pio32 4 ALT1 IN>;
					};
				};
			};

			spdif_out {
				pinctrl_spdif_out: spdif_out {
					st,pins {
						spdif_out = <&pio34 7 ALT1 OUT>;
					};
				};
			};

			serial3 {
				pinctrl_serial3: serial3-0 {
					st,pins {
						tx = <&pio31 3 ALT1 OUT>;
						rx = <&pio31 4 ALT1 IN>;
					};
				};
			};
		};

		pin-controller-flash@923f080 {
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "st,stih407-flash-pinctrl";
			st,syscfg = <&syscfg_flash>;
			reg = <0x0923f080 0x4>;
			reg-names = "irqmux";
			interrupts = <GIC_SPI 192 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "irqmux";
			ranges = <0 0x09230000 0x3000>;

			pio40: gpio@9230000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0 0x100>;
				st,bank-name = "PIO40";
			};
			pio41: gpio@9231000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x1000 0x100>;
				st,bank-name = "PIO41";
			};
			pio42: gpio@9232000 {
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				reg = <0x2000 0x100>;
				st,bank-name = "PIO42";
			};

			mmc0 {
				pinctrl_mmc0: mmc0-0 {
					st,pins {
						emmc_clk = <&pio40 6 ALT1 BIDIR>;
						emmc_cmd = <&pio40 7 ALT1 BIDIR_PU>;
						emmc_d0 = <&pio41 0 ALT1 BIDIR_PU>;
						emmc_d1 = <&pio41 1 ALT1 BIDIR_PU>;
						emmc_d2 = <&pio41 2 ALT1 BIDIR_PU>;
						emmc_d3 = <&pio41 3 ALT1 BIDIR_PU>;
						emmc_d4 = <&pio41 4 ALT1 BIDIR_PU>;
						emmc_d5 = <&pio41 5 ALT1 BIDIR_PU>;
						emmc_d6 = <&pio41 6 ALT1 BIDIR_PU>;
						emmc_d7 = <&pio41 7 ALT1 BIDIR_PU>;
					};
				};
				pinctrl_sd0: sd0-0 {
					st,pins {
						sd_clk = <&pio40 6 ALT1 BIDIR>;
						sd_cmd = <&pio40 7 ALT1 BIDIR_PU>;
						sd_dat0 = <&pio41 0 ALT1 BIDIR_PU>;
						sd_dat1 = <&pio41 1 ALT1 BIDIR_PU>;
						sd_dat2 = <&pio41 2 ALT1 BIDIR_PU>;
						sd_dat3 = <&pio41 3 ALT1 BIDIR_PU>;
						sd_led = <&pio42 0 ALT2 OUT>;
						sd_pwren = <&pio42 2 ALT2 OUT>;
						sd_vsel = <&pio42 3 ALT2 OUT>;
						sd_cd = <&pio42 4 ALT2 IN>;
						sd_wp = <&pio42 5 ALT2 IN>;
					};
				};
			};

			fsm {
				pinctrl_fsm: fsm {
					st,pins {
						spi-fsm-clk = <&pio40 1 ALT1 OUT>;
						spi-fsm-cs = <&pio40 0 ALT1 OUT>;
						spi-fsm-mosi = <&pio40 2 ALT1 OUT>;
						spi-fsm-miso = <&pio40 3 ALT1 IN>;
						spi-fsm-hol = <&pio40 5 ALT1 OUT>;
						spi-fsm-wp = <&pio40 4 ALT1 OUT>;
					};
				};
			};

			nand {
				pinctrl_nand: nand {
					st,pins {
						nand_cs1 = <&pio40 6 ALT3 OUT>;
						nand_cs0 = <&pio40 7 ALT3 OUT>;
						nand_d0 = <&pio41 0 ALT3 BIDIR>;
						nand_d1 = <&pio41 1 ALT3 BIDIR>;
						nand_d2 = <&pio41 2 ALT3 BIDIR>;
						nand_d3 = <&pio41 3 ALT3 BIDIR>;
						nand_d4 = <&pio41 4 ALT3 BIDIR>;
						nand_d5 = <&pio41 5 ALT3 BIDIR>;
						nand_d6 = <&pio41 6 ALT3 BIDIR>;
						nand_d7 = <&pio41 7 ALT3 BIDIR>;
						nand_we = <&pio42 0 ALT3 OUT>;
						nand_dqs = <&pio42 1 ALT3 OUT>;
						nand_ale = <&pio42 2 ALT3 OUT>;
						nand_cle = <&pio42 3 ALT3 OUT>;
						nand_rnb = <&pio42 4 ALT3 IN>;
						nand_oe = <&pio42 5 ALT3 OUT>;
					};
				};
			};
		};
	};
};
