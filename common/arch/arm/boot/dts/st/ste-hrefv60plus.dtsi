// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright 2012 ST-<PERSON><PERSON> AB
 */

#include "ste-href-ab8500.dtsi"
#include "ste-href.dtsi"

/ {
	model = "ST-Ericsson HREF (v60+) platform with Device Tree";
	compatible = "st-er<PERSON><PERSON>,hrefv60+", "st-er<PERSON><PERSON>,u8500";

	thermal-zones {
		chassis-thermal {
			/* Poll every 20 seconds */
			polling-delay = <20000>;
			/* Poll every 2nd second when cooling */
			polling-delay-passive = <2000>;

			thermal-sensors = <&therm1>, <&therm2>;

			/* Tripping points made from rough guess about operating conditions */
			trips {
				chassis_alert: chassis-alert {
					/* At 50 degrees take down the CPU frequency */
					temperature = <50000>;
					hysteresis = <3000>;
					type = "active";
				};
				chassis_crit: chassis-crit {
					/* Just shut down at 70 degrees */
					temperature = <70000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};

			/* Push down the operating frequency of the SoC when it gets hot */
			cooling-maps {
				map0 {
					trip = <&chassis_alert>;
					cooling-device = <&CPU0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
					contribution = <100>;
				};
			};
		};
	};

	/*
	 * Thermistors on the board, formally to monitor battery temperatures
	 * but what they measure is the board temperature.
	 */
	therm1: thermistor@0 {
		compatible = "murata,ncp18wb473";
		io-channels = <&gpadc 0x06>; /* AUX1 */
		pullup-uv = <1800000>;
		pullup-ohm = <220000>;
		pulldown-ohm = <0>;
		#thermal-sensor-cells = <0>;
	};

	therm2: thermistor@1 {
		compatible = "murata,ncp18wb473";
		io-channels = <&gpadc 0x07>; /* AUX2 */
		pullup-uv = <1800000>;
		pullup-ohm = <220000>;
		pulldown-ohm = <0>;
		#thermal-sensor-cells = <0>;
	};

	soc {
		/* Name the GPIO muxed rails on the HREF boards */
		gpio@8012e000 {
			/* GPIOs 0 - 31 */
			gpio-line-names =
				     /* GPIO0,1 used for UART0 BT RX/TX */
				     "", "",
				     "UART_WAKE",
				     "BT_WAKE",
				     "",
				     "SDMMC_1V8_3V_SEL",
				     "FLASH_LED_SYNC (FLASH_CTRL_0)",
				     "XENON_READY (FLASH_CTRL_1)",
				     "", "", "", "", "", "", "", "",
				     "", "", "", "",
				     "",
				     "FLASH_LED_EN (FLASH_CTRL_3)",
				     "", "",
				     "", "", "", "", "",
				     /* Used by UART2 (console) */
				     "", "",
				     "MAGNETOMETER_INT";
		};

		gpio@8012e080 {
			/* GPIOs 32 - 63 */
			gpio-line-names =
				     "MAGNETOMETER_DRDY",
				     "", "", "", "", "", "", "",
				     "", "", "", "", "", "", "", "",
				     "", "", "", "", "", "", "", "",
				     "", "", "", "", "", "", "", "";
		};

		gpio@8000e000 {
			/* GPIOs 64 - 95 */
			gpio-line-names = "XENON_EN2 (FLASH_CTRL_4)",
				     "DISP1_RST",
				     "DISP2_RST",
				     "TOUCH_INT2",
				     "LCD_VSI0_A",
				     "LCD_VSI1_A",
				     /* GPIO 70-77 used for ETM */
				     "", "", "", "", "", "", "", "",
				     /* GPIO 78-81 used for YCBCR */
				     "", "", "", "",
				     "ACCELEROMETER_INT1_RDY",
				     "ACCELEROMETER_INT2",
				     "TOUCH_INT",
				     "WLAN_ENA",
				     "", "", "", "", "",
				     "FORCE_SENSING_INT",
				     "FORCE_SENSING_RESET",
				     "", "",
				     "SDMMC_CD";
		};

		gpio@8000e080 {
			/* GPIOs 96 - 127 */
			gpio-line-names = "",
				     "FORCE_SENSING_WU",
				     "", "", "", "", "", "",
				     "", "", "", "", "", "", "", "",
				     "", "", "", "", "", "", "", "",
				     "", "", "", "", "", "", "", "";
		};

		gpio@8000e100 {
			/* GPIOs 128 - 159 */
			gpio-line-names = "", "", "", "", "", "", "", "",
				     "", "", "",
				     "DIPRO_INT", /* GPIO139 */
				     "XSHUTDOWN_SECONDARY_SENSOR",
				     "XSHUTDOWN_PRIMARY_SENSOR",
				     "NFC_RST (NFC_CTRL_",
				     "TOUCH_RST",
				     "NFC_IRQ (NFC_CTRL_1)",
				     "HAL_SW",
				     "TOUCH_RST2",
				     "", "",
				     "VAUDIO_HF_EN", /* GPIO149 */
				     "", "", "", "", "", "", "", "", "", "";
		};

		gpio@8000e180 {
			/* GPIOs 160 - 191 */
			gpio-line-names = "", "", "", "", "", "", "", "",
				     "",
				     "SDMMC_EN",
				     "XENON_CHARGE (FLASH_CONTROL_5)",
				     "GBF_ENA_RESET",
				     "", "", "", "",
				     "", "", "", "", "", "", "", "",
				     "", "", "", "", "", "", "", "";
		};

		gpio@8011e000 {
			/* GPIOs 192 - 223 */
			gpio-line-names = "HDTV_INTN",
				     "", "", "",
				     "HDTV_RSTN",
				     "", "", "",
				     "", /* GPIO200 */
				     "", "", "", "", "", "", "",
				     /* GPIO208-216 used for WGBF_MC1 */
				     "", "", "", "", "", "", "", "", "",
				     "SW_FRONT_PROXIMITY", /* GPIO217 */
				     "KPD_CTRL_INT", /* Keypad controller */
				     "", "", "", "", "";
		};

		gpio@8011e080 {
			/* GPIOs 224 - 255 */
			gpio-line-names = "", "",
				     "HSIT_ACWAKE0",
				     "", "", "", "", "",
				     "", "", "", "", "", "", "", "",
				     "", "", "", "", "", "", "", "",
				     "", "", "", "", "", "", "", "";
		};

		// External Micro SD slot
		mmc@80126000 {
			cd-gpios  = <&gpio2 31 GPIO_ACTIVE_HIGH>; // 95
		};

		pinctrl {
			/*
			 * Set this up using hogs, as time goes by and as seems fit, these
			 * can be moved over to being controlled by respective device.
			 */
			pinctrl-names = "default";
			pinctrl-0 = <&ipgpio_hrefv60_mode>,
				  <&etm_hrefv60_mode>,
				  <&nahj_hrefv60_mode>,
				  <&nfc_hrefv60_mode>,
				  <&force_hrefv60_mode>,
				  <&dipro_hrefv60_mode>,
				  <&vaudio_hf_hrefv60_mode>,
				  <&gbf_hrefv60_mode>,
				  <&hdtv_hrefv60_mode>,
				  <&gpios_hrefv60_mode>;

			sdi0 {
				sdi0_default_mode: sdi0_default {
					/* SD card detect GPIO pin, extend default state */
					default_hrefv60_cfg1 {
						pins = "GPIO95_E8";
						ste,config = <&gpio_in_pu>;
					};
				};
			};
			ipgpio {
				/*
				 * XENON Flashgun on image processor GPIO (controlled from image
				 * processor firmware), mux in these image processor GPIO lines 0
				 * (XENON_FLASH_ID), 1 (XENON_READY) and there is an assistant
				 * LED on IP GPIO 4 (XENON_EN2) on altfunction C, that need bias
				 * from GPIO21 so pull up 0, 1 and drive 4 and GPIO21 low as output.
				 */
				ipgpio_hrefv60_mode: ipgpio_hrefv60 {
					hrefv60_mux {
						function = "ipgpio";
						groups = "ipgpio0_c_1", "ipgpio1_c_1", "ipgpio4_c_1";
					};
					hrefv60_cfg1 {
						pins = "GPIO6_AF6", "GPIO7_AG5";
						ste,config = <&in_pu>;
					};
					hrefv60_cfg2 {
						pins = "GPIO21_AB3";
						ste,config = <&gpio_out_lo>;
					};
					hrefv60_cfg3 {
						pins = "GPIO64_F3";
						ste,config = <&out_lo>;
					};
				};
			};
			etm {
				/*
				 * Drive D19-D23 for the ETM PTM trace interface low,
				 * (presumably pins are unconnected therefore grounded here,
				 * the "other alt C1" setting enables these pins)
				 */
				etm_hrefv60_mode: etm_hrefv60 {
					hrefv60_cfg1 {
						pins =
						"GPIO70_G5",
						"GPIO71_G4",
						"GPIO72_H4",
						"GPIO73_H3",
						"GPIO74_J3";
						ste,config = <&gpio_out_lo>;
					};
				 };
			};
			nahj {
				nahj_hrefv60_mode: nahj_hrefv60 {
					/* NAHJ CTRL on GPIO76 to low, CTRL_INV on GPIO216 to high */
					hrefv60_cfg1 {
						pins = "GPIO76_J2";
						ste,config = <&gpio_out_lo>;
					};
					hrefv60_cfg2 {
						pins = "GPIO216_AG12";
						ste,config = <&gpio_out_hi>;
					};
				 };
			};
			nfc {
				nfc_hrefv60_mode: nfc_hrefv60 {
					/* NFC ENA and RESET to low, pulldown IRQ line */
					hrefv60_cfg1 {
						pins =
						"GPIO77_H1", /* NFC_ENA */
						"GPIO142_C11"; /* NFC_RESET */
						ste,config = <&gpio_out_lo>;
					};
					hrefv60_cfg2 {
						pins = "GPIO144_B13"; /* NFC_IRQ */
						ste,config = <&gpio_in_pd>;
					};
				 };
			};
			force {
				force_hrefv60_mode: force_hrefv60 {
					hrefv60_cfg1 {
						pins = "GPIO91_B6"; /* FORCE_SENSING_INT */
						ste,config = <&gpio_in_pu>;
					};
					hrefv60_cfg2 {
						pins =
						"GPIO92_D6", /* FORCE_SENSING_RST */
						"GPIO97_D9"; /* FORCE_SENSING_WU */
						ste,config = <&gpio_out_lo>;
					};
				 };
			};
			dipro {
				dipro_hrefv60_mode: dipro_hrefv60 {
					hrefv60_cfg1 {
						pins = "GPIO139_C9"; /* DIPRO_INT */
						ste,config = <&gpio_in_pu>;
					};
				 };
			};
			vaudio_hf {
				vaudio_hf_hrefv60_mode: vaudio_hf_hrefv60 {
					/* Audio Amplifier HF enable GPIO */
					hrefv60_cfg1 {
						pins = "GPIO149_B14"; /* VAUDIO_HF_EN, enable MAX8968 */
						ste,config = <&gpio_out_hi>;
					};
				 };
			};
			gbf {
				gbf_hrefv60_mode: gbf_hrefv60 {
					/*
					 * GBF (GPS, Bluetooth, FM-radio) interface,
					 * pull low to reset state
					 */
					hrefv60_cfg1 {
						pins = "GPIO171_D23"; /* GBF_ENA_RESET */
						ste,config = <&gpio_out_lo>;
					};
				 };
			};
			hdtv {
				hdtv_hrefv60_mode: hdtv_hrefv60 {
					/* MSP : HDTV INTERFACE GPIO line */
					hrefv60_cfg1 {
						pins = "GPIO192_AJ27";
						ste,config = <&gpio_in_pd>;
					};
				 };
			};
			mcde {
				lcd_hrefv60_mode: lcd_hrefv60 {
					/*
					 * Display Interface 1 uses GPIO 65 for RST (reset).
					 * Display Interface 2 uses GPIO 66 for RST (reset).
					 * Drive DISP1 reset high (not reset), driver DISP2 reset low (reset)
					 */
					hrefv60_cfg1 {
						pins = "GPIO65_F1";
						ste,config = <&gpio_out_hi>;
					};
					hrefv60_cfg2 {
						pins = "GPIO66_G3";
						ste,config = <&gpio_out_lo>;
					};
				};
			};
			gpios {
				/* Dangling GPIO pins */
				gpios_hrefv60_mode: gpios_hrefv60 {
					default_cfg1 {
						/* Normally UART1 RXD, now dangling */
						pins = "GPIO4_AH6";
						ste,config = <&in_pu>;
					};
				};
			};
		};
	};
};
