// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 STMicroelectronics Limited.
 * Author: <PERSON> <<EMAIL>>
 */
#include "stih418-clock.dtsi"
#include "stih407-family.dtsi"
#include "stih410-pinctrl.dtsi"
/ {
	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <2>;
			/* u-boot puts hpen in SBC dmem at 0xa4 offset */
			cpu-release-addr = <0x94100A4>;
		};
		cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <3>;
			/* u-boot puts hpen in SBC dmem at 0xa4 offset */
			cpu-release-addr = <0x94100A4>;
		};
	};

	usb2_picophy1: phy2 {
		compatible = "st,stih407-usb2-phy";
		#phy-cells = <0>;
		st,syscfg = <&syscfg_core 0xf8 0xf4>;
		resets = <&softreset STIH407_PICOPHY_SOFTRESET>,
			 <&picophyreset STIH407_PICOPHY0_RESET>;
		reset-names = "global", "port";
	};

	usb2_picophy2: phy3 {
		compatible = "st,stih407-usb2-phy";
		#phy-cells = <0>;
		st,syscfg = <&syscfg_core 0xfc 0xf4>;
		resets = <&softreset STIH407_PICOPHY_SOFTRESET>,
			 <&picophyreset STIH407_PICOPHY1_RESET>;
		reset-names = "global", "port";
	};

	soc {
		rng11: rng@8a8a000 {
			status = "disabled";
		};

		ohci0: usb@9a03c00 {
			compatible = "st,st-ohci-300x";
			reg = <0x9a03c00 0x100>;
			interrupts = <GIC_SPI 180 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_TX_ICN_DISP_0>;
			resets = <&powerdown STIH407_USB2_PORT0_POWERDOWN>,
				 <&softreset STIH407_USB2_PORT0_SOFTRESET>;
			reset-names = "power", "softreset";
			phys = <&usb2_picophy1>;
			phy-names = "usb";
		};

		ehci0: usb@9a03e00 {
			compatible = "st,st-ehci-300x";
			reg = <0x9a03e00 0x100>;
			interrupts = <GIC_SPI 151 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb0>;
			clocks = <&clk_s_c0_flexgen CLK_TX_ICN_DISP_0>;
			resets = <&powerdown STIH407_USB2_PORT0_POWERDOWN>,
				 <&softreset STIH407_USB2_PORT0_SOFTRESET>;
			reset-names = "power", "softreset";
			phys = <&usb2_picophy1>;
			phy-names = "usb";
		};

		ohci1: usb@9a83c00 {
			compatible = "st,st-ohci-300x";
			reg = <0x9a83c00 0x100>;
			interrupts = <GIC_SPI 181 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_TX_ICN_DISP_0>;
			resets = <&powerdown STIH407_USB2_PORT1_POWERDOWN>,
				 <&softreset STIH407_USB2_PORT1_SOFTRESET>;
			reset-names = "power", "softreset";
			phys = <&usb2_picophy2>;
			phy-names = "usb";
		};

		ehci1: usb@9a83e00 {
			compatible = "st,st-ehci-300x";
			reg = <0x9a83e00 0x100>;
			interrupts = <GIC_SPI 153 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb1>;
			clocks = <&clk_s_c0_flexgen CLK_TX_ICN_DISP_0>;
			resets = <&powerdown STIH407_USB2_PORT1_POWERDOWN>,
				 <&softreset STIH407_USB2_PORT1_SOFTRESET>;
			reset-names = "power", "softreset";
			phys = <&usb2_picophy2>;
			phy-names = "usb";
		};

		mmc0: sdhci@9060000 {
			assigned-clocks = <&clk_s_c0_flexgen CLK_MMC_0>;
			assigned-clock-parents = <&clk_s_c0_pll1 0>;
			assigned-clock-rates = <200000000>;
		};

		thermal@91a0000 {
			compatible = "st,stih407-thermal";
			reg = <0x91a0000 0x28>;
			clock-names = "thermal";
			clocks = <&clk_sysin>;
			interrupts = <GIC_SPI 205 IRQ_TYPE_EDGE_RISING>;
		};
	};
};
