// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2016 STMicroelectronics (R&D) Limited.
 * Author: <PERSON><PERSON> <<EMAIL>>
 */
/dts-v1/;
#include "stih410.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "STiH410 B2260";
	compatible = "st,stih410-b2260", "st,stih410";

	chosen {
		stdout-path = &uart1;
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x40000000>;
	};

	aliases {
		serial1 = &uart1;
		ethernet0 = &ethernet0;
	};

	leds {
		compatible = "gpio-leds";
		led-user-green-1 {
			label = "User_green_1";
			gpios = <&pio1 3 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "heartbeat";
			default-state = "off";
		};

		led-user-green-2 {
			label = "User_green_2";
			gpios = <&pio4 1 GPIO_ACTIVE_LOW>;
			default-state = "off";
		};

		led-user-green-3 {
			label = "User_green_3";
			gpios = <&pio2 1 GPIO_ACTIVE_LOW>;
			default-state = "off";
		};

		led-user-green-4 {
			label = "User_green_4";
			gpios = <&pio2 5 GPIO_ACTIVE_LOW>;
			default-state = "off";
		};
	};

	sound: sound {
		compatible = "simple-audio-card";
		simple-audio-card,name = "STI-B2260";
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;

		simple-audio-card,dai-link@0 {
			reg = <0>;
			/* DAC */
			format = "i2s";
			mclk-fs = <128>;
			cpu {
				sound-dai = <&sti_uni_player0>;
			};

			codec {
				sound-dai = <&sti_hdmi>;
			};
		};
	};

	miphy28lp_phy: miphy28lp {

		phy_port1: port@9b2a000 {
			st,osc-force-ext;
		};
	};

	usb2_picophy1: phy2 {
		status = "okay";
	};

	usb2_picophy2: phy3 {
		status = "okay";
	};

	soc {
		/* Low speed expansion connector */
		uart0: serial@9830000 {
			label = "LS-UART0";
			pinctrl-names = "default", "no-hw-flowctrl";
			pinctrl-0 = <&pinctrl_serial0_hw_flowctrl>;
			pinctrl-1 = <&pinctrl_serial0>;
			rts-gpios = <&pio17 3 GPIO_ACTIVE_LOW>;
			uart-has-rtscts;
			status = "okay";
		};

		/* Low speed expansion connector */
		uart1: serial@9831000 {
			label = "LS-UART1";
			status = "okay";
		};

		/* Low speed expansion connector */
		spi0: spi@9844000 {
			label = "LS-SPI0";
			cs-gpios = <&pio30 3 0>;
			status = "okay";
		};

		/* Low speed expansion connector */
		i2c0: i2c@9840000 {
			label = "LS-I2C0";
			status = "okay";
		};

		/* Low speed expansion connector */
		i2c1: i2c@9841000 {
			label = "LS-I2C1";
			status = "okay";
		};

		/* high speed expansion connector */
		i2c2: i2c@9842000 {
			label = "HS-I2C2";
			pinctrl-0 = <&pinctrl_i2c2_alt2_1>;
			status = "okay";
		};

		/* high speed expansion connector */
		i2c3: i2c@9843000 {
			label = "HS-I2C3";
			pinctrl-0 = <&pinctrl_i2c3_alt3_0>;
			status = "okay";
		};

		mmc0: sdhci@9060000 {
			pinctrl-0 = <&pinctrl_sd0>;
			bus-width = <4>;
			status = "okay";
		};

		/* high speed expansion connector */
		mmc1: sdhci@9080000 {
			status = "okay";
		};

		pwm0: pwm@9810000 {
			status = "okay";
		};

		pwm1: pwm@9510000 {
			status = "okay";
		};

		ohci0: usb@9a03c00 {
			status = "okay";
		};

		ehci0: usb@9a03e00 {
			status = "okay";
		};

		ohci1: usb@9a83c00 {
			status = "okay";
		};

		ehci1: usb@9a83e00 {
			status = "okay";
		};

		st_dwc3: dwc3@8f94000 {
			status = "okay";
		};

		ethernet0: dwmac@9630000 {
			phy-mode = "rgmii";
			pinctrl-0 = <&pinctrl_rgmii1 &pinctrl_rgmii1_mdio_1>;

			snps,reset-gpio = <&pio0 7 0>;
			snps,reset-active-low;
			snps,reset-delays-us = <0 10000 1000000>;

			status = "okay";
		};

		sti_uni_player0: sti-uni-player@8d80000 {
			status = "okay";
		};
		/* SSC11 to HDMI */
		hdmiddc: i2c@9541000 {
			/* HDMI V1.3a supports Standard mode only */
			clock-frequency = <100000>;
			st,i2c-min-scl-pulse-width-us = <0>;
			st,i2c-min-sda-pulse-width-us = <5>;
			status = "okay";
		};

		sata1: sata@9b28000 {
			status = "okay";
		};
	};
};
