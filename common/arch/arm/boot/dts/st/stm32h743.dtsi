/*
 * Copyright 2017 - <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include "../armv7-m.dtsi"
#include <dt-bindings/clock/stm32h7-clks.h>
#include <dt-bindings/mfd/stm32h7-rcc.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	clocks {
		clk_hse: clk-hse {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <0>;
		};

		clk_lse: clk-lse {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
		};

		clk_i2s: i2s_ckin {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <0>;
		};
	};

	soc {
		timer5: timer@40000c00 {
			compatible = "st,stm32-timer";
			reg = <0x40000c00 0x400>;
			interrupts = <50>;
			clocks = <&rcc TIM5_CK>;
		};

		lptimer1: timer@40002400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-lptimer";
			reg = <0x40002400 0x400>;
			clocks = <&rcc LPTIM1_CK>;
			clock-names = "mux";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm-lp";
				#pwm-cells = <3>;
				status = "disabled";
			};

			trigger@0 {
				compatible = "st,stm32-lptimer-trigger";
				reg = <0>;
				status = "disabled";
			};

			counter {
				compatible = "st,stm32-lptimer-counter";
				status = "disabled";
			};
		};

		spi2: spi@40003800 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32h7-spi";
			reg = <0x40003800 0x400>;
			interrupts = <36>;
			resets = <&rcc STM32H7_APB1L_RESET(SPI2)>;
			clocks = <&rcc SPI2_CK>;
			status = "disabled";

		};

		spi3: spi@40003c00 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32h7-spi";
			reg = <0x40003c00 0x400>;
			interrupts = <51>;
			resets = <&rcc STM32H7_APB1L_RESET(SPI3)>;
			clocks = <&rcc SPI3_CK>;
			status = "disabled";
		};

		usart2: serial@40004400 {
			compatible = "st,stm32h7-uart";
			reg = <0x40004400 0x400>;
			interrupts = <38>;
			status = "disabled";
			clocks = <&rcc USART2_CK>;
		};

		usart3: serial@40004800 {
			compatible = "st,stm32h7-uart";
			reg = <0x40004800 0x400>;
			interrupts = <39>;
			status = "disabled";
			clocks = <&rcc USART3_CK>;
		};

		uart4: serial@40004c00 {
			compatible = "st,stm32h7-uart";
			reg = <0x40004c00 0x400>;
			interrupts = <52>;
			status = "disabled";
			clocks = <&rcc UART4_CK>;
		};

		i2c1: i2c@40005400 {
			compatible = "st,stm32f7-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x40005400 0x400>;
			interrupts = <31>,
				     <32>;
			resets = <&rcc STM32H7_APB1L_RESET(I2C1)>;
			clocks = <&rcc I2C1_CK>;
			status = "disabled";
		};

		i2c2: i2c@40005800 {
			compatible = "st,stm32f7-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x40005800 0x400>;
			interrupts = <33>,
				     <34>;
			resets = <&rcc STM32H7_APB1L_RESET(I2C2)>;
			clocks = <&rcc I2C2_CK>;
			status = "disabled";
		};

		i2c3: i2c@40005c00 {
			compatible = "st,stm32f7-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x40005C00 0x400>;
			interrupts = <72>,
				     <73>;
			resets = <&rcc STM32H7_APB1L_RESET(I2C3)>;
			clocks = <&rcc I2C3_CK>;
			status = "disabled";
		};

		dac: dac@40007400 {
			compatible = "st,stm32h7-dac-core";
			reg = <0x40007400 0x400>;
			clocks = <&rcc DAC12_CK>;
			clock-names = "pclk";
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			dac1: dac@1 {
				compatible = "st,stm32-dac";
				#io-channel-cells = <1>;
				reg = <1>;
				status = "disabled";
			};

			dac2: dac@2 {
				compatible = "st,stm32-dac";
				#io-channel-cells = <1>;
				reg = <2>;
				status = "disabled";
			};
		};

		usart1: serial@40011000 {
			compatible = "st,stm32h7-uart";
			reg = <0x40011000 0x400>;
			interrupts = <37>;
			status = "disabled";
			clocks = <&rcc USART1_CK>;
		};

		spi1: spi@40013000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32h7-spi";
			reg = <0x40013000 0x400>;
			interrupts = <35>;
			resets = <&rcc STM32H7_APB2_RESET(SPI1)>;
			clocks = <&rcc SPI1_CK>;
			status = "disabled";
		};

		spi4: spi@40013400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32h7-spi";
			reg = <0x40013400 0x400>;
			interrupts = <84>;
			resets = <&rcc STM32H7_APB2_RESET(SPI4)>;
			clocks = <&rcc SPI4_CK>;
			status = "disabled";
		};

		spi5: spi@40015000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32h7-spi";
			reg = <0x40015000 0x400>;
			interrupts = <85>;
			resets = <&rcc STM32H7_APB2_RESET(SPI5)>;
			clocks = <&rcc SPI5_CK>;
			status = "disabled";
		};

		dma1: dma-controller@40020000 {
			compatible = "st,stm32-dma";
			reg = <0x40020000 0x400>;
			interrupts = <11>,
				     <12>,
				     <13>,
				     <14>,
				     <15>,
				     <16>,
				     <17>,
				     <47>;
			clocks = <&rcc DMA1_CK>;
			#dma-cells = <4>;
			st,mem2mem;
			dma-requests = <8>;
			status = "disabled";
		};

		dma2: dma-controller@40020400 {
			compatible = "st,stm32-dma";
			reg = <0x40020400 0x400>;
			interrupts = <56>,
				     <57>,
				     <58>,
				     <59>,
				     <60>,
				     <68>,
				     <69>,
				     <70>;
			clocks = <&rcc DMA2_CK>;
			#dma-cells = <4>;
			st,mem2mem;
			dma-requests = <8>;
			status = "disabled";
		};

		dmamux1: dma-router@40020800 {
			compatible = "st,stm32h7-dmamux";
			reg = <0x40020800 0x40>;
			#dma-cells = <3>;
			dma-channels = <16>;
			dma-requests = <128>;
			dma-masters = <&dma1 &dma2>;
			clocks = <&rcc DMA1_CK>;
		};

		adc_12: adc@40022000 {
			compatible = "st,stm32h7-adc-core";
			reg = <0x40022000 0x400>;
			interrupts = <18>;
			clocks = <&rcc ADC12_CK>;
			clock-names = "bus";
			interrupt-controller;
			#interrupt-cells = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			adc1: adc@0 {
				compatible = "st,stm32h7-adc";
				#io-channel-cells = <1>;
				reg = <0x0>;
				interrupt-parent = <&adc_12>;
				interrupts = <0>;
				status = "disabled";
			};

			adc2: adc@100 {
				compatible = "st,stm32h7-adc";
				#io-channel-cells = <1>;
				reg = <0x100>;
				interrupt-parent = <&adc_12>;
				interrupts = <1>;
				status = "disabled";
			};
		};

		usbotg_hs: usb@40040000 {
			compatible = "st,stm32f7-hsotg";
			reg = <0x40040000 0x40000>;
			interrupts = <77>;
			clocks = <&rcc USB1OTG_CK>;
			clock-names = "otg";
			g-rx-fifo-size = <256>;
			g-np-tx-fifo-size = <32>;
			g-tx-fifo-size = <128 128 64 64 64 64 32 32>;
			status = "disabled";
		};

		usbotg_fs: usb@40080000 {
			compatible = "st,stm32f4x9-fsotg";
			reg = <0x40080000 0x40000>;
			interrupts = <101>;
			clocks = <&rcc USB2OTG_CK>;
			clock-names = "otg";
			status = "disabled";
		};

		ltdc: display-controller@50001000 {
			compatible = "st,stm32-ltdc";
			reg = <0x50001000 0x200>;
			interrupts = <88>, <89>;
			resets = <&rcc STM32H7_APB3_RESET(LTDC)>;
			clocks = <&rcc LTDC_CK>;
			clock-names = "lcd";
			status = "disabled";
		};

		mdma1: dma-controller@52000000 {
			compatible = "st,stm32h7-mdma";
			reg = <0x52000000 0x1000>;
			interrupts = <122>;
			clocks = <&rcc MDMA_CK>;
			#dma-cells = <5>;
			dma-channels = <16>;
			dma-requests = <32>;
		};

		sdmmc1: mmc@52007000 {
			compatible = "arm,pl18x", "arm,primecell";
			arm,primecell-periphid = <0x10153180>;
			reg = <0x52007000 0x1000>;
			interrupts = <49>;
			clocks = <&rcc SDMMC1_CK>;
			clock-names = "apb_pclk";
			resets = <&rcc STM32H7_AHB3_RESET(SDMMC1)>;
			cap-sd-highspeed;
			cap-mmc-highspeed;
			max-frequency = <120000000>;
		};

		sdmmc2: mmc@48022400 {
			compatible = "arm,pl18x", "arm,primecell";
			arm,primecell-periphid = <0x10153180>;
			reg = <0x48022400 0x400>;
			interrupts = <124>;
			clocks = <&rcc SDMMC2_CK>;
			clock-names = "apb_pclk";
			resets = <&rcc STM32H7_AHB2_RESET(SDMMC2)>;
			cap-sd-highspeed;
			cap-mmc-highspeed;
			max-frequency = <120000000>;
			status = "disabled";
		};

		exti: interrupt-controller@58000000 {
			compatible = "st,stm32h7-exti";
			interrupt-controller;
			#interrupt-cells = <2>;
			reg = <0x58000000 0x400>;
			interrupts = <1>, <2>, <3>, <6>, <7>, <8>, <9>, <10>, <23>, <40>, <41>, <62>, <76>;
		};

		syscfg: syscon@58000400 {
			compatible = "st,stm32-syscfg", "syscon";
			reg = <0x58000400 0x400>;
		};

		spi6: spi@58001400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32h7-spi";
			reg = <0x58001400 0x400>;
			interrupts = <86>;
			resets = <&rcc STM32H7_APB4_RESET(SPI6)>;
			clocks = <&rcc SPI6_CK>;
			status = "disabled";
		};

		i2c4: i2c@58001c00 {
			compatible = "st,stm32f7-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x58001C00 0x400>;
			interrupts = <95>,
				     <96>;
			resets = <&rcc STM32H7_APB4_RESET(I2C4)>;
			clocks = <&rcc I2C4_CK>;
			status = "disabled";
		};

		lptimer2: timer@58002400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-lptimer";
			reg = <0x58002400 0x400>;
			clocks = <&rcc LPTIM2_CK>;
			clock-names = "mux";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm-lp";
				#pwm-cells = <3>;
				status = "disabled";
			};

			trigger@1 {
				compatible = "st,stm32-lptimer-trigger";
				reg = <1>;
				status = "disabled";
			};

			counter {
				compatible = "st,stm32-lptimer-counter";
				status = "disabled";
			};
		};

		lptimer3: timer@58002800 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-lptimer";
			reg = <0x58002800 0x400>;
			clocks = <&rcc LPTIM3_CK>;
			clock-names = "mux";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm-lp";
				#pwm-cells = <3>;
				status = "disabled";
			};

			trigger@2 {
				compatible = "st,stm32-lptimer-trigger";
				reg = <2>;
				status = "disabled";
			};
		};

		lptimer4: timer@58002c00 {
			compatible = "st,stm32-lptimer";
			reg = <0x58002c00 0x400>;
			clocks = <&rcc LPTIM4_CK>;
			clock-names = "mux";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm-lp";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		lptimer5: timer@58003000 {
			compatible = "st,stm32-lptimer";
			reg = <0x58003000 0x400>;
			clocks = <&rcc LPTIM5_CK>;
			clock-names = "mux";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm-lp";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		vrefbuf: regulator@58003c00 {
			compatible = "st,stm32-vrefbuf";
			reg = <0x58003C00 0x8>;
			clocks = <&rcc VREF_CK>;
			regulator-min-microvolt = <1500000>;
			regulator-max-microvolt = <2500000>;
			status = "disabled";
		};

		rtc: rtc@58004000 {
			compatible = "st,stm32h7-rtc";
			reg = <0x58004000 0x400>;
			clocks = <&rcc RTCAPB_CK>, <&rcc RTC_CK>;
			clock-names = "pclk", "rtc_ck";
			assigned-clocks = <&rcc RTC_CK>;
			assigned-clock-parents = <&rcc LSE_CK>;
			interrupt-parent = <&exti>;
			interrupts = <17 IRQ_TYPE_EDGE_RISING>;
			st,syscfg = <&pwrcfg 0x00 0x100>;
			status = "disabled";
		};

		rcc: reset-clock-controller@58024400 {
			compatible = "st,stm32h743-rcc", "st,stm32-rcc";
			reg = <0x58024400 0x400>;
			#clock-cells = <1>;
			#reset-cells = <1>;
			clocks = <&clk_hse>, <&clk_lse>, <&clk_i2s>;
			st,syscfg = <&pwrcfg>;
		};

		pwrcfg: power-config@58024800 {
			compatible = "st,stm32-power-config", "syscon";
			reg = <0x58024800 0x400>;
		};

		adc_3: adc@58026000 {
			compatible = "st,stm32h7-adc-core";
			reg = <0x58026000 0x400>;
			interrupts = <127>;
			clocks = <&rcc ADC3_CK>;
			clock-names = "bus";
			interrupt-controller;
			#interrupt-cells = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			adc3: adc@0 {
				compatible = "st,stm32h7-adc";
				#io-channel-cells = <1>;
				reg = <0x0>;
				interrupt-parent = <&adc_3>;
				interrupts = <0>;
				status = "disabled";
			};
		};

		mac: ethernet@40028000 {
			compatible = "st,stm32-dwmac", "snps,dwmac-4.10a";
			reg = <0x40028000 0x8000>;
			reg-names = "stmmaceth";
			interrupts = <61>;
			interrupt-names = "macirq";
			clock-names = "stmmaceth", "mac-clk-tx", "mac-clk-rx";
			clocks = <&rcc ETH1MAC_CK>, <&rcc ETH1TX_CK>, <&rcc ETH1RX_CK>;
			st,syscon = <&syscfg 0x4>;
			snps,pbl = <8>;
			status = "disabled";
		};

		pinctrl: pinctrl@******** {
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "st,stm32h743-pinctrl";
			ranges = <0 0x******** 0x3000>;
			interrupt-parent = <&exti>;
			st,syscfg = <&syscfg 0x8>;

			gpioa: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				reg = <0x0 0x400>;
				clocks = <&rcc GPIOA_CK>;
				st,bank-name = "GPIOA";
				interrupt-controller;
				#interrupt-cells = <2>;
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 0 16>;
			};

			gpiob: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				reg = <0x400 0x400>;
				clocks = <&rcc GPIOB_CK>;
				st,bank-name = "GPIOB";
				interrupt-controller;
				#interrupt-cells = <2>;
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 16 16>;
			};

			gpioc: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				reg = <0x800 0x400>;
				clocks = <&rcc GPIOC_CK>;
				st,bank-name = "GPIOC";
				interrupt-controller;
				#interrupt-cells = <2>;
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 32 16>;
			};

			gpiod: gpio@58020c00 {
				gpio-controller;
				#gpio-cells = <2>;
				reg = <0xc00 0x400>;
				clocks = <&rcc GPIOD_CK>;
				st,bank-name = "GPIOD";
				interrupt-controller;
				#interrupt-cells = <2>;
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 48 16>;
			};

			gpioe: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				reg = <0x1000 0x400>;
				clocks = <&rcc GPIOE_CK>;
				st,bank-name = "GPIOE";
				interrupt-controller;
				#interrupt-cells = <2>;
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 64 16>;
			};

			gpiof: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				reg = <0x1400 0x400>;
				clocks = <&rcc GPIOF_CK>;
				st,bank-name = "GPIOF";
				interrupt-controller;
				#interrupt-cells = <2>;
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 80 16>;
			};

			gpiog: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				reg = <0x1800 0x400>;
				clocks = <&rcc GPIOG_CK>;
				st,bank-name = "GPIOG";
				interrupt-controller;
				#interrupt-cells = <2>;
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 96 16>;
			};

			gpioh: gpio@58021c00 {
				gpio-controller;
				#gpio-cells = <2>;
				reg = <0x1c00 0x400>;
				clocks = <&rcc GPIOH_CK>;
				st,bank-name = "GPIOH";
				interrupt-controller;
				#interrupt-cells = <2>;
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 112 16>;
			};

			gpioi: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				reg = <0x2000 0x400>;
				clocks = <&rcc GPIOI_CK>;
				st,bank-name = "GPIOI";
				interrupt-controller;
				#interrupt-cells = <2>;
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 128 16>;
			};

			gpioj: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				reg = <0x2400 0x400>;
				clocks = <&rcc GPIOJ_CK>;
				st,bank-name = "GPIOJ";
				interrupt-controller;
				#interrupt-cells = <2>;
				ngpios = <16>;
				gpio-ranges = <&pinctrl 0 144 16>;
			};

			gpiok: gpio@******** {
				gpio-controller;
				#gpio-cells = <2>;
				reg = <0x2800 0x400>;
				clocks = <&rcc GPIOK_CK>;
				st,bank-name = "GPIOK";
				interrupt-controller;
				#interrupt-cells = <2>;
				ngpios = <8>;
				gpio-ranges = <&pinctrl 0 160 8>;
			};
		};
	};
};

&systick {
	clock-frequency = <*********>;
	status = "okay";
};
