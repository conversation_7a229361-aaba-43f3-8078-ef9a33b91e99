// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright 2012 ST-<PERSON><PERSON> AB
 */

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/leds/common.h>
#include "ste-href-family-pinctrl.dtsi"

/ {
	memory {
		device_type = "memory";
		reg = <0x00000000 0x20000000>;
	};

	battery: battery {
		compatible = "simple-battery";
		battery-type = "lithium-ion-polymer";
	};

	thermal-zones {
		battery-thermal {
			/* This zone will be polled by the battery temperature code */
			polling-delay = <0>;
			polling-delay-passive = <0>;
			thermal-sensors = <&bat_therm>;

			trips {
				battery-crit-hi {
					temperature = <70000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};
	};

	bat_therm: thermistor {
		compatible = "murata,ncp18wb473";
		io-channels = <&gpadc 0x02>; /* BatTemp */
		pullup-uv = <1800000>;
		pullup-ohm = <230000>;
		pulldown-ohm = <0>;
		#thermal-sensor-cells = <0>;
	};

	soc {
		serial@80120000 {
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&u0_a_1_default>;
			pinctrl-1 = <&u0_a_1_sleep>;
			status = "okay";
		};

		/* This UART is unused and thus left disabled */
		serial@80121000 {
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&u1rxtx_a_1_default>;
			pinctrl-1 = <&u1rxtx_a_1_sleep>;
		};

		serial@80007000 {
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&u2rxtx_c_1_default>;
			pinctrl-1 = <&u2rxtx_c_1_sleep>;
			status = "okay";
		};

		i2c@80004000 {
			pinctrl-names = "default","sleep";
			pinctrl-0 = <&i2c0_a_1_default>;
			pinctrl-1 = <&i2c0_a_1_sleep>;
			status = "okay";
		};

		i2c@80122000 {
			pinctrl-names = "default","sleep";
			pinctrl-0 = <&i2c1_b_2_default>;
			pinctrl-1 = <&i2c1_b_2_sleep>;
			status = "okay";
		};

		i2c@80128000 {
			pinctrl-names = "default","sleep";
			pinctrl-0 = <&i2c2_b_2_default>;
			pinctrl-1 = <&i2c2_b_2_sleep>;
			status = "okay";
			lp5521@33 {
				compatible = "national,lp5521";
				reg = <0x33>;
				label = "lp5521_pri";
				clock-mode = /bits/ 8 <2>;
				#address-cells = <1>;
				#size-cells = <0>;
				led@0 {
					reg = <0>;
					led-cur = /bits/ 8 <0x2f>;
					max-cur = /bits/ 8 <0x5f>;
					color = <LED_COLOR_ID_BLUE>;
					linux,default-trigger = "heartbeat";
				};
				led@1 {
					reg = <1>;
					led-cur = /bits/ 8 <0x2f>;
					max-cur = /bits/ 8 <0x5f>;
					color = <LED_COLOR_ID_BLUE>;
				};
				led@2 {
					reg = <2>;
					led-cur = /bits/ 8 <0x2f>;
					max-cur = /bits/ 8 <0x5f>;
					color = <LED_COLOR_ID_BLUE>;
				};
			};
			lp5521@34 {
				compatible = "national,lp5521";
				reg = <0x34>;
				label = "lp5521_sec";
				clock-mode = /bits/ 8 <2>;
				#address-cells = <1>;
				#size-cells = <0>;
				led@0 {
					reg = <0>;
					led-cur = /bits/ 8 <0x2f>;
					max-cur = /bits/ 8 <0x5f>;
					color = <LED_COLOR_ID_BLUE>;
				};
				led@1 {
					reg = <1>;
					led-cur = /bits/ 8 <0x2f>;
					max-cur = /bits/ 8 <0x5f>;
					color = <LED_COLOR_ID_BLUE>;
				};
				led@2 {
					reg = <2>;
					led-cur = /bits/ 8 <0x2f>;
					max-cur = /bits/ 8 <0x5f>;
					color = <LED_COLOR_ID_BLUE>;
				};
			};
			bh1780@29 {
				compatible = "rohm,bh1780gli";
				reg = <0x29>;
			};
		};

		i2c@80110000 {
			pinctrl-names = "default","sleep";
			pinctrl-0 = <&i2c3_c_2_default>;
			pinctrl-1 = <&i2c3_c_2_sleep>;
			status = "okay";
		};

		// External Micro SD slot
		mmc@80126000 {
			arm,primecell-periphid = <0x10480180>;
			max-frequency = <100000000>;
			bus-width = <4>;
			cap-sd-highspeed;
			cap-mmc-highspeed;
			sd-uhs-sdr12;
			sd-uhs-sdr25;
			full-pwr-cycle;
			st,sig-dir-dat0;
			st,sig-dir-dat2;
			st,sig-dir-cmd;
			st,sig-pin-fbclk;
			vmmc-supply = <&ab8500_ldo_aux3_reg>;
			vqmmc-supply = <&vmmci>;
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&mc0_a_1_default &sdi0_default_mode>;
			pinctrl-1 = <&mc0_a_1_sleep>;

			status = "okay";
		};

		// WLAN SDIO channel
		mmc@80118000 {
			arm,primecell-periphid = <0x10480180>;
			max-frequency = <100000000>;
			bus-width = <4>;
			non-removable;
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&mc1_a_1_default>;
			pinctrl-1 = <&mc1_a_1_sleep>;

			status = "okay";
		};

		// PoP:ed eMMC
		mmc@80005000 {
			arm,primecell-periphid = <0x10480180>;
			max-frequency = <100000000>;
			bus-width = <8>;
			cap-mmc-highspeed;
			non-removable;
			no-sdio;
			no-sd;
			vmmc-supply = <&db8500_vsmps2_reg>;
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&mc2_a_1_default>;
			pinctrl-1 = <&mc2_a_1_sleep>;

			status = "okay";
		};

		// On-board eMMC
		mmc@80114000 {
			arm,primecell-periphid = <0x10480180>;
		        max-frequency = <100000000>;
			bus-width = <8>;
			cap-mmc-highspeed;
			non-removable;
			no-sdio;
			no-sd;
			vmmc-supply = <&ab8500_ldo_aux2_reg>;
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&mc4_a_1_default>;
			pinctrl-1 = <&mc4_a_1_sleep>;

			status = "okay";
		};

		msp0: msp@80123000 {
			pinctrl-names = "default";
			pinctrl-0 = <&msp0txrxtfstck_a_1_default>;
			status = "okay";
		};

		msp1: msp@80124000 {
			pinctrl-names = "default";
			pinctrl-0 = <&msp1txrx_a_1_default>;
			status = "okay";
		};

		msp2: msp@80117000 {
			pinctrl-names = "default";
			pinctrl-0 = <&msp2_a_1_default>;
		};

		msp3: msp@80125000 {
			status = "okay";
		};

		prcmu@80157000 {
			ab8500 {
				gpio {
				};

				phy {
					pinctrl-names = "default", "sleep";
					pinctrl-0 = <&usb_a_1_default>;
					pinctrl-1 = <&usb_a_1_sleep>;
				};

				regulator {
					ab8500_ldo_aux1_reg: ab8500_ldo_aux1 {
						regulator-name = "V-DISPLAY";
					};

					ab8500_ldo_aux2_reg: ab8500_ldo_aux2 {
						regulator-name = "V-eMMC1";
					};

					ab8500_ldo_aux3_reg: ab8500_ldo_aux3 {
						regulator-name = "V-MMC-SD";
					};

					ab8500_ldo_intcore_reg: ab8500_ldo_intcore {
						regulator-name = "V-INTCORE";
					};

					ab8500_ldo_tvout_reg: ab8500_ldo_tvout {
						regulator-name = "V-TVOUT";
					};

					ab8500_ldo_audio_reg: ab8500_ldo_audio {
						regulator-name = "V-AUD";
					};

					ab8500_ldo_anamic1_reg: ab8500_ldo_anamic1 {
						regulator-name = "V-AMIC1";
					};

					ab8500_ldo_anamic2_reg: ab8500_ldo_anamic2 {
						regulator-name = "V-AMIC2";
					};

					ab8500_ldo_dmic_reg: ab8500_ldo_dmic {
						regulator-name = "V-DMIC";
					};

					ab8500_ldo_ana_reg: ab8500_ldo_ana {
						regulator-name = "V-CSI/DSI";
					};
				};
			};
		};

		pinctrl {
			sdi0 {
				sdi0_default_mode: sdi0_default {
					/* Some boards set additional settings here */
				};
			};
		};

		mcde@a0350000 {
			pinctrl-names = "default", "sleep";
			pinctrl-0 = <&lcd_default_mode>;
			pinctrl-1 = <&lcd_sleep_mode>;
		};
	};
};
