/*
 * Copyright 2016 - <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 *     You should have received a copy of the GNU General Public
 *     License along with this file; if not, write to the Free
 *     Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston,
 *     MA 02110-1301 USA
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "stm32f469.dtsi"
#include "stm32f469-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "STMicroelectronics STM32F469i-DISCO board";
	compatible = "st,stm32f469i-disco", "st,stm32f469";

	chosen {
		bootargs = "root=/dev/ram";
		stdout-path = "serial0:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x1000000>;
	};

	aliases {
		serial0 = &usart3;
	};

	mmc_vcard: mmc_vcard {
		compatible = "regulator-fixed";
		regulator-name = "mmc_vcard";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	vdd_dsi: vdd-dsi {
		compatible = "regulator-fixed";
		regulator-name = "vdd_dsi";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	soc {
		dma-ranges = <0xc0000000 0x0 0x10000000>;
	};

	leds {
		compatible = "gpio-leds";
		led-green {
			gpios = <&gpiog 6 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "heartbeat";
		};
		led-orange {
			gpios = <&gpiod 4 GPIO_ACTIVE_LOW>;
		};
		led-red {
			gpios = <&gpiod 5 GPIO_ACTIVE_LOW>;
		};
		led-blue {
			gpios = <&gpiok 3 GPIO_ACTIVE_LOW>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;
		button-0 {
			label = "User";
			linux,code = <KEY_WAKEUP>;
			gpios = <&gpioa 0 GPIO_ACTIVE_HIGH>;
		};
	};

	/* This turns on vbus for otg for host mode (dwc2) */
	vcc5v_otg: vcc5v-otg-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpiob 2 GPIO_ACTIVE_HIGH>;
		regulator-name = "vcc5_host1";
		regulator-always-on;
	};
};

&rcc {
	compatible = "st,stm32f469-rcc", "st,stm32f42xx-rcc", "st,stm32-rcc";
};

&clk_hse {
	clock-frequency = <8000000>;
};

&dma2d {
	status = "okay";
};

&dsi {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			dsi_in: endpoint {
				remote-endpoint = <&ltdc_out_dsi>;
			};
		};

		port@1 {
			reg = <1>;
			dsi_out: endpoint {
				remote-endpoint = <&dsi_panel_in>;
			};
		};
	};

	panel@0 {
		compatible = "orisetech,otm8009a";
		reg = <0>; /* dsi virtual channel (0..3) */
		reset-gpios = <&gpioh 7 GPIO_ACTIVE_LOW>;
		power-supply = <&vdd_dsi>;
		status = "okay";

		port {
			dsi_panel_in: endpoint {
				remote-endpoint = <&dsi_out>;
			};
		};
	};
};

&ltdc {
	status = "okay";

	port {
		ltdc_out_dsi: endpoint {
			remote-endpoint = <&dsi_in>;
		};
	};
};

&rtc {
	status = "okay";
};

&timers1 {
	status = "okay";

	pwm {
		pinctrl-0 = <&pwm1_pins>;
		pinctrl-names = "default";
		status = "okay";
	};

	timer@0 {
		status = "okay";
	};
};

&timers3 {
	status = "okay";

	pwm {
		pinctrl-0 = <&pwm3_pins>;
		pinctrl-names = "default";
		status = "okay";
	};

	timer@2 {
		status = "okay";
	};
};

&sdio {
	status = "okay";
	vmmc-supply = <&mmc_vcard>;
	cd-gpios = <&gpiog 2 GPIO_ACTIVE_LOW>;
	broken-cd;
	pinctrl-names = "default", "opendrain";
	pinctrl-0 = <&sdio_pins>;
	pinctrl-1 = <&sdio_pins_od>;
	bus-width = <4>;
};

&timers5 {
	/* Override timer5 to act as clockevent */
	compatible = "st,stm32-timer";
	interrupts = <50>;
	status = "okay";
	/delete-property/#address-cells;
	/delete-property/#size-cells;
	/delete-property/clock-names;
	/delete-node/pwm;
	/delete-node/timer@4;
};

&usart3 {
	pinctrl-0 = <&usart3_pins_a>;
	pinctrl-names = "default";
	status = "okay";
};

&usbotg_fs {
	dr_mode = "host";
	pinctrl-0 = <&usbotg_fs_pins_a>;
	pinctrl-names = "default";
	status = "okay";
};
