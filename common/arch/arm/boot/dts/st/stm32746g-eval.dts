/*
 * Copyright 2015 - <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "stm32f746.dtsi"
#include "stm32f746-pinctrl.dtsi"
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "STMicroelectronics STM32746g-EVAL board";
	compatible = "st,stm32746g-eval", "st,stm32f746";

	chosen {
		bootargs = "root=/dev/ram";
		stdout-path = "serial0:115200n8";
	};

	memory@c0000000 {
		device_type = "memory";
		reg = <0xc0000000 0x2000000>;
	};

	aliases {
		serial0 = &usart1;
	};

	leds {
		compatible = "gpio-leds";
		led-green {
			gpios = <&gpiof 10 1>;
			linux,default-trigger = "heartbeat";
		};
		led-orange {
			gpios = <&stmfx_pinctrl 17 1>;
		};
		led-red {
			gpios = <&gpiob 7 1>;
		};
		led-blue {
			gpios = <&stmfx_pinctrl 19 1>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;
		button-0 {
			label = "Wake up";
			linux,code = <KEY_WAKEUP>;
			gpios = <&gpioc 13 0>;
		};
	};

	joystick {
		compatible = "gpio-keys";
		pinctrl-0 = <&joystick_pins>;
		pinctrl-names = "default";
		button-0 {
			label = "JoySel";
			linux,code = <KEY_ENTER>;
			interrupt-parent = <&stmfx_pinctrl>;
			interrupts = <0 IRQ_TYPE_EDGE_FALLING>;
		};
		button-1 {
			label = "JoyDown";
			linux,code = <KEY_DOWN>;
			interrupt-parent = <&stmfx_pinctrl>;
			interrupts = <1 IRQ_TYPE_EDGE_FALLING>;
		};
		button-2 {
			label = "JoyLeft";
			linux,code = <KEY_LEFT>;
			interrupt-parent = <&stmfx_pinctrl>;
			interrupts = <2 IRQ_TYPE_EDGE_FALLING>;
		};
		button-3 {
			label = "JoyRight";
			linux,code = <KEY_RIGHT>;
			interrupt-parent = <&stmfx_pinctrl>;
			interrupts = <3 IRQ_TYPE_EDGE_FALLING>;
		};
		button-4 {
			label = "JoyUp";
			linux,code = <KEY_UP>;
			interrupt-parent = <&stmfx_pinctrl>;
			interrupts = <4 IRQ_TYPE_EDGE_FALLING>;
		};
	};

	usbotg_hs_phy: usb-phy {
		#phy-cells = <0>;
		compatible = "usb-nop-xceiv";
		clocks = <&rcc 0 STM32F7_AHB1_CLOCK(OTGHSULPI)>;
		clock-names = "main_clk";
	};

	mmc_vcard: mmc_vcard {
		compatible = "regulator-fixed";
		regulator-name = "mmc_vcard";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};
};

&clk_hse {
	clock-frequency = <25000000>;
};

&crc {
	status = "okay";
};

&i2c1 {
	pinctrl-0 = <&i2c1_pins_b>;
	pinctrl-names = "default";
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";

	stmfx: stmfx@42 {
		compatible = "st,stmfx-0300";
		reg = <0x42>;
		interrupts = <8 IRQ_TYPE_EDGE_RISING>;
		interrupt-parent = <&gpioi>;

		stmfx_pinctrl: pinctrl {
			compatible = "st,stmfx-0300-pinctrl";
			gpio-controller;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
			gpio-ranges = <&stmfx_pinctrl 0 0 24>;

			joystick_pins: joystick {
				pins = "gpio0", "gpio1", "gpio2", "gpio3", "gpio4";
				drive-push-pull;
				bias-pull-up;
			};
		};
	};
};

&rtc {
	status = "okay";
};

&sdio1 {
	status = "okay";
	vmmc-supply = <&mmc_vcard>;
	broken-cd;
	pinctrl-names = "default", "opendrain";
	pinctrl-0 = <&sdio_pins_a>;
	pinctrl-1 = <&sdio_pins_od_a>;
	bus-width = <4>;
};

&timers5 {
	/* Override timer5 to act as clockevent */
	compatible = "st,stm32-timer";
	interrupts = <50>;
	status = "okay";
	/delete-property/#address-cells;
	/delete-property/#size-cells;
	/delete-property/clock-names;
	/delete-node/pwm;
	/delete-node/timer@4;
};

&usart1 {
	pinctrl-0 = <&usart1_pins_a>;
	pinctrl-names = "default";
	status = "okay";
};

&usbotg_hs {
	dr_mode = "otg";
	phys = <&usbotg_hs_phy>;
	phy-names = "usb2-phy";
	pinctrl-0 = <&usbotg_hs_pins_a>;
	pinctrl-names = "default";
	status = "okay";
};
