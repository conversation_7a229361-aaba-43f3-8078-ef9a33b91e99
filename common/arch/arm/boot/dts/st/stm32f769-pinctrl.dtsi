// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2017 - All Rights Reserved
 * Author: <PERSON>  <<EMAIL>> for STMicroelectronics.
 */

#include "stm32f7-pinctrl.dtsi"

&pinctrl {
	compatible = "st,stm32f769-pinctrl";

	gpioa: gpio@40020000 {
		gpio-ranges = <&pinctrl 0 0 16>;
	};

	gpiob: gpio@40020400 {
		gpio-ranges = <&pinctrl 0 16 16>;
	};

	gpioc: gpio@40020800 {
		gpio-ranges = <&pinctrl 0 32 16>;
	};

	gpiod: gpio@40020c00 {
		gpio-ranges = <&pinctrl 0 48 16>;
	};

	gpioe: gpio@40021000 {
		gpio-ranges = <&pinctrl 0 64 16>;
	};

	gpiof: gpio@40021400 {
		gpio-ranges = <&pinctrl 0 80 16>;
	};

	gpiog: gpio@40021800 {
		gpio-ranges = <&pinctrl 0 96 16>;
	};

	gpioh: gpio@40021c00 {
		gpio-ranges = <&pinctrl 0 112 16>;
	};

	gpioi: gpio@40022000 {
		gpio-ranges = <&pinctrl 0 128 16>;
	};

	gpioj: gpio@40022400 {
		gpio-ranges = <&pinctrl 0 144 16>;
	};

	gpiok: gpio@40022800 {
		gpio-ranges = <&pinctrl 0 160 8>;
	};
};
