// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) Phytec GmbH 2019-2020 - All Rights Reserved
 * Author: Dom VOVARD <<EMAIL>>.
 */

/dts-v1/;

#include <dt-bindings/pinctrl/stm32-pinfunc.h>
#include "stm32mp157.dtsi"
#include "stm32mp15xc.dtsi"
#include "stm32mp15xxac-pinctrl.dtsi"
#include "stm32mp157c-phycore-stm32mp15-som.dtsi"

/ {
	model = "PHYTEC phyCORE-STM32MP1-3 Dev Board";
	compatible = "phytec,phycore-stm32mp1-3",
		     "phytec,phycore-stm32mp157c-som", "st,stm32mp157";

	aliases {
		mmc0 = &sdmmc1;
		mmc1 = &sdmmc2;
		mmc2 = &sdmmc3;
		serial0 = &uart4;
		serial1 = &usart3;
		serial2 = &usart1;
	};
};

&cryp1 {
	status = "okay";
};

&dts {
	status = "okay";
};

&fmc {
	status = "disabled";
};

&gpu {
	status = "okay";
};

&i2c4_eeprom {
	status = "okay";
};

&i2c4_rtc {
	status = "okay";
};

&qspi {
	status = "okay";
};

&sdmmc2 {
	status = "okay";
};
