// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2017 - All Rights Reserved
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>> for STMicroelectronics.
 */
/dts-v1/;

#include "stm32mp157.dtsi"
#include "stm32mp15xc.dtsi"
#include "stm32mp15-pinctrl.dtsi"
#include "stm32mp15xxaa-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/mfd/st,stpmic1.h>

/ {
	model = "STMicroelectronics STM32MP157C eval daughter";
	compatible = "st,stm32mp157c-ed1", "st,stm32mp157";

	aliases {
		serial0 = &uart4;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	memory@c0000000 {
		device_type = "memory";
		reg = <0xC0000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		mcuram2: mcuram2@10000000 {
			compatible = "shared-dma-pool";
			reg = <0x10000000 0x40000>;
			no-map;
		};

		vdev0vring0: vdev0vring0@10040000 {
			compatible = "shared-dma-pool";
			reg = <0x10040000 0x1000>;
			no-map;
		};

		vdev0vring1: vdev0vring1@10041000 {
			compatible = "shared-dma-pool";
			reg = <0x10041000 0x1000>;
			no-map;
		};

		vdev0buffer: vdev0buffer@10042000 {
			compatible = "shared-dma-pool";
			reg = <0x10042000 0x4000>;
			no-map;
		};

		mcuram: mcuram@30000000 {
			compatible = "shared-dma-pool";
			reg = <0x30000000 0x40000>;
			no-map;
		};

		retram: retram@38000000 {
			compatible = "shared-dma-pool";
			reg = <0x38000000 0x10000>;
			no-map;
		};
	};

	sd_switch: regulator-sd_switch {
		compatible = "regulator-gpio";
		regulator-name = "sd_switch";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <2900000>;
		regulator-type = "voltage";
		regulator-always-on;

		gpios = <&gpiof 14 GPIO_ACTIVE_HIGH>;
		gpios-states = <0>;
		states = <1800000 0x1>,
			 <2900000 0x0>;
	};

	vin: vin {
		compatible = "regulator-fixed";
		regulator-name = "vin";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};
};

&adc {
	/* ANA0, ANA1 are dedicated pins and don't need pinctrl: only in6. */
	pinctrl-0 = <&adc1_in6_pins_a>;
	pinctrl-names = "default";
	vdd-supply = <&vdd>;
	vdda-supply = <&vdda>;
	vref-supply = <&vdda>;
	status = "disabled";
	adc1: adc@0 {
		status = "okay";
		channel@0 {
			reg = <0>;
			/* 16.5 ck_cycles sampling time */
			st,min-sample-time-ns = <400>;
		};
		channel@1 {
			reg = <1>;
			st,min-sample-time-ns = <400>;
		};
		channel@6 {
			reg = <6>;
			st,min-sample-time-ns = <400>;
		};
	};
};

&crc1 {
	status = "okay";
};

&cryp1 {
	status = "okay";
};

&dac {
	pinctrl-names = "default";
	pinctrl-0 = <&dac_ch1_pins_a &dac_ch2_pins_a>;
	vref-supply = <&vdda>;
	status = "disabled";
	dac1: dac@1 {
		status = "okay";
	};
	dac2: dac@2 {
		status = "okay";
	};
};

&dts {
	status = "okay";
};

&hash1 {
	status = "okay";
};

&i2c4 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c4_pins_a>;
	pinctrl-1 = <&i2c4_sleep_pins_a>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	clock-frequency = <400000>;
	status = "okay";
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	pmic: stpmic@33 {
		compatible = "st,stpmic1";
		reg = <0x33>;
		interrupts-extended = <&gpioa 0 IRQ_TYPE_EDGE_FALLING>;
		interrupt-controller;
		#interrupt-cells = <2>;
		status = "okay";

		regulators {
			compatible = "st,stpmic1-regulators";
			buck1-supply = <&vin>;
			buck2-supply = <&vin>;
			buck3-supply = <&vin>;
			buck4-supply = <&vin>;
			ldo1-supply = <&v3v3>;
			ldo2-supply = <&v3v3>;
			ldo3-supply = <&vdd_ddr>;
			ldo4-supply = <&vin>;
			ldo5-supply = <&v3v3>;
			ldo6-supply = <&v3v3>;
			vref_ddr-supply = <&vin>;
			boost-supply = <&vin>;
			pwr_sw1-supply = <&bst_out>;
			pwr_sw2-supply = <&bst_out>;

			vddcore: buck1 {
				regulator-name = "vddcore";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd_ddr: buck2 {
				regulator-name = "vdd_ddr";
				regulator-min-microvolt = <1350000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd: buck3 {
				regulator-name = "vdd";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				st,mask-reset;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			v3v3: buck4 {
				regulator-name = "v3v3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-over-current-protection;
				regulator-initial-mode = <0>;
			};

			vdda: ldo1 {
				regulator-name = "vdda";
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				interrupts = <IT_CURLIM_LDO1 0>;
			};

			v2v8: ldo2 {
				regulator-name = "v2v8";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				interrupts = <IT_CURLIM_LDO2 0>;
			};

			vtt_ddr: ldo3 {
				regulator-name = "vtt_ddr";
				regulator-min-microvolt = <500000>;
				regulator-max-microvolt = <750000>;
				regulator-always-on;
				regulator-over-current-protection;
			};

			vdd_usb: ldo4 {
				regulator-name = "vdd_usb";
				interrupts = <IT_CURLIM_LDO4 0>;
			};

			vdd_sd: ldo5 {
				regulator-name = "vdd_sd";
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				interrupts = <IT_CURLIM_LDO5 0>;
				regulator-boot-on;
			};

			v1v8: ldo6 {
				regulator-name = "v1v8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				interrupts = <IT_CURLIM_LDO6 0>;
			};

			vref_ddr: vref_ddr {
				regulator-name = "vref_ddr";
				regulator-always-on;
			};

			bst_out: boost {
				regulator-name = "bst_out";
				interrupts = <IT_OCP_BOOST 0>;
			};

			vbus_otg: pwr_sw1 {
				regulator-name = "vbus_otg";
				interrupts = <IT_OCP_OTG 0>;
			 };

			 vbus_sw: pwr_sw2 {
				regulator-name = "vbus_sw";
				interrupts = <IT_OCP_SWOUT 0>;
				regulator-active-discharge = <1>;
			 };
		};

		onkey {
			compatible = "st,stpmic1-onkey";
			interrupts = <IT_PONKEY_F 0>, <IT_PONKEY_R 0>;
			interrupt-names = "onkey-falling", "onkey-rising";
			power-off-time-sec = <10>;
			status = "okay";
		};

		watchdog {
			compatible = "st,stpmic1-wdt";
			status = "disabled";
		};
	};
};

&ipcc {
	status = "okay";
};

&iwdg2 {
	timeout-sec = <32>;
	status = "okay";
};

&m4_rproc {
	memory-region = <&retram>, <&mcuram>, <&mcuram2>, <&vdev0vring0>,
			<&vdev0vring1>, <&vdev0buffer>;
	mboxes = <&ipcc 0>, <&ipcc 1>, <&ipcc 2>, <&ipcc 3>;
	mbox-names = "vq0", "vq1", "shutdown", "detach";
	interrupt-parent = <&exti>;
	interrupts = <68 1>;
	status = "okay";
};

&pwr_regulators {
	vdd-supply = <&vdd>;
	vdd_3v3_usbfs-supply = <&vdd_usb>;
};

&rng1 {
	status = "okay";
};

&rtc {
	status = "okay";
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a &sdmmc1_dir_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a &sdmmc1_dir_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a &sdmmc1_dir_sleep_pins_a>;
	cd-gpios = <&gpiog 1 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
	disable-wp;
	st,sig-dir;
	st,neg-edge;
	st,use-ckin;
	bus-width = <4>;
	vmmc-supply = <&vdd_sd>;
	vqmmc-supply = <&sd_switch>;
	sd-uhs-sdr12;
	sd-uhs-sdr25;
	sd-uhs-sdr50;
	sd-uhs-ddr50;
	status = "okay";
};

&sdmmc2 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc2_b4_pins_a &sdmmc2_d47_pins_a>;
	pinctrl-1 = <&sdmmc2_b4_od_pins_a &sdmmc2_d47_pins_a>;
	pinctrl-2 = <&sdmmc2_b4_sleep_pins_a &sdmmc2_d47_sleep_pins_a>;
	non-removable;
	no-sd;
	no-sdio;
	st,neg-edge;
	bus-width = <8>;
	vmmc-supply = <&v3v3>;
	vqmmc-supply = <&vdd>;
	mmc-ddr-3_3v;
	status = "okay";
};

&timers6 {
	status = "okay";
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;
	timer@5 {
		status = "okay";
	};
};

&uart4 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&uart4_pins_a>;
	pinctrl-1 = <&uart4_sleep_pins_a>;
	pinctrl-2 = <&uart4_idle_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&usbotg_hs {
	vbus-supply = <&vbus_otg>;
};

&usbphyc_port0 {
	phy-supply = <&vdd_usb>;
};

&usbphyc_port1 {
	phy-supply = <&vdd_usb>;
};
