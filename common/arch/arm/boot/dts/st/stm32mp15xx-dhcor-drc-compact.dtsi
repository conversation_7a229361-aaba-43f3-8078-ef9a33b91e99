// SPDX-License-Identifier: (GPL-2.0 OR BSD-3-Clause)
/*
 * Copyright (C) 2022 <PERSON><PERSON> <<EMAIL>>
 */

/ {
	aliases {
		ethernet0 = &ethernet0;
		ethernet1 = &ksz8851;
		mmc0 = &sdmmc1;
		rtc0 = &hwrtc;
		rtc1 = &rtc;
		serial0 = &uart4;
		serial1 = &uart8;
		serial2 = &usart3;
		serial3 = &uart5;
		spi0 = &qspi;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	led {
		compatible = "gpio-leds";
		led1 {
			label = "yellow:user0";
			gpios = <&gpioz 6 GPIO_ACTIVE_LOW>;
			default-state = "off";
		};

		led2 {
			label = "red:user1";
			gpios = <&gpioz 3 GPIO_ACTIVE_LOW>;
			default-state = "off";
		};
	};

	ethernet_vio: vioregulator {
		compatible = "regulator-fixed";
		regulator-name = "vio";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpioh 2 GPIO_ACTIVE_LOW>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vdd>;
	};
};

&adc {	/* X11 ADC inputs */
	pinctrl-names = "default";
	pinctrl-0 = <&adc12_ain_pins_b>;
	vdd-supply = <&vdd>;
	vdda-supply = <&vdda>;
	vref-supply = <&vdda>;
	status = "okay";

	adc1: adc@0 {
		status = "okay";
		channel@0 {
			reg = <0>;
			st,min-sample-time-ns = <5000>;
		};
		channel@1 {
			reg = <1>;
			st,min-sample-time-ns = <5000>;
		};
		channel@6 {
			reg = <6>;
			st,min-sample-time-ns = <5000>;
		};
	};

	adc2: adc@100 {
		status = "okay";
		channel@0 {
			reg = <0>;
			st,min-sample-time-ns = <5000>;
		};
		channel@1 {
			reg = <1>;
			st,min-sample-time-ns = <5000>;
		};
		channel@2 {
			reg = <2>;
			st,min-sample-time-ns = <5000>;
		};
	};
};

&ethernet0 {
	status = "okay";
	pinctrl-0 = <&ethernet0_rgmii_pins_c>;
	pinctrl-1 = <&ethernet0_rgmii_sleep_pins_c>;
	pinctrl-names = "default", "sleep";
	phy-mode = "rgmii";
	max-speed = <1000>;
	phy-handle = <&phy0>;

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "snps,dwmac-mdio";
		reset-gpios = <&gpioz 2 GPIO_ACTIVE_LOW>;
		reset-delay-us = <1000>;
		reset-post-delay-us = <1000>;

		phy0: ethernet-phy@7 {
			reg = <7>;

			rxc-skew-ps = <1500>;
			rxdv-skew-ps = <540>;
			rxd0-skew-ps = <420>;
			rxd1-skew-ps = <420>;
			rxd2-skew-ps = <420>;
			rxd3-skew-ps = <420>;

			txc-skew-ps = <1440>;
			txen-skew-ps = <540>;
			txd0-skew-ps = <420>;
			txd1-skew-ps = <420>;
			txd2-skew-ps = <420>;
			txd3-skew-ps = <420>;
		};
	};
};

&fmc {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&fmc_pins_b>;
	pinctrl-1 = <&fmc_sleep_pins_b>;
	status = "okay";

	ksz8851: ethernet@1,0 {
		compatible = "micrel,ks8851-mll";
		reg = <1 0x0 0x2>, <1 0x2 0x20000>;
		interrupt-parent = <&gpioc>;
		interrupts = <3 IRQ_TYPE_LEVEL_LOW>;
		bank-width = <2>;

		/* Timing values are in nS */
		st,fmc2-ebi-cs-mux-enable;
		st,fmc2-ebi-cs-transaction-type = <4>;
		st,fmc2-ebi-cs-buswidth = <16>;
		st,fmc2-ebi-cs-address-setup-ns = <5>;
		st,fmc2-ebi-cs-address-hold-ns = <5>;
		st,fmc2-ebi-cs-bus-turnaround-ns = <5>;
		st,fmc2-ebi-cs-data-setup-ns = <45>;
		st,fmc2-ebi-cs-data-hold-ns = <1>;
		st,fmc2-ebi-cs-write-address-setup-ns = <5>;
		st,fmc2-ebi-cs-write-address-hold-ns = <5>;
		st,fmc2-ebi-cs-write-bus-turnaround-ns = <5>;
		st,fmc2-ebi-cs-write-data-setup-ns = <45>;
		st,fmc2-ebi-cs-write-data-hold-ns = <1>;
	};
};

&gpioa {
	gpio-line-names = "", "", "", "",
			  "DRCC-VAR2", "", "", "",
			  "", "", "", "",
			  "", "", "", "";
};

&gpioe {
	gpio-line-names = "", "", "", "",
			  "", "DRCC-GPIO0", "", "",
			  "", "", "", "",
			  "", "", "", "";
};

&gpiog {
	gpio-line-names = "", "", "", "",
			  "", "", "", "",
			  "", "", "", "",
			  "DRCC-GPIO5", "", "", "";
};

&gpioh {
	gpio-line-names = "", "", "", "DRCC-HW2",
			  "DRCC-GPIO4", "", "", "",
			  "DRCC-HW1", "DRCC-HW0", "", "DRCC-VAR1",
			  "DRCC-VAR0", "", "", "DRCC-GPIO6";
};

&gpioi {
	gpio-line-names = "", "", "", "",
			  "", "", "", "DRCC-GPIO2",
			  "", "DRCC-GPIO1", "", "",
			  "", "", "", "";
};

&i2c1 {	/* X11 I2C1 */
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins_b>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;
};

&i2c4 {
	hwrtc: rtc@32 {
		compatible = "microcrystal,rv8803";
		reg = <0x32>;
	};

	eeprom@50 {
		compatible = "atmel,24c04";
		reg = <0x50>;
		pagesize = <16>;
	};

	dh_mac_eeprom: eeprom@53 {
		compatible = "atmel,24c02";
		reg = <0x53>;
		pagesize = <16>;
	};
};

&sdmmc1 {	/* MicroSD */
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a>;
	cd-gpios = <&gpioi 8 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
	disable-wp;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&vdd>;
	vqmmc-supply = <&vdd>;
	status = "okay";
};

&sdmmc2 {	/* eMMC */
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc2_b4_pins_a &sdmmc2_d47_pins_c>;
	pinctrl-1 = <&sdmmc2_b4_od_pins_a &sdmmc2_d47_pins_c>;
	pinctrl-2 = <&sdmmc2_b4_sleep_pins_a &sdmmc2_d47_sleep_pins_c>;
	bus-width = <8>;
	no-sd;
	no-sdio;
	non-removable;
	st,neg-edge;
	vmmc-supply = <&v3v3>;
	vqmmc-supply = <&vdd>;
	status = "okay";
};

&sdmmc3 {	/* SDIO Wi-Fi */
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc3_b4_pins_a>;
	pinctrl-1 = <&sdmmc3_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc3_b4_sleep_pins_a>;
	broken-cd;
	bus-width = <4>;
	mmc-ddr-3_3v;
	st,neg-edge;
	vmmc-supply = <&v3v3>;
	vqmmc-supply = <&v3v3>;
	status = "okay";
};

&spi2 {	/* X11 SPI */
	pinctrl-names = "default";
	pinctrl-0 = <&spi2_pins_b>;
	cs-gpios = <&gpioi 0 0>;
	status = "disabled";
	/delete-property/dmas;
	/delete-property/dma-names;
};

&uart4 {
	label = "UART0";
	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pins_d>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&uart5 {	/* X11 UART */
	label = "X11-UART5";
	pinctrl-names = "default";
	pinctrl-0 = <&uart5_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&uart8 {
	label = "RS485-1";
	pinctrl-names = "default";
	pinctrl-0 = <&uart8_pins_a &uart8_rtscts_pins_a>;
	uart-has-rtscts;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&usart3 {	/* RS485 or RS232 */
	label = "RS485-2";
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&usart3_pins_e>;
	pinctrl-1 = <&usart3_sleep_pins_e>;
	uart-has-rtscts;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&usbh_ehci {
	phys = <&usbphyc_port0>;
	status = "okay";
};

&usbh_ohci {
	phys = <&usbphyc_port0>;
	status = "okay";
};

&usbotg_hs {
	dr_mode = "otg";
	pinctrl-0 = <&usbotg_hs_pins_a>;
	pinctrl-names = "default";
	phy-names = "usb2-phy";
	phys = <&usbphyc_port1 0>;
	vbus-supply = <&vbus_otg>;
	status = "okay";
};

&usbphyc {
	status = "okay";
};

&usbphyc_port0 {
	phy-supply = <&vdd_usb>;
	connector {
		compatible = "usb-a-connector";
		vbus-supply = <&vbus_sw>;
	};
};

&usbphyc_port1 {
	phy-supply = <&vdd_usb>;
};
