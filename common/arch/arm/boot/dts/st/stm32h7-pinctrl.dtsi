/*
 * Copyright 2017 - <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include <dt-bindings/pinctrl/stm32-pinfunc.h>

&pinctrl {

	i2c1_pins_a: i2c1-0 {
		pins {
			pinmux = <STM32_PINMUX('B', 6, AF4)>, /* I2C1_SCL */
				 <STM32_PINMUX('B', 7, AF4)>; /* I2C1_SDA */
			bias-disable;
			drive-open-drain;
			slew-rate = <0>;
		};
	};

	ethernet_rmii: rmii-0 {
		pins {
			pinmux = <STM32_PINMUX('G', 11, AF11)>,
				 <STM32_PINMUX('G', 13, AF11)>,
				 <STM32_PINMUX('G', 12, AF11)>,
				 <STM32_PINMUX('C', 4, AF11)>,
				 <STM32_PINMUX('C', 5, AF11)>,
				 <STM32_PINMUX('A', 7, AF11)>,
				 <STM32_PINMUX('C', 1, AF11)>,
				 <STM32_PINMUX('A', 2, AF11)>,
				 <STM32_PINMUX('A', 1, AF11)>;
			slew-rate = <2>;
		};
	};

	sdmmc1_b4_pins_a: sdmmc1-b4-0 {
		pins {
			pinmux = <STM32_PINMUX('C', 8, AF12)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('C', 9, AF12)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('C', 10, AF12)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('C', 11, AF12)>, /* SDMMC1_D3 */
				 <STM32_PINMUX('C', 12, AF12)>, /* SDMMC1_CK */
				 <STM32_PINMUX('D', 2, AF12)>; /* SDMMC1_CMD */
			slew-rate = <3>;
			drive-push-pull;
			bias-disable;
		};
	};

	sdmmc1_b4_od_pins_a: sdmmc1-b4-od-0 {
		pins1 {
			pinmux = <STM32_PINMUX('C', 8, AF12)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('C', 9, AF12)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('C', 10, AF12)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('C', 11, AF12)>, /* SDMMC1_D3 */
				 <STM32_PINMUX('C', 12, AF12)>; /* SDMMC1_CK */
			slew-rate = <3>;
			drive-push-pull;
			bias-disable;
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 2, AF12)>; /* SDMMC1_CMD */
			slew-rate = <3>;
			drive-open-drain;
			bias-disable;
		};
	};

	sdmmc1_b4_sleep_pins_a: sdmmc1-b4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('C', 8, ANALOG)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('C', 9, ANALOG)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('C', 10, ANALOG)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('C', 11, ANALOG)>, /* SDMMC1_D3 */
				 <STM32_PINMUX('C', 12, ANALOG)>, /* SDMMC1_CK */
				 <STM32_PINMUX('D', 2, ANALOG)>; /* SDMMC1_CMD */
		};
	};

	sdmmc1_dir_pins_a: sdmmc1-dir-0 {
		pins1 {
			pinmux = <STM32_PINMUX('C', 6, AF8)>, /* SDMMC1_D0DIR */
				 <STM32_PINMUX('C', 7, AF8)>, /* SDMMC1_D123DIR */
				 <STM32_PINMUX('B', 9, AF7)>; /* SDMMC1_CDIR */
			slew-rate = <3>;
			drive-push-pull;
			bias-pull-up;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 8, AF7)>; /* SDMMC1_CKIN */
			bias-pull-up;
		};
	};

	sdmmc1_dir_sleep_pins_a: sdmmc1-dir-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('C', 6, ANALOG)>, /* SDMMC1_D0DIR */
				 <STM32_PINMUX('C', 7, ANALOG)>, /* SDMMC1_D123DIR */
				 <STM32_PINMUX('B', 9, ANALOG)>, /* SDMMC1_CDIR */
				 <STM32_PINMUX('B', 8, ANALOG)>; /* SDMMC1_CKIN */
		};
	};

	sdmmc2_b4_pins_a: sdmmc2-b4-0 {
		pins {
			pinmux = <STM32_PINMUX('B', 14, AF9)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('B', 15, AF9)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('B', 3, AF9)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('B', 4, AF9)>, /* SDMMC1_D3 */
				 <STM32_PINMUX('D', 6, AF11)>, /* SDMMC1_CK */
				 <STM32_PINMUX('D', 7, AF11)>; /* SDMMC1_CMD */
			slew-rate = <3>;
			drive-push-pull;
			bias-disable;
		};
	};

	sdmmc2_b4_od_pins_a: sdmmc2-b4-od-0 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 14, AF9)>, /* SDMMC2_D0 */
				 <STM32_PINMUX('B', 15, AF9)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('B', 3, AF9)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('B', 4, AF9)>, /* SDMMC1_D3 */
				 <STM32_PINMUX('D', 6, AF11)>; /* SDMMC1_CK */
			slew-rate = <3>;
			drive-push-pull;
			bias-disable;
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 7, AF11)>; /* SDMMC1_CMD */
			slew-rate = <3>;
			drive-open-drain;
			bias-disable;
		};
	};

	sdmmc2_b4_sleep_pins_a: sdmmc2-b4-sleep-0 {
		pins {
			pinmux = <STM32_PINMUX('B', 14, ANALOG)>, /* SDMMC1_D0 */
				 <STM32_PINMUX('B', 15, ANALOG)>, /* SDMMC1_D1 */
				 <STM32_PINMUX('B', 3, ANALOG)>, /* SDMMC1_D2 */
				 <STM32_PINMUX('B', 4, ANALOG)>, /* SDMMC1_D3 */
				 <STM32_PINMUX('D', 6, ANALOG)>, /* SDMMC1_CK */
				 <STM32_PINMUX('D', 7, ANALOG)>; /* SDMMC1_CMD */
		};
	};

	spi1_pins: spi1-0 {
		pins1 {
			pinmux = <STM32_PINMUX('A', 5, AF5)>,
				/* SPI1_CLK */
				 <STM32_PINMUX('B', 5, AF5)>;
				/* SPI1_MOSI */
			bias-disable;
			drive-push-pull;
			slew-rate = <2>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('G', 9, AF5)>;
				/* SPI1_MISO */
			bias-disable;
		};
	};

	uart4_pins: uart4-0 {
		pins1 {
			pinmux = <STM32_PINMUX('A', 0, AF8)>; /* UART4_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('I', 9, AF8)>; /* UART4_RX */
			bias-disable;
		};
	};

	usart1_pins: usart1-0 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 14, AF4)>; /* USART1_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 15, AF4)>; /* USART1_RX */
			bias-disable;
		};
	};

	usart2_pins: usart2-0 {
		pins1 {
			pinmux = <STM32_PINMUX('D', 5, AF7)>; /* USART2_TX */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('D', 6, AF7)>; /* USART2_RX */
			bias-disable;
		};
	};

	usart3_pins: usart3-0 {
		pins1 {
			pinmux = <STM32_PINMUX('B', 10, AF7)>, /* USART3_TX */
				 <STM32_PINMUX('D', 12, AF7)>; /* USART3_RTS_DE */
			bias-disable;
			drive-push-pull;
			slew-rate = <0>;
		};
		pins2 {
			pinmux = <STM32_PINMUX('B', 11, AF7)>, /* USART3_RX */
				 <STM32_PINMUX('D', 11, AF7)>; /* USART3_CTS_NSS */
			bias-disable;
		};
	};

	usbotg_hs_pins_a: usbotg-hs-0 {
		pins {
			pinmux = <STM32_PINMUX('H', 4, AF10)>,	/* ULPI_NXT */
					 <STM32_PINMUX('I', 11, AF10)>, /* ULPI_DIR> */
					 <STM32_PINMUX('C', 0, AF10)>,	/* ULPI_STP> */
					 <STM32_PINMUX('A', 5, AF10)>,	/* ULPI_CK> */
					 <STM32_PINMUX('A', 3, AF10)>,	/* ULPI_D0> */
					 <STM32_PINMUX('B', 0, AF10)>,	/* ULPI_D1> */
					 <STM32_PINMUX('B', 1, AF10)>,	/* ULPI_D2> */
					 <STM32_PINMUX('B', 10, AF10)>, /* ULPI_D3> */
					 <STM32_PINMUX('B', 11, AF10)>, /* ULPI_D4> */
					 <STM32_PINMUX('B', 12, AF10)>, /* ULPI_D5> */
					 <STM32_PINMUX('B', 13, AF10)>, /* ULPI_D6> */
					 <STM32_PINMUX('B', 5, AF10)>;	/* ULPI_D7> */
			bias-disable;
			drive-push-pull;
			slew-rate = <2>;
		};
	};
};

