// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 STMicroelectronics Limited.
 * Author: <PERSON> <<EMAIL>>
 */
#include "stih410-clock.dtsi"
#include "stih407-family.dtsi"
#include "stih410-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>
/ {
	aliases {
		bdisp0 = &bdisp0;
	};

	usb2_picophy1: phy2 {
		compatible = "st,stih407-usb2-phy";
		#phy-cells = <0>;
		st,syscfg = <&syscfg_core 0xf8 0xf4>;
		resets = <&softreset STIH407_PICOPHY_SOFTRESET>,
			 <&picophyreset STIH407_PICOPHY0_RESET>;
		reset-names = "global", "port";

		status = "disabled";
	};

	usb2_picophy2: phy3 {
		compatible = "st,stih407-usb2-phy";
		#phy-cells = <0>;
		st,syscfg = <&syscfg_core 0xfc 0xf4>;
		resets = <&softreset STIH407_PICOPHY_SOFTRESET>,
			 <&picophyreset STIH407_PICOPHY1_RESET>;
		reset-names = "global", "port";

		status = "disabled";
	};

	soc {
		ohci0: usb@9a03c00 {
			compatible = "st,st-ohci-300x";
			reg = <0x9a03c00 0x100>;
			interrupts = <GIC_SPI 180 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_TX_ICN_DISP_0>,
				 <&clk_s_c0_flexgen CLK_RX_ICN_DISP_0>;
			resets = <&powerdown STIH407_USB2_PORT0_POWERDOWN>,
				 <&softreset STIH407_USB2_PORT0_SOFTRESET>;
			reset-names = "power", "softreset";
			phys = <&usb2_picophy1>;
			phy-names = "usb";

			status = "disabled";
		};

		ehci0: usb@9a03e00 {
			compatible = "st,st-ehci-300x";
			reg = <0x9a03e00 0x100>;
			interrupts = <GIC_SPI 151 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb0>;
			clocks = <&clk_s_c0_flexgen CLK_TX_ICN_DISP_0>,
				 <&clk_s_c0_flexgen CLK_RX_ICN_DISP_0>;
			resets = <&powerdown STIH407_USB2_PORT0_POWERDOWN>,
				 <&softreset STIH407_USB2_PORT0_SOFTRESET>;
			reset-names = "power", "softreset";
			phys = <&usb2_picophy1>;
			phy-names = "usb";

			status = "disabled";
		};

		ohci1: usb@9a83c00 {
			compatible = "st,st-ohci-300x";
			reg = <0x9a83c00 0x100>;
			interrupts = <GIC_SPI 181 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_s_c0_flexgen CLK_TX_ICN_DISP_0>,
				 <&clk_s_c0_flexgen CLK_RX_ICN_DISP_0>;
			resets = <&powerdown STIH407_USB2_PORT1_POWERDOWN>,
				 <&softreset STIH407_USB2_PORT1_SOFTRESET>;
			reset-names = "power", "softreset";
			phys = <&usb2_picophy2>;
			phy-names = "usb";

			status = "disabled";
		};

		ehci1: usb@9a83e00 {
			compatible = "st,st-ehci-300x";
			reg = <0x9a83e00 0x100>;
			interrupts = <GIC_SPI 153 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_usb1>;
			clocks = <&clk_s_c0_flexgen CLK_TX_ICN_DISP_0>,
				 <&clk_s_c0_flexgen CLK_RX_ICN_DISP_0>;
			resets = <&powerdown STIH407_USB2_PORT1_POWERDOWN>,
				 <&softreset STIH407_USB2_PORT1_SOFTRESET>;
			reset-names = "power", "softreset";
			phys = <&usb2_picophy2>;
			phy-names = "usb";

			status = "disabled";
		};

		sti-display-subsystem@0 {
			compatible = "st,sti-display-subsystem";
			#address-cells = <1>;
			#size-cells = <1>;

			reg = <0 0>;
			assigned-clocks = <&clk_s_d2_quadfs 0>,
					  <&clk_s_d2_quadfs 1>,
					  <&clk_s_c0_pll1 0>,
					  <&clk_s_c0_flexgen CLK_COMPO_DVP>,
					  <&clk_s_c0_flexgen CLK_MAIN_DISP>,
					  <&clk_s_d2_flexgen CLK_PIX_MAIN_DISP>,
					  <&clk_s_d2_flexgen CLK_PIX_AUX_DISP>,
					  <&clk_s_d2_flexgen CLK_PIX_GDP1>,
					  <&clk_s_d2_flexgen CLK_PIX_GDP2>,
					  <&clk_s_d2_flexgen CLK_PIX_GDP3>,
					  <&clk_s_d2_flexgen CLK_PIX_GDP4>;

			assigned-clock-parents = <0>,
						 <0>,
						 <0>,
						 <&clk_s_c0_pll1 0>,
						 <&clk_s_c0_pll1 0>,
						 <&clk_s_d2_quadfs 0>,
						 <&clk_s_d2_quadfs 1>,
						 <&clk_s_d2_quadfs 0>,
						 <&clk_s_d2_quadfs 0>,
						 <&clk_s_d2_quadfs 0>,
						 <&clk_s_d2_quadfs 0>;

			assigned-clock-rates = <297000000>,
					       <297000000>,
					       <0>,
					       <400000000>,
					       <400000000>;

			ranges;

			sti-compositor@9d11000 {
				compatible = "st,stih407-compositor";
				reg = <0x9d11000 0x1000>;

				clock-names = "compo_main",
					      "compo_aux",
					      "pix_main",
					      "pix_aux",
					      "pix_gdp1",
					      "pix_gdp2",
					      "pix_gdp3",
					      "pix_gdp4",
					      "main_parent",
					      "aux_parent";

				clocks = <&clk_s_c0_flexgen CLK_COMPO_DVP>,
					 <&clk_s_c0_flexgen CLK_COMPO_DVP>,
					 <&clk_s_d2_flexgen CLK_PIX_MAIN_DISP>,
					 <&clk_s_d2_flexgen CLK_PIX_AUX_DISP>,
					 <&clk_s_d2_flexgen CLK_PIX_GDP1>,
					 <&clk_s_d2_flexgen CLK_PIX_GDP2>,
					 <&clk_s_d2_flexgen CLK_PIX_GDP3>,
					 <&clk_s_d2_flexgen CLK_PIX_GDP4>,
					 <&clk_s_d2_quadfs 0>,
					 <&clk_s_d2_quadfs 1>;

				reset-names = "compo-main", "compo-aux";
				resets = <&softreset STIH407_COMPO_SOFTRESET>,
					 <&softreset STIH407_COMPO_SOFTRESET>;
				st,vtg = <&vtg_main>, <&vtg_aux>;
			};

			sti-tvout@8d08000 {
				compatible = "st,stih407-tvout";
				reg = <0x8d08000 0x1000>;
				reg-names = "tvout-reg";
				reset-names = "tvout";
				resets = <&softreset STIH407_HDTVOUT_SOFTRESET>;
				#address-cells = <1>;
				#size-cells = <1>;
				assigned-clocks = <&clk_s_d2_flexgen CLK_PIX_HDMI>,
						  <&clk_s_d2_flexgen CLK_TMDS_HDMI>,
						  <&clk_s_d2_flexgen CLK_REF_HDMIPHY>,
						  <&clk_s_d0_flexgen CLK_PCM_0>,
						  <&clk_s_d2_flexgen CLK_PIX_HDDAC>,
						  <&clk_s_d2_flexgen CLK_HDDAC>;

				assigned-clock-parents = <&clk_s_d2_quadfs 0>,
							 <&clk_tmdsout_hdmi>,
							 <&clk_s_d2_quadfs 0>,
							 <&clk_s_d0_quadfs 0>,
							 <&clk_s_d2_quadfs 0>,
							 <&clk_s_d2_quadfs 0>;
			};

			sti_hdmi: sti-hdmi@8d04000 {
				compatible = "st,stih407-hdmi";
				reg = <0x8d04000 0x1000>;
				reg-names = "hdmi-reg";
				#sound-dai-cells = <0>;
				interrupts = <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names = "irq";
				clock-names = "pix",
					      "tmds",
					      "phy",
					      "audio",
					      "main_parent",
					      "aux_parent";

				clocks = <&clk_s_d2_flexgen CLK_PIX_HDMI>,
					 <&clk_s_d2_flexgen CLK_TMDS_HDMI>,
					 <&clk_s_d2_flexgen CLK_REF_HDMIPHY>,
					 <&clk_s_d0_flexgen CLK_PCM_0>,
					 <&clk_s_d2_quadfs 0>,
					 <&clk_s_d2_quadfs 1>;

				hdmi,hpd-gpio = <&pio5 3 GPIO_ACTIVE_LOW>;
				reset-names = "hdmi";
				resets = <&softreset STIH407_HDMI_TX_PHY_SOFTRESET>;
				ddc = <&hdmiddc>;
			};

			sti-hda@8d02000 {
				compatible = "st,stih407-hda";
				status = "disabled";
				reg = <0x8d02000 0x400>, <0x92b0120 0x4>;
				reg-names = "hda-reg", "video-dacs-ctrl";
				clock-names = "pix",
					      "hddac",
					      "main_parent",
					      "aux_parent";
				clocks = <&clk_s_d2_flexgen CLK_PIX_HDDAC>,
					 <&clk_s_d2_flexgen CLK_HDDAC>,
					 <&clk_s_d2_quadfs 0>,
					 <&clk_s_d2_quadfs 1>;
			};

			sti-hqvdp@9c00000 {
				compatible = "st,stih407-hqvdp";
				reg = <0x9C00000 0x100000>;
				clock-names = "hqvdp", "pix_main";
				clocks = <&clk_s_c0_flexgen CLK_MAIN_DISP>,
					 <&clk_s_d2_flexgen CLK_PIX_MAIN_DISP>;
				reset-names = "hqvdp";
				resets = <&softreset STIH407_HDQVDP_SOFTRESET>;
				st,vtg = <&vtg_main>;
			};
		};

		bdisp0:bdisp@9f10000 {
			compatible = "st,stih407-bdisp";
			reg = <0x9f10000 0x1000>;
			interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "bdisp";
			clocks = <&clk_s_c0_flexgen CLK_IC_BDISP_0>;
		};

		hva@8c85000 {
			compatible = "st,st-hva";
			reg = <0x8c85000 0x400>, <0x6000000 0x40000>;
			reg-names = "hva_registers", "hva_esram";
			interrupts = <GIC_SPI 58 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 59 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "clk_hva";
			clocks = <&clk_s_c0_flexgen CLK_HVA>;
		};

		thermal@91a0000 {
			compatible = "st,stih407-thermal";
			reg = <0x91a0000 0x28>;
			clock-names = "thermal";
			clocks = <&clk_sysin>;
			interrupts = <GIC_SPI 205 IRQ_TYPE_EDGE_RISING>;
		};

		cec@94a087c {
			compatible = "st,stih-cec";
			reg = <0x94a087c 0x64>;
			clocks = <&clk_sysin>;
			clock-names = "cec-clk";
			interrupts = <GIC_SPI 140 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "cec-irq";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_cec0_default>;
			resets = <&softreset STIH407_LPM_SOFTRESET>;
			hdmi-phandle = <&sti_hdmi>;
		};
	};
};
