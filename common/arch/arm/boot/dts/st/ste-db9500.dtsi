// SPDX-License-Identifier: GPL-2.0-or-later

#include "ste-dbx5x0.dtsi"

/ {
	cpus {
		cpu@300 {
			operating-points = <998400 0
					    798720 0
					    399360 0
					    199680 0>;
		};
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		/*
		 * Initial Secure Software ISSW memory
		 *
		 * This is probably only used if the kernel tries
		 * to actually call into trustzone to run secure
		 * applications, which the mainline kernel probably
		 * will not do on this old chipset. But you can never
		 * be too careful, so reserve this memory anyway.
		 */
		ram@17f00000 {
			reg = <0x17f00000 0x00100000>;
			no-map;
		};
	};
};
