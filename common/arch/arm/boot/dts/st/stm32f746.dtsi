/*
 * Copyright 2015 - <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include "../armv7-m.dtsi"
#include <dt-bindings/clock/stm32fx-clock.h>
#include <dt-bindings/mfd/stm32f7-rcc.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	clocks {
		clk_hse: clk-hse {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <0>;
		};

		clk-lse {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
		};

		clk-lsi {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32000>;
		};

		clk_i2s_ckin: clk-i2s-ckin {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <48000000>;
		};
	};

	soc {
		timers2: timers@40000000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40000000 0x400>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(TIM2)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@1 {
				compatible = "st,stm32-timer-trigger";
				reg = <1>;
				status = "disabled";
			};
		};

		timers3: timers@40000400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40000400 0x400>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(TIM3)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@2 {
				compatible = "st,stm32-timer-trigger";
				reg = <2>;
				status = "disabled";
			};
		};

		timers4: timers@40000800 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40000800 0x400>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(TIM4)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@3 {
				compatible = "st,stm32-timer-trigger";
				reg = <3>;
				status = "disabled";
			};
		};

		timers5: timers@40000c00 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40000C00 0x400>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(TIM5)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@4 {
				compatible = "st,stm32-timer-trigger";
				reg = <4>;
				status = "disabled";
			};
		};

		timers6: timers@40001000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40001000 0x400>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(TIM6)>;
			clock-names = "int";
			status = "disabled";

			timer@5 {
				compatible = "st,stm32-timer-trigger";
				reg = <5>;
				status = "disabled";
			};
		};

		timers7: timers@40001400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40001400 0x400>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(TIM7)>;
			clock-names = "int";
			status = "disabled";

			timer@6 {
				compatible = "st,stm32-timer-trigger";
				reg = <6>;
				status = "disabled";
			};
		};

		timers12: timers@40001800 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40001800 0x400>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(TIM12)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@11 {
				compatible = "st,stm32-timer-trigger";
				reg = <11>;
				status = "disabled";
			};
		};

		timers13: timers@40001c00 {
			compatible = "st,stm32-timers";
			reg = <0x40001C00 0x400>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(TIM13)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		timers14: timers@40002000 {
			compatible = "st,stm32-timers";
			reg = <0x40002000 0x400>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(TIM14)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		rtc: rtc@40002800 {
			compatible = "st,stm32-rtc";
			reg = <0x40002800 0x400>;
			clocks = <&rcc 1 CLK_RTC>;
			assigned-clocks = <&rcc 1 CLK_RTC>;
			assigned-clock-parents = <&rcc 1 CLK_LSE>;
			interrupt-parent = <&exti>;
			interrupts = <17 1>;
			st,syscfg = <&pwrcfg 0x00 0x100>;
			status = "disabled";
		};

		can3: can@40003400 {
			compatible = "st,stm32f4-bxcan";
			reg = <0x40003400 0x200>;
			interrupts = <104>, <105>, <106>, <107>;
			interrupt-names = "tx", "rx0", "rx1", "sce";
			resets = <&rcc STM32F7_APB1_RESET(CAN3)>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(CAN3)>;
			st,gcan = <&gcan3>;
			status = "disabled";
		};

		gcan3: gcan@40003600 {
			compatible = "st,stm32f4-gcan", "syscon";
			reg = <0x40003600 0x200>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(CAN3)>;
		};

		usart2: serial@40004400 {
			compatible = "st,stm32f7-uart";
			reg = <0x40004400 0x400>;
			interrupts = <38>;
			clocks = <&rcc 1 CLK_USART2>;
			status = "disabled";
		};

		usart3: serial@40004800 {
			compatible = "st,stm32f7-uart";
			reg = <0x40004800 0x400>;
			interrupts = <39>;
			clocks = <&rcc 1 CLK_USART3>;
			status = "disabled";
		};

		usart4: serial@40004c00 {
			compatible = "st,stm32f7-uart";
			reg = <0x40004c00 0x400>;
			interrupts = <52>;
			clocks = <&rcc 1 CLK_UART4>;
			status = "disabled";
		};

		usart5: serial@40005000 {
			compatible = "st,stm32f7-uart";
			reg = <0x40005000 0x400>;
			interrupts = <53>;
			clocks = <&rcc 1 CLK_UART5>;
			status = "disabled";
		};

		i2c1: i2c@40005400 {
			compatible = "st,stm32f7-i2c";
			reg = <0x40005400 0x400>;
			interrupts = <31>,
				     <32>;
			resets = <&rcc STM32F7_APB1_RESET(I2C1)>;
			clocks = <&rcc 1 CLK_I2C1>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		i2c2: i2c@40005800 {
			compatible = "st,stm32f7-i2c";
			reg = <0x40005800 0x400>;
			interrupts = <33>,
				     <34>;
			resets = <&rcc STM32F7_APB1_RESET(I2C2)>;
			clocks = <&rcc 1 CLK_I2C2>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		i2c3: i2c@40005c00 {
			compatible = "st,stm32f7-i2c";
			reg = <0x40005c00 0x400>;
			interrupts = <72>,
				     <73>;
			resets = <&rcc STM32F7_APB1_RESET(I2C3)>;
			clocks = <&rcc 1 CLK_I2C3>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		i2c4: i2c@40006000 {
			compatible = "st,stm32f7-i2c";
			reg = <0x40006000 0x400>;
			interrupts = <95>,
				     <96>;
			resets = <&rcc STM32F7_APB1_RESET(I2C4)>;
			clocks = <&rcc 1 CLK_I2C4>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		can1: can@40006400 {
			compatible = "st,stm32f4-bxcan";
			reg = <0x40006400 0x200>;
			interrupts = <19>, <20>, <21>, <22>;
			interrupt-names = "tx", "rx0", "rx1", "sce";
			resets = <&rcc STM32F7_APB1_RESET(CAN1)>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(CAN1)>;
			st,can-primary;
			st,gcan = <&gcan1>;
			status = "disabled";
		};

		gcan1: gcan@40006600 {
			compatible = "st,stm32f4-gcan", "syscon";
			reg = <0x40006600 0x200>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(CAN1)>;
		};

		can2: can@40006800 {
			compatible = "st,stm32f4-bxcan";
			reg = <0x40006800 0x200>;
			interrupts = <63>, <64>, <65>, <66>;
			interrupt-names = "tx", "rx0", "rx1", "sce";
			resets = <&rcc STM32F7_APB1_RESET(CAN2)>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(CAN2)>;
			st,can-secondary;
			st,gcan = <&gcan1>;
			status = "disabled";
		};

		cec: cec@40006c00 {
			compatible = "st,stm32-cec";
			reg = <0x40006C00 0x400>;
			interrupts = <94>;
			clocks = <&rcc 0 STM32F7_APB1_CLOCK(CEC)>, <&rcc 1 CLK_HDMI_CEC>;
			clock-names = "cec", "hdmi-cec";
			status = "disabled";
		};

		usart7: serial@40007800 {
			compatible = "st,stm32f7-uart";
			reg = <0x40007800 0x400>;
			interrupts = <82>;
			clocks = <&rcc 1 CLK_UART7>;
			status = "disabled";
		};

		usart8: serial@40007c00 {
			compatible = "st,stm32f7-uart";
			reg = <0x40007c00 0x400>;
			interrupts = <83>;
			clocks = <&rcc 1 CLK_UART8>;
			status = "disabled";
		};

		timers1: timers@40010000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40010000 0x400>;
			clocks = <&rcc 0 STM32F7_APB2_CLOCK(TIM1)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@0 {
				compatible = "st,stm32-timer-trigger";
				reg = <0>;
				status = "disabled";
			};
		};

		timers8: timers@40010400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40010400 0x400>;
			clocks = <&rcc 0 STM32F7_APB2_CLOCK(TIM8)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@7 {
				compatible = "st,stm32-timer-trigger";
				reg = <7>;
				status = "disabled";
			};
		};

		usart1: serial@40011000 {
			compatible = "st,stm32f7-uart";
			reg = <0x40011000 0x400>;
			interrupts = <37>;
			clocks = <&rcc 1 CLK_USART1>;
			status = "disabled";
		};

		usart6: serial@40011400 {
			compatible = "st,stm32f7-uart";
			reg = <0x40011400 0x400>;
			interrupts = <71>;
			clocks = <&rcc 1 CLK_USART6>;
			status = "disabled";
		};

		sdio2: mmc@40011c00 {
			compatible = "arm,pl180", "arm,primecell";
			arm,primecell-periphid = <0x00880180>;
			reg = <0x40011c00 0x400>;
			clocks = <&rcc 0 STM32F7_APB2_CLOCK(SDMMC2)>;
			clock-names = "apb_pclk";
			interrupts = <103>;
			max-frequency = <48000000>;
			status = "disabled";
		};

		sdio1: mmc@40012c00 {
			compatible = "arm,pl180", "arm,primecell";
			arm,primecell-periphid = <0x00880180>;
			reg = <0x40012c00 0x400>;
			clocks = <&rcc 0 STM32F7_APB2_CLOCK(SDMMC1)>;
			clock-names = "apb_pclk";
			interrupts = <49>;
			max-frequency = <48000000>;
			status = "disabled";
		};

		syscfg: syscon@40013800 {
			compatible = "st,stm32-syscfg", "syscon";
			reg = <0x40013800 0x400>;
		};

		exti: interrupt-controller@40013c00 {
			compatible = "st,stm32-exti";
			interrupt-controller;
			#interrupt-cells = <2>;
			reg = <0x40013C00 0x400>;
			interrupts = <1>, <2>, <3>, <6>, <7>, <8>, <9>, <10>, <23>, <40>, <41>, <42>, <62>, <76>;
		};

		timers9: timers@40014000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "st,stm32-timers";
			reg = <0x40014000 0x400>;
			clocks = <&rcc 0 STM32F7_APB2_CLOCK(TIM9)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};

			timer@8 {
				compatible = "st,stm32-timer-trigger";
				reg = <8>;
				status = "disabled";
			};
		};

		timers10: timers@40014400 {
			compatible = "st,stm32-timers";
			reg = <0x40014400 0x400>;
			clocks = <&rcc 0 STM32F7_APB2_CLOCK(TIM10)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		timers11: timers@40014800 {
			compatible = "st,stm32-timers";
			reg = <0x40014800 0x400>;
			clocks = <&rcc 0 STM32F7_APB2_CLOCK(TIM11)>;
			clock-names = "int";
			status = "disabled";

			pwm {
				compatible = "st,stm32-pwm";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		ltdc: display-controller@40016800 {
			compatible = "st,stm32-ltdc";
			reg = <0x40016800 0x200>;
			interrupts = <88>, <89>;
			resets = <&rcc STM32F7_APB2_RESET(LTDC)>;
			clocks = <&rcc 1 CLK_LCD>;
			clock-names = "lcd";
			status = "disabled";
		};

		pwrcfg: power-config@40007000 {
			compatible = "st,stm32-power-config", "syscon";
			reg = <0x40007000 0x400>;
		};

		crc: crc@40023000 {
			compatible = "st,stm32f7-crc";
			reg = <0x40023000 0x400>;
			clocks = <&rcc 0 STM32F7_AHB1_CLOCK(CRC)>;
			status = "disabled";
		};

		rcc: rcc@40023800 {
			#reset-cells = <1>;
			#clock-cells = <2>;
			compatible = "st,stm32f746-rcc", "st,stm32-rcc";
			reg = <0x40023800 0x400>;
			clocks = <&clk_hse>, <&clk_i2s_ckin>;
			st,syscfg = <&pwrcfg>;
			assigned-clocks = <&rcc 1 CLK_HSE_RTC>;
			assigned-clock-rates = <1000000>;
		};

		dma1: dma-controller@40026000 {
			compatible = "st,stm32-dma";
			reg = <0x40026000 0x400>;
			interrupts = <11>,
				     <12>,
				     <13>,
				     <14>,
				     <15>,
				     <16>,
				     <17>,
				     <47>;
			clocks = <&rcc 0 STM32F7_AHB1_CLOCK(DMA1)>;
			#dma-cells = <4>;
			status = "disabled";
		};

		dma2: dma-controller@40026400 {
			compatible = "st,stm32-dma";
			reg = <0x40026400 0x400>;
			interrupts = <56>,
				     <57>,
				     <58>,
				     <59>,
				     <60>,
				     <68>,
				     <69>,
				     <70>;
			clocks = <&rcc 0 STM32F7_AHB1_CLOCK(DMA2)>;
			#dma-cells = <4>;
			st,mem2mem;
			status = "disabled";
		};

		usbotg_hs: usb@40040000 {
			compatible = "st,stm32f7-hsotg";
			reg = <0x40040000 0x40000>;
			interrupts = <77>;
			clocks = <&rcc 0 STM32F7_AHB1_CLOCK(OTGHS)>;
			clock-names = "otg";
			g-rx-fifo-size = <256>;
			g-np-tx-fifo-size = <32>;
			g-tx-fifo-size = <128 128 64 64 64 64 32 32>;
			status = "disabled";
		};

		usbotg_fs: usb@50000000 {
			compatible = "st,stm32f4x9-fsotg";
			reg = <0x50000000 0x40000>;
			interrupts = <67>;
			clocks = <&rcc 0 STM32F7_AHB2_CLOCK(OTGFS)>;
			clock-names = "otg";
			status = "disabled";
		};
	};
};

&systick {
	clocks = <&rcc 1 0>;
	status = "okay";
};
