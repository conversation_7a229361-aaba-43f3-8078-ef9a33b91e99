// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * DTS file for SPEAr300 Evaluation Baord
 *
 * Copyright 2012 V<PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
/include/ "spear300.dtsi"

/ {
	model = "ST SPEAr300 Evaluation Board";
	compatible = "st,spear300-evb", "st,spear300";
	#address-cells = <1>;
	#size-cells = <1>;

	memory {
		reg = <0 0x40000000>;
	};

	ahb {
		pinmux@99000000 {
			st,pinmux-mode = <2>;
			pinctrl-names = "default";
			pinctrl-0 = <&state_default>;

			state_default: pinmux {
				i2c0 {
					st,pins = "i2c0_grp";
					st,function = "i2c0";
				};
				ssp0 {
					st,pins = "ssp0_grp";
					st,function = "ssp0";
				};
				mii0 {
					st,pins = "mii0_grp";
					st,function = "mii0";
				};
				uart0 {
					st,pins = "uart0_grp";
					st,function = "uart0";
				};
				clcd {
					st,pins = "clcd_pfmode_grp";
					st,function = "clcd";
				};
				sdhci {
					st,pins = "sdhci_4bit_grp";
					st,function = "sdhci";
				};
				gpio1 {
					st,pins = "gpio1_4_to_7_grp",
						"gpio1_0_to_3_grp";
					st,function = "gpio1";
				};
			};
		};

		clcd@60000000 {
			status = "okay";
		};

		dma@fc400000 {
			status = "okay";
		};

		fsmc: flash@94000000 {
			status = "okay";
		};

		gmac: eth@e0800000 {
			status = "okay";
		};

		sdhci@70000000 {
			cd-gpios = <&gpio1 0 0>;
			status = "okay";
		};

		smi: flash@fc000000 {
			status = "okay";
			clock-rate = <50000000>;

			flash@f8000000 {
				#address-cells = <1>;
				#size-cells = <1>;
				reg = <0xf8000000 0x800000>;
				st,smi-fast-mode;

				partition@0 {
					label = "xloader";
					reg = <0x0 0x10000>;
				};
				partition@10000 {
					label = "u-boot";
					reg = <0x10000 0x50000>;
				};
				partition@60000 {
					label = "environment";
					reg = <0x60000 0x10000>;
				};
				partition@70000 {
					label = "dtb";
					reg = <0x70000 0x10000>;
				};
				partition@80000 {
					label = "linux";
					reg = <0x80000 0x310000>;
				};
				partition@390000 {
					label = "rootfs";
					reg = <0x390000 0x0>;
				};
			};
		};

		spi0: spi@d0100000 {
			status = "okay";
		};

		ehci@e1800000 {
			status = "okay";
		};

		ohci@e1900000 {
			status = "okay";
		};

		ohci@e2100000 {
			status = "okay";
		};

		apb {
			gpio0: gpio@fc980000 {
			       status = "okay";
			};

			gpio1: gpio@a9000000 {
			       status = "okay";
			};

			i2c0: i2c@d0180000 {
			       status = "okay";
			};

			kbd@a0000000 {
				linux,keymap = < 0x00000001
						 0x00010002
						 0x00020003
						 0x00030004
						 0x00040005
						 0x00050006
						 0x00060007
						 0x00070008
						 0x00080009
						 0x0100000a
						 0x0101000c
						 0x0102000d
						 0x0103000e
						 0x0104000f
						 0x01050010
						 0x01060011
						 0x01070012
						 0x01080013
						 0x02000014
						 0x02010015
						 0x02020016
						 0x02030017
						 0x02040018
						 0x02050019
						 0x0206001a
						 0x0207001b
						 0x0208001c
						 0x0300001d
						 0x0301001e
						 0x0302001f
						 0x03030020
						 0x03040021
						 0x03050022
						 0x03060023
						 0x03070024
						 0x03080025
						 0x04000026
						 0x04010027
						 0x04020028
						 0x04030029
						 0x0404002a
						 0x0405002b
						 0x0406002c
						 0x0407002d
						 0x0408002e
						 0x0500002f
						 0x05010030
						 0x05020031
						 0x05030032
						 0x05040033
						 0x05050034
						 0x05060035
						 0x05070036
						 0x05080037
						 0x06000038
						 0x06010039
						 0x0602003a
						 0x0603003b
						 0x0604003c
						 0x0605003d
						 0x0606003e
						 0x0607003f
						 0x06080040
						 0x07000041
						 0x07010042
						 0x07020043
						 0x07030044
						 0x07040045
						 0x07050046
						 0x07060047
						 0x07070048
						 0x07080049
						 0x0800004a
						 0x0801004b
						 0x0802004c
						 0x0803004d
						 0x0804004e
						 0x0805004f
						 0x08060050
						 0x08070051
						 0x08080052 >;
			       autorepeat;
			       st,mode = <0>;
			       status = "okay";
			};

			rtc@fc900000 {
			       status = "okay";
			};

			serial@d0000000 {
			       status = "okay";
				pinctrl-names = "default";
				pinctrl-0 = <>;
			};

			wdt@fc880000 {
			       status = "okay";
			};
		};
	};
};
