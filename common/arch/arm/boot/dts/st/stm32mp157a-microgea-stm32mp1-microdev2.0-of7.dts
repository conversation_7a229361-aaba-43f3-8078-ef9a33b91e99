// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (c) STMicroelectronics 2019 - All Rights Reserved
 * Copyright (c) 2020 Engicam srl
 * Copyright (c) 2020 Amarula Solutions(India)
 */

/dts-v1/;
#include "stm32mp157.dtsi"
#include "stm32mp157a-microgea-stm32mp1.dtsi"
#include "stm32mp15-pinctrl.dtsi"
#include "stm32mp15xxaa-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Engicam MicroGEA STM32MP1 MicroDev 2.0 7\" Open Frame";
	compatible = "engicam,microgea-stm32mp1-microdev2.0-of7",
		     "engicam,microgea-stm32mp1", "st,stm32mp157";

	aliases {
		serial0 = &uart4;
		serial1 = &uart8;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	backlight: backlight {
		compatible = "gpio-backlight";
		gpios = <&gpiod 13 GPIO_ACTIVE_HIGH>;
		default-on;
	};

	lcd_3v3: regulator-lcd-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "lcd_3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpiof 10 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		regulator-always-on;
		power-supply = <&panel_pwr>;
	};

	panel_pwr: regulator-panel-pwr {
		compatible = "regulator-fixed";
		regulator-name = "panel_pwr";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpiob 10 GPIO_ACTIVE_HIGH>;
		regulator-always-on;
	};

	panel {
		compatible = "auo,b101aw03";
		backlight = <&backlight>;
		enable-gpios = <&gpiof 2 GPIO_ACTIVE_HIGH>;
		power-supply = <&lcd_3v3>;

		port {
			panel_in: endpoint {
				remote-endpoint = <&ltdc_ep0_out>;
			};
		};
	};
};

&i2c2 {
	i2c-scl-falling-time-ns = <20>;
	i2c-scl-rising-time-ns = <185>;
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&i2c2_pins_a>;
	pinctrl-1 = <&i2c2_sleep_pins_a>;
	status = "okay";
};

&ltdc {
	pinctrl-names = "default";
	pinctrl-0 = <&ltdc_pins>;
	status = "okay";

	port {
		ltdc_ep0_out: endpoint {
			remote-endpoint = <&panel_in>;
		};
	};
};

&pinctrl {
	ltdc_pins: ltdc-0 {
		pins {
			pinmux = <STM32_PINMUX('G', 10, AF14)>,	/* LTDC_B2 */
				 <STM32_PINMUX('H', 12, AF14)>,	/* LTDC_R6 */
				 <STM32_PINMUX('H', 11, AF14)>,	/* LTDC_R5 */
				 <STM32_PINMUX('D', 10, AF14)>,	/* LTDC_B3 */
				 <STM32_PINMUX('D', 9, AF14)>,	/* LTDC_B0 */
				 <STM32_PINMUX('E', 5, AF14)>,	/* LTDC_G0 */
				 <STM32_PINMUX('E', 6, AF14)>,	/* LTDC_G1 */
				 <STM32_PINMUX('E', 13, AF14)>,	/* LTDC_DE */
				 <STM32_PINMUX('E', 15, AF14)>,	/* LTDC_R7 */
				 <STM32_PINMUX('G', 7, AF14)>,	/* LTDC_CLK */
				 <STM32_PINMUX('G', 12, AF14)>,	/* LTDC_B1 */
				 <STM32_PINMUX('H', 2, AF14)>,	/* LTDC_R0 */
				 <STM32_PINMUX('H', 3, AF14)>,	/* LTDC_R1 */
				 <STM32_PINMUX('H', 8, AF14)>,	/* LTDC_R2 */
				 <STM32_PINMUX('H', 9, AF14)>,	/* LTDC_R3 */
				 <STM32_PINMUX('H', 10, AF14)>,	/* LTDC_R4 */
				 <STM32_PINMUX('H', 13, AF14)>,	/* LTDC_G2 */
				 <STM32_PINMUX('H', 14, AF14)>,	/* LTDC_G3 */
				 <STM32_PINMUX('H', 15, AF14)>,	/* LTDC_G4 */
				 <STM32_PINMUX('I', 0, AF14)>,	/* LTDC_G5 */
				 <STM32_PINMUX('I', 1, AF14)>,	/* LTDC_G6 */
				 <STM32_PINMUX('I', 2, AF14)>,	/* LTDC_G7 */
				 <STM32_PINMUX('I', 4, AF14)>,	/* LTDC_B4 */
				 <STM32_PINMUX('I', 5, AF14)>,	/* LTDC_B5 */
				 <STM32_PINMUX('B', 8, AF14)>,	/* LTDC_B6 */
				 <STM32_PINMUX('I', 7, AF14)>,	/* LTDC_B7 */
				 <STM32_PINMUX('I', 9, AF14)>,	/* LTDC_VSYNC */
				 <STM32_PINMUX('I', 10, AF14)>;	/* LTDC_HSYNC */
			bias-disable;
			drive-push-pull;
			slew-rate = <3>;
		};
	};
};

&sdmmc1 {
	bus-width = <4>;
	disable-wp;
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a>;
	st,neg-edge;
	vmmc-supply = <&vdd>;
	status = "okay";
};

&uart4 {
	pinctrl-names = "default", "sleep", "idle";
	pinctrl-0 = <&uart4_pins_a>;
	pinctrl-1 = <&uart4_sleep_pins_a>;
	pinctrl-2 = <&uart4_idle_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

/* J31: RS323 */
&uart8 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart8_pins_a>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};
