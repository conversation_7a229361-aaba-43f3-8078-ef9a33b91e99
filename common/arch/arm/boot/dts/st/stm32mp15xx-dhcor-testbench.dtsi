// SPDX-License-Identifier: (GPL-2.0 OR BSD-3-Clause)
/*
 * Copyright (C) 2022 <PERSON><PERSON> <<EMAIL>>
 */

/ {
	aliases {
		ethernet0 = &ethernet0;
		mmc0 = &sdmmc1;
		mmc1 = &sdmmc2;
		serial0 = &uart4;
		serial1 = &uart7;
		spi0 = &qspi;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	sd_switch: regulator-sd_switch {
		compatible = "regulator-gpio";
		regulator-name = "sd_switch";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <2900000>;
		regulator-type = "voltage";
		regulator-always-on;

		gpios = <&gpioi 5 GPIO_ACTIVE_HIGH>;
		gpios-states = <0>;
		states = <1800000 0x1>,
			 <2900000 0x0>;
	};
};

&adc {
	pinctrl-names = "default";
	pinctrl-0 = <&adc12_ain_pins_b>;
	vdd-supply = <&vdd>;
	vdda-supply = <&vdda>;
	vref-supply = <&vdda>;
	status = "okay";

	adc1: adc@0 {
		status = "okay";
		channel@0 {
			reg = <0>;
			st,min-sample-time-ns = <5000>;
		};
		channel@1 {
			reg = <1>;
			st,min-sample-time-ns = <5000>;
		};
		channel@6 {
			reg = <6>;
			st,min-sample-time-ns = <5000>;
		};
	};

	adc2: adc@100 {
		status = "okay";
		channel@0 {
			reg = <0>;
			st,min-sample-time-ns = <5000>;
		};
		channel@1 {
			reg = <1>;
			st,min-sample-time-ns = <5000>;
		};
		channel@2 {
			reg = <2>;
			st,min-sample-time-ns = <5000>;
		};
	};
};

&ethernet0 {
	status = "okay";
	pinctrl-0 = <&ethernet0_rgmii_pins_c>;
	pinctrl-1 = <&ethernet0_rgmii_sleep_pins_c>;
	pinctrl-names = "default", "sleep";
	phy-mode = "rgmii";
	max-speed = <1000>;
	phy-handle = <&phy0>;

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "snps,dwmac-mdio";
		reset-gpios = <&gpioz 2 GPIO_ACTIVE_LOW>;
		reset-delay-us = <1000>;
		reset-post-delay-us = <1000>;

		phy0: ethernet-phy@7 {
			reg = <7>;

			rxc-skew-ps = <1500>;
			rxdv-skew-ps = <540>;
			rxd0-skew-ps = <420>;
			rxd1-skew-ps = <420>;
			rxd2-skew-ps = <420>;
			rxd3-skew-ps = <420>;

			txc-skew-ps = <1440>;
			txen-skew-ps = <540>;
			txd0-skew-ps = <420>;
			txd1-skew-ps = <420>;
			txd2-skew-ps = <420>;
			txd3-skew-ps = <420>;
		};
	};
};

&i2c4 {
	dh_mac_eeprom: eeprom@53 {
		compatible = "atmel,24c02";
		reg = <0x53>;
		pagesize = <16>;
	};
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a &sdmmc1_dir_pins_b>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a &sdmmc1_dir_pins_b>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a &sdmmc1_dir_sleep_pins_b>;
	cd-gpios = <&gpioi 8 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
	disable-wp;
	st,sig-dir;
	st,neg-edge;
	st,use-ckin;
	bus-width = <4>;
	vmmc-supply = <&vdd_sd>;
	vqmmc-supply = <&sd_switch>;
	status = "okay";
};

&sdmmc2 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc2_b4_pins_a &sdmmc2_d47_pins_c>;
	pinctrl-1 = <&sdmmc2_b4_od_pins_a &sdmmc2_d47_pins_c>;
	pinctrl-2 = <&sdmmc2_b4_sleep_pins_a &sdmmc2_d47_sleep_pins_c>;
	bus-width = <8>;
	mmc-ddr-1_8v;
	no-sd;
	no-sdio;
	non-removable;
	st,neg-edge;
	vmmc-supply = <&v3v3>;
	vqmmc-supply = <&v3v3>;
	status = "okay";
};

&uart4 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pins_b>;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&uart7 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart7_pins_a>;
	uart-has-rtscts;
	/delete-property/dmas;
	/delete-property/dma-names;
	status = "okay";
};

&usbh_ehci {
	phys = <&usbphyc_port0>;
	status = "okay";
};

&usbh_ohci {
	phys = <&usbphyc_port0>;
	status = "okay";
};

&usbotg_hs {
	pinctrl-0 = <&usbotg_hs_pins_a>;
	pinctrl-names = "default";
	phy-names = "usb2-phy";
	phys = <&usbphyc_port1 0>;
	status = "okay";
	vbus-supply = <&vbus_otg>;
};

&usbphyc {
	status = "okay";
};

&usbphyc_port0 {
	phy-supply = <&vdd_usb>;
};

&usbphyc_port1 {
	phy-supply = <&vdd_usb>;
};
