// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) STMicroelectronics 2019 - All Rights Reserved
 * Author: <PERSON> <<EMAIL>> for STMicroelectronics.
 */

&pinctrl {
	st,package = <STM32MP_PKG_AD>;

	gpioa: gpio@50002000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 0 16>;
	};

	gpiob: gpio@50003000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 16 16>;
	};

	gpioc: gpio@50004000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 32 16>;
	};

	gpiod: gpio@50005000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 48 16>;
	};

	gpioe: gpio@50006000 {
		status = "okay";
		ngpios = <16>;
		gpio-ranges = <&pinctrl 0 64 16>;
	};

	gpiof: gpio@50007000 {
		status = "okay";
		ngpios = <6>;
		gpio-ranges = <&pinctrl 6 86 6>;
	};

	gpiog: gpio@50008000 {
		status = "okay";
		ngpios = <10>;
		gpio-ranges = <&pinctrl 6 102 10>;
	};

	gpioh: gpio@50009000 {
		status = "okay";
		ngpios = <2>;
		gpio-ranges = <&pinctrl 0 112 2>;
	};
};
