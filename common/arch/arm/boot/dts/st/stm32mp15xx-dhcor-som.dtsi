// SPDX-License-Identifier: (GPL-2.0 OR BSD-3-Clause)
/*
 * Copyright (C) Linaro Ltd 2019 - All Rights Reserved
 * Author: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2020 Marek Vasut <<EMAIL>>
 */

#include "stm32mp15-pinctrl.dtsi"
#include "stm32mp15xxac-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/mfd/st,stpmic1.h>

/ {
	aliases {
		spi0 = &qspi;
	};

	memory@c0000000 {
		device_type = "memory";
		reg = <0xc0000000 0x40000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		mcuram2: mcuram2@10000000 {
			compatible = "shared-dma-pool";
			reg = <0x10000000 0x40000>;
			no-map;
		};

		vdev0vring0: vdev0vring0@10040000 {
			compatible = "shared-dma-pool";
			reg = <0x10040000 0x1000>;
			no-map;
		};

		vdev0vring1: vdev0vring1@10041000 {
			compatible = "shared-dma-pool";
			reg = <0x10041000 0x1000>;
			no-map;
		};

		vdev0buffer: vdev0buffer@10042000 {
			compatible = "shared-dma-pool";
			reg = <0x10042000 0x4000>;
			no-map;
		};

		mcuram: mcuram@30000000 {
			compatible = "shared-dma-pool";
			reg = <0x30000000 0x40000>;
			no-map;
		};

		retram: retram@38000000 {
			compatible = "shared-dma-pool";
			reg = <0x38000000 0x10000>;
			no-map;
		};
	};
};

&crc1 {
	status = "okay";
};

&dts {
	status = "okay";
};

&i2c4 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4_pins_a>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;

	pmic: stpmic@33 {
		compatible = "st,stpmic1";
		reg = <0x33>;
		interrupts-extended = <&gpioa 0 IRQ_TYPE_EDGE_FALLING>;
		interrupt-controller;
		#interrupt-cells = <2>;
		status = "okay";

		regulators {
			compatible = "st,stpmic1-regulators";

			ldo1-supply = <&v3v3>;
			ldo2-supply = <&v3v3>;
			ldo3-supply = <&vdd_ddr>;
			ldo5-supply = <&v3v3>;
			ldo6-supply = <&v3v3>;
			pwr_sw1-supply = <&bst_out>;
			pwr_sw2-supply = <&bst_out>;

			vddcore: buck1 {
				regulator-name = "vddcore";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd_ddr: buck2 {
				regulator-name = "vdd_ddr";
				regulator-min-microvolt = <1350000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd: buck3 {
				regulator-name = "vdd";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			v3v3: buck4 {
				regulator-name = "v3v3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-over-current-protection;
				regulator-initial-mode = <0>;
			};

			vdda: ldo1 {
				regulator-name = "vdda";
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				interrupts = <IT_CURLIM_LDO1 0>;
			};

			v2v8: ldo2 {
				regulator-name = "v2v8";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				interrupts = <IT_CURLIM_LDO2 0>;
			};

			vtt_ddr: ldo3 {
				regulator-name = "vtt_ddr";
				regulator-min-microvolt = <500000>;
				regulator-max-microvolt = <750000>;
				regulator-always-on;
				regulator-over-current-protection;
			};

			vdd_usb: ldo4 {
				regulator-name = "vdd_usb";
				interrupts = <IT_CURLIM_LDO4 0>;
			};

			vdd_sd: ldo5 {
				regulator-name = "vdd_sd";
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				interrupts = <IT_CURLIM_LDO5 0>;
				regulator-boot-on;
			};

			v1v8: ldo6 {
				regulator-name = "v1v8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				interrupts = <IT_CURLIM_LDO6 0>;
				regulator-enable-ramp-delay = <300000>;
			};

			vref_ddr: vref_ddr {
				regulator-name = "vref_ddr";
				regulator-always-on;
			};

			bst_out: boost {
				regulator-name = "bst_out";
				interrupts = <IT_OCP_BOOST 0>;
			};

			vbus_otg: pwr_sw1 {
				regulator-name = "vbus_otg";
				interrupts = <IT_OCP_OTG 0>;
				regulator-active-discharge = <1>;
			};

			vbus_sw: pwr_sw2 {
				regulator-name = "vbus_sw";
				interrupts = <IT_OCP_SWOUT 0>;
				regulator-active-discharge = <1>;
			};
		};

		onkey {
			compatible = "st,stpmic1-onkey";
			interrupts = <IT_PONKEY_F 0>, <IT_PONKEY_R 1>;
			interrupt-names = "onkey-falling", "onkey-rising";
			status = "okay";
		};

		watchdog {
			compatible = "st,stpmic1-wdt";
			status = "disabled";
		};
	};
};

&ipcc {
	status = "okay";
};

&iwdg2 {
	timeout-sec = <32>;
	status = "okay";
};

&m4_rproc {
	memory-region = <&retram>, <&mcuram>, <&mcuram2>, <&vdev0vring0>,
			<&vdev0vring1>, <&vdev0buffer>;
	mboxes = <&ipcc 0>, <&ipcc 1>, <&ipcc 2>, <&ipcc 3>;
	mbox-names = "vq0", "vq1", "shutdown", "detach";
	interrupt-parent = <&exti>;
	interrupts = <68 1>;
	status = "okay";
};

&pwr_regulators {
	vdd-supply = <&vdd>;
	vdd_3v3_usbfs-supply = <&vdd_usb>;
};

&qspi {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&qspi_clk_pins_a
		     &qspi_bk1_pins_a
		     &qspi_cs1_pins_a>;
	pinctrl-1 = <&qspi_clk_sleep_pins_a
		     &qspi_bk1_sleep_pins_a
		     &qspi_cs1_sleep_pins_a>;
	reg = <0x58003000 0x1000>, <0x70000000 0x200000>;
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	flash0: flash@0 {
		compatible = "jedec,spi-nor";
		reg = <0>;
		spi-rx-bus-width = <4>;
		spi-max-frequency = <50000000>;
		#address-cells = <1>;
		#size-cells = <1>;
	};
};

&rng1 {
	status = "okay";
};

&rtc {
	status = "okay";
};
