// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * DTS file for all SPEAr1340 SoCs
 *
 * Copyright 2012 V<PERSON><PERSON> <<EMAIL>>
 */

/include/ "spear13xx.dtsi"

/ {
	compatible = "st,spear1340";

	ahb {

		spics: spics@e0700000 {
			compatible = "st,spear-spics-gpio";
			reg = <0xe0700000 0x1000>;
			st-spics,peripcfg-reg = <0x42c>;
			st-spics,sw-enable-bit = <21>;
			st-spics,cs-value-bit = <20>;
			st-spics,cs-enable-mask = <3>;
			st-spics,cs-enable-shift = <18>;
			gpio-controller;
			#gpio-cells = <2>;
			status = "disabled";
		};

		miphy0: miphy@eb800000 {
			compatible = "st,spear1340-miphy";
			reg = <0xeb800000 0x4000>;
			misc = <&misc>;
			#phy-cells = <1>;
			status = "disabled";
		};

		ahci0: ahci@b1000000 {
			compatible = "snps,spear-ahci";
			reg = <0xb1000000 0x10000>;
			interrupts = <0 72 0x4>;
			phys = <&miphy0 0>;
			phy-names = "sata-phy";
			status = "disabled";
		};

		pcie0: pcie@b1000000 {
			compatible = "st,spear1340-pcie", "snps,dw-pcie";
			reg = <0xb1000000 0x4000>, <0x80000000 0x20000>;
			reg-names = "dbi", "config";
			interrupts = <0 68 0x4>;
			num-lanes = <1>;
			phys = <&miphy0 1>;
			phy-names = "pcie-phy";
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			ranges = <0x81000000 0 0	 0x80020000 0 0x00010000   /* downstream I/O */
				0x82000000 0 0x80030000 0xc0030000 0 0x0ffd0000>; /* non-prefetchable memory */
			bus-range = <0x00 0xff>;
			status = "disabled";
		};

		i2s-play@b2400000 {
			compatible = "snps,designware-i2s";
			reg = <0xb2400000 0x10000>;
			interrupt-names = "play_irq";
			interrupts = <0 98 0x4>,
				     <0 99 0x4>;
			play;
			channel = <8>;
			status = "disabled";
		};

		i2s-rec@b2000000 {
			compatible = "snps,designware-i2s";
			reg = <0xb2000000 0x10000>;
			interrupt-names = "record_irq";
			interrupts = <0 100 0x4>,
				     <0 101 0x4>;
			record;
			channel = <8>;
			status = "disabled";
		};

		pinmux: pinmux@e0700000 {
			compatible = "st,spear1340-pinmux";
			reg = <0xe0700000 0x1000>;
			#gpio-range-cells = <3>;
		};

		pwm: pwm@e0180000 {
			compatible = "st,spear13xx-pwm";
			reg = <0xe0180000 0x1000>;
			#pwm-cells = <2>;
			status = "disabled";
		};

		spdif-in@d0100000 {
			compatible = "st,spdif-in";
			reg = < 0xd0100000 0x20000
				0xd0110000 0x10000 >;
			interrupts = <0 84 0x4>;
			status = "disabled";
		};

		spdif-out@d0000000 {
			compatible = "st,spdif-out";
			reg = <0xd0000000 0x20000>;
			interrupts = <0 85 0x4>;
			status = "disabled";
		};

		spi1: spi@5d400000 {
			compatible = "arm,pl022", "arm,primecell";
			reg = <0x5d400000 0x1000>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <0 99 0x4>;
			status = "disabled";
		};

		apb {
			i2c1: i2c@b4000000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,designware-i2c";
				reg = <0xb4000000 0x1000>;
				interrupts = <0 104 0x4>;
				write-16bit;
				status = "disabled";
			};

			serial@b4100000 {
				compatible = "arm,pl011", "arm,primecell";
				reg = <0xb4100000 0x1000>;
				interrupts = <0 105 0x4>;
				status = "disabled";
				dmas = <&dwdma0 13 0 1>,
					<&dwdma0 12 1 0>;
				dma-names = "rx", "tx";
			};

			thermal@e07008c4 {
				st,thermal-flags = <0x2a00>;
			};

			gpiopinctrl: gpio@e2800000 {
				compatible = "st,spear-plgpio";
				reg = <0xe2800000 0x1000>;
				interrupts = <0 107 0x4>;
				#interrupt-cells = <1>;
				interrupt-controller;
				gpio-controller;
				#gpio-cells = <2>;
				gpio-ranges = <&pinmux 0 0 252>;
				status = "disabled";

				st-plgpio,ngpio = <250>;
				st-plgpio,wdata-reg = <0x40>;
				st-plgpio,dir-reg = <0x00>;
				st-plgpio,ie-reg = <0x80>;
				st-plgpio,rdata-reg = <0x20>;
				st-plgpio,mis-reg = <0xa0>;
				st-plgpio,eit-reg = <0x60>;
			};
		};
	};
};
