// SPDX-License-Identifier: (GPL-2.0 OR MIT)
//
// Copyright (c) 2021 emtrion GmbH
// Author: <PERSON><PERSON><PERSON> <<EMAIL>>.
//

/dts-v1/;

#include "stm32mp157c-emstamp-argon.dtsi"

/ {
	model = "emtrion STM32MP157C emSBC-Argon Developer Board";
	compatible = "emtrion,stm32mp157c-emsbc-argon", "emtrion,stm32mp157c-emstamp-argon",
			"st,stm32mp157";

	led: gpio_leds {
		compatible = "gpio-leds";
		led-2 {
			label = "red";
			gpios = <&gpiof 12 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "none";
			default-state = "off";
		};
		led-3 {
			label = "green";
			gpios = <&gpioe 7 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "none";
			default-state = "off";
		};
	};
};

&dac {
	status = "okay";
};

&sdmmc1 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc1_b4_pins_a>;
	pinctrl-1 = <&sdmmc1_b4_od_pins_a>;
	pinctrl-2 = <&sdmmc1_b4_sleep_pins_a>;
	cd-gpios = <&gpiob 7 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
	disable-wp;
	st,neg-edge;
	bus-width = <4>;
	vmmc-supply = <&vdd_sd>;
	status = "okay";
};

&spi1 {
	status = "okay";
};

