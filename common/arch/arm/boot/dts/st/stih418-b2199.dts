// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2015 STMicroelectronics (R&D) Limited.
 * Author: <PERSON><PERSON> <<EMAIL>>
 */
/dts-v1/;
#include "stih418.dtsi"
#include <dt-bindings/gpio/gpio.h>
/ {
	model = "STiH418 B2199";
	compatible = "st,stih418-b2199", "st,stih418";

	chosen {
		stdout-path = &sbc_serial0;
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0xc0000000>;
	};

	aliases {
		serial0 = &sbc_serial0;
		ethernet0 = &ethernet0;
	};

	leds {
		compatible = "gpio-leds";
		led-red {
			label = "Front Panel LED";
			gpios = <&pio4 1 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
		};
		led-green {
			gpios = <&pio1 3 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};
	};

	miphy28lp_phy: miphy28lp {

		phy_port0: port@9b22000 {
			st,osc-rdy;
		};

		phy_port1: port@9b2a000 {
			st,osc-force-ext;
		};
	};

	soc {
		sbc_serial0: serial@9530000 {
			status = "okay";
		};

		i2c@9842000 {
			status = "okay";
		};

		i2c@9843000 {
			status = "okay";
		};

		i2c@9844000 {
			status = "okay";
		};

		i2c@9845000 {
			status = "okay";
		};

		i2c@9540000 {
			status = "okay";
		};

		/* SSC11 to HDMI */
		i2c@9541000 {
			status = "okay";
			/* HDMI V1.3a supports Standard mode only */
			clock-frequency = <100000>;
			st,i2c-min-scl-pulse-width-us = <0>;
			st,i2c-min-sda-pulse-width-us = <5>;
		};

		mmc1: sdhci@9080000 {
			status = "okay";
		};

		mmc0: sdhci@9060000 {
			status = "okay";
			max-frequency = <200000000>;
			sd-uhs-sdr50;
			sd-uhs-sdr104;
			sd-uhs-ddr50;
			non-removable;
		};

		st_dwc3: dwc3@8f94000 {
			status = "okay";
		};

		ethernet0: dwmac@9630000 {
			st,tx-retime-src = "clkgen";
			status = "okay";
			phy-mode = "rgmii";
			fixed-link = <0 1 1000 0 0>;
		};
	};
};
