// SPDX-License-Identifier: GPL-2.0+ OR BSD-3-Clause
/*
 * Copyright (C) 2020 <PERSON><PERSON> <<EMAIL>>
 *
 * DHCOM STM32MP1 variant:
 * DHCM-STM32MP153C-C065-R102-F0819-SPI-E2-CAN2-RTC-I-01D2
 * DHCOM PCB number: 587-200 or newer
 * DRC02 PCB number: 568-100 or newer
 */
/dts-v1/;

#include "stm32mp153.dtsi"
#include "stm32mp15xc.dtsi"
#include "stm32mp15xx-dhcom-som.dtsi"
#include "stm32mp15xx-dhcom-drc02.dtsi"

/ {
	model = "DH electronics STM32MP153C DHCOM DRC02";
	compatible = "dh,stm32mp153c-dhcom-drc02", "dh,stm32mp153c-dhcom-som",
		     "st,stm32mp153";
};

&cryp1 {
	status = "okay";
};

&m_can1 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&m_can1_pins_a>;
	pinctrl-1 = <&m_can1_sleep_pins_a>;
	status = "okay";
};

&m_can2 {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&m_can2_pins_a>;
	pinctrl-1 = <&m_can2_sleep_pins_a>;
	status = "okay";
};
