// SPDX-License-Identifier: (GPL-2.0+ OR BSD-3-Clause)
/*
 * Copyright (C) 2020 <PERSON><PERSON> <marcin.s<PERSON><PERSON><PERSON>@gmail.com>.
 */

/dts-v1/;

#include "stm32mp157.dtsi"
#include "stm32mp15xc.dtsi"
#include "stm32mp15-pinctrl.dtsi"
#include "stm32mp15xxac-pinctrl.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/leds/common.h>
#include <dt-bindings/mfd/st,stpmic1.h>

/ {
	model = "Seeed Studio Odyssey-STM32MP157C SOM";
	compatible = "seeed,stm32mp157c-odyssey-som", "st,stm32mp157";

	memory@c0000000 {
		device_type = "memory";
		reg = <0xc0000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		mcuram2: mcuram2@10000000 {
			compatible = "shared-dma-pool";
			reg = <0x10000000 0x40000>;
			no-map;
		};

		vdev0vring0: vdev0vring0@10040000 {
			compatible = "shared-dma-pool";
			reg = <0x10040000 0x1000>;
			no-map;
		};

		vdev0vring1: vdev0vring1@10041000 {
			compatible = "shared-dma-pool";
			reg = <0x10041000 0x1000>;
			no-map;
		};

		vdev0buffer: vdev0buffer@10042000 {
			compatible = "shared-dma-pool";
			reg = <0x10042000 0x4000>;
			no-map;
		};

		mcuram: mcuram@30000000 {
			compatible = "shared-dma-pool";
			reg = <0x30000000 0x40000>;
			no-map;
		};

		retram: retram@38000000 {
			compatible = "shared-dma-pool";
			reg = <0x38000000 0x10000>;
			no-map;
		};
	};

	led {
		compatible = "gpio-leds";
		led-blue {
			color = <LED_COLOR_ID_BLUE>;
			function = LED_FUNCTION_HEARTBEAT;
			gpios = <&gpiog 3 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
		};
	};
};

&i2c2 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_pins_a>;
	i2c-scl-rising-time-ns = <185>;
	i2c-scl-falling-time-ns = <20>;
	status = "okay";
	/* spare dmas for other usage */
	/delete-property/dmas;
	/delete-property/dma-names;

	pmic: stpmic@33 {
		compatible = "st,stpmic1";
		reg = <0x33>;
		interrupts-extended = <&gpioa 0 IRQ_TYPE_EDGE_FALLING>;
		interrupt-controller;
		#interrupt-cells = <2>;

		regulators {
			compatible = "st,stpmic1-regulators";
			ldo1-supply = <&v3v3>;
			ldo3-supply = <&vdd_ddr>;
			ldo6-supply = <&v3v3>;
			pwr_sw1-supply = <&bst_out>;
			pwr_sw2-supply = <&bst_out>;

			vddcore: buck1 {
				regulator-name = "vddcore";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd_ddr: buck2 {
				regulator-name = "vdd_ddr";
				regulator-min-microvolt = <1350000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			vdd: buck3 {
				regulator-name = "vdd";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				st,mask-reset;
				regulator-initial-mode = <0>;
				regulator-over-current-protection;
			};

			v3v3: buck4 {
				regulator-name = "v3v3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-over-current-protection;
				regulator-initial-mode = <0>;
			};

			v1v8_audio: ldo1 {
				regulator-name = "v1v8_audio";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				interrupts = <IT_CURLIM_LDO1 0>;
			};

			v3v3_hdmi: ldo2 {
				regulator-name = "v3v3_hdmi";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				interrupts = <IT_CURLIM_LDO2 0>;
			};

			vtt_ddr: ldo3 {
				regulator-name = "vtt_ddr";
				regulator-min-microvolt = <500000>;
				regulator-max-microvolt = <750000>;
				regulator-always-on;
				regulator-over-current-protection;
			};

			vdd_usb: ldo4 {
				regulator-name = "vdd_usb";
				interrupts = <IT_CURLIM_LDO4 0>;
			};

			vdda: ldo5 {
				regulator-name = "vdda";
				regulator-min-microvolt = <2900000>;
				regulator-max-microvolt = <2900000>;
				interrupts = <IT_CURLIM_LDO5 0>;
				regulator-boot-on;
			};

			v1v2_hdmi: ldo6 {
				regulator-name = "v1v2_hdmi";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				interrupts = <IT_CURLIM_LDO6 0>;
			};

			vref_ddr: vref_ddr {
				regulator-name = "vref_ddr";
				regulator-always-on;
			};

			 bst_out: boost {
				regulator-name = "bst_out";
				interrupts = <IT_OCP_BOOST 0>;
			 };

			vbus_otg: pwr_sw1 {
				regulator-name = "vbus_otg";
				interrupts = <IT_OCP_OTG 0>;
			 };

			 vbus_sw: pwr_sw2 {
				regulator-name = "vbus_sw";
				interrupts = <IT_OCP_SWOUT 0>;
				regulator-active-discharge = <1>;
			 };
		};

		onkey {
			compatible = "st,stpmic1-onkey";
			interrupts = <IT_PONKEY_F 0>, <IT_PONKEY_R 0>;
			interrupt-names = "onkey-falling", "onkey-rising";
			power-off-time-sec = <10>;
		};

		watchdog {
			compatible = "st,stpmic1-wdt";
			status = "disabled";
		};
	};
};

&ipcc {
	status = "okay";
};

&iwdg2 {
	timeout-sec = <32>;
	status = "okay";
};

&m4_rproc {
	memory-region = <&retram>, <&mcuram>, <&mcuram2>, <&vdev0vring0>,
			<&vdev0vring1>, <&vdev0buffer>;
	mboxes = <&ipcc 0>, <&ipcc 1>, <&ipcc 2>, <&ipcc 3>;
	mbox-names = "vq0", "vq1", "shutdown", "detach";
	interrupt-parent = <&exti>;
	interrupts = <68 1>;
	status = "okay";
};

&rng1 {
	status = "okay";
};

&rtc {
	status = "okay";
};

&sdmmc2 {
	pinctrl-names = "default", "opendrain", "sleep";
	pinctrl-0 = <&sdmmc2_b4_pins_a &sdmmc2_d47_pins_d>;
	pinctrl-1 = <&sdmmc2_b4_od_pins_a &sdmmc2_d47_pins_d>;
	pinctrl-2 = <&sdmmc2_b4_sleep_pins_a &sdmmc2_d47_sleep_pins_d>;
	non-removable;
	no-sd;
	no-sdio;
	st,neg-edge;
	bus-width = <8>;
	vmmc-supply = <&v3v3>;
	vqmmc-supply = <&vdd>;
	mmc-ddr-3_3v;
	status = "okay";
};

