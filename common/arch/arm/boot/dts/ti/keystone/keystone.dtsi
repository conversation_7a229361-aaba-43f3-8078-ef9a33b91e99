// SPDX-License-Identifier: GPL-2.0
/*
 * Copyright (C) 2013-2017 Texas Instruments Incorporated - http://www.ti.com/
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	compatible = "ti,keystone";
	model = "Texas Instruments Keystone 2 SoC";
	#address-cells = <2>;
	#size-cells = <2>;
	interrupt-parent = <&gic>;

	aliases {
		serial0 = &uart0;
		spi0 = &spi0;
		spi1 = &spi1;
		spi2 = &spi2;
	};

	chosen { };

	memory: memory@80000000 {
		device_type = "memory";
		reg = <0x00000000 0x80000000 0x00000000 0x40000000>;
	};

	gic: interrupt-controller@2561000 {
		compatible = "arm,gic-400", "arm,cortex-a15-gic";
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0x0 0x02561000 0x0 0x1000>,
		      <0x0 0x02562000 0x0 0x2000>,
		      <0x0 0x02564000 0x0 0x2000>,
		      <0x0 0x02566000 0x0 0x2000>;
		interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(4) |
				IRQ_TYPE_LEVEL_HIGH)>;
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts =
			<GIC_PPI 13
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			<GIC_PPI 14
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			<GIC_PPI 11
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			<GIC_PPI 10
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
	};

	pmu {
		compatible = "arm,cortex-a15-pmu";
		interrupts = <GIC_SPI 20 IRQ_TYPE_EDGE_RISING>,
			     <GIC_SPI 21 IRQ_TYPE_EDGE_RISING>,
			     <GIC_SPI 22 IRQ_TYPE_EDGE_RISING>,
			     <GIC_SPI 23 IRQ_TYPE_EDGE_RISING>;
	};

	psci {
		compatible = "arm,psci";
		method = "smc";
		cpu_suspend = <0x84000001>;
		cpu_off = <0x84000002>;
		cpu_on = <0x84000003>;
	};

	soc0: soc@0 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		interrupt-parent = <&gic>;
		ranges = <0x0 0x0 0x0 0xc0000000>;
		dma-ranges = <0x80000000 0x8 0x00000000 0x80000000>;

		pllctrl: pll-controller@2310000 {
			compatible = "ti,keystone-pllctrl", "syscon";
			reg = <0x02310000 0x200>;
		};

		psc: power-sleep-controller@2350000 {
			compatible = "syscon", "simple-mfd";
			reg = <0x02350000 0x1000>;
		};

		devctrl: device-state-control@2620000 {
			compatible = "ti,keystone-devctrl", "syscon", "simple-mfd";
			reg = <0x02620000 0x1000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x02620000 0x1000>;

			kirq0: keystone_irq@2a0 {
				compatible = "ti,keystone-irq";
				reg = <0x2a0 0x4>;
				interrupts = <GIC_SPI 4 IRQ_TYPE_EDGE_RISING>;
				interrupt-controller;
				#interrupt-cells = <1>;
				ti,syscon-dev = <&devctrl 0x2a0>;
			};

			rstctrl: reset-controller@328 {
				compatible = "ti,keystone-reset";
				reg = <0x328 0x10>;
				ti,syscon-pll = <&pllctrl 0xe4>;
				ti,syscon-dev = <&devctrl 0x328>;
				ti,wdt-list = <0>;
			};
		};

		/include/ "keystone-clocks.dtsi"

		uart0: serial@2530c00 {
			compatible = "ti,da830-uart", "ns16550a";
			current-speed = <115200>;
			reg-shift = <2>;
			reg-io-width = <4>;
			reg = <0x02530c00 0x100>;
			clocks = <&clkuart0>;
			interrupts = <GIC_SPI 277 IRQ_TYPE_EDGE_RISING>;
		};

		uart1:	serial@2531000 {
			compatible = "ti,da830-uart", "ns16550a";
			current-speed = <115200>;
			reg-shift = <2>;
			reg-io-width = <4>;
			reg = <0x02531000 0x100>;
			clocks = <&clkuart1>;
			interrupts = <GIC_SPI 280 IRQ_TYPE_EDGE_RISING>;
		};

		i2c0: i2c@2530000 {
			compatible = "ti,davinci-i2c";
			reg = <0x02530000 0x400>;
			clock-frequency = <100000>;
			clocks = <&clki2c>;
			interrupts = <GIC_SPI 283 IRQ_TYPE_EDGE_RISING>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c1: i2c@2530400 {
			compatible = "ti,davinci-i2c";
			reg = <0x02530400 0x400>;
			clock-frequency = <100000>;
			clocks = <&clki2c>;
			interrupts = <GIC_SPI 286 IRQ_TYPE_EDGE_RISING>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2: i2c@2530800 {
			compatible = "ti,davinci-i2c";
			reg = <0x02530800 0x400>;
			clock-frequency = <100000>;
			clocks = <&clki2c>;
			interrupts = <GIC_SPI 289 IRQ_TYPE_EDGE_RISING>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi0: spi@21000400 {
			compatible = "ti,keystone-spi", "ti,dm6441-spi";
			reg = <0x21000400 0x200>;
			num-cs = <4>;
			ti,davinci-spi-intr-line = <0>;
			interrupts = <GIC_SPI 292 IRQ_TYPE_EDGE_RISING>;
			clocks = <&clkspi>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi1: spi@21000600 {
			compatible = "ti,keystone-spi", "ti,dm6441-spi";
			reg = <0x21000600 0x200>;
			num-cs = <4>;
			ti,davinci-spi-intr-line = <0>;
			interrupts = <GIC_SPI 296 IRQ_TYPE_EDGE_RISING>;
			clocks = <&clkspi>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi2: spi@21000800 {
			compatible = "ti,keystone-spi", "ti,dm6441-spi";
			reg = <0x21000800 0x200>;
			num-cs = <4>;
			ti,davinci-spi-intr-line = <0>;
			interrupts = <GIC_SPI 300 IRQ_TYPE_EDGE_RISING>;
			clocks = <&clkspi>;
			#address-cells = <1>;
			#size-cells = <0>;
		};

		usb_phy: usb_phy@2620738 {
			compatible = "ti,keystone-usbphy";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x2620738 24>;
			status = "disabled";
		};

		keystone_usb0: usb@2680000 {
			compatible = "ti,keystone-dwc3";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x2680000 0x10000>;
			clocks = <&clkusb>;
			clock-names = "usb";
			interrupts = <GIC_SPI 393 IRQ_TYPE_EDGE_RISING>;
			ranges;
			dma-coherent;
			dma-ranges;
			status = "disabled";

			usb0: usb@2690000 {
				compatible = "snps,dwc3";
				reg = <0x2690000 0x70000>;
				interrupts = <GIC_SPI 393 IRQ_TYPE_EDGE_RISING>;
				usb-phy = <&usb_phy>, <&usb_phy>;
			};
		};

		wdt: wdt@22f0080 {
			compatible = "ti,keystone-wdt","ti,davinci-wdt";
			reg = <0x022f0080 0x80>;
			clocks = <&clkwdtimer0>;
		};

		clock_event: timer@22f0000 {
			compatible = "ti,keystone-timer";
			reg = <0x022f0000 0x80>;
			interrupts = <GIC_SPI 110 IRQ_TYPE_EDGE_RISING>;
			clocks = <&clktimer15>;
		};

		gpio0: gpio@260bf00 {
			compatible = "ti,keystone-gpio";
			reg = <0x0260bf00 0x100>;
			gpio-controller;
			#gpio-cells = <2>;
			/* HW Interrupts mapped to GPIO pins */
			interrupts = <GIC_SPI 120 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 121 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 122 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 123 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 124 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 125 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 126 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 127 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 128 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 129 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 130 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 131 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 132 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 133 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 134 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 135 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 136 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 137 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 138 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 139 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 140 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 141 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 142 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 143 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 144 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 145 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 146 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 147 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 148 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 149 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 150 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 151 IRQ_TYPE_EDGE_RISING>;
			clocks = <&clkgpio>;
			clock-names = "gpio";
			ti,ngpio = <32>;
			ti,davinci-gpio-unbanked = <32>;
		};

		aemif: aemif@21000a00 {
			compatible = "ti,keystone-aemif", "ti,davinci-aemif";
			#address-cells = <2>;
			#size-cells = <1>;
			clocks = <&clkaemif>;
			clock-names = "aemif";
			clock-ranges;

			reg = <0x21000a00 0x00000100>;
			ranges = <0 0 0x30000000 0x10000000
				  1 0 0x21000a00 0x00000100>;
		};

		pcie0: pcie@******** {
			compatible = "ti,keystone-pcie", "snps,dw-pcie";
			clocks = <&clkpcie>;
			clock-names = "pcie";
			#address-cells = <3>;
			#size-cells = <2>;
			reg = <0x21801000 0x2000>, <0x******** 0x1000>, <0x02620128 4>;
			ranges = <0x82000000 0 0x50000000 0x50000000
				  0 0x10000000>;

			status = "disabled";
			device_type = "pci";
			num-lanes = <2>;
			bus-range = <0x00 0xff>;

			/* error interrupt */
			interrupts = <GIC_SPI 38 IRQ_TYPE_EDGE_RISING>;
			#interrupt-cells = <1>;
			interrupt-map-mask = <0 0 0 7>;
			interrupt-map = <0 0 0 1 &pcie_intc0 0>, /* INT A */
					<0 0 0 2 &pcie_intc0 1>, /* INT B */
					<0 0 0 3 &pcie_intc0 2>, /* INT C */
					<0 0 0 4 &pcie_intc0 3>; /* INT D */

			pcie_msi_intc0: msi-interrupt-controller {
				interrupt-controller;
				#interrupt-cells = <1>;
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 30 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 31 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 32 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 33 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 34 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 35 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 36 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 37 IRQ_TYPE_EDGE_RISING>;
			};

			pcie_intc0: legacy-interrupt-controller {
				interrupt-controller;
				#interrupt-cells = <1>;
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 26 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 27 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 28 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 29 IRQ_TYPE_EDGE_RISING>;
			};
		};

		emif: emif@21010000 {
			compatible = "ti,emif-keystone";
			reg = <0x21010000 0x200>;
			interrupts = <GIC_SPI 448 IRQ_TYPE_EDGE_RISING>;
			interrupt-parent = <&gic>;
		};
	};
};
