// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for Keystone 2 Edison Netcp driver
 *
 * Copyright (C) 2015-2017 Texas Instruments Incorporated - http://www.ti.com/
 */

qmss: qmss@2a40000 {
	compatible = "ti,keystone-navigator-qmss";
	dma-coherent;
	#address-cells = <1>;
	#size-cells = <1>;
	clocks = <&chipclk13>;
	ranges;
	queue-range = <0 0x2000>;
	linkram0 = <0x100000 0x4000>;
	linkram1 = <0 0x10000>;

	qmgrs {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		qmgr0 {
			managed-queues = <0 0x2000>;
			reg = <0x2a40000 0x20000>,
			      <0x2a06000 0x400>,
			      <0x2a02000 0x1000>,
			      <0x2a03000 0x1000>,
			      <0x23a80000 0x20000>,
			      <0x2a80000 0x20000>;
			reg-names = "peek", "status", "config",
				    "region", "push", "pop";
		};
	};
	queue-pools {
		qpend {
			qpend-0 {
				qrange = <658 8>;
				interrupts =<0 40 0xf04 0 41 0xf04 0 42 0xf04
					     0 43 0xf04 0 44 0xf04 0 45 0xf04
					     0 46 0xf04 0 47 0xf04>;
			};
			qpend-1 {
				qrange = <528 16>;
				interrupts = <0 48 0xf04 0 49 0xf04 0 50 0xf04
					      0 51 0xf04 0 52 0xf04 0 53 0xf04
					      0 54 0xf04 0 55 0xf04 0 56 0xf04
					      0 57 0xf04 0 58 0xf04 0 59 0xf04
					      0 60 0xf04 0 61 0xf04 0 62 0xf04
					      0 63 0xf04>;
				qalloc-by-id;
			};
			qpend-2 {
				qrange = <544 16>;
				interrupts = <0 64 0xf04 0 65 0xf04 0 66 0xf04
					      0 59 0xf04 0 68 0xf04 0 69 0xf04
					      0 70 0xf04 0 71 0xf04 0 72 0xf04
					      0 73 0xf04 0 74 0xf04 0 75 0xf04
					      0 76 0xf04 0 77 0xf04 0 78 0xf04
					      0 79 0xf04>;
			};
		};
		general-purpose {
			gp-0 {
				qrange = <4000 64>;
			};
			netcp-tx {
				qrange = <896 128>;
				qalloc-by-id;
			};
		};
		accumulator {
			acc-low-0 {
				qrange = <480 32>;
				accumulator = <0 47 16 2 50>;
				interrupts = <0 226 0xf01>;
				multi-queue;
				qalloc-by-id;
			};
		};
	};

	descriptor-regions {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		region-12 {
			id = <12>;
			region-spec = <8192 128>;	/* num_desc desc_size */
			link-index = <0x4000>;
		};
	};

	pdsps {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		pdsp0@2a10000 {
			reg = <0x2a10000 0x1000    /*iram */
			       0x2a0f000 0x100     /*reg*/
			       0x2a0c000 0x3c8	   /*intd */
			       0x2a20000 0x4000>;  /*cmd*/
			id = <0>;
		};
	};
}; /* qmss */

knav_dmas: knav_dmas@0 {
	compatible = "ti,keystone-navigator-dma";
	clocks = <&papllclk>;
	#address-cells = <1>;
	#size-cells = <1>;
	ranges;
	ti,navigator-cloud-address = <0x23a80000 0x23a90000
				 0x23a80000 0x23a90000>;

	dma_gbe: dma_gbe@0 {
		reg = <0x24186000 0x100>,
			  <0x24187000 0x2a0>,
			  <0x24188000 0xb60>,
			  <0x24186100 0x80>,
			  <0x24189000 0x1000>;
		reg-names = "global", "txchan", "rxchan",
				"txsched", "rxflow";
	};
};

netcp: netcp@24000000 {
	reg = <0x2620110 0x8>;
	reg-names = "efuse";
	compatible = "ti,netcp-1.0";
	#address-cells = <1>;
	#size-cells = <1>;

	/* NetCP address range */
	ranges = <0 0x24000000 0x1000000>;

	clocks = <&clkpa>, <&clkcpgmac>;
	clock-names = "pa_clk", "ethss_clk";
	dma-coherent;

	ti,navigator-dmas = <&dma_gbe 0>,
			<&dma_gbe 8>,
			<&dma_gbe 0>;
	ti,navigator-dma-names = "netrx0", "netrx1", "nettx";

	netcp-devices {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		gbe@200000 { /* ETHSS */
			label = "netcp-gbe";
			compatible = "ti,netcp-gbe-9";
			reg = <0x200000 0x900>, <0x220000 0x20000>;
			/* enable-ale; */
			tx-queue = <896>;
			tx-channel = "nettx";

			cpts {
				clocks = <&cpts_refclk_mux>;
				clock-names = "cpts";

				cpts_refclk_mux: cpts-refclk-mux {
					#clock-cells = <0>;
					clocks = <&chipclk12>, <&chipclk13>,
						 <&timi0>, <&timi1>,
						 <&tsipclka>, <&tsrefclk>,
						 <&tsipclkb>;
					ti,mux-tbl = <0x0>, <0x1>, <0x2>,
						<0x3>, <0x4>, <0x8>, <0xc>;
					assigned-clocks = <&cpts_refclk_mux>;
					assigned-clock-parents = <&chipclk12>;
				};
			};

			interfaces {
				gbe0: interface-0 {
					slave-port = <0>;
					link-interface = <1>;
					phy-handle = <&ethphy0>;
				};
				gbe1: interface-1 {
					slave-port = <1>;
					link-interface = <1>;
					phy-handle = <&ethphy1>;
				};
			};

			secondary-slave-ports {
				port-2 {
					slave-port = <2>;
					link-interface = <2>;
				};
				port-3 {
					slave-port = <3>;
					link-interface = <2>;
				};
				port-4 {
					slave-port = <4>;
					link-interface = <2>;
				};
				port-5 {
					slave-port = <5>;
					link-interface = <2>;
				};
				port-6 {
					slave-port = <6>;
					link-interface = <2>;
				};
				port-7 {
					slave-port = <7>;
					link-interface = <2>;
				};
			};
		};
	};

	netcp-interfaces {
		interface-0 {
			rx-channel = "netrx0";
			rx-pool = <1024 12>;
			tx-pool = <1024 12>;
			rx-queue-depth = <128 128 0 0>;
			rx-buffer-size = <1518 4096 0 0>;
			rx-queue = <528>;
			tx-completion-queue = <530>;
			efuse-mac = <1>;
			netcp-gbe = <&gbe0>;

		};
		interface-1 {
			rx-channel = "netrx1";
			rx-pool = <1024 12>;
			tx-pool = <1024 12>;
			rx-queue-depth = <128 128 0 0>;
			rx-buffer-size = <1518 4096 0 0>;
			rx-queue = <529>;
			tx-completion-queue = <531>;
			efuse-mac = <0>;
			local-mac-address = [02 18 31 7e 3e 00];
			netcp-gbe = <&gbe1>;
		};
	};
};

sa_subsys: subsys@24080000 {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "simple-bus";
	ranges = <0 0x24080000 0x40000>;

	sa_config: subsys@0 {
		compatible = "syscon";
		reg = <0x0 0x100>;
	};

	rng@24000 {
		compatible = "ti,keystone-rng";
		reg = <0x24000 0x1000>;
		ti,syscon-sa-cfg = <&sa_config>;
		clocks = <&clksa>;
		clock-names = "fck";
	};
};
