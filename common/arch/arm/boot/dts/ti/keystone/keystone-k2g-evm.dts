// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for K2G EVM
 *
 * Copyright (C) 2016-2017 Texas Instruments Incorporated - http://www.ti.com/
 */
/dts-v1/;

#include "keystone-k2g.dtsi"

/ {
	compatible =  "ti,k2g-evm", "ti,k2g", "ti,keystone";
	model = "Texas Instruments K2G General Purpose EVM";

	memory@800000000 {
		device_type = "memory";
		reg = <0x00000008 0x00000000 0x00000000 0x80000000>;
	};

	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		dsp_common_memory: dsp-common-memory@81f800000 {
			compatible = "shared-dma-pool";
			reg = <0x00000008 0x1f800000 0x00000000 0x800000>;
			reusable;
			status = "okay";
		};
	};

	vcc3v3_dcin_reg: fixedregulator-vcc3v3-dcin {
		compatible = "regulator-fixed";
		regulator-name = "mmc0_fixed";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	vcc1v8_ldo1_reg: fixedregulator-vcc1v8-ldo1 {
		compatible = "regulator-fixed";
		regulator-name = "ldo1";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	vcc1v8_ldo2_reg: fixedregulator-vcc1v8-ldo2 {
		compatible = "regulator-fixed";
		regulator-name = "ldo2";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	hdmi: connector {
		compatible = "hdmi-connector";
		label = "hdmi";

		type = "a";

		port {
			hdmi_connector_in: endpoint {
				remote-endpoint = <&sii9022_out>;
			};
		};
	};

	aud_mclk: aud_mclk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <********>;
	};

	sound0: sound@0 {
		compatible = "simple-audio-card";
		simple-audio-card,name = "K2G-EVM";
		simple-audio-card,widgets =
			"Headphone", "Headphone Jack",
			"Line", "Line In";
		simple-audio-card,routing =
			"Headphone Jack",	"HPLOUT",
			"Headphone Jack",	"HPROUT",
			"LINE1L",		"Line In",
			"LINE1R",		"Line In";

		simple-audio-card,dai-link@0 {
			format = "i2s";
			bitclock-master = <&sound0_0_master>;
			frame-master = <&sound0_0_master>;
			sound0_0_master: cpu {
				sound-dai = <&mcasp2>;
				clocks = <&k2g_clks 0x6 1>;
				system-clock-direction-out;
			};

			codec {
				sound-dai = <&tlv320aic3106>;
				clocks = <&aud_mclk>;
			};
		};

		simple-audio-card,dai-link@1 {
			format = "i2s";
			bitclock-master = <&sound0_1_master>;
			frame-master = <&sound0_1_master>;
			sound0_1_master: cpu {
				sound-dai = <&mcasp2>;
				clocks = <&k2g_clks 0x6 1>;
				system-clock-direction-out;
			};

			codec {
				sound-dai = <&sii9022>;
				clocks = <&aud_mclk>;
			};
		};
	};
};

&k2g_pinctrl {
	uart0_pins: uart0-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x11cc) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0)	/* uart0_rxd.uart0_rxd */
			K2G_CORE_IOPAD(0x11d0) (BUFFER_CLASS_B | PIN_PULLDOWN | MUX_MODE0)	/* uart0_txd.uart0_txd */
		>;
	};

	mmc0_pins: mmc0-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x1300) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE2)	/* mmc0_dat3.mmc0_dat3 */
			K2G_CORE_IOPAD(0x1304) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE2)	/* mmc0_dat2.mmc0_dat2 */
			K2G_CORE_IOPAD(0x1308) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE2)	/* mmc0_dat1.mmc0_dat1 */
			K2G_CORE_IOPAD(0x130c) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE2)	/* mmc0_dat0.mmc0_dat0 */
			K2G_CORE_IOPAD(0x1310) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE2)	/* mmc0_clk.mmc0_clk */
			K2G_CORE_IOPAD(0x1314) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE2)	/* mmc0_cmd.mmc0_cmd */
			K2G_CORE_IOPAD(0x12ec) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* mmc0_sdcd.gpio1_12 */
		>;
	};

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x10ec) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat7.mmc1_dat7 */
			K2G_CORE_IOPAD(0x10f0) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat6.mmc1_dat6 */
			K2G_CORE_IOPAD(0x10f4) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat5.mmc1_dat5 */
			K2G_CORE_IOPAD(0x10f8) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat4.mmc1_dat4 */
			K2G_CORE_IOPAD(0x10fc) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat3.mmc1_dat3 */
			K2G_CORE_IOPAD(0x1100) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat2.mmc1_dat2 */
			K2G_CORE_IOPAD(0x1104) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat1.mmc1_dat1 */
			K2G_CORE_IOPAD(0x1108) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat0.mmc1_dat0 */
			K2G_CORE_IOPAD(0x110c) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_clk.mmc1_clk */
			K2G_CORE_IOPAD(0x1110) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_cmd.mmc1_cmd */
		>;
	};

	i2c0_pins: i2c0-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x137c) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* i2c0_scl.i2c0_scl */
			K2G_CORE_IOPAD(0x1380) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* i2c0_sda.i2c0_sda */
		>;
	};

	i2c1_pins: i2c1-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x1384) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* i2c1_scl.i2c1_scl */
			K2G_CORE_IOPAD(0x1388) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* i2c1_sda.i2c1_sda */
		>;
	};

	ecap0_pins: ecap0-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x1374) (BUFFER_CLASS_B | MUX_MODE4)	/* pr1_mdio_data.ecap0_in_apwm0_out */
		>;
	};

	spi1_pins: spi1-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x11a4) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0)	/* spi1_scs0.spi1_scs0 */
			K2G_CORE_IOPAD(0x11ac) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0)	/* spi1_clk.spi1_clk */
			K2G_CORE_IOPAD(0x11b0) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0)	/* spi1_miso.spi1_miso */
			K2G_CORE_IOPAD(0x11b4) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0)	/* spi1_mosi.spi1_mosi */
		>;
	};

	qspi_pins: qspi-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x1204) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_clk.qspi_clk */
			K2G_CORE_IOPAD(0x1208) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_rclk.qspi_rclk */
			K2G_CORE_IOPAD(0x120c) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_d0.qspi_d0 */
			K2G_CORE_IOPAD(0x1210) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_d1.qspi_d1 */
			K2G_CORE_IOPAD(0x1214) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_d2.qspi_d2 */
			K2G_CORE_IOPAD(0x1218) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_d3.qspi_d3 */
			K2G_CORE_IOPAD(0x121c) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_csn0.qspi_csn0 */
		>;
	};

	uart2_pins: uart2-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x11ec) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0)      /* uart2_rxd.uart2_rxd */
			K2G_CORE_IOPAD(0x11f0) (BUFFER_CLASS_B | PIN_PULLDOWN | MUX_MODE0)      /* uart2_txd.uart2_txd */
		>;
	};

	dcan0_pins: dcan0-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x11fc) (BUFFER_CLASS_B | PULL_DISABLE  | MUX_MODE0)	/* dcan0tx.dcan0tx */
			K2G_CORE_IOPAD(0x1200) (BUFFER_CLASS_B | PIN_PULLDOWN  | MUX_MODE0)	/* dcan0rx.dcan0rx */
		>;
	};

	dcan1_pins: dcan1-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x1224) (BUFFER_CLASS_B | PULL_DISABLE  | MUX_MODE1)	/* qspicsn2.dcan1tx */
			K2G_CORE_IOPAD(0x1228) (BUFFER_CLASS_B | PIN_PULLDOWN  | MUX_MODE1)	/* qspicsn3.dcan1rx */
		>;
	};

	emac_pins: emac-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x113c) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXD1.RGMII_RXD1 */
			K2G_CORE_IOPAD(0x1138) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXD2.RGMII_RXD2 */
			K2G_CORE_IOPAD(0x1134) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXD3.RGMII_RXD3 */
			K2G_CORE_IOPAD(0x1140) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXD0.RGMII_RXD0 */
			K2G_CORE_IOPAD(0x1178) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXD0.RGMII_TXD0 */
			K2G_CORE_IOPAD(0x1174) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXD1.RGMII_TXD1 */
			K2G_CORE_IOPAD(0x1170) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXD2.RGMII_TXD2 */
			K2G_CORE_IOPAD(0x116c) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXD3.RGMII_TXD3 */
			K2G_CORE_IOPAD(0x1154) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXCLK.RGMII_TXC */
			K2G_CORE_IOPAD(0x117c) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXEN.RGMII_TXCTL */
			K2G_CORE_IOPAD(0x1120) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXCLK.RGMII_RXC */
			K2G_CORE_IOPAD(0x1144) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXDV.RGMII_RXCTL */
		>;
	};

	mdio_pins: mdio-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x118c) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0)	/* MDIO_CLK.MDIO_CLK */
			K2G_CORE_IOPAD(0x1188) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0)	/* MDIO_DATA.MDIO_DATA */
		>;
	};

	vout_pins: vout-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x1078) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata23.dssdata23 */
			K2G_CORE_IOPAD(0x107c) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata22.dssdata22 */
			K2G_CORE_IOPAD(0x1080) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata21.dssdata21 */
			K2G_CORE_IOPAD(0x1084) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata20.dssdata20 */
			K2G_CORE_IOPAD(0x1088) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata19.dssdata19 */
			K2G_CORE_IOPAD(0x108c) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata18.dssdata18 */
			K2G_CORE_IOPAD(0x1090) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata17.dssdata17 */
			K2G_CORE_IOPAD(0x1094) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata16.dssdata16 */
			K2G_CORE_IOPAD(0x1098) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata15.dssdata15 */
			K2G_CORE_IOPAD(0x109c) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata14.dssdata14 */
			K2G_CORE_IOPAD(0x10a0) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata13.dssdata13 */
			K2G_CORE_IOPAD(0x10a4) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata12.dssdata12 */
			K2G_CORE_IOPAD(0x10a8) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata11.dssdata11 */
			K2G_CORE_IOPAD(0x10ac) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata10.dssdata10 */
			K2G_CORE_IOPAD(0x10b0) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata9.dssdata9 */
			K2G_CORE_IOPAD(0x10b4) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata8.dssdata8 */
			K2G_CORE_IOPAD(0x10b8) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata7.dssdata7 */
			K2G_CORE_IOPAD(0x10bc) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata6.dssdata6 */
			K2G_CORE_IOPAD(0x10c0) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata5.dssdata5 */
			K2G_CORE_IOPAD(0x10c4) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata4.dssdata4 */
			K2G_CORE_IOPAD(0x10c8) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata3.dssdata3 */
			K2G_CORE_IOPAD(0x10cc) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata2.dssdata2 */
			K2G_CORE_IOPAD(0x10d0) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata1.dssdata1 */
			K2G_CORE_IOPAD(0x10d4) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssdata0.dssdata0 */
			K2G_CORE_IOPAD(0x10d8) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssvsync.dssvsync */
			K2G_CORE_IOPAD(0x10dc) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dsshsync.dsshsync */
			K2G_CORE_IOPAD(0x10e0) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dsspclk.dsspclk */
			K2G_CORE_IOPAD(0x10e4) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssde.dssde */
			K2G_CORE_IOPAD(0x10e8) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* dssfid.dssfid */
		>;
	};

	mcasp2_pins: mcasp2-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x1234) (BUFFER_CLASS_B | PIN_PULLDOWN | MUX_MODE4) /* pr0_pru_gpo2.mcasp2_axr2 */
			K2G_CORE_IOPAD(0x1238) (BUFFER_CLASS_B | PIN_PULLDOWN | MUX_MODE4) /* pr0_pru_gpo3.mcasp2_axr3 */
			K2G_CORE_IOPAD(0x1254) (BUFFER_CLASS_B | PIN_PULLDOWN | MUX_MODE4) /* pr0_pru_gpo10.mcasp2_afsx */
			K2G_CORE_IOPAD(0x125c) (BUFFER_CLASS_B | PIN_PULLDOWN | MUX_MODE4) /* pr0_pru_gpo12.mcasp2_aclkx */
		>;
	};
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pins>;
	status = "okay";
};

&gpio1 {
	status = "okay";
};

&mmc0 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc0_pins>;
	vmmc-supply = <&vcc3v3_dcin_reg>;
	vqmmc-supply = <&vcc3v3_dcin_reg>;
	cd-gpios = <&gpio1 12 GPIO_ACTIVE_LOW>;
	status = "okay";
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
	vmmc-supply = <&vcc3v3_dcin_reg>; /* VCC3V3_EMMC is connected to VCC3V3_DCIN */
	vqmmc-supply = <&vcc1v8_ldo1_reg>;
	ti,non-removable;
	status = "okay";
};

&dsp0 {
	memory-region = <&dsp_common_memory>;
	status = "okay";
};

&i2c0 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c0_pins>;
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c1024";
		reg = <0x50>;
	};
};

&keystone_usb0 {
	status = "okay";
};

&usb0_phy {
	status = "okay";
};

&usb0 {
	dr_mode = "host";
	status = "okay";
};

&keystone_usb1 {
	status = "okay";
};

&usb1_phy {
	status = "okay";
};

&usb1 {
	dr_mode = "peripheral";
	status = "okay";
};

&ecap0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&ecap0_pins>;
};

&spi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi1_pins>;
	status = "okay";

	spi_nor: flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "jedec,spi-nor";
		spi-max-frequency = <5000000>;
		m25p,fast-read;
		reg = <0>;

		partition@0 {
			label = "u-boot-spl";
			reg = <0x0 0x100000>;
			read-only;
		};

		partition@1 {
			label = "misc";
			reg = <0x100000 0xf00000>;
		};
	};
};

&qspi {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&qspi_pins>;
	cdns,rclk-en;

	flash0: flash@0 {
		compatible = "s25fl512s", "jedec,spi-nor";
		reg = <0>;
		spi-tx-bus-width = <1>;
		spi-rx-bus-width = <4>;
		spi-max-frequency = <96000000>;
		#address-cells = <1>;
		#size-cells = <1>;
		cdns,read-delay = <5>;
		cdns,tshsl-ns = <500>;
		cdns,tsd2d-ns = <500>;
		cdns,tchsh-ns = <119>;
		cdns,tslch-ns = <119>;

		partition@0 {
			label = "QSPI.u-boot-spl-os";
			reg = <0x00000000 0x00100000>;
		};
		partition@1 {
			label = "QSPI.u-boot-env";
			reg = <0x00100000 0x00040000>;
		};
		partition@2 {
			label = "QSPI.skern";
			reg = <0x00140000 0x0040000>;
		};
		partition@3 {
			label = "QSPI.pmmc-firmware";
			reg = <0x00180000 0x0040000>;
		};
		partition@4 {
			label = "QSPI.kernel";
			reg = <0x001c0000 0x0800000>;
		};
		partition@5 {
			label = "QSPI.file-system";
			reg = <0x009c0000 0x3640000>;
		};
	};
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pins>;
	status = "okay";
};

&dcan0 {
	pinctrl-names = "default";
	pinctrl-0 = <&dcan0_pins>;
	status = "okay";
};

&dcan1 {
	pinctrl-names = "default";
	pinctrl-0 = <&dcan1_pins>;
	status = "okay";
};

&qmss {
	status = "okay";
};

&knav_dmas {
	status = "okay";
};

&mdio {
	pinctrl-names = "default";
	pinctrl-0 = <&mdio_pins>;
	status = "okay";
	ethphy0: ethernet-phy@0 {
		reg = <0>;
	};
};

&gbe0 {
	phy-handle = <&ethphy0>;
	phy-mode = "rgmii-rxid";
	status = "okay";
};

&netcp {
	pinctrl-names = "default";
	pinctrl-0 = <&emac_pins>;
	status = "okay";
};

&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins>;
	status = "okay";
	clock-frequency = <400000>;

	sii9022: sii9022@3b {
		#sound-dai-cells = <0>;
		compatible = "sil,sii9022";
		reg = <0x3b>;

		sil,i2s-data-lanes = < 0 >;
		clocks = <&aud_mclk>;
		clock-names = "mclk";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				sii9022_in: endpoint {
					remote-endpoint = <&dpi_out>;
				};
			};

			port@1 {
				reg = <1>;

				sii9022_out: endpoint {
					remote-endpoint = <&hdmi_connector_in>;
				};
			};
		};
	};

	tlv320aic3106: tlv320aic3106@1b {
		#sound-dai-cells = <0>;
		compatible = "ti,tlv320aic3106";
		reg = <0x1b>;
		status = "okay";

		/* Regulators */
		AVDD-supply = <&vcc3v3_dcin_reg>;
		IOVDD-supply = <&vcc3v3_dcin_reg>;
		DRVDD-supply = <&vcc3v3_dcin_reg>;
		DVDD-supply = <&vcc1v8_ldo2_reg>;
	};
};

&dss {
	pinctrl-names = "default";
	pinctrl-0 = <&vout_pins>;
	status = "okay";

	port {
		dpi_out: endpoint {
			remote-endpoint = <&sii9022_in>;
			data-lines = <24>;
		};
	};
};

&mcasp2 {
	#sound-dai-cells = <0>;

	pinctrl-names = "default";
	pinctrl-0 = <&mcasp2_pins>;

	assigned-clocks = <&k2g_clks 0x4c 2>, <&k2g_clks 0x6 1>;
	assigned-clock-parents = <0>, <&k2g_clks 0x6 2>;
	assigned-clock-rates = <22579200>, <0>;

	status = "okay";

	op-mode = <0>;		/* MCASP_IIS_MODE */
	tdm-slots = <2>;
	/* 6 serializer */
	serial-dir = <	/* 0: INACTIVE, 1: TX, 2: RX */
		0 0 1 2 0 0 // AXR2: TX, AXR3: rx
	>;
	tx-num-evt = <32>;
	rx-num-evt = <32>;
};
