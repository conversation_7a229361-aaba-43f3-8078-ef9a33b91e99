// SPDX-License-Identifier: GPL-2.0
/*
 * Keystone 2 Edison SoC specific device tree
 *
 * Copyright (C) 2014-2017 Texas Instruments Incorporated - http://www.ti.com/
 */

clocks {
	mainpllclk: mainpllclk@2310110 {
		#clock-cells = <0>;
		compatible = "ti,keystone,main-pll-clock";
		clocks = <&refclksys>;
		reg = <0x02620350 4>, <0x02310110 4>, <0x02310108 4>;
		reg-names = "control", "multiplier", "post-divider";
	};

	papllclk: papllclk@2620358 {
		#clock-cells = <0>;
		compatible = "ti,keystone,pll-clock";
		clocks = <&refclkpass>;
		clock-output-names = "papllclk";
		reg = <0x02620358 4>;
		reg-names = "control";
	};

	ddr3apllclk: ddr3apllclk@2620360 {
		#clock-cells = <0>;
		compatible = "ti,keystone,pll-clock";
		clocks = <&refclkddr3a>;
		clock-output-names = "ddr-3a-pll-clk";
		reg = <0x02620360 4>;
		reg-names = "control";
	};

	clkusb1: clkusb1@2350004 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk16>;
		clock-output-names = "usb1";
		reg = <0x02350004 0xb00>, <0x02350000 0x400>;
		reg-names = "control", "domain";
		domain-id = <0>;
	};

	clkhyperlink0: clkhyperlink0@2350030 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk12>;
		clock-output-names = "hyperlink-0";
		reg = <0x02350030 0xb00>, <0x02350014 0x400>;
		reg-names = "control", "domain";
		domain-id = <5>;
	};

	clkpcie1: clkpcie1@235006c {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk12>;
		clock-output-names = "pcie1";
		reg = <0x0235006c 0xb00>, <0x02350048 0x400>;
		reg-names = "control", "domain";
		domain-id = <18>;
	};

	clkxge: clkxge@23500c8 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "xge";
		reg = <0x023500c8 0xb00>, <0x02350074 0x400>;
		reg-names = "control", "domain";
		domain-id = <29>;
	};

	/*
	 * Below are set of fixed, input clocks definitions,
	 * for which real frequencies have to be defined in board files.
	 * Those clocks can be used as reference clocks for some HW modules
	 * (as cpts, for example) by configuring corresponding clock muxes.
	 */
	tsipclka: tsipclka {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <0>;
		clock-output-names = "tsipclka";
	};

	tsipclkb: tsipclkb {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <0>;
		clock-output-names = "tsipclkb";
	};
};
