// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for K2G Netcp driver
 *
 * Copyright (C) 2018 Texas Instruments Incorporated - http://www.ti.com/
 */

qmss: qmss@4020000 {
	compatible = "ti,66ak2g-navss-qm";
	dma-coherent;
	#address-cells = <1>;
	#size-cells = <1>;
	power-domains = <&k2g_pds 0x0018>;
	clocks = <&k2g_clks 0x0018 0>;
	clock-names = "nss_vclk";
	ranges;
	queue-range = <0 0x80>;
	linkram0 = <0x4020000 0x7ff>;
	status = "disabled";

	qmgrs {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		qmgr0 {
			managed-queues = <0 0x80>;
			reg = <0x4100000 0x800>,
			      <0x4040000 0x100>,
			      <0x4080000 0x800>,
			      <0x40c0000 0x800>;
			reg-names = "peek", "config",
				    "region", "push";
		};

	};
	queue-pools {
		qpend {
			qpend-0 {
				qrange = <77 8>;
				interrupts =<0 308 0xf04 0 309 0xf04 0 310 0xf04
					     0 311 0xf04 0 312 0xf04 0 313 0xf04
					     0 314 0xf04 0 315 0xf04>;
				qalloc-by-id;
			};
		};
		general-purpose {
			gp-0 {
				qrange = <112 8>;
			};
			netcp-tx {
				qrange = <5 8>;
				qalloc-by-id;
			};
		};
	};

	descriptor-regions {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		region-12 {
			id = <12>;
			region-spec = <1023 128>; /* num_desc desc_size */
			link-index = <0x400>;
		};
	};
}; /* qmss */

knav_dmas: knav_dmas@0 {
	compatible = "ti,keystone-navigator-dma";
	#address-cells = <1>;
	#size-cells = <1>;
	status = "disabled";
	power-domains = <&k2g_pds 0x0018>;
	clocks = <&k2g_clks 0x0018 0>;
	clock-names = "nss_vclk";
	ranges;
	ti,navigator-cloud-address = <0x40c0000 0x40c0000 0x40c0000 0x40c0000>;

	dma_gbe: dma_gbe@0 {
		reg = <0x4010000 0x100>,
		      <0x4011000 0x2a0>, /* 21 Tx channels */
		      <0x4012000 0x400>, /* 32 Rx channels */
		      <0x4010100 0x80>,
		      <0x4013000 0x400>; /* 32 Rx flows */
		reg-names = "global", "txchan", "rxchan",
			    "txsched", "rxflow";
	};

};

netcp: netcp@4000000 {
	reg = <0x2620110 0x8>;
	reg-names = "efuse";
	compatible = "ti,netcp-1.0";
	#address-cells = <1>;
	#size-cells = <1>;
	status = "disabled";
	power-domains = <&k2g_pds 0x0018>;
	clocks = <&k2g_clks 0x0018 3>, <&k2g_clks 0x0018 8>;
	clock-names = "ethss_clk", "cpts";

	/* NetCP address range */
	ranges = <0 0x4000000 0x1000000>;

	dma-coherent;

	ti,navigator-dmas = <&dma_gbe 0>, <&dma_gbe 5>;
	ti,navigator-dma-names = "netrx0", "nettx";

	netcp-devices {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		gbe: gbe@200000 {
			label = "netcp-gbe";
			compatible = "ti,netcp-gbe-2";
			reg = <0x200000 0x20>, <0x220000 0x20000>;
			enable-ale;
			tx-queue = <5>;
			tx-channel = "nettx";
			cpts-rftclk-sel = <0>;
			cpts-ext-ts-inputs = <8>;

			interfaces {
				gbe0: interface-0 {
					slave-port = <0>;
					link-interface = <5>;
				};
			};
		};
	};

	netcp-interfaces {
		interface-0 {
			rx-channel = "netrx0";
			rx-pool = <512 12>;
			tx-pool = <511 12>;
			rx-queue-depth = <128 128 0 0>;
			rx-buffer-size = <1518 4096 0 0>;
			rx-queue = <77>;
			tx-completion-queue = <78>;
			efuse-mac = <1>;
			netcp-gbe = <&gbe0>;
		};
	};
};
