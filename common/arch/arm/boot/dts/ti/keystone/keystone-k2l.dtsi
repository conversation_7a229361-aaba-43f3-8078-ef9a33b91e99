// SPDX-License-Identifier: GPL-2.0
/*
 * Keystone 2 Lamarr SoC specific device tree
 *
 * Copyright (C) 2014-2017 Texas Instruments Incorporated - http://www.ti.com/
 */

#include <dt-bindings/reset/ti-syscon.h>

/ {
	compatible = "ti,k2l", "ti,keystone";
	model = "Texas Instruments Keystone 2 Lamarr SoC";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		interrupt-parent = <&gic>;

		cpu@0 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			reg = <0>;
		};

		cpu@1 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			reg = <1>;
		};
	};

	aliases {
		rproc0 = &dsp0;
		rproc1 = &dsp1;
		rproc2 = &dsp2;
		rproc3 = &dsp3;
	};
};

&soc0 {
		/include/ "keystone-k2l-clocks.dtsi"

		uart2: serial@2348400 {
			compatible = "ti,da830-uart", "ns16550a";
			current-speed = <115200>;
			reg-shift = <2>;
			reg-io-width = <4>;
			reg = <0x02348400 0x100>;
			clocks = <&clkuart2>;
			interrupts = <GIC_SPI 432 IRQ_TYPE_EDGE_RISING>;
		};

		uart3:	serial@2348800 {
			compatible = "ti,da830-uart", "ns16550a";
			current-speed = <115200>;
			reg-shift = <2>;
			reg-io-width = <4>;
			reg = <0x02348800 0x100>;
			clocks = <&clkuart3>;
			interrupts = <GIC_SPI 435 IRQ_TYPE_EDGE_RISING>;
		};

		gpio1: gpio@2348000 {
			compatible = "ti,keystone-gpio";
			reg = <0x02348000 0x100>;
			gpio-controller;
			#gpio-cells = <2>;
			/* HW Interrupts mapped to GPIO pins */
			interrupts = <GIC_SPI 152 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 153 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 154 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 155 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 156 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 157 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 158 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 159 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 160 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 161 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 162 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 163 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 164 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 165 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 166 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 167 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 168 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 169 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 170 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 171 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 172 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 173 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 174 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 175 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 176 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 401 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 402 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 403 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 404 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 405 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 406 IRQ_TYPE_EDGE_RISING>,
					<GIC_SPI 407 IRQ_TYPE_EDGE_RISING>;
			clocks = <&clkgpio>;
			clock-names = "gpio";
			ti,ngpio = <32>;
			ti,davinci-gpio-unbanked = <32>;
		};

		k2l_pmx: pinmux@2620690 {
			compatible = "pinctrl-single";
			reg = <0x02620690 0xc>;
			#address-cells = <1>;
			#size-cells = <0>;
			#pinctrl-cells = <2>;
			pinctrl-single,bit-per-mux;
			pinctrl-single,register-width = <32>;
			pinctrl-single,function-mask = <0x1>;
			status = "disabled";

			uart3_emifa_pins: uart3-emifa-pins {
				pinctrl-single,bits = <
					/* UART3_EMIFA_SEL */
					0x0 0x0  0xc0
				>;
			};

			uart2_emifa_pins: uart2-emifa-pins {
			pinctrl-single,bits = <
					/* UART2_EMIFA_SEL */
					0x0 0x0  0x30
				>;
			};

			uart01_spi2_pins: uart01-spi2-pins {
				pinctrl-single,bits = <
					/* UART01_SPI2_SEL */
					0x0 0x0 0x4
				>;
			};

			dfesync_rp1_pins: dfesync-rp1-pins {
				pinctrl-single,bits = <
					/* DFESYNC_RP1_SEL */
					0x0 0x0 0x2
				>;
			};

			avsif_pins: avsif-pins {
				pinctrl-single,bits = <
					/* AVSIF_SEL */
					0x0 0x0 0x1
				>;
			};

			gpio_emu_pins: gpio-emu-pins {
				pinctrl-single,bits = <
				/*
				 * GPIO_EMU_SEL[31]: 0-GPIO31, 1-EMU33
				 * GPIO_EMU_SEL[30]: 0-GPIO30, 1-EMU32
				 * GPIO_EMU_SEL[29]: 0-GPIO29, 1-EMU31
				 * GPIO_EMU_SEL[28]: 0-GPIO28, 1-EMU30
				 * GPIO_EMU_SEL[27]: 0-GPIO27, 1-EMU29
				 * GPIO_EMU_SEL[26]: 0-GPIO26, 1-EMU28
				 * GPIO_EMU_SEL[25]: 0-GPIO25, 1-EMU27
				 * GPIO_EMU_SEL[24]: 0-GPIO24, 1-EMU26
				 * GPIO_EMU_SEL[23]: 0-GPIO23, 1-EMU25
				 * GPIO_EMU_SEL[22]: 0-GPIO22, 1-EMU24
				 * GPIO_EMU_SEL[21]: 0-GPIO21, 1-EMU23
				 * GPIO_EMU_SEL[20]: 0-GPIO20, 1-EMU22
				 * GPIO_EMU_SEL[19]: 0-GPIO19, 1-EMU21
				 * GPIO_EMU_SEL[18]: 0-GPIO18, 1-EMU20
				 * GPIO_EMU_SEL[17]: 0-GPIO17, 1-EMU19
				 */
					0x4 0x0000 0xfffe0000
				>;
			};

			gpio_timio_pins: gpio-timio-pins {
				pinctrl-single,bits = <
				/*
				 * GPIO_TIMIO_SEL[15]: 0-GPIO15, 1-TIMO7
				 * GPIO_TIMIO_SEL[14]: 0-GPIO14, 1-TIMO6
				 * GPIO_TIMIO_SEL[13]: 0-GPIO13, 1-TIMO5
				 * GPIO_TIMIO_SEL[12]: 0-GPIO12, 1-TIMO4
				 * GPIO_TIMIO_SEL[11]: 0-GPIO11, 1-TIMO3
				 * GPIO_TIMIO_SEL[10]: 0-GPIO10, 1-TIMO2
				 * GPIO_TIMIO_SEL[9]: 0-GPIO9, 1-TIMI7
				 * GPIO_TIMIO_SEL[8]: 0-GPIO8, 1-TIMI6
				 * GPIO_TIMIO_SEL[7]: 0-GPIO7, 1-TIMI5
				 * GPIO_TIMIO_SEL[6]: 0-GPIO6, 1-TIMI4
				 * GPIO_TIMIO_SEL[5]: 0-GPIO5, 1-TIMI3
				 * GPIO_TIMIO_SEL[4]: 0-GPIO4, 1-TIMI2
				 */
					0x4 0x0 0xfff0
				>;
			};

			gpio_spi2cs_pins: gpio-spi2cs-pins {
				pinctrl-single,bits = <
				/*
				 * GPIO_SPI2CS_SEL[3]: 0-GPIO3, 1-SPI2CS4
				 * GPIO_SPI2CS_SEL[2]: 0-GPIO2, 1-SPI2CS3
				 * GPIO_SPI2CS_SEL[1]: 0-GPIO1, 1-SPI2CS2
				 * GPIO_SPI2CS_SEL[0]: 0-GPIO0, 1-SPI2CS1
				 */
					0x4 0x0 0xf
				>;
			};

			gpio_dfeio_pins: gpio-dfeio-pins {
				pinctrl-single,bits = <
				/*
				 * GPIO_DFEIO_SEL[31]: 0-DFEIO17, 1-GPIO63
				 * GPIO_DFEIO_SEL[30]: 0-DFEIO16, 1-GPIO62
				 * GPIO_DFEIO_SEL[29]: 0-DFEIO15, 1-GPIO61
				 * GPIO_DFEIO_SEL[28]: 0-DFEIO14, 1-GPIO60
				 * GPIO_DFEIO_SEL[27]: 0-DFEIO13, 1-GPIO59
				 * GPIO_DFEIO_SEL[26]: 0-DFEIO12, 1-GPIO58
				 * GPIO_DFEIO_SEL[25]: 0-DFEIO11, 1-GPIO57
				 * GPIO_DFEIO_SEL[24]: 0-DFEIO10, 1-GPIO56
				 * GPIO_DFEIO_SEL[23]: 0-DFEIO9, 1-GPIO55
				 * GPIO_DFEIO_SEL[22]: 0-DFEIO8, 1-GPIO54
				 * GPIO_DFEIO_SEL[21]: 0-DFEIO7, 1-GPIO53
				 * GPIO_DFEIO_SEL[20]: 0-DFEIO6, 1-GPIO52
				 * GPIO_DFEIO_SEL[19]: 0-DFEIO5, 1-GPIO51
				 * GPIO_DFEIO_SEL[18]: 0-DFEIO4, 1-GPIO50
				 * GPIO_DFEIO_SEL[17]: 0-DFEIO3, 1-GPIO49
				 * GPIO_DFEIO_SEL[16]: 0-DFEIO2, 1-GPIO48
				 */
					0x8 0x0 0xffff0000
				>;
			};

			gpio_emifa_pins: gpio-emifa-pins {
				pinctrl-single,bits = <
				/*
				 * GPIO_EMIFA_SEL[15]: 0-EMIFA17, 1-GPIO47
				 * GPIO_EMIFA_SEL[14]: 0-EMIFA16, 1-GPIO46
				 * GPIO_EMIFA_SEL[13]: 0-EMIFA15, 1-GPIO45
				 * GPIO_EMIFA_SEL[12]: 0-EMIFA14, 1-GPIO44
				 * GPIO_EMIFA_SEL[11]: 0-EMIFA13, 1-GPIO43
				 * GPIO_EMIFA_SEL[10]: 0-EMIFA10, 1-GPIO42
				 * GPIO_EMIFA_SEL[9]: 0-EMIFA9, 1-GPIO41
				 * GPIO_EMIFA_SEL[8]: 0-EMIFA8, 1-GPIO40
				 * GPIO_EMIFA_SEL[7]: 0-EMIFA7, 1-GPIO39
				 * GPIO_EMIFA_SEL[6]: 0-EMIFA6, 1-GPIO38
				 * GPIO_EMIFA_SEL[5]: 0-EMIFA5, 1-GPIO37
				 * GPIO_EMIFA_SEL[4]: 0-EMIFA4, 1-GPIO36
				 * GPIO_EMIFA_SEL[3]: 0-EMIFA3, 1-GPIO35
				 * GPIO_EMIFA_SEL[2]: 0-EMIFA2, 1-GPIO34
				 * GPIO_EMIFA_SEL[1]: 0-EMIFA1, 1-GPIO33
				 * GPIO_EMIFA_SEL[0]: 0-EMIFA0, 1-GPIO32
				 */
					0x8 0x0 0xffff
				>;
			};
		};

		msm_ram: sram@c000000 {
			compatible = "mmio-sram";
			reg = <0x0c000000 0x200000>;
			ranges = <0x0 0x0c000000 0x200000>;
			#address-cells = <1>;
			#size-cells = <1>;

			bm-sram@1f8000 {
				reg = <0x001f8000 0x8000>;
			};
		};

		psc: power-sleep-controller@2350000 {
			pscrst: reset-controller {
				compatible = "ti,k2l-pscrst", "ti,syscon-reset";
				#reset-cells = <1>;

				ti,reset-bits = <
					0xa3c 8 0xa3c 8 0x83c 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 0: dsp0 */
					0xa40 8 0xa40 8 0x840 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 1: dsp1 */
					0xa44 8 0xa44 8 0x844 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 2: dsp2 */
					0xa48 8 0xa48 8 0x848 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 3: dsp3 */
				>;
			};
		};

		osr: sram@70000000 {
			compatible = "mmio-sram";
			reg = <0x70000000 0x10000>;
			#address-cells = <1>;
			#size-cells = <1>;
			clocks = <&clkosr>;
		};

		devctrl: device-state-control@2620000 {
			dspgpio0: keystone_dsp_gpio@240 {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x240 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x240>;
			};

			dspgpio1: keystone_dsp_gpio@244 {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x244 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x244>;
			};

			dspgpio2: keystone_dsp_gpio@248 {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x248 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x248>;
			};

			dspgpio3: keystone_dsp_gpio@24c {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x24c 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x24c>;
			};
		};

		dsp0: dsp@10800000 {
			compatible = "ti,k2l-dsp";
			reg = <0x10800000 0x00100000>,
			      <0x10e00000 0x00008000>,
			      <0x10f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem0>;
			ti,syscon-dev = <&devctrl 0x844>;
			resets = <&pscrst 0>;
			interrupt-parent = <&kirq0>;
			interrupts = <0 8>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio0 27 0>;
			status = "disabled";
		};

		dsp1: dsp@11800000 {
			compatible = "ti,k2l-dsp";
			reg = <0x11800000 0x00100000>,
			      <0x11e00000 0x00008000>,
			      <0x11f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem1>;
			ti,syscon-dev = <&devctrl 0x848>;
			resets = <&pscrst 1>;
			interrupt-parent = <&kirq0>;
			interrupts = <1 9>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio1 27 0>;
			status = "disabled";
		};

		dsp2: dsp@12800000 {
			compatible = "ti,k2l-dsp";
			reg = <0x12800000 0x00100000>,
			      <0x12e00000 0x00008000>,
			      <0x12f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem2>;
			ti,syscon-dev = <&devctrl 0x84c>;
			resets = <&pscrst 2>;
			interrupt-parent = <&kirq0>;
			interrupts = <2 10>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio2 27 0>;
			status = "disabled";
		};

		dsp3: dsp@13800000 {
			compatible = "ti,k2l-dsp";
			reg = <0x13800000 0x00100000>,
			      <0x13e00000 0x00008000>,
			      <0x13f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem3>;
			ti,syscon-dev = <&devctrl 0x850>;
			resets = <&pscrst 3>;
			interrupt-parent = <&kirq0>;
			interrupts = <3 11>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio3 27 0>;
			status = "disabled";
		};

		mdio: mdio@26200f00 {
			compatible = "ti,keystone_mdio", "ti,davinci_mdio";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x26200f00 0x100>;
			status = "disabled";
			clocks = <&clkcpgmac>;
			clock-names = "fck";
			bus_freq = <2500000>;
		};
		/include/ "keystone-k2l-netcp.dtsi"
};

&spi0 {
       ti,davinci-spi-num-cs = <5>;
};

&spi1 {
       ti,davinci-spi-num-cs = <3>;
};

&spi2 {
       ti,davinci-spi-num-cs = <5>;
       /* Pin muxed. Enabled and configured by Bootloader */
       status = "disabled";
};
