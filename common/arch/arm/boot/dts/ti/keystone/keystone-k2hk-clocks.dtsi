// SPDX-License-Identifier: GPL-2.0
/*
 * Keystone 2 Kepler/Hawking SoC clock nodes
 *
 * Copyright (C) 2013-2017 Texas Instruments Incorporated - http://www.ti.com/
 */

clocks {
	armpllclk: armpllclk@2620370 {
		#clock-cells = <0>;
		compatible = "ti,keystone,pll-clock";
		clocks = <&refclkarm>;
		clock-output-names = "arm-pll-clk";
		reg = <0x02620370 4>;
		reg-names = "control";
	};

	mainpllclk: mainpllclk@2310110 {
		#clock-cells = <0>;
		compatible = "ti,keystone,main-pll-clock";
		clocks = <&refclksys>;
		reg = <0x02620350 4>, <0x02310110 4>, <0x02310108 4>;
		reg-names = "control", "multiplier", "post-divider";
	};

	papllclk: papllclk@2620358 {
		#clock-cells = <0>;
		compatible = "ti,keystone,pll-clock";
		clocks = <&refclkpass>;
		clock-output-names = "papllclk";
		reg = <0x02620358 4>;
		reg-names = "control";
	};

	ddr3apllclk: ddr3apllclk@2620360 {
		#clock-cells = <0>;
		compatible = "ti,keystone,pll-clock";
		clocks = <&refclkddr3a>;
		clock-output-names = "ddr-3a-pll-clk";
		reg = <0x02620360 4>;
		reg-names = "control";
	};

	ddr3bpllclk: ddr3bpllclk@2620368 {
		#clock-cells = <0>;
		compatible = "ti,keystone,pll-clock";
		clocks = <&refclkddr3b>;
		clock-output-names = "ddr-3b-pll-clk";
		reg = <0x02620368 4>;
		reg-names = "control";
	};

	clktsip: clktsip@2350000 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk16>;
		clock-output-names = "tsip";
		reg = <0x02350000 0xb00>, <0x02350000 0x400>;
		reg-names = "control", "domain";
		domain-id = <0>;
	};

	clksrio: clksrio@235002c {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk1rstiso13>;
		clock-output-names = "srio";
		reg = <0x0235002c 0xb00>, <0x02350010 0x400>;
		reg-names = "control", "domain";
		domain-id = <4>;
	};

	clkhyperlink0: clkhyperlink0@2350030 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk12>;
		clock-output-names = "hyperlink-0";
		reg = <0x02350030 0xb00>, <0x02350014 0x400>;
		reg-names = "control", "domain";
		domain-id = <5>;
	};

	clkgem1: clkgem1@2350040 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk1>;
		clock-output-names = "gem1";
		reg = <0x02350040 0xb00>, <0x02350024 0x400>;
		reg-names = "control", "domain";
		domain-id = <9>;
	};

	clkgem2: clkgem2@2350044 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk1>;
		clock-output-names = "gem2";
		reg = <0x02350044 0xb00>, <0x02350028 0x400>;
		reg-names = "control", "domain";
		domain-id = <10>;
	};

	clkgem3: clkgem3@2350048 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk1>;
		clock-output-names = "gem3";
		reg = <0x02350048 0xb00>, <0x0235002c 0x400>;
		reg-names = "control", "domain";
		domain-id = <11>;
	};

	clkgem4: clkgem4@235004c {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk1>;
		clock-output-names = "gem4";
		reg = <0x0235004c 0xb00>, <0x02350030 0x400>;
		reg-names = "control", "domain";
		domain-id = <12>;
	};

	clkgem5: clkgem5@2350050 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk1>;
		clock-output-names = "gem5";
		reg = <0x02350050 0xb00>, <0x02350034 0x400>;
		reg-names = "control", "domain";
		domain-id = <13>;
	};

	clkgem6: clkgem6@2350054 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk1>;
		clock-output-names = "gem6";
		reg = <0x02350054 0xb00>, <0x02350038 0x400>;
		reg-names = "control", "domain";
		domain-id = <14>;
	};

	clkgem7: clkgem7@2350058 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk1>;
		clock-output-names = "gem7";
		reg = <0x02350058 0xb00>, <0x0235003c 0x400>;
		reg-names = "control", "domain";
		domain-id = <15>;
	};

	clkddr31: clkddr31@2350060 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "ddr3-1";
		reg = <0x02350060 0xb00>, <0x02350040 0x400>;
		reg-names = "control", "domain";
		domain-id = <16>;
	};

	clktac: clktac@2350064 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "tac";
		reg = <0x02350064 0xb00>, <0x02350044 0x400>;
		reg-names = "control", "domain";
		domain-id = <17>;
	};

	clkrac01: clkrac01@2350068 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "rac-01";
		reg = <0x02350068 0xb00>, <0x02350044 0x400>;
		reg-names = "control", "domain";
		domain-id = <17>;
	};

	clkrac23: clkrac23@235006c {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "rac-23";
		reg = <0x0235006c 0xb00>, <0x02350048 0x400>;
		reg-names = "control", "domain";
		domain-id = <18>;
	};

	clkfftc0: clkfftc0@2350070 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "fftc-0";
		reg = <0x02350070 0xb00>, <0x0235004c 0x400>;
		reg-names = "control", "domain";
		domain-id = <19>;
	};

	clkfftc1: clkfftc1@2350074 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "fftc-1";
		reg = <0x02350074 0xb00>, <0x0235004c 0x400>;
		reg-names = "control", "domain";
		domain-id = <19>;
	};

	clkfftc2: clkfftc2@2350078 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "fftc-2";
		reg = <0x02350078 0xb00>, <0x02350050 0x400>;
		reg-names = "control", "domain";
		domain-id = <20>;
	};

	clkfftc3: clkfftc3@235007c {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "fftc-3";
		reg = <0x0235007c 0xb00>, <0x02350050 0x400>;
		reg-names = "control", "domain";
		domain-id = <20>;
	};

	clkfftc4: clkfftc4@2350080 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "fftc-4";
		reg = <0x02350080 0xb00>, <0x02350050 0x400>;
		reg-names = "control", "domain";
		domain-id = <20>;
	};

	clkfftc5: clkfftc5@2350084 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "fftc-5";
		reg = <0x02350084 0xb00>, <0x02350050 0x400>;
		reg-names = "control", "domain";
		domain-id = <20>;
	};

	clkaif: clkaif@2350088 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "aif";
		reg = <0x02350088 0xb00>, <0x02350054 0x400>;
		reg-names = "control", "domain";
		domain-id = <21>;
	};

	clktcp3d0: clktcp3d0@235008c {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "tcp3d-0";
		reg = <0x0235008c 0xb00>, <0x02350058 0x400>;
		reg-names = "control", "domain";
		domain-id = <22>;
	};

	clktcp3d1: clktcp3d1@2350090 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "tcp3d-1";
		reg = <0x02350090 0xb00>, <0x02350058 0x400>;
		reg-names = "control", "domain";
		domain-id = <22>;
	};

	clktcp3d2: clktcp3d2@2350094 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "tcp3d-2";
		reg = <0x02350094 0xb00>, <0x0235005c 0x400>;
		reg-names = "control", "domain";
		domain-id = <23>;
	};

	clktcp3d3: clktcp3d3@2350098 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "tcp3d-3";
		reg = <0x02350098 0xb00>, <0x0235005c 0x400>;
		reg-names = "control", "domain";
		domain-id = <23>;
	};

	clkvcp0: clkvcp0@235009c {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-0";
		reg = <0x0235009c 0xb00>, <0x02350060 0x400>;
		reg-names = "control", "domain";
		domain-id = <24>;
	};

	clkvcp1: clkvcp1@23500a0 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-1";
		reg = <0x023500a0 0xb00>, <0x02350060 0x400>;
		reg-names = "control", "domain";
		domain-id = <24>;
	};

	clkvcp2: clkvcp2@23500a4 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-2";
		reg = <0x023500a4 0xb00>, <0x02350060 0x400>;
		reg-names = "control", "domain";
		domain-id = <24>;
	};

	clkvcp3: clkvcp3@23500a8 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-3";
		reg = <0x023500a8 0xb00>, <0x02350060 0x400>;
		reg-names = "control", "domain";
		domain-id = <24>;
	};

	clkvcp4: clkvcp4@23500ac {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-4";
		reg = <0x023500ac 0xb00>, <0x02350064 0x400>;
		reg-names = "control", "domain";
		domain-id = <25>;
	};

	clkvcp5: clkvcp5@23500b0 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-5";
		reg = <0x023500b0 0xb00>, <0x02350064 0x400>;
		reg-names = "control", "domain";
		domain-id = <25>;
	};

	clkvcp6: clkvcp6@23500b4 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-6";
		reg = <0x023500b4 0xb00>, <0x02350064 0x400>;
		reg-names = "control", "domain";
		domain-id = <25>;
	};

	clkvcp7: clkvcp7@23500b8 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-7";
		reg = <0x023500b8 0xb00>, <0x02350064 0x400>;
		reg-names = "control", "domain";
		domain-id = <25>;
	};

	clkbcp: clkbcp@23500bc {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "bcp";
		reg = <0x023500bc 0xb00>, <0x02350068 0x400>;
		reg-names = "control", "domain";
		domain-id = <26>;
	};

	clkdxb: clkdxb@23500c0 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "dxb";
		reg = <0x023500c0 0xb00>, <0x0235006c 0x400>;
		reg-names = "control", "domain";
		domain-id = <27>;
	};

	clkhyperlink1: clkhyperlink1@23500c4 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk12>;
		clock-output-names = "hyperlink-1";
		reg = <0x023500c4 0xb00>, <0x02350070 0x400>;
		reg-names = "control", "domain";
		domain-id = <28>;
	};

	clkxge: clkxge@23500c8 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "xge";
		reg = <0x023500c8 0xb00>, <0x02350074 0x400>;
		reg-names = "control", "domain";
		domain-id = <29>;
	};
};
