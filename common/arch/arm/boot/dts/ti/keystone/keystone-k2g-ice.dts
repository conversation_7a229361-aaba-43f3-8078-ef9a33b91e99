// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for K2G Industrial Communication Engine EVM
 *
 * Copyright (C) 2017 Texas Instruments Incorporated - http://www.ti.com/
 */
/dts-v1/;

#include "keystone-k2g.dtsi"
#include <dt-bindings/net/ti-dp83867.h>

/ {
	compatible = "ti,k2g-ice", "ti,k2g", "ti,keystone";
	model = "Texas Instruments K2G Industrial Communication EVM";

	memory@800000000 {
		device_type = "memory";
		reg = <0x00000008 0x00000000 0x00000000 0x20000000>;
	};

	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		dsp_common_memory: dsp-common-memory@81f800000 {
			compatible = "shared-dma-pool";
			reg = <0x00000008 0x1f800000 0x00000000 0x800000>;
			reusable;
			status = "okay";
		};
	};

	vmain: fixedregulator-vmain {
		compatible = "regulator-fixed";
		regulator-name = "vmain_fixed";
		regulator-min-microvolt = <24000000>;
		regulator-max-microvolt = <24000000>;
		regulator-always-on;
	};

	v5_0: fixedregulator-v5_0 {
		/* TPS54531 */
		compatible = "regulator-fixed";
		regulator-name = "v5_0_fixed";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&vmain>;
		regulator-always-on;
	};

	vdd_3v3: fixedregulator-vdd_3v3 {
		/* TLV62084 */
		compatible = "regulator-fixed";
		regulator-name = "vdd_3v3_fixed";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&v5_0>;
		regulator-always-on;
	};

	vdd_1v8: fixedregulator-vdd_1v8 {
		/* TLV62084 */
		compatible = "regulator-fixed";
		regulator-name = "vdd_1v8_fixed";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&v5_0>;
		regulator-always-on;
	};

	vdds_ddr: fixedregulator-vdds_ddr {
		/* TLV62080 */
		compatible = "regulator-fixed";
		regulator-name = "vdds_ddr_fixed";
		regulator-min-microvolt = <1350000>;
		regulator-max-microvolt = <1350000>;
		vin-supply = <&v5_0>;
		regulator-always-on;
	};

	vref_ddr: fixedregulator-vref_ddr {
		/* LP2996A */
		compatible = "regulator-fixed";
		regulator-name = "vref_ddr_fixed";
		regulator-min-microvolt = <675000>;
		regulator-max-microvolt = <675000>;
		vin-supply = <&vdd_3v3>;
		regulator-always-on;
	};

	vtt_ddr: fixedregulator-vtt_ddr {
		/* LP2996A */
		compatible = "regulator-fixed";
		regulator-name = "vtt_ddr_fixed";
		regulator-min-microvolt = <675000>;
		regulator-max-microvolt = <675000>;
		vin-supply = <&vdd_3v3>;
		regulator-always-on;
	};

	vdd_0v9: fixedregulator-vdd_0v9 {
		/* TPS62180 */
		compatible = "regulator-fixed";
		regulator-name = "vdd_0v9_fixed";
		regulator-min-microvolt = <900000>;
		regulator-max-microvolt = <900000>;
		vin-supply = <&v5_0>;
		regulator-always-on;
	};

	vddb: fixedregulator-vddb {
		/* TPS22945 */
		compatible = "regulator-fixed";
		regulator-name = "vddb_fixed";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;

		gpio = <&gpio1 53 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	gpio-decoder {
		compatible = "gpio-decoder";
		gpios = <&pca9536 3 GPIO_ACTIVE_HIGH>,
			<&pca9536 2 GPIO_ACTIVE_HIGH>,
			<&pca9536 1 GPIO_ACTIVE_HIGH>,
			<&pca9536 0 GPIO_ACTIVE_HIGH>;
		linux,axis = <0>; /* ABS_X */
		decoder-max-value = <9>;
	};

	leds1 {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&user_leds>;

		led0 {
			label = "status0:red:cpu0";
			gpios = <&gpio0 11 GPIO_ACTIVE_HIGH>;
			default-state = "off";
			linux,default-trigger = "cpu0";
		};

		led1 {
			label = "status0:green:usr";
			gpios = <&gpio0 12 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led2 {
			label = "status0:yellow:usr";
			gpios = <&gpio0 13 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led3 {
			label = "status1:red:mmc0";
			gpios = <&gpio0 14 GPIO_ACTIVE_HIGH>;
			default-state = "off";
			linux,default-trigger = "mmc0";
		};

		led4 {
			label = "status1:green:usr";
			gpios = <&gpio0 15 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led5 {
			label = "status1:yellow:usr";
			gpios = <&gpio0 16 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led6 {
			label = "status2:red:usr";
			gpios = <&gpio0 44 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led7 {
			label = "status2:green:usr";
			gpios = <&gpio0 43 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led8 {
			label = "status2:yellow:usr";
			gpios = <&gpio0 42 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led9 {
			label = "status3:red:usr";
			gpios = <&gpio0 41 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led10 {
			label = "status3:green:usr";
			gpios = <&gpio0 101 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led11 {
			label = "status3:yellow:usr";
			gpios = <&gpio0 102 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};

		led12 {
			label = "status4:green:heartbeat";
			gpios = <&gpio0 19 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
		};
	};
};

&k2g_pinctrl {
	uart0_pins: uart0-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x11cc) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0)	/* uart0_rxd.uart0_rxd */
			K2G_CORE_IOPAD(0x11d0) (BUFFER_CLASS_B | PIN_PULLDOWN | MUX_MODE0)	/* uart0_txd.uart0_txd */
		>;
	};

	qspi_pins: qspi-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x1204) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_clk.qspi_clk */
			K2G_CORE_IOPAD(0x1208) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_rclk.qspi_rclk */
			K2G_CORE_IOPAD(0x120c) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_d0.qspi_d0 */
			K2G_CORE_IOPAD(0x1210) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_d1.qspi_d1 */
			K2G_CORE_IOPAD(0x1214) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_d2.qspi_d2 */
			K2G_CORE_IOPAD(0x1218) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_d3.qspi_d3 */
			K2G_CORE_IOPAD(0x121c) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0) /* qspi_csn0.qspi_csn0 */
		>;
	};

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x10fc) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat3.mmc1_dat3 */
			K2G_CORE_IOPAD(0x1100) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat2.mmc1_dat2 */
			K2G_CORE_IOPAD(0x1104) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat1.mmc1_dat1 */
			K2G_CORE_IOPAD(0x1108) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_dat0.mmc1_dat0 */
			K2G_CORE_IOPAD(0x110c) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_clk.mmc1_clk */
			K2G_CORE_IOPAD(0x1110) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_cmd.mmc1_cmd */
			K2G_CORE_IOPAD(0x1114) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* mmc1_sdcd.gpio0_69 */
			K2G_CORE_IOPAD(0x1118) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_sdwp.mmc1_sdwp */
			K2G_CORE_IOPAD(0x111c) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* mmc1_pow.mmc1_pow */
		>;
	};

	i2c0_pins: i2c0-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x137c) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* i2c0_scl.i2c0_scl */
			K2G_CORE_IOPAD(0x1380) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* i2c0_sda.i2c0_sda */
		>;
	};

	i2c1_pins: i2c1-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x1384) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* i2c1_scl.i2c1_scl */
			K2G_CORE_IOPAD(0x1388) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE0)	/* i2c1_sda.i2c1_sda */
		>;
	};

	user_leds: user-leds-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x102c) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* gpmc_ad11.gpio0_11 */
			K2G_CORE_IOPAD(0x1030) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* gpmc_ad12.gpio0_12 */
			K2G_CORE_IOPAD(0x1034) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* gpmc_ad13.gpio0_13 */
			K2G_CORE_IOPAD(0x1038) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* gpmc_ad14.gpio0_14 */
			K2G_CORE_IOPAD(0x103c) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* gpmc_ad15.gpio0_15 */
			K2G_CORE_IOPAD(0x1040) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* gpmc_clk.gpio0_16 */
			K2G_CORE_IOPAD(0x104c) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* gpmc_wen.gpio0_19 */
			K2G_CORE_IOPAD(0x10b0) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* dss_data9.gpio0_44 */
			K2G_CORE_IOPAD(0x10ac) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* dss_data10.gpio0_43 */
			K2G_CORE_IOPAD(0x10a8) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* dss_data11.gpio0_42 */
			K2G_CORE_IOPAD(0x10a4) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* dss_data12.gpio0_41 */
			K2G_CORE_IOPAD(0x11b8) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* spi2_scsn0.gpio0_101 */
			K2G_CORE_IOPAD(0x11bc) (BUFFER_CLASS_B | PIN_PULLUP | MUX_MODE3)	/* spi2_scsn1.gpio0_102 */
		>;
	};

	emac_pins: emac-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x113c) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXD1.RGMII_RXD1 */
			K2G_CORE_IOPAD(0x1138) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXD2.RGMII_RXD2 */
			K2G_CORE_IOPAD(0x1134) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXD3.RGMII_RXD3 */
			K2G_CORE_IOPAD(0x1140) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXD0.RGMII_RXD0 */
			K2G_CORE_IOPAD(0x1178) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXD0.RGMII_TXD0 */
			K2G_CORE_IOPAD(0x1174) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXD1.RGMII_TXD1 */
			K2G_CORE_IOPAD(0x1170) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXD2.RGMII_TXD2 */
			K2G_CORE_IOPAD(0x116c) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXD3.RGMII_TXD3 */
			K2G_CORE_IOPAD(0x1154) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXCLK.RGMII_TXC */
			K2G_CORE_IOPAD(0x117c) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_TXEN.RGMII_TXCTL */
			K2G_CORE_IOPAD(0x1120) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXCLK.RGMII_RXC */
			K2G_CORE_IOPAD(0x1144) (BUFFER_CLASS_D | PULL_DISABLE | MUX_MODE1)	/* MII_RXDV.RGMII_RXCTL */
		>;
	};

	mdio_pins: mdio-pins {
		pinctrl-single,pins = <
			K2G_CORE_IOPAD(0x118c) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0)	/* MDIO_CLK.MDIO_CLK */
			K2G_CORE_IOPAD(0x1188) (BUFFER_CLASS_B | PULL_DISABLE | MUX_MODE0)	/* MDIO_DATA.MDIO_DATA */
		>;
	};
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pins>;
	status = "okay";
};

&dsp0 {
	memory-region = <&dsp_common_memory>;
	status = "okay";
};

&qspi {
	pinctrl-names = "default";
	pinctrl-0 = <&qspi_pins>;
	cdns,rclk-en;
	status = "okay";

	flash0: flash@0 {
		compatible = "s25fl256s1", "jedec,spi-nor";
		reg = <0>;
		spi-tx-bus-width = <1>;
		spi-rx-bus-width = <4>;
		spi-max-frequency = <96000000>;
		#address-cells = <1>;
		#size-cells = <1>;
		cdns,read-delay = <5>;
		cdns,tshsl-ns = <500>;
		cdns,tsd2d-ns = <500>;
		cdns,tchsh-ns = <119>;
		cdns,tslch-ns = <119>;

		partition@0 {
			label = "QSPI.u-boot";
			reg = <0x00000000 0x00100000>;
		};
		partition@1 {
			label = "QSPI.u-boot-env";
			reg = <0x00100000 0x00040000>;
		};
		partition@2 {
			label = "QSPI.skern";
			reg = <0x00140000 0x0040000>;
		};
		partition@3 {
			label = "QSPI.pmmc-firmware";
			reg = <0x00180000 0x0040000>;
		};
		partition@4 {
			label = "QSPI.kernel";
			reg = <0x001c0000 0x0800000>;
		};
		partition@5 {
			label = "QSPI.u-boot-spl-os";
			reg = <0x009c0000 0x0040000>;
		};
		partition@6 {
			label = "QSPI.file-system";
			reg = <0x00a00000 0x1600000>;
		};
	};
};

&gpio0 {
	status = "okay";
};

&gpio1 {
	status = "okay";
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
	vmmc-supply = <&vdd_3v3>;
	cd-gpios = <&gpio0 69 GPIO_ACTIVE_LOW>;
	status = "okay";
};

&i2c0 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c0_pins>;
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c256";
		reg = <0x50>;
	};
};

&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins>;
	status = "okay";
	clock-frequency = <400000>;

	pca9536: gpio@41 {
		compatible = "ti,pca9536";
		reg = <0x41>;
		gpio-controller;
		#gpio-cells = <2>;
		vcc-supply = <&vdd_3v3>;
	};
};

&qmss {
	status = "okay";
};

&knav_dmas {
	status = "okay";
};

&netcp {
	pinctrl-names = "default";
	pinctrl-0 = <&emac_pins>;
	status = "okay";
};

&mdio {
	pinctrl-names = "default";
	pinctrl-0 = <&mdio_pins>;
	status = "okay";
	ethphy0: ethernet-phy@0 {
		reg = <0>;
		ti,rx-internal-delay = <DP83867_RGMIIDCTL_2_25_NS>;
		ti,tx-internal-delay = <DP83867_RGMIIDCTL_250_PS>;
		ti,fifo-depth = <DP83867_PHYCR_FIFO_DEPTH_8_B_NIB>;
		ti,min-output-impedance;
		ti,dp83867-rxctrl-strap-quirk;
	};
};

&gbe0 {
	phy-handle = <&ethphy0>;
	phy-mode = "rgmii-id";
	status = "okay";
};
