// SPDX-License-Identifier: GPL-2.0
/*
 * Keystone 2 Kepler/Hawking soc specific device tree
 *
 * Copyright (C) 2013-2017 Texas Instruments Incorporated - http://www.ti.com/
 */

#include <dt-bindings/reset/ti-syscon.h>

/ {
	compatible = "ti,k2hk", "ti,keystone";
	model = "Texas Instruments Keystone 2 Kepler/Hawking SoC";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		interrupt-parent = <&gic>;

		cpu@0 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			reg = <0>;
		};

		cpu@1 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			reg = <1>;
		};

		cpu@2 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			reg = <2>;
		};

		cpu@3 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			reg = <3>;
		};
	};

	aliases {
		rproc0 = &dsp0;
		rproc1 = &dsp1;
		rproc2 = &dsp2;
		rproc3 = &dsp3;
		rproc4 = &dsp4;
		rproc5 = &dsp5;
		rproc6 = &dsp6;
		rproc7 = &dsp7;
	};
};

&soc0 {
		/include/ "keystone-k2hk-clocks.dtsi"

		msm_ram: sram@c000000 {
			compatible = "mmio-sram";
			reg = <0x0c000000 0x600000>;
			ranges = <0x0 0x0c000000 0x600000>;
			#address-cells = <1>;
			#size-cells = <1>;

			bm-sram@5f0000 {
				reg = <0x5f0000 0x8000>;
			};
		};

		psc: power-sleep-controller@2350000 {
			pscrst: reset-controller {
				compatible = "ti,k2hk-pscrst", "ti,syscon-reset";
				#reset-cells = <1>;

				ti,reset-bits = <
					0xa3c 8 0xa3c 8 0x83c 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 0: dsp0 */
					0xa40 8 0xa40 8 0x840 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 1: dsp1 */
					0xa44 8 0xa44 8 0x844 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 2: dsp2 */
					0xa48 8 0xa48 8 0x848 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 3: dsp3 */
					0xa4c 8 0xa4c 8 0x84c 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 4: dsp4 */
					0xa50 8 0xa50 8 0x850 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 5: dsp5 */
					0xa54 8 0xa54 8 0x854 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 6: dsp6 */
					0xa58 8 0xa58 8 0x858 8 (ASSERT_CLEAR | DEASSERT_SET | STATUS_CLEAR) /* 7: dsp7 */
				>;
			};
		};

		devctrl: device-state-control@2620000 {
			dspgpio0: keystone_dsp_gpio@240 {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x240 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x240>;
			};

			dspgpio1: keystone_dsp_gpio@244 {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x244 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x244>;
			};

			dspgpio2: keystone_dsp_gpio@248 {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x248 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x248>;
			};

			dspgpio3: keystone_dsp_gpio@24c {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x24c 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x24c>;
			};

			dspgpio4: keystone_dsp_gpio@250 {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x250 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x250>;
			};

			dspgpio5: keystone_dsp_gpio@254 {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x254 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x254>;
			};

			dspgpio6: keystone_dsp_gpio@258 {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x258 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x258>;
			};

			dspgpio7: keystone_dsp_gpio@25c {
				compatible = "ti,keystone-dsp-gpio";
				reg = <0x25c 0x4>;
				gpio-controller;
				#gpio-cells = <2>;
				gpio,syscon-dev = <&devctrl 0x25c>;
			};
		};

		dsp0: dsp@10800000 {
			compatible = "ti,k2hk-dsp";
			reg = <0x10800000 0x00100000>,
			      <0x10e00000 0x00008000>,
			      <0x10f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem0>;
			ti,syscon-dev = <&devctrl 0x40>;
			resets = <&pscrst 0>;
			interrupt-parent = <&kirq0>;
			interrupts = <0 8>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio0 27 0>;
			status = "disabled";
		};

		dsp1: dsp@11800000 {
			compatible = "ti,k2hk-dsp";
			reg = <0x11800000 0x00100000>,
			      <0x11e00000 0x00008000>,
			      <0x11f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem1>;
			ti,syscon-dev = <&devctrl 0x44>;
			resets = <&pscrst 1>;
			interrupt-parent = <&kirq0>;
			interrupts = <1 9>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio1 27 0>;
			status = "disabled";
		};

		dsp2: dsp@12800000 {
			compatible = "ti,k2hk-dsp";
			reg = <0x12800000 0x00100000>,
			      <0x12e00000 0x00008000>,
			      <0x12f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem2>;
			ti,syscon-dev = <&devctrl 0x48>;
			resets = <&pscrst 2>;
			interrupt-parent = <&kirq0>;
			interrupts = <2 10>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio2 27 0>;
			status = "disabled";
		};

		dsp3: dsp@13800000 {
			compatible = "ti,k2hk-dsp";
			reg = <0x13800000 0x00100000>,
			      <0x13e00000 0x00008000>,
			      <0x13f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem3>;
			ti,syscon-dev = <&devctrl 0x4c>;
			resets = <&pscrst 3>;
			interrupt-parent = <&kirq0>;
			interrupts = <3 11>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio3 27 0>;
			status = "disabled";
		};

		dsp4: dsp@14800000 {
			compatible = "ti,k2hk-dsp";
			reg = <0x14800000 0x00100000>,
			      <0x14e00000 0x00008000>,
			      <0x14f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem4>;
			ti,syscon-dev = <&devctrl 0x50>;
			resets = <&pscrst 4>;
			interrupt-parent = <&kirq0>;
			interrupts = <4 12>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio4 27 0>;
			status = "disabled";
		};

		dsp5: dsp@15800000 {
			compatible = "ti,k2hk-dsp";
			reg = <0x15800000 0x00100000>,
			      <0x15e00000 0x00008000>,
			      <0x15f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem5>;
			ti,syscon-dev = <&devctrl 0x54>;
			resets = <&pscrst 5>;
			interrupt-parent = <&kirq0>;
			interrupts = <5 13>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio5 27 0>;
			status = "disabled";
		};

		dsp6: dsp@16800000 {
			compatible = "ti,k2hk-dsp";
			reg = <0x16800000 0x00100000>,
			      <0x16e00000 0x00008000>,
			      <0x16f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem6>;
			ti,syscon-dev = <&devctrl 0x58>;
			resets = <&pscrst 6>;
			interrupt-parent = <&kirq0>;
			interrupts = <6 14>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio6 27 0>;
			status = "disabled";
		};

		dsp7: dsp@17800000 {
			compatible = "ti,k2hk-dsp";
			reg = <0x17800000 0x00100000>,
			      <0x17e00000 0x00008000>,
			      <0x17f00000 0x00008000>;
			reg-names = "l2sram", "l1pram", "l1dram";
			clocks = <&clkgem7>;
			ti,syscon-dev = <&devctrl 0x5c>;
			resets = <&pscrst 7>;
			interrupt-parent = <&kirq0>;
			interrupts = <7 15>;
			interrupt-names = "vring", "exception";
			kick-gpios = <&dspgpio7 27 0>;
			status = "disabled";
		};

		mdio: mdio@2090300 {
			compatible = "ti,keystone_mdio", "ti,davinci_mdio";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x02090300 0x100>;
			status = "disabled";
			clocks = <&clkcpgmac>;
			clock-names = "fck";
			bus_freq = <2500000>;
		};
		/include/ "keystone-k2hk-netcp.dtsi"
};
