// SPDX-License-Identifier: GPL-2.0
/*
 * Keystone 2 lamarr SoC clock nodes
 *
 * Copyright (C) 2013-2017 Texas Instruments Incorporated - http://www.ti.com/
 */

clocks {
	armpllclk: armpllclk@2620370 {
		#clock-cells = <0>;
		compatible = "ti,keystone,pll-clock";
		clocks = <&refclksys>;
		clock-output-names = "arm-pll-clk";
		reg = <0x02620370 4>;
		reg-names = "control";
	};

	mainpllclk: mainpllclk@2310110 {
		#clock-cells = <0>;
		compatible = "ti,keystone,main-pll-clock";
		clocks = <&refclksys>;
		reg = <0x02620350 4>, <0x02310110 4>, <0x02310108 4>;
		reg-names = "control", "multiplier", "post-divider";
	};

	papllclk: papllclk@2620358 {
		#clock-cells = <0>;
		compatible = "ti,keystone,pll-clock";
		clocks = <&refclksys>;
		clock-output-names = "papllclk";
		reg = <0x02620358 4>;
		reg-names = "control";
	};

	ddr3apllclk: ddr3apllclk@2620360 {
		#clock-cells = <0>;
		compatible = "ti,keystone,pll-clock";
		clocks = <&refclksys>;
		clock-output-names = "ddr-3a-pll-clk";
		reg = <0x02620360 4>;
		reg-names = "control";
	};

	clkdfeiqnsys: clkdfeiqnsys@2350004 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk12>;
		clock-output-names = "dfe";
		reg-names = "control", "domain";
		reg = <0x02350004 0xb00>, <0x02350000 0x400>;
		domain-id = <0>;
	};

	clkpcie1: clkpcie1@235002c {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk12>;
		clock-output-names = "pcie";
		reg = <0x0235002c 0xb00>, <0x02350000 0x400>;
		reg-names = "control", "domain";
		domain-id = <4>;
	};

	clkgem1: clkgem1@2350040 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk1>;
		clock-output-names = "gem1";
		reg = <0x02350040 0xb00>, <0x02350024 0x400>;
		reg-names = "control", "domain";
		domain-id = <9>;
	};

	clkgem2: clkgem2@2350044 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk1>;
		clock-output-names = "gem2";
		reg = <0x02350044 0xb00>, <0x02350028 0x400>;
		reg-names = "control", "domain";
		domain-id = <10>;
	};

	clkgem3: clkgem3@2350048 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk1>;
		clock-output-names = "gem3";
		reg = <0x02350048 0xb00>, <0x0235002c 0x400>;
		reg-names = "control", "domain";
		domain-id = <11>;
	};

	clktac: clktac@2350064 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "tac";
		reg = <0x02350064 0xb00>, <0x02350044 0x400>;
		reg-names = "control", "domain";
		domain-id = <17>;
	};

	clkrac: clkrac@2350068 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "rac";
		reg = <0x02350068 0xb00>, <0x02350044 0x400>;
		reg-names = "control", "domain";
		domain-id = <17>;
	};

	clkdfepd0: clkdfepd0@235006c {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "dfe-pd0";
		reg = <0x0235006c 0xb00>, <0x02350044 0x400>;
		reg-names = "control", "domain";
		domain-id = <18>;
	};

	clkfftc0: clkfftc0@2350070 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "fftc-0";
		reg = <0x02350070 0xb00>, <0x0235004c 0x400>;
		reg-names = "control", "domain";
		domain-id = <19>;
	};

	clkosr: clkosr@2350088 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "osr";
		reg = <0x02350088 0xb00>, <0x0235004c 0x400>;
		reg-names = "control", "domain";
		domain-id = <21>;
	};

	clktcp3d0: clktcp3d0@235008c {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "tcp3d-0";
		reg = <0x0235008c 0xb00>, <0x02350058 0x400>;
		reg-names = "control", "domain";
		domain-id = <22>;
	};

	clktcp3d1: clktcp3d1@2350094 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "tcp3d-1";
		reg = <0x02350094 0xb00>, <0x02350058 0x400>;
		reg-names = "control", "domain";
		domain-id = <23>;
	};

	clkvcp0: clkvcp0@235009c {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-0";
		reg = <0x0235009c 0xb00>, <0x02350060 0x400>;
		reg-names = "control", "domain";
		domain-id = <24>;
	};

	clkvcp1: clkvcp1@23500a0 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-1";
		reg = <0x023500a0 0xb00>, <0x02350060 0x400>;
		reg-names = "control", "domain";
		domain-id = <24>;
	};

	clkvcp2: clkvcp2@23500a4 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-2";
		reg = <0x023500a4 0xb00>, <0x02350060 0x400>;
		reg-names = "control", "domain";
		domain-id = <24>;
	};

	clkvcp3: clkvcp3@23500a8 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "vcp-3";
		reg = <0x023500a8 0xb00>, <0x02350060 0x400>;
		reg-names = "control", "domain";
		domain-id = <24>;
	};

	clkbcp: clkbcp@23500bc {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "bcp";
		reg = <0x023500bc 0xb00>, <0x02350068 0x400>;
		reg-names = "control", "domain";
		domain-id = <26>;
	};

	clkdfepd1: clkdfepd1@23500c0 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "dfe-pd1";
		reg = <0x023500c0 0xb00>, <0x02350044 0x400>;
		reg-names = "control", "domain";
		domain-id = <27>;
	};

	clkfftc1: clkfftc1@23500c4 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "fftc-1";
		reg = <0x023500c4 0xb00>, <0x023504c0 0x400>;
		reg-names = "control", "domain";
		domain-id = <28>;
	};

	clkiqnail: clkiqnail@23500c8 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&chipclk13>;
		clock-output-names = "iqn-ail";
		reg = <0x023500c8 0xb00>, <0x0235004c 0x400>;
		reg-names = "control", "domain";
		domain-id = <29>;
	};

	clkuart2: clkuart2@2350000 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&clkmodrst0>;
		clock-output-names = "uart2";
		reg = <0x02350000 0xb00>, <0x02350000 0x400>;
		reg-names = "control", "domain";
		domain-id = <0>;
	};

	clkuart3: clkuart3@2350000 {
		#clock-cells = <0>;
		compatible = "ti,keystone,psc-clock";
		clocks = <&clkmodrst0>;
		clock-output-names = "uart3";
		reg = <0x02350000 0xb00>, <0x02350000 0x400>;
		reg-names = "control", "domain";
		domain-id = <0>;
	};
};
