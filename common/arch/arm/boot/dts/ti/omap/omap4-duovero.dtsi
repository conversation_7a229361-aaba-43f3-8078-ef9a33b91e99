// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 <PERSON><PERSON><PERSON>, EPFL Mobots group
 */

#include "omap443x.dtsi"
#include "omap4-mcpdm.dtsi"

/ {
	model = "Gumstix Duovero";
	compatible = "gumstix,omap4-duovero", "ti,omap4430", "ti,omap4";

	chosen {
		stdout-path = &uart3;
	};

	memory@******** {
		device_type = "memory";
		reg = <0x******** 0x40000000>; /* 1 GB */
	};

	sound {
		compatible = "ti,abe-twl6040";
		ti,model = "DuoVero";

		ti,mclk-freq = <********>;

		ti,mcpdm = <&mcpdm>;

		ti,twl6040 = <&twl6040>;

		/* Audio routing */
		ti,audio-routing =
			"Headset Stereophone", "HSOL",
			"Headset Stereophone", "HSOR",
			"HSMIC", "Headset Mic",
			"Headset Mic", "Headset Mic Bias";
	};

	/* HS USB Host PHY on PORT 1 */
	hsusb1_phy: hsusb1_phy {
		compatible = "usb-nop-xceiv";
		reset-gpios = <&gpio2 30 GPIO_ACTIVE_LOW>;	/* gpio_62 */
		#phy-cells = <0>;

		pinctrl-names = "default";
		pinctrl-0 = <&hsusb1phy_pins>;

		clocks = <&auxclk3_ck>;
		clock-names = "main_clk";
		clock-frequency = <********>;
	};

	/* regulator for w2cbw0015 on sdio5 */
	w2cbw0015_vmmc: w2cbw0015_vmmc {
		pinctrl-names = "default";
		pinctrl-0 = <&w2cbw0015_pins>;
		compatible = "regulator-fixed";
		regulator-name = "w2cbw0015";
		regulator-min-microvolt = <3000000>;
		regulator-max-microvolt = <3000000>;
		gpio = <&gpio2 11 GPIO_ACTIVE_LOW>;		/* gpio_43 */
		startup-delay-us = <70000>;
		enable-active-high;
		regulator-boot-on;
	};
};

&omap4_pmx_core {
	pinctrl-names = "default";
	pinctrl-0 = <
			&twl6040_pins
			&hsusbb1_pins
	>;

	twl6040_pins: twl6040-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x166, PIN_OUTPUT | MUX_MODE3)		/* usbb2_ulpitll_nxt.gpio_160 */
			OMAP4_IOPAD(0x1a0, PIN_INPUT | MUX_MODE0)		/* sys_nirq2.sys_nirq2 */
		>;
	};

	mcbsp1_pins: mcbsp1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x0fe, PIN_INPUT | MUX_MODE0)		/* abe_mcbsp1_clkx.abe_mcbsp1_clkx */
			OMAP4_IOPAD(0x100, PIN_INPUT_PULLDOWN | MUX_MODE0)	/* abe_mcbsp1_dr.abe_mcbsp1_dr */
			OMAP4_IOPAD(0x102, PIN_OUTPUT_PULLDOWN | MUX_MODE0)	/* abe_mcbsp1_dx.abe_mcbsp1_dx */
			OMAP4_IOPAD(0x104, PIN_INPUT | MUX_MODE0)		/* abe_mcbsp1_fsx.abe_mcbsp1_fsx */
		>;
	};

	hsusbb1_pins: hsusbb1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x0c2, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_clk.usbb1_ulpiphy_clk */
			OMAP4_IOPAD(0x0c4, PIN_OUTPUT | MUX_MODE4)		/* usbb1_ulpitll_stp.usbb1_ulpiphy_stp */
			OMAP4_IOPAD(0x0c6, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dir.usbb1_ulpiphy_dir */
			OMAP4_IOPAD(0x0c8, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_nxt.usbb1_ulpiphy_nxt */
			OMAP4_IOPAD(0x0ca, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat0.usbb1_ulpiphy_dat0 */
			OMAP4_IOPAD(0x0cc, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat1.usbb1_ulpiphy_dat1 */
			OMAP4_IOPAD(0x0ce, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat2.usbb1_ulpiphy_dat2 */
			OMAP4_IOPAD(0x0d0, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat3.usbb1_ulpiphy_dat3 */
			OMAP4_IOPAD(0x0d2, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat4.usbb1_ulpiphy_dat4 */
			OMAP4_IOPAD(0x0d4, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat5.usbb1_ulpiphy_dat5 */
			OMAP4_IOPAD(0x0d6, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat6.usbb1_ulpiphy_dat6 */
			OMAP4_IOPAD(0x0d8, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat7.usbb1_ulpiphy_dat7 */
		>;
	};

	hsusb1phy_pins: hsusb1phy-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x08c, PIN_OUTPUT | MUX_MODE3)		/* gpmc_wait1.gpio_62 */
		>;
	};

	w2cbw0015_pins: w2cbw0015-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x066, PIN_OUTPUT | MUX_MODE3)		/* gpmc_a19.gpio_43 */
			OMAP4_IOPAD(0x07a, PIN_INPUT | MUX_MODE3)		/* gpmc_ncs3.gpio_53 */
		>;
	};

	i2c1_pins: i2c1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x122, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c1_scl */
			OMAP4_IOPAD(0x124, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c1_sda */
		>;
	};

	i2c4_pins: i2c4-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x12e, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c4_scl */
			OMAP4_IOPAD(0x130, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c4_sda */
		>;
	};

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x0e2, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_clk */
			OMAP4_IOPAD(0x0e4, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmcc1_cmd */
			OMAP4_IOPAD(0x0e6, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmcc1_dat0 */
			OMAP4_IOPAD(0x0e8, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat1 */
			OMAP4_IOPAD(0x0ea, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat2 */
			OMAP4_IOPAD(0x0ec, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat3 */
		>;
	};

	mmc5_pins: mmc5-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x148, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_clk */
			OMAP4_IOPAD(0x14a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmcc5_cmd */
			OMAP4_IOPAD(0x14c, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmcc5_dat0 */
			OMAP4_IOPAD(0x14e, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_dat1 */
			OMAP4_IOPAD(0x150, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_dat2 */
			OMAP4_IOPAD(0x152, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_dat3 */
		>;
	};
};

/* PMIC */
&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins>;

	clock-frequency = <400000>;

	twl: twl@48 {
		reg = <0x48>;
		interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;		/* IRQ_SYS_1N cascaded to gic */
	};

	twl6040: twl@4b {
		compatible = "ti,twl6040";
		#clock-cells = <0>;
		reg = <0x4b>;
		interrupts = <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>;		/* IRQ_SYS_2N cascaded to gic */
		ti,audpwron-gpio = <&gpio6 0 GPIO_ACTIVE_HIGH>;		/* gpio_160 */

		vio-supply = <&v1v8>;
		v2v1-supply = <&v2v1>;
		enable-active-high;
	};
};

#include "twl6030.dtsi"
#include "twl6030_omap4.dtsi"

/* on-board bluetooth / WiFi module */
&i2c4 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4_pins>;

	clock-frequency = <400000>;
};

&mcbsp1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mcbsp1_pins>;
	status = "okay";
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;

	vmmc-supply = <&vmmc>;
	ti,bus-width = <4>;
	ti,non-removable;		/* FIXME: use PMIC_MMC detect */
};

&mmc2 {
	status = "disabled";
};

/* mmc3 is available to the expansion board */

&mmc4 {
	status = "disabled";
};

/* on-board WiFi module */
&mmc5 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc5_pins>;

	vmmc-supply = <&w2cbw0015_vmmc>;
	ti,bus-width = <4>;
	ti,non-removable;
	cap-power-off-card;
	keep-power-in-suspend;
};

&twl_usb_comparator {
	usb-supply = <&vusb>;
};

&usb_otg_hs {
	interface-type = <1>;
	mode = <3>;
	power = <50>;
};

&usbhshost {
	port1-mode = "ehci-phy";
};

&usbhsehci {
	phys = <&hsusb1_phy>;
};

