// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2013 Texas Instruments Incorporated - https://www.ti.com/
 */
/dts-v1/;

#include "omap34xx.dtsi"

/ {
	model = "TI OMAP3430 SDP";
	compatible = "ti,omap3430-sdp", "ti,omap3430", "ti,omap3";

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x10000000>; /* 256 MB */
	};
};

&i2c1 {
	clock-frequency = <2600000>;

	twl: twl@48 {
		reg = <0x48>;
		interrupts = <7>; /* SYS_NIRQ cascaded to intc */
	};
};

#include "twl4030.dtsi"
#include "twl4030_omap3.dtsi"

&mmc1 {
	vmmc-supply = <&vmmc1>;
	vqmmc-supply = <&vsim>;
	/*
	 * S6-3 must be in ON position for 8 bit mode to function
	 * Else, use 4 bit mode
	 */
	bus-width = <8>;
};

&mmc2 {
	status = "disabled";
};

&mmc3 {
	status = "disabled";
};

&gpmc {
	ranges = <0 0 0x10000000 0x08000000>,
		 <1 0 0x28000000 0x1000000>,	/* CS1: 16MB for NAND */
		 <2 0 0x20000000 0x1000000>;	/* CS2: 16MB for OneNAND */

	nor@0,0 {
		compatible = "cfi-flash";
		linux,mtd-name = "intel,pf48f6000m0y1be";
		#address-cells = <1>;
		#size-cells = <1>;
		reg = <0 0 0x08000000>;
		bank-width = <2>;

		gpmc,mux-add-data = <2>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <186>;
		gpmc,cs-wr-off-ns = <186>;
		gpmc,adv-on-ns = <12>;
		gpmc,adv-rd-off-ns = <48>;
		gpmc,adv-wr-off-ns = <48>;
		gpmc,oe-on-ns = <54>;
		gpmc,oe-off-ns = <168>;
		gpmc,we-on-ns = <54>;
		gpmc,we-off-ns = <168>;
		gpmc,rd-cycle-ns = <186>;
		gpmc,wr-cycle-ns = <186>;
		gpmc,access-ns = <114>;
		gpmc,page-burst-access-ns = <6>;
		gpmc,bus-turnaround-ns = <12>;
		gpmc,cycle2cycle-delay-ns = <18>;
		gpmc,wr-data-mux-bus-ns = <90>;
		gpmc,wr-access-ns = <186>;
		gpmc,cycle2cycle-samecsen;
		gpmc,cycle2cycle-diffcsen;

		partition@0 {
			label = "bootloader-nor";
			reg = <0 0x40000>;
		};
		partition@40000 {
			label = "params-nor";
			reg = <0x40000 0x40000>;
		};
		partition@80000 {
			label = "kernel-nor";
			reg = <0x80000 0x200000>;
		};
		partition@280000 {
			label = "filesystem-nor";
			reg = <0x240000 0x7d80000>;
		};
	};

	nand@1,0 {
		compatible = "ti,omap2-nand";
		reg = <1 0 4>; /* CS1, offset 0, IO size 4 */
		interrupt-parent = <&gpmc>;
		interrupts = <0 IRQ_TYPE_NONE>, /* fifoevent */
			     <1 IRQ_TYPE_NONE>;	/* termcount */
		linux,mtd-name = "micron,mt29f1g08abb";
		#address-cells = <1>;
		#size-cells = <1>;
		ti,nand-ecc-opt = "sw";
		nand-bus-width = <8>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <36>;
		gpmc,cs-wr-off-ns = <36>;
		gpmc,adv-on-ns = <6>;
		gpmc,adv-rd-off-ns = <24>;
		gpmc,adv-wr-off-ns = <36>;
		gpmc,oe-on-ns = <6>;
		gpmc,oe-off-ns = <48>;
		gpmc,we-on-ns = <6>;
		gpmc,we-off-ns = <30>;
		gpmc,rd-cycle-ns = <72>;
		gpmc,wr-cycle-ns = <72>;
		gpmc,access-ns = <54>;
		gpmc,wr-access-ns = <30>;

		partition@0 {
			label = "xloader-nand";
			reg = <0 0x80000>;
		};
		partition@80000 {
			label = "bootloader-nand";
			reg = <0x80000 0x140000>;
		};
		partition@1c0000 {
			label = "params-nand";
			reg = <0x1c0000 0xc0000>;
		};
		partition@280000 {
			label = "kernel-nand";
			reg = <0x280000 0x500000>;
		};
		partition@780000 {
			label = "filesystem-nand";
			reg = <0x780000 0x7880000>;
		};
	};

	onenand@2,0 {
		linux,mtd-name = "samsung,kfm2g16q2m-deb8";
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "ti,omap2-onenand";
		reg = <2 0 0x20000>;	/* CS2, offset 0, IO size 4 */

		gpmc,device-width = <2>;
		gpmc,mux-add-data = <2>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <84>;
		gpmc,cs-wr-off-ns = <72>;
		gpmc,adv-on-ns = <0>;
		gpmc,adv-rd-off-ns = <18>;
		gpmc,adv-wr-off-ns = <18>;
		gpmc,oe-on-ns = <30>;
		gpmc,oe-off-ns = <84>;
		gpmc,we-on-ns = <0>;
		gpmc,we-off-ns = <42>;
		gpmc,rd-cycle-ns = <108>;
		gpmc,wr-cycle-ns = <96>;
		gpmc,access-ns = <78>;
		gpmc,wr-data-mux-bus-ns = <30>;

		partition@0 {
			label = "xloader-onenand";
			reg = <0 0x80000>;
		};
		partition@80000 {
			label = "bootloader-onenand";
			reg = <0x80000 0x40000>;
		};
		partition@c0000 {
			label = "params-onenand";
			reg = <0xc0000 0x20000>;
		};
		partition@e0000 {
			label = "kernel-onenand";
			reg = <0xe0000 0x200000>;
		};
		partition@2e0000 {
			label = "filesystem-onenand";
			reg = <0x2e0000 0xfd20000>;
		};
	};
};
