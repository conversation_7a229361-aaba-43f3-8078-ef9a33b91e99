// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2012 <PERSON><PERSON><PERSON>, EPFL Mobots group
 */

/*
 * The Gumstix Overo must be combined with an expansion board.
 */

/ {

	memory@0 {
		device_type = "memory";
		reg = <0 0>;
	};

	led-controller {
		compatible = "pwm-leds";

		led-1 {
			label = "overo:blue:COM";
			pwms = <&twl_pwmled 1 7812500>;
			max-brightness = <127>;
			linux,default-trigger = "mmc0";
		};
	};

	sound {
		compatible = "ti,omap-twl4030";
		ti,model = "overo";

		ti,mcbsp = <&mcbsp2>;
	};

	/* HS USB Port 2 Power */
	hsusb2_power: hsusb2_power_reg {
		compatible = "regulator-fixed";
		regulator-name = "hsusb2_vbus";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&gpio6 8 GPIO_ACTIVE_HIGH>;		/* gpio_168: vbus enable */
		startup-delay-us = <70000>;
		enable-active-high;
	};

	/* HS USB Host PHY on PORT 2 */
	hsusb2_phy: hsusb2-phy-pins {
		compatible = "usb-nop-xceiv";
		reset-gpios = <&gpio6 23 GPIO_ACTIVE_LOW>;	/* gpio_183 */
		vcc-supply = <&hsusb2_power>;
		#phy-cells = <0>;
	};

	/* Regulator to trigger the nPoweron signal of the Wifi module */
	w3cbw003c_npoweron: regulator-w3cbw003c-npoweron {
		compatible = "regulator-fixed";
		regulator-name = "regulator-w3cbw003c-npoweron";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio2 22 GPIO_ACTIVE_HIGH>;		/* gpio_54: nPoweron */
		enable-active-high;
	};

	/* Regulator to trigger the nReset signal of the Wifi module */
	w3cbw003c_wifi_nreset: regulator-w3cbw003c-wifi-nreset {
		pinctrl-names = "default";
		pinctrl-0 = <&w3cbw003c_pins &w3cbw003c_2_pins>;
		compatible = "regulator-fixed";
		regulator-name = "regulator-w3cbw003c-wifi-nreset";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio1 16 GPIO_ACTIVE_HIGH>;		/* gpio_16: WiFi nReset */
		startup-delay-us = <10000>;
	};
};

&omap3_pmx_core {
	pinctrl-names = "default";
	pinctrl-0 = <
			&hsusb2_pins
	>;

	uart2_pins: uart2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x216c, PIN_INPUT | MUX_MODE1)	/* mcbsp3_dx.uart2_cts */
			OMAP3_CORE1_IOPAD(0x216e, PIN_OUTPUT | MUX_MODE1)	/* mcbsp3_dr.uart2_rts */
			OMAP3_CORE1_IOPAD(0x2170, PIN_OUTPUT | MUX_MODE1)	/* mcbsp3_clk.uart2_tx */
			OMAP3_CORE1_IOPAD(0x2172, PIN_INPUT | MUX_MODE1)	/* mcbsp3_fsx.uart2_rx */
		>;
	};

	i2c1_pins: i2c1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21ba, PIN_INPUT | MUX_MODE0)		/* i2c1_scl.i2c1_scl */
			OMAP3_CORE1_IOPAD(0x21bc, PIN_INPUT | MUX_MODE0)		/* i2c1_sda.i2c1_sda */
		>;
	};

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2144, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_clk.sdmmc1_clk */
			OMAP3_CORE1_IOPAD(0x2146, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_cmd.sdmmc1_cmd */
			OMAP3_CORE1_IOPAD(0x2148, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_dat0.sdmmc1_dat0 */
			OMAP3_CORE1_IOPAD(0x214a, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_dat1.sdmmc1_dat1 */
			OMAP3_CORE1_IOPAD(0x214c, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_dat2.sdmmc1_dat2 */
			OMAP3_CORE1_IOPAD(0x214e, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_dat3.sdmmc1_dat3 */
		>;
	};

	mmc2_pins: mmc2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2158, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_clk.sdmmc2_clk */
			OMAP3_CORE1_IOPAD(0x215a, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_cmd.sdmmc2_cmd */
			OMAP3_CORE1_IOPAD(0x215c, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_dat0.sdmmc2_dat0 */
			OMAP3_CORE1_IOPAD(0x215e, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_dat1.sdmmc2_dat1 */
			OMAP3_CORE1_IOPAD(0x2160, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_dat2.sdmmc2_dat2 */
			OMAP3_CORE1_IOPAD(0x2162, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_dat3.sdmmc2_dat3 */
		>;
	};

	/* WiFi/BT combo */
	w3cbw003c_pins: w3cbw003c-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20b4, PIN_OUTPUT | MUX_MODE4)		/* gpmc_ncs3.gpio_54 */
			OMAP3_CORE1_IOPAD(0x219c, PIN_OUTPUT | MUX_MODE4)		/* uart3_rts_sd.gpio_164 */
		>;
	};

	hsusb2_pins: hsusb2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21d4, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi1_cs3.hsusb2_data2 */
			OMAP3_CORE1_IOPAD(0x21d6, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_clk.hsusb2_data7 */
			OMAP3_CORE1_IOPAD(0x21d8, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_simo.hsusb2_data4 */
			OMAP3_CORE1_IOPAD(0x21da, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_somi.hsusb2_data5 */
			OMAP3_CORE1_IOPAD(0x21dc, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_cs0.hsusb2_data6 */
			OMAP3_CORE1_IOPAD(0x21de, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_cs1.hsusb2_data3 */
			OMAP3_CORE1_IOPAD(0x21be, PIN_OUTPUT | MUX_MODE4)		/* i2c2_scl.gpio_168 */
			OMAP3_CORE1_IOPAD(0x21c0, PIN_OUTPUT | MUX_MODE4)		/* i2c2_sda.gpio_183 */
		>;
	};
};

&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins>;
	clock-frequency = <2600000>;

	twl: twl@48 {
		reg = <0x48>;
		interrupts = <7>; /* SYS_NIRQ cascaded to intc */
		interrupt-parent = <&intc>;

		twl_audio: audio {
			compatible = "ti,twl4030-audio";
			codec {
			};
		};
	};
};

#include "twl4030.dtsi"
#include "twl4030_omap3.dtsi"

/* i2c2 pins are used for gpio */
&i2c2 {
	status = "disabled";
};

/* on board microSD slot */
&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
	vmmc-supply = <&vmmc1>;
	bus-width = <4>;
};

/* optional on board WiFi */
&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_pins>;
	vmmc-supply = <&w3cbw003c_npoweron>;
	vqmmc-supply = <&w3cbw003c_wifi_nreset>;
	bus-width = <4>;
	cap-sdio-irq;
	non-removable;
};

&twl_gpio {
	ti,use-leds;
};

&usb_otg_hs {
	interface-type = <0>;
	usb-phy = <&usb2_phy>;
	phys = <&usb2_phy>;
	phy-names = "usb2-phy";
	mode = <3>;
	power = <50>;
};

&usbhshost {
	port2-mode = "ehci-phy";
};

&usbhsehci {
	phys = <0 &hsusb2_phy>;
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pins>;
};

&mcbsp2 {
	status = "okay";
};

&gpmc {
	ranges = <0 0 0x30000000 0x1000000>,	/* CS0 */
		 <4 0 0x2b000000 0x1000000>,	/* CS4 */
		 <5 0 0x2c000000 0x1000000>;	/* CS5 */

	nand@0,0 {
		compatible = "ti,omap2-nand";
		linux,mtd-name = "micron,mt29c4g96maz";
		reg = <0 0 4>;	/* CS0, offset 0, IO size 4 */
		interrupt-parent = <&gpmc>;
		interrupts = <0 IRQ_TYPE_NONE>, /* fifoevent */
			     <1 IRQ_TYPE_NONE>;	/* termcount */
		nand-bus-width = <16>;
		gpmc,device-width = <2>;
		ti,nand-ecc-opt = "bch8";

		gpmc,sync-clk-ps = <0>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <44>;
		gpmc,cs-wr-off-ns = <44>;
		gpmc,adv-on-ns = <6>;
		gpmc,adv-rd-off-ns = <34>;
		gpmc,adv-wr-off-ns = <44>;
		gpmc,we-off-ns = <40>;
		gpmc,oe-off-ns = <54>;
		gpmc,access-ns = <64>;
		gpmc,rd-cycle-ns = <82>;
		gpmc,wr-cycle-ns = <82>;
		gpmc,wr-access-ns = <40>;
		gpmc,wr-data-mux-bus-ns = <0>;

		#address-cells = <1>;
		#size-cells = <1>;

		partition@0 {
			label = "SPL";
			reg = <0 0x80000>; /* 512KiB */
		};
		partition@80000 {
			label = "U-Boot";
			reg = <0x80000 0x1C0000>; /* 1792KiB */
		};
		partition@1c0000 {
			label = "Environment";
			reg = <0x240000 0x40000>; /* 256KiB */
		};
		partition@280000 {
			label = "Kernel";
			reg = <0x280000 0x800000>; /* 8192KiB */
		};
		partition@780000 {
			label = "Filesystem";
			reg = <0xA80000 0>;
			/* HACK: MTDPART_SIZ_FULL=0 so fill to end */
		};
	};
};
