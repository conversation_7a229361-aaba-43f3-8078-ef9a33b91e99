// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 * Copyright (C) 2012 Variscite Ltd. - https://www.variscite.com
 */
#include "omap4460.dtsi"
#include "omap4-mcpdm.dtsi"

/ {
	model = "Variscite VAR-SOM-OM44";
	compatible = "variscite,var-som-om44", "ti,omap4460", "ti,omap4";

	memory@******** {
		device_type = "memory";
		reg = <0x******** 0x40000000>; /* 1 GB */
	};

	sound: sound {
		compatible = "ti,abe-twl6040";
		ti,model = "VAR-SOM-OM44";

		ti,mclk-freq = <********>;
		ti,mcpdm = <&mcpdm>;
		ti,twl6040 = <&twl6040>;

		/* Audio routing */
		ti,audio-routing =
			"Headset Stereophone", "HSOL",
			"Headset Stereophone", "HSOR",
			"AFML", "Line In",
			"AFMR", "Line In";
	};

	/* HS USB Host PHY on PORT 1 */
	hsusb1_phy: hsusb1_phy {
		compatible = "usb-nop-xceiv";
		pinctrl-names = "default";
		pinctrl-0 = <
			&hsusbb1_phy_clk_pins
			&hsusbb1_phy_rst_pins
		>;

		reset-gpios = <&gpio6 17 GPIO_ACTIVE_LOW>; /* gpio 177 */
		vcc-supply = <&vbat>;
		#phy-cells = <0>;

		clocks = <&auxclk3_ck>;
		clock-names = "main_clk";
		clock-frequency = <********>;
	};

	vbat: fixedregulator-vbat {
		compatible = "regulator-fixed";
		regulator-name = "VBAT";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
	};
};

&omap4_pmx_core {
	pinctrl-names = "default";
	pinctrl-0 = <
			&hsusbb1_pins
	>;

	twl6040_pins: twl6040-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x19c, PIN_OUTPUT | MUX_MODE3)		/* fref_clk2_out.gpio_182 */
			OMAP4_IOPAD(0x1a0, PIN_INPUT | MUX_MODE0)		/* sys_nirq2.sys_nirq2 */
		>;
	};

	tsc2004_pins: tsc2004-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x090, PIN_INPUT | MUX_MODE3)		/* gpmc_ncs4.gpio_101 (irq) */
			OMAP4_IOPAD(0x092, PIN_OUTPUT | MUX_MODE3)		/* gpmc_ncs5.gpio_102 (rst) */
		>;
	};

	uart3_pins: uart3-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x140, PIN_INPUT_PULLUP | MUX_MODE0)	/* uart3_cts_rctx.uart3_cts_rctx */
			OMAP4_IOPAD(0x142, PIN_OUTPUT | MUX_MODE0)		/* uart3_rts_sd.uart3_rts_sd */
			OMAP4_IOPAD(0x144, PIN_INPUT | MUX_MODE0)		/* uart3_rx_irrx.uart3_rx_irrx */
			OMAP4_IOPAD(0x146, PIN_OUTPUT | MUX_MODE0)		/* uart3_tx_irtx.uart3_tx_irtx */
		>;
	};

	hsusbb1_pins: hsusbb1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x0c2, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_clk.usbb1_ulpiphy_clk */
			OMAP4_IOPAD(0x0c4, PIN_OUTPUT | MUX_MODE4)		/* usbb1_ulpitll_stp.usbb1_ulpiphy_stp */
			OMAP4_IOPAD(0x0c6, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dir.usbb1_ulpiphy_dir */
			OMAP4_IOPAD(0x0c8, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_nxt.usbb1_ulpiphy_nxt */
			OMAP4_IOPAD(0x0ca, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat0.usbb1_ulpiphy_dat0 */
			OMAP4_IOPAD(0x0cc, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat1.usbb1_ulpiphy_dat1 */
			OMAP4_IOPAD(0x0ce, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat2.usbb1_ulpiphy_dat2 */
			OMAP4_IOPAD(0x0d0, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat3.usbb1_ulpiphy_dat3 */
			OMAP4_IOPAD(0x0d2, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat4.usbb1_ulpiphy_dat4 */
			OMAP4_IOPAD(0x0d4, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat5.usbb1_ulpiphy_dat5 */
			OMAP4_IOPAD(0x0d6, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat6.usbb1_ulpiphy_dat6 */
			OMAP4_IOPAD(0x0d8, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat7.usbb1_ulpiphy_dat7 */
		>;
	};

	hsusbb1_phy_rst_pins: hsusbb1-phy-rst-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x18c, PIN_OUTPUT | MUX_MODE3)		/* kpd_row2.gpio_177 */
		>;
	};

	i2c1_pins: i2c1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x122, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c1_scl */
			OMAP4_IOPAD(0x124, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c1_sda */
		>;
	};

	i2c3_pins: i2c3-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x12a, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c3_scl */
			OMAP4_IOPAD(0x12c, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c3_sda */
		>;
	};

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x0e2, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_clk.sdmmc1_clk */
			OMAP4_IOPAD(0x0e4, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_cmd.sdmmc1_cmd */
			OMAP4_IOPAD(0x0e6, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat0.sdmmc1_dat0 */
			OMAP4_IOPAD(0x0e8, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat1.sdmmc1_dat1 */
			OMAP4_IOPAD(0x0ea, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat2.sdmmc1_dat2 */
			OMAP4_IOPAD(0x0ec, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat3.sdmmc1_dat3 */
		>;
	};
};

&omap4_pmx_wkup {
	pinctrl-names = "default";
	pinctrl-0 = <
		&hsusbb1_hub_rst_pins
		&lan7500_rst_pins
	>;

	hsusbb1_phy_clk_pins: hsusbb1-phy-clk-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x058, PIN_OUTPUT | MUX_MODE0)	/* fref_clk3_out */
		>;
	};

	hsusbb1_hub_rst_pins: hsusbb1-hub-rst-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x042, PIN_OUTPUT | MUX_MODE3)	/* gpio_wk1 */
		>;
	};

	lan7500_rst_pins: lan7500-rst-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x040, PIN_OUTPUT | MUX_MODE3)	/* gpio_wk0 */
		>;
	};
};

&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins>;
	status = "okay";

	clock-frequency = <400000>;

	twl: twl@48 {
		reg = <0x48>;
		/* SPI = 0, IRQ# = 7, 4 = active high level-sensitive */
		interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>; /* IRQ_SYS_1N cascaded to gic */
	};

	twl6040: twl@4b {
		compatible = "ti,twl6040";
		#clock-cells = <0>;
		reg = <0x4b>;

		pinctrl-names = "default";
		pinctrl-0 = <&twl6040_pins>;

		/* SPI = 0, IRQ# = 119, 4 = active high level-sensitive */
		interrupts = <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>; /* IRQ_SYS_2N cascaded to gic */
		ti,audpwron-gpio = <&gpio6 22 GPIO_ACTIVE_HIGH>; /* gpio 182 */

		vio-supply = <&v1v8>;
		v2v1-supply = <&v2v1>;
		enable-active-high;
	};
};

#include "twl6030.dtsi"
#include "twl6030_omap4.dtsi"

&vusim {
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-always-on;
};

&i2c2 {
	status = "disabled";
};

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;
	status = "okay";

	clock-frequency = <400000>;

	touchscreen: tsc2004@48 {
		compatible = "ti,tsc2004";
		reg = <0x48>;
		pinctrl-names = "default";
		pinctrl-0 = <&tsc2004_pins>;
		interrupt-parent = <&gpio4>;
		interrupts = <5 IRQ_TYPE_LEVEL_LOW>; /* gpio 101 */
		status = "disabled";
	};

	tmp105@49 {
		compatible = "ti,tmp105";
		reg = <0x49>;
	};

	eeprom@50 {
		compatible = "microchip,24c32", "atmel,24c32";
		reg = <0x50>;
	};
};

&i2c4 {
	status = "disabled";
};

&gpmc {
	status = "disabled";
};

&mcspi1 {
	status = "disabled";
};

&mcspi2 {
	status = "disabled";
};

&mcspi3 {
	status = "disabled";
};

&mcspi4 {
	status = "disabled";
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
	vmmc-supply = <&vmmc>;
	bus-width = <4>;
	ti,non-removable;
	status = "okay";
};

&mmc2 {
	status = "disabled";
};

&mmc3 {
	status = "disabled";
};

&mmc4 {
	status = "disabled";
};

&mmc5 {
	status = "disabled";
};

&uart1 {
	status = "disabled";
};

&uart2 {
	status = "disabled";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>;
	status = "okay";
};

&uart4 {
	status = "disabled";
};

&keypad {
	status = "disabled";
};

&twl_usb_comparator {
	usb-supply = <&vusb>;
};

&usb_otg_hs {
	interface-type = <1>;
	mode = <3>;
	power = <50>;
};

&usbhshost {
	port1-mode = "ehci-phy";
};

&usbhsehci {
	phys = <&hsusb1_phy>;
};
