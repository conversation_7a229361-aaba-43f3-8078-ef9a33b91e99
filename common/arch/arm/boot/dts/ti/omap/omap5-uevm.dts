// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2013 Texas Instruments Incorporated - https://www.ti.com/
 */
/dts-v1/;

#include "omap5-board-common.dtsi"

/ {
	model = "TI OMAP5 uEVM board";
	compatible = "ti,omap5-uevm", "ti,omap5";

	memory@80000000 {
		device_type = "memory";
		reg = <0 0x80000000 0 0x7f000000>; /* 2032 MB */
	};

	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		dsp_memory_region: dsp-memory@95000000 {
			compatible = "shared-dma-pool";
			reg = <0 0x95000000 0 0x800000>;
			reusable;
			status = "okay";
		};

		ipu_memory_region: ipu-memory@95800000 {
			compatible = "shared-dma-pool";
			reg = <0 0x95800000 0 0x3800000>;
			reusable;
			status = "okay";
		};
	};

	aliases {
		ethernet = &ethernet;
	};

	leds {
		compatible = "gpio-leds";
		led1 {
			label = "omap5:blue:usr1";
			gpios = <&gpio5 25 GPIO_ACTIVE_HIGH>; /* gpio5_153 D1 LED */
			linux,default-trigger = "heartbeat";
			default-state = "off";
		};
	};

	evm_keys {
		compatible = "gpio-keys";

		pinctrl-names = "default";
		pinctrl-0 = <&evm_keys_pins>;

		#address-cells = <7>;
		#size-cells = <0>;

		btn1 {
			label = "BTN1";
			linux,code = <169>;
			gpios = <&gpio3 19 GPIO_ACTIVE_LOW>;	/* gpio3_83 */
			wakeup-source;
			autorepeat;
			debounce-interval = <50>;
		};
	};

	evm_leds {
		compatible = "gpio-leds";

		led1 {
			label = "omap5:red:led";
			gpios = <&gpio9 17 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "mmc0";
			default-state = "off";
		};

		led2 {
			label = "omap5:green:led";
			gpios = <&gpio9 18 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "mmc1";
			default-state = "off";
		};

		led3 {
			label = "omap5:blue:led";
			gpios = <&gpio9 19 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "mmc2";
			default-state = "off";
		};

		led4 {
			label = "omap5:green:led1";
			gpios = <&gpio9 2 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
			default-state = "off";
		};

		led5 {
			label = "omap5:green:led2";
			gpios = <&gpio9 3 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "default-on";
			default-state = "off";
		};

		led6 {
			label = "omap5:green:led3";
			gpios = <&gpio9 4 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
			default-state = "off";
		};

		led7 {
			label = "omap5:green:led4";
			gpios = <&gpio9 5 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "default-on";
			default-state = "off";
		};

		led8 {
			label = "omap5:green:led5";
			gpios = <&gpio9 6 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
			default-state = "off";
		};
	};
};

&hdmi {
	vdda-supply = <&ldo4_reg>;
};

&i2c1 {
	eeprom@50 {
		compatible = "atmel,24c02";
		reg = <0x50>;
	};
};

&i2c5 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c5_pins>;

	clock-frequency = <400000>;

	gpio9: gpio@22 {
		compatible = "ti,tca6424";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
	};
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;

	cd-gpios = <&gpio5 24 GPIO_ACTIVE_LOW>;	/* gpio5_152 */
};

&omap5_pmx_core {
	evm_keys_pins: evm-keys-gpio-pins {
		pinctrl-single,pins = <
			OMAP5_IOPAD(0x0b6, PIN_INPUT | MUX_MODE6)	/* gpio3_83 */
		>;
	};

	i2c5_pins: i2c5-pins {
		pinctrl-single,pins = <
			OMAP5_IOPAD(0x1c6, PIN_INPUT | MUX_MODE0)		/* i2c5_scl */
			OMAP5_IOPAD(0x1c8, PIN_INPUT | MUX_MODE0)		/* i2c5_sda */
		>;
	};

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			OMAP5_IOPAD(0x1d4, PIN_INPUT_PULLUP | MUX_MODE6)	/* gpio5_152 */
		>;
	};
};

&tpd12s015 {
	gpios = <&gpio9 0 GPIO_ACTIVE_HIGH>,	/* TCA6424A P01, CT CP HPD */
		<&gpio9 1 GPIO_ACTIVE_HIGH>,	/* TCA6424A P00, LS OE */
		<&gpio7 1 GPIO_ACTIVE_HIGH>;	/* GPIO 193, HPD */
};

&twl6040 {
	ti,audpwron-gpio = <&gpio5 13 GPIO_ACTIVE_HIGH>;  /* gpio line 141 */
};

&twl6040_pins {
	pinctrl-single,pins = <
		OMAP5_IOPAD(0x1be, PIN_OUTPUT | MUX_MODE6)	/* mcspi1_somi.gpio5_141 */
	>;
};

&usbhsehci {
	#address-cells = <1>;
	#size-cells = <0>;

	hub@2 {
		compatible = "usb424,3503";
		reg = <2>;
		#address-cells = <1>;
		#size-cells = <0>;
	};

	ethernet: ethernet@3 {
		compatible = "usb424,9730";
		reg = <3>;
	};
};

&wlcore {
	compatible = "ti,wl1837";
};

&dsp {
	status = "okay";
	memory-region = <&dsp_memory_region>;
	ti,timers = <&timer5>;
	ti,watchdog-timers = <&timer6>;
};

&ipu {
	status = "okay";
	memory-region = <&ipu_memory_region>;
	ti,timers = <&timer3>;
	ti,watchdog-timers = <&timer9>, <&timer11>;
};
