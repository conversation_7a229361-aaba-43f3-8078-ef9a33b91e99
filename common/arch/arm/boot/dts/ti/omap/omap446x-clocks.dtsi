// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for OMAP4 clock data
 *
 * Copyright (C) 2013 Texas Instruments, Inc.
 */
&prm_clocks {
	div_ts_ck: div_ts_ck@1888 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "div_ts_ck";
		clocks = <&l4_wkup_clk_mux_ck>;
		ti,bit-shift = <24>;
		reg = <0x1888>;
		ti,dividers = <8>, <16>, <32>;
	};

	bandgap_ts_fclk: bandgap_ts_fclk@1888 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "bandgap_ts_fclk";
		clocks = <&div_ts_ck>;
		ti,bit-shift = <8>;
		reg = <0x1888>;
	};
};
