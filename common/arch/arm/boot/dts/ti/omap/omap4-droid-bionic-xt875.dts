// SPDX-License-Identifier: GPL-2.0-only
/dts-v1/;

#include "motorola-mapphone-common.dtsi"

/ {
	model = "Motorola Droid Bionic XT875";
	compatible = "motorola,droid-bionic", "ti,omap4430", "ti,omap4";
};

&keypad {
	keypad,num-rows = <8>;
	keypad,num-columns = <8>;
	linux,keymap = <
	MATRIX_KEY(5, 0, KEY_VOLUMEUP)
	MATRIX_KEY(3, 0, KEY_VOLUMEDOWN)
	>;
};

&i2c1 {
	led-controller@38 {
		compatible = "ti,lm3532";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x38>;

		enable-gpios = <&gpio6 12 GPIO_ACTIVE_HIGH>;

		ramp-up-us = <1024>;
		ramp-down-us = <8193>;

		backlight_led: led@0 {
			reg = <0>;
			led-sources = <2>;
			ti,led-mode = <0>;
			label = ":backlight";
		};
	};
};

&i2c4 {
	kxtf9: accelerometer@f {
		compatible = "kionix,kxtf9";
		reg = <0x0f>;

		vdd-supply = <&vhvio>;

		interrupt-parent = <&gpio2>;
		interrupts = <2 IRQ_TYPE_EDGE_RISING>;

		rotation-matrix = "0", "-1", "0",
				  "1", "0", "0",
				  "0", "0", "1";
	};
};
