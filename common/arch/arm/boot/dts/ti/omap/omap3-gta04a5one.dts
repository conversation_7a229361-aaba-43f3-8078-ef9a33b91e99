// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014-18 <PERSON><PERSON> <<EMAIL>>
 */

#include "omap3-gta04a5.dts"

/ {
	model = "Goldelico GTA04A5/Letux 2804 with OneNAND";
};

&omap3_pmx_core {
	gpmc_pins: gpmc-pins {
		pinctrl-single,pins = <

			/* address lines */
			OMAP3_CORE1_IOPAD(0x207a, PIN_OUTPUT | MUX_MODE0)       /* gpmc_a1.gpmc_a1 */
			OMAP3_CORE1_IOPAD(0x207c, PIN_OUTPUT | MUX_MODE0)       /* gpmc_a2.gpmc_a2 */
			OMAP3_CORE1_IOPAD(0x207e, PIN_OUTPUT | MUX_MODE0)       /* gpmc_a3.gpmc_a3 */

			/* data lines, gpmc_d0..d7 not muxable according to TRM */
			OMAP3_CORE1_IOPAD(0x209e, PIN_INPUT | MUX_MODE0)        /* gpmc_d8.gpmc_d8 */
			OMAP3_CORE1_IOPAD(0x20a0, PIN_INPUT | MUX_MODE0)        /* gpmc_d9.gpmc_d9 */
			OMAP3_CORE1_IOPAD(0x20a2, PIN_INPUT | MUX_MODE0)        /* gpmc_d10.gpmc_d10 */
			OMAP3_CORE1_IOPAD(0x20a4, PIN_INPUT | MUX_MODE0)        /* gpmc_d11.gpmc_d11 */
			OMAP3_CORE1_IOPAD(0x20a6, PIN_INPUT | MUX_MODE0)        /* gpmc_d12.gpmc_d12 */
			OMAP3_CORE1_IOPAD(0x20a8, PIN_INPUT | MUX_MODE0)        /* gpmc_d13.gpmc_d13 */
			OMAP3_CORE1_IOPAD(0x20aa, PIN_INPUT | MUX_MODE0)        /* gpmc_d14.gpmc_d14 */
			OMAP3_CORE1_IOPAD(0x20ac, PIN_INPUT | MUX_MODE0)        /* gpmc_d15.gpmc_d15 */

			/*
			 * gpmc_ncs0, gpmc_nadv_ale, gpmc_noe, gpmc_nwe, gpmc_wait0 not muxable
			 * according to TRM. OneNAND seems to require PIN_INPUT on clock.
			 */
			OMAP3_CORE1_IOPAD(0x20b0, PIN_OUTPUT | MUX_MODE0)       /* gpmc_ncs1.gpmc_ncs1 */
			OMAP3_CORE1_IOPAD(0x20be, PIN_INPUT | MUX_MODE0)        /* gpmc_clk.gpmc_clk */
		>;
	};
};

&gpmc {
	/* switch inherited setup to OneNAND */

	ranges = <0 0 0x04000000 0x1000000>;	/* CS0: 16MB for OneNAND */
	pinctrl-names = "default";
	pinctrl-0 = <&gpmc_pins>;

	/delete-node/ nand@0,0;

	onenand@0,0 {

		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "ti,omap2-onenand";
		reg = <0 0 0x20000>;	/* CS0, offset 0, IO size 128K */

		gpmc,sync-read;
		gpmc,sync-write;
		gpmc,burst-length = <16>;
		gpmc,burst-read;
		gpmc,burst-wrap;
		gpmc,burst-write;
		gpmc,device-width = <2>;
		gpmc,mux-add-data = <2>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <87>;
		gpmc,cs-wr-off-ns = <87>;
		gpmc,adv-on-ns = <0>;
		gpmc,adv-rd-off-ns = <10>;
		gpmc,adv-wr-off-ns = <10>;
		gpmc,oe-on-ns = <15>;
		gpmc,oe-off-ns = <87>;
		gpmc,we-on-ns = <0>;
		gpmc,we-off-ns = <87>;
		gpmc,rd-cycle-ns = <112>;
		gpmc,wr-cycle-ns = <112>;
		gpmc,access-ns = <81>;
		gpmc,page-burst-access-ns = <15>;
		gpmc,bus-turnaround-ns = <0>;
		gpmc,cycle2cycle-delay-ns = <0>;
		gpmc,wait-monitoring-ns = <0>;
		gpmc,clk-activation-ns = <5>;
		gpmc,wr-data-mux-bus-ns = <30>;
		gpmc,wr-access-ns = <81>;
		gpmc,sync-clk-ps = <15000>;

		x-loader@0 {
			label = "X-Loader";
			reg = <0 0x80000>;
		};

		bootloaders@80000 {
			label = "U-Boot";
			reg = <0x80000 0x1c0000>;
		};

		bootloaders_env@240000 {
			label = "U-Boot Env";
			reg = <0x240000 0x40000>;
		};

		kernel@280000 {
			label = "Kernel";
			reg = <0x280000 0x600000>;
		};

		filesystem@880000 {
			label = "File System";
			reg = <0x880000 0>;	/* 0 = MTDPART_SIZ_FULL */
		};

	};
};
