// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for OMAP443x SoC
 *
 * Copyright (C) 2013 Texas Instruments Incorporated - https://www.ti.com/
 */

#include "omap4.dtsi"

/ {
	cpus {
		cpu0: cpu@0 {
			/* OMAP443x variants OPP50-OPPNT */
			operating-points = <
				/* kHz    uV */
				300000  1025000
				600000  1200000
				800000  1313000
				1008000 1375000
			>;
			clock-latency = <300000>; /* From legacy driver */

			/* cooling options */
			#cooling-cells = <2>; /* min followed by max */
		};
	};

	thermal-zones {
		#include "omap4-cpu-thermal.dtsi"
	};

	ocp {
		/* 4430 has only gpio_86 tshut and no talert interrupt */
		bandgap: bandgap@4a002260 {
			reg = <0x4a002260 0x4
			       0x4a00232C 0x4>;
			compatible = "ti,omap4430-bandgap";
			gpios = <&gpio3 22 GPIO_ACTIVE_HIGH>;

			#thermal-sensor-cells = <0>;
		};
	};

	ocp {
		abb_mpu: regulator-abb-mpu {
			status = "okay";

			reg = <0x4a307bd0 0x8>, <0x4a306014 0x4>;
			reg-names = "base-address", "int-address";

			ti,abb_info = <
			/*uV		ABB	efuse	rbb_m	fbb_m	vset_m*/
			1025000		0	0	0	0	0
			1200000		0	0	0	0	0
			1313000		0	0	0	0	0
			1375000		1	0	0	0	0
			1389000		1	0	0	0	0
			>;
		};

		/* Default unused, just provide register info for record */
		abb_iva: regulator-abb-iva {
			reg = <0x4a307bd8 0x8>, <0x4a306010 0x4>;
			reg-names = "base-address", "int-address";
		};

	};

};

&cpu_thermal {
	thermal-sensors = <&bandgap>;
	coefficients = <0 20000>;
};

/include/ "omap443x-clocks.dtsi"

/*
 * Use dpll_per for sgx at 307.2MHz like droid4 stock v3.0.8 Android kernel
 */
&sgx_module {
	assigned-clocks = <&l3_gfx_clkctrl OMAP4_GPU_CLKCTRL 24>,
			  <&dpll_per_m7x2_ck>;
	assigned-clock-rates = <0>, <307200000>;
	assigned-clock-parents = <&dpll_per_m7x2_ck>;
};
