// SPDX-License-Identifier: GPL-2.0-only
/*
 * omap3-n9.dts - Device Tree file for Nokia N9
 *
 * Written by: <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "omap3-n950-n9.dtsi"
#include <dt-bindings/input/input.h>

/ {
	model = "Nokia N9";
	compatible = "nokia,omap3-n9", "ti,omap3630", "ti,omap3";
};

&i2c2 {
	smia_1: camera@10 {
		compatible = "nokia,smia";
		reg = <0x10>;
		/* No reset gpio */
		vana-supply = <&vaux3>;
		clocks = <&isp 0>;
		clock-frequency = <9600000>;
		flash-leds = <&as3645a_flash &as3645a_indicator>;
		port {
			smia_1_1: endpoint {
				link-frequencies = /bits/ 64 <199200000 210000000 499200000>;
				clock-lanes = <0>;
				data-lanes = <1 2>;
				remote-endpoint = <&csi2a_ep>;
			};
		};
	};
};

&i2c3 {
	ak8975@f {
		compatible = "asahi-kasei,ak8975";
		reg = <0x0f>;
	};
};

&isp {
	vdd-csiphy1-supply = <&vaux2>;
	vdd-csiphy2-supply = <&vaux2>;
	ports {
		port@2 {
			reg = <2>;
			csi2a_ep: endpoint {
				remote-endpoint = <&smia_1_1>;
				clock-lanes = <2>;
				data-lanes = <1 3>;
				crc = <1>;
				lane-polarities = <1 1 1>;
			};
		};
	};
};

&modem {
	compatible = "nokia,n9-modem";
};

&lis302 {
	st,axis-x = <1>;    /* LIS3_DEV_X */
	st,axis-y = <(-2)>; /* LIS3_INV_DEV_Y */
	st,axis-z = <(-3)>; /* LIS3_INV_DEV_Z */

	st,min-limit-x = <(-46)>;
	st,min-limit-y = <3>;
	st,min-limit-z = <3>;

	st,max-limit-x = <(-3)>;
	st,max-limit-y = <46>;
	st,max-limit-z = <46>;
};

&twl_keypad {
	linux,keymap = < MATRIX_KEY(6, 8, KEY_VOLUMEUP)
			 MATRIX_KEY(7, 8, KEY_VOLUMEDOWN)
			 >;
};
