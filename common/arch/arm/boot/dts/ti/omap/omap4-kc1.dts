// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2016 <PERSON> <<EMAIL>>
 */
/dts-v1/;

#include "omap443x.dtsi"

/ {
	model = "Amazon Kindle Fire (first generation)";
	compatible = "amazon,omap4-kc1", "ti,omap4430", "ti,omap4";

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x20000000>; /* 512 MB */
	};

	led-controller {
		compatible = "pwm-leds";

		led-1 {
			label = "green";
			pwms = <&twl_pwm 0 7812500>;
			max-brightness = <127>;
		};

		led-2 {
			label = "orange";
			pwms = <&twl_pwm 1 7812500>;
			max-brightness = <127>;
		};
	};
};

&omap4_pmx_core {
	pinctrl-names = "default";

	uart3_pins: uart3-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x144, PIN_INPUT | MUX_MODE0)		/* uart3_rx_irrx */
			OMAP4_IOPAD(0x146, PIN_OUTPUT | MUX_MODE0)		/* uart3_tx_irtx */
		>;
	};

	i2c1_pins: i2c1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x122, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c1_scl */
			OMAP4_IOPAD(0x124, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c1_sda */
		>;
	};

	i2c2_pins: i2c2-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x126, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c2_scl */
			OMAP4_IOPAD(0x128, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c2_sda */
		>;
	};

	i2c3_pins: i2c3-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x12a, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c3_scl */
			OMAP4_IOPAD(0x12c, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c3_sda */
		>;
	};

	i2c4_pins: i2c4-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x12e, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c4_scl */
			OMAP4_IOPAD(0x130, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c4_sda */
		>;
	};

	mmc2_pins: mmc2-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x040, PIN_INPUT_PULLUP | MUX_MODE1)	/* sdmmc2_dat0 */
			OMAP4_IOPAD(0x042, PIN_INPUT_PULLUP | MUX_MODE1)	/* sdmmc2_dat1 */
			OMAP4_IOPAD(0x044, PIN_INPUT_PULLUP | MUX_MODE1)	/* sdmmc2_dat2 */
			OMAP4_IOPAD(0x046, PIN_INPUT_PULLUP | MUX_MODE1)	/* sdmmc2_dat3 */
			OMAP4_IOPAD(0x048, PIN_INPUT_PULLUP | MUX_MODE1)	/* sdmmc2_dat4 */
			OMAP4_IOPAD(0x04a, PIN_INPUT_PULLUP | MUX_MODE1)	/* sdmmc2_dat5 */
			OMAP4_IOPAD(0x04c, PIN_INPUT_PULLUP | MUX_MODE1)	/* sdmmc2_dat6 */
			OMAP4_IOPAD(0x04e, PIN_INPUT_PULLUP | MUX_MODE1)	/* sdmmc2_dat7 */
			OMAP4_IOPAD(0x082, PIN_INPUT_PULLUP | MUX_MODE1)	/* sdmmc2_clk */
			OMAP4_IOPAD(0x084, PIN_INPUT_PULLUP | MUX_MODE1)	/* sdmmc2_cmd */
		>;
	};

	usb_otg_hs_pins: usb-otg-hs-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x194, PIN_OUTPUT_PULLDOWN | MUX_MODE0)	/* usba0_otg_ce */
			OMAP4_IOPAD(0x196, PIN_INPUT | MUX_MODE0)		/* usba0_otg_dp */
			OMAP4_IOPAD(0x198, PIN_INPUT | MUX_MODE0)		/* usba0_otg_dm */
		>;
	};
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>;

	interrupts-extended = <&wakeupgen GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH
			       &omap4_pmx_core OMAP4_UART3_RX>;
};

&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins>;

	clock-frequency = <400000>;

	twl: twl@48 {
		reg = <0x48>;
		/* IRQ# = 7 */
		interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>; /* IRQ_SYS_1N cascaded to gic */

		twl_power: power {
			compatible = "ti,twl6030-power";
			ti,system-power-controller;
		};
	};
};

&i2c2 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_pins>;

	clock-frequency = <400000>;
};

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;

	clock-frequency = <400000>;
};

&i2c4 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4_pins>;

	clock-frequency = <400000>;
};

&mmc1 {
	status = "disabled";
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_pins>;

	vmmc-supply = <&vaux1>;
	ti,non-removable;
	bus-width = <8>;
};

&mmc3 {
	status = "disabled";
};

&mmc4 {
	status = "disabled";
};

&usb_otg_hs {
	pinctrl-names = "default";
	pinctrl-0 = <&usb_otg_hs_pins>;

	interface-type = <1>;
	mode = <3>;
	power = <50>;
};

#include "twl6030.dtsi"
#include "twl6030_omap4.dtsi"

&twl_usb_comparator {
	usb-supply = <&vusb>;
};
