// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for OMAP4460 SoC
 *
 * Copyright (C) 2012 Texas Instruments Incorporated - https://www.ti.com/
 */
#include "omap4.dtsi"

/ {
	cpus {
		/* OMAP446x 'standard device' variants OPP50 to OPPTurbo */
		cpu0: cpu@0 {
			operating-points = <
				/* kHz    uV */
				350000  1025000
				700000  1200000
				920000  1313000
			>;
			clock-latency = <300000>; /* From legacy driver */

			/* cooling options */
			#cooling-cells = <2>; /* min followed by max */
		};
	};

	thermal-zones {
		#include "omap4-cpu-thermal.dtsi"
	};

	ocp {
		bandgap: bandgap@4a002260 {
			reg = <0x4a002260 0x4
			       0x4a00232C 0x4
			       0x4a002378 0x18>;
			compatible = "ti,omap4460-bandgap";
			interrupts = <0 126 IRQ_TYPE_LEVEL_HIGH>; /* talert */
			gpios = <&gpio3 22 GPIO_ACTIVE_HIGH>; /* tshut */

			#thermal-sensor-cells = <0>;
		};

		abb_mpu: regulator-abb-mpu {
			status = "okay";

			reg = <0x4a307bd0 0x8>, <0x4a306014 0x4>,
			      <0x4A002268 0x4>;
			reg-names = "base-address", "int-address",
				    "efuse-address";

			ti,abb_info = <
			/*uV		ABB	efuse	rbb_m	fbb_m	vset_m*/
			1025000		0	0	0	0	0
			1200000		0	0	0	0	0
			1313000		0	0	0x100000 0x40000 0
			1375000		1	0	0	0	0
			1389000		1	0	0	0	0
			>;
		};

		abb_iva: regulator-abb-iva {
			status = "okay";

			reg = <0x4a307bd8 0x8>, <0x4a306010 0x4>,
			      <0x4A002268 0x4>;
			reg-names = "base-address", "int-address",
				    "efuse-address";

			ti,abb_info = <
			/*uV		ABB	efuse	rbb_m	fbb_m	vset_m*/
			950000		0	0	0	0	0
			1140000		0	0	0	0	0
			1291000		0	0	0x200000 0	0
			1375000		1	0	0	0	0
			1376000		1	0	0	0	0
			>;
		};
	};

};

&cpu_thermal {
	thermal-sensors = <&bandgap>;
	coefficients = <348 (-9301)>;
};

/* Only some L4 CFG interconnect ranges are different on 4460 */
&l4_cfg_segment_300000 {
	ranges = <0x00000000 0x00300000 0x020000>,	/* ap 67 */
		 <0x00040000 0x00340000 0x001000>,	/* ap 68 */
		 <0x00020000 0x00320000 0x004000>,	/* ap 71 */
		 <0x00024000 0x00324000 0x002000>,	/* ap 72 */
		 <0x00026000 0x00326000 0x001000>,	/* ap 73 */
		 <0x00027000 0x00327000 0x001000>,	/* ap 74 */
		 <0x00028000 0x00328000 0x001000>,	/* ap 75 */
		 <0x00029000 0x00329000 0x001000>,	/* ap 76 */
		 <0x00030000 0x00330000 0x010000>,	/* ap 77 */
		 <0x0002a000 0x0032a000 0x002000>,	/* ap 90 */
		 <0x0002c000 0x0032c000 0x004000>,	/* ap 91 */
		 <0x00010000 0x00310000 0x008000>,	/* ap 92 */
		 <0x00018000 0x00318000 0x004000>,	/* ap 93 */
		 <0x0001c000 0x0031c000 0x002000>,	/* ap 94 */
		 <0x0001e000 0x0031e000 0x002000>;	/* ap 95 */
};

&l4_cfg_target_0 {
	ranges = <0x00000000 0x00000000 0x00010000>,
		 <0x00010000 0x00010000 0x00008000>,
		 <0x00018000 0x00018000 0x00004000>,
		 <0x0001c000 0x0001c000 0x00002000>,
		 <0x0001e000 0x0001e000 0x00002000>,
		 <0x00020000 0x00020000 0x00004000>,
		 <0x00024000 0x00024000 0x00002000>,
		 <0x00026000 0x00026000 0x00001000>,
		 <0x00027000 0x00027000 0x00001000>,
		 <0x00028000 0x00028000 0x00001000>,
		 <0x00029000 0x00029000 0x00001000>,
		 <0x0002a000 0x0002a000 0x00002000>,
		 <0x0002c000 0x0002c000 0x00004000>,
		 <0x00030000 0x00030000 0x00010000>;
};

&pmu {
	compatible = "arm,cortex-a9-pmu";
	interrupts = <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;
};

/include/ "omap446x-clocks.dtsi"
