// SPDX-License-Identifier: GPL-2.0-only
/*
 * omap3-n950.dts - Device Tree file for Nokia N950
 *
 * Written by: <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "omap3-n950-n9.dtsi"
#include <dt-bindings/input/input.h>

/ {
	model = "Nokia N950";
	compatible = "nokia,omap3-n950", "ti,omap3630", "ti,omap3";

	keys {
		compatible = "gpio-keys";

		keypad_slide {
			label = "Keypad Slide";
			gpios = <&gpio4 13 GPIO_ACTIVE_LOW>; /* 109 */
			linux,input-type = <EV_SW>;
			linux,code = <SW_KEYPAD_SLIDE>;
			wakeup-source;
			pinctrl-names = "default";
			pinctrl-0 = <&keypad_slide_pins>;
		};
	};
};

&omap3_pmx_core {
	keypad_slide_pins: debug-led-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x212a, PIN_INPUT | MUX_MODE4)       /* cam_d10.gpio_109 */
		>;
	};
};

&omap3_pmx_core {
	spi4_pins: spi4-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x218c, PIN_INPUT_PULLDOWN | MUX_MODE1) /* mcspi4_clk */
			OMAP3_CORE1_IOPAD(0x2190, PIN_OUTPUT | MUX_MODE1) /* mcspi4_simo */
			OMAP3_CORE1_IOPAD(0x2192, PIN_INPUT_PULLDOWN | MUX_MODE1) /* mcspi4_somi */
			OMAP3_CORE1_IOPAD(0x2196, PIN_OUTPUT | MUX_MODE1) /* mcspi4_cs0 */
		>;
	};
};

&omap3_pmx_core {
	dsi_pins: dsi-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20dc, PIN_OUTPUT | MUX_MODE1) /* dsi_dx0 - data0+ */
			OMAP3_CORE1_IOPAD(0x20de, PIN_OUTPUT | MUX_MODE1) /* dsi_dy0 - data0- */
			OMAP3_CORE1_IOPAD(0x20e0, PIN_OUTPUT | MUX_MODE1) /* dsi_dx1 - clk+   */
			OMAP3_CORE1_IOPAD(0x20e2, PIN_OUTPUT | MUX_MODE1) /* dsi_dy1 - clk-   */
			OMAP3_CORE1_IOPAD(0x20e4, PIN_OUTPUT | MUX_MODE1) /* dsi_dx2 - data1+ */
			OMAP3_CORE1_IOPAD(0x20e6, PIN_OUTPUT | MUX_MODE1) /* dsi_dy2 - data1- */
		>;
	};

	display_pins: display-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20ca, PIN_INPUT | MUX_MODE4) /* gpio 62 - display te */
			OMAP3_CORE1_IOPAD(0x20fe, PIN_OUTPUT | MUX_MODE4) /* gpio 87 - display reset */
		>;
	};
};

&i2c2 {
	smia_1: camera@10 {
		compatible = "nokia,smia";
		reg = <0x10>;
		/* No reset gpio */
		vana-supply = <&vaux3>;
		clocks = <&isp 0>;
		clock-frequency = <9600000>;
		flash-leds = <&as3645a_flash &as3645a_indicator>;
		port {
			smia_1_1: endpoint {
				link-frequencies = /bits/ 64 <210000000 333600000 398400000>;
				clock-lanes = <0>;
				data-lanes = <1 2>;
				remote-endpoint = <&csi2a_ep>;
			};
		};
	};
};

&isp {
	vdd-csiphy1-supply = <&vaux2>;
	vdd-csiphy2-supply = <&vaux2>;
	ports {
		port@2 {
			reg = <2>;
			csi2a_ep: endpoint {
				remote-endpoint = <&smia_1_1>;
				clock-lanes = <2>;
				data-lanes = <3 1>;
				crc = <1>;
				lane-polarities = <1 1 1>;
			};
		};
	};
};

&mcspi4 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&spi4_pins>;

	wlcore: wlcore@0 {
		compatible = "ti,wl1271";
		pinctrl-names = "default";
		pinctrl-0 = <&wlan_pins>;
		reg = <0>;
		spi-max-frequency = <48000000>;
		clock-xtal;
		ref-clock-frequency = <38400000>;
		interrupts-extended = <&gpio2 10 IRQ_TYPE_LEVEL_HIGH>; /* gpio 42 */
		vwlan-supply = <&vwlan_fixed>;
	};
};

&modem {
	compatible = "nokia,n950-modem";
};

&twl {
	twl_audio: audio {
		compatible = "ti,twl4030-audio";
		ti,enable-vibra = <1>;
	};
};

&twl_keypad {
	linux,keymap = < MATRIX_KEY(0x00, 0x00, KEY_BACKSLASH)
			 MATRIX_KEY(0x01, 0x00, KEY_LEFTSHIFT)
			 MATRIX_KEY(0x02, 0x00, KEY_COMPOSE)
			 MATRIX_KEY(0x03, 0x00, KEY_LEFTMETA)
			 MATRIX_KEY(0x04, 0x00, KEY_RIGHTCTRL)
			 MATRIX_KEY(0x05, 0x00, KEY_BACKSPACE)
			 MATRIX_KEY(0x06, 0x00, KEY_VOLUMEDOWN)
			 MATRIX_KEY(0x07, 0x00, KEY_VOLUMEUP)

			 MATRIX_KEY(0x03, 0x01, KEY_Z)
			 MATRIX_KEY(0x04, 0x01, KEY_A)
			 MATRIX_KEY(0x05, 0x01, KEY_Q)
			 MATRIX_KEY(0x06, 0x01, KEY_W)
			 MATRIX_KEY(0x07, 0x01, KEY_E)

			 MATRIX_KEY(0x03, 0x02, KEY_X)
			 MATRIX_KEY(0x04, 0x02, KEY_S)
			 MATRIX_KEY(0x05, 0x02, KEY_D)
			 MATRIX_KEY(0x06, 0x02, KEY_C)
			 MATRIX_KEY(0x07, 0x02, KEY_V)

			 MATRIX_KEY(0x03, 0x03, KEY_O)
			 MATRIX_KEY(0x04, 0x03, KEY_I)
			 MATRIX_KEY(0x05, 0x03, KEY_U)
			 MATRIX_KEY(0x06, 0x03, KEY_L)
			 MATRIX_KEY(0x07, 0x03, KEY_APOSTROPHE)

			 MATRIX_KEY(0x03, 0x04, KEY_Y)
			 MATRIX_KEY(0x04, 0x04, KEY_K)
			 MATRIX_KEY(0x05, 0x04, KEY_J)
			 MATRIX_KEY(0x06, 0x04, KEY_H)
			 MATRIX_KEY(0x07, 0x04, KEY_G)

			 MATRIX_KEY(0x03, 0x05, KEY_B)
			 MATRIX_KEY(0x04, 0x05, KEY_COMMA)
			 MATRIX_KEY(0x05, 0x05, KEY_M)
			 MATRIX_KEY(0x06, 0x05, KEY_N)
			 MATRIX_KEY(0x07, 0x05, KEY_DOT)

			 MATRIX_KEY(0x00, 0x06, KEY_SPACE)
			 MATRIX_KEY(0x03, 0x06, KEY_T)
			 MATRIX_KEY(0x04, 0x06, KEY_UP)
			 MATRIX_KEY(0x05, 0x06, KEY_LEFT)
			 MATRIX_KEY(0x06, 0x06, KEY_RIGHT)
			 MATRIX_KEY(0x07, 0x06, KEY_DOWN)

			 MATRIX_KEY(0x03, 0x07, KEY_P)
			 MATRIX_KEY(0x04, 0x07, KEY_ENTER)
			 MATRIX_KEY(0x05, 0x07, KEY_SLASH)
			 MATRIX_KEY(0x06, 0x07, KEY_F)
			 MATRIX_KEY(0x07, 0x07, KEY_R)
			 >;
};

&lis302 {
	st,axis-x = <(-2)>; /* LIS3_INV_DEV_Y */
	st,axis-y = <(-1)>; /* LIS3_INV_DEV_X */
	st,axis-z = <(-3)>; /* LIS3_INV_DEV_Z */

	st,min-limit-x = <(-32)>;
	st,min-limit-y = <3>;
	st,min-limit-z = <3>;

	st,max-limit-x = <(-3)>;
	st,max-limit-y = <32>;
	st,max-limit-z = <32>;
};

&dss {
	status = "okay";

	vdda_video-supply = <&vdac>;
};

&dsi {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&dsi_pins>;

	vdd-supply = <&vpll2>;

	port {
		dsi_out_ep: endpoint {
			remote-endpoint = <&lcd0_in>;
			lanes = <2 3 0 1 4 5>;
		};
	};

	lcd0: panel@0 {
		compatible = "nokia,himalaya", "panel-dsi-cm";
		reg = <0>;
		label = "lcd0";

		pinctrl-names = "default";
		pinctrl-0 = <&display_pins>;

		vpnl-supply = <&vmmc2>;
		vddi-supply = <&vio>;

		reset-gpios = <&gpio3 23 GPIO_ACTIVE_HIGH>;	/* 87 */
		te-gpios = <&gpio2 30 GPIO_ACTIVE_HIGH>;	/* 62 */

		width-mm = <49>; /* 48.960 mm */
		height-mm = <88>; /* 88.128 mm */

		/* TODO:
		 * - panel is upside-down
		 * - top + bottom 5px are not visible
		 */
		panel-timing {
			clock-frequency = <0>;          /* Calculated by dsi */

			hback-porch = <2>;
			hactive = <480>;
			hfront-porch = <0>;
			hsync-len = <2>;

			vback-porch = <1>;
			vactive = <864>;
			vfront-porch = <0>;
			vsync-len = <1>;

			hsync-active = <0>;
			vsync-active = <0>;
			de-active = <1>;
			pixelclk-active = <1>;
		};

		port {
			lcd0_in: endpoint {
				remote-endpoint = <&dsi_out_ep>;
			};
		};
	};
};
