// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2012 Texas Instruments Incorporated - https://www.ti.com/
 */
/dts-v1/;

#include "omap4460.dtsi"
#include "omap4-panda-common.dtsi"

/ {
	model = "TI OMAP4 PandaBoard-ES";
	compatible = "ti,omap4-panda-es", "ti,omap4-panda", "ti,omap4460", "ti,omap4430", "ti,omap4";
};

/* Audio routing is differnet between PandaBoard4430 and PandaBoardES */
&sound {
	ti,model = "PandaBoardES";

	/* Audio routing */
	ti,audio-routing =
		"Headset Stereophone", "HSOL",
		"Headset Stereophone", "HSOR",
		"Ext Spk", "HFL",
		"Ext Spk", "HFR",
		"Line Out", "AUXL",
		"Line Out", "AUXR",
		"AFML", "Line In",
		"AFMR", "Line In";
};

/* PandaboardES has external pullups on SCL & SDA */
&dss_hdmi_pins {
	pinctrl-single,pins = <
		OMAP4_IOPAD(0x09a, PIN_INPUT | MUX_MODE0)		/* hdmi_cec.hdmi_cec */
		OMAP4_IOPAD(0x09c, PIN_INPUT | MUX_MODE0)		/* hdmi_scl.hdmi_scl */
		OMAP4_IOPAD(0x09e, PIN_INPUT | MUX_MODE0)		/* hdmi_sda.hdmi_sda */
		>;
};

&omap4_pmx_core {
	led_gpio_pins: gpio-led-pmx-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x0f6, PIN_OUTPUT | MUX_MODE3)	/* gpio_110 */
		>;
	};

	button_pins: button-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x0fc, PIN_INPUT_PULLUP | MUX_MODE3) /* gpio_113 */
		>;
	};

	bt_pins: bt-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x06c, PIN_OUTPUT | MUX_MODE3)		/* gpmc_a22.gpio_46 - BTEN */
			OMAP4_IOPAD(0x072, PIN_OUTPUT_PULLUP | MUX_MODE3)	/* gpmc_a25.gpio_49 - BTWAKEUP */
		>;
	};

	uart2_pins: uart2-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x118, PIN_INPUT_PULLUP | MUX_MODE0)	/* uart2_cts.uart2_cts - HCI */
			OMAP4_IOPAD(0x11a, PIN_OUTPUT | MUX_MODE0)		/* uart2_rts.uart2_rts */
			OMAP4_IOPAD(0x11c, PIN_INPUT_PULLUP | MUX_MODE0)	/* uart2_rx.uart2_rx */
			OMAP4_IOPAD(0x11e, PIN_OUTPUT | MUX_MODE0)		/* uart2_tx.uart2_tx */
		>;
	};
};

&led_wkgpio_pins {
	pinctrl-single,pins = <
		OMAP4_IOPAD(0x05c, PIN_OUTPUT | MUX_MODE3)	/* gpio_wk8 */
	>;
};

&leds {
	pinctrl-0 = <
		&led_gpio_pins
		&led_wkgpio_pins
	>;

	led-heartbeat {
		gpios = <&gpio4 14 GPIO_ACTIVE_HIGH>;
	};
	led-mmc {
		gpios = <&gpio1 8 GPIO_ACTIVE_HIGH>;
	};
};

&gpio_keys {
	buttonS2 {
		gpios = <&gpio4 17 GPIO_ACTIVE_LOW>; /* gpio_113 */
	};
};

&gpio1_target {
	 ti,no-reset-on-init;
};

&wl12xx_gpio {
	pinctrl-single,pins = <
		OMAP4_IOPAD(0x066, PIN_OUTPUT | MUX_MODE3)		/* gpmc_a19.gpio_43 */
		OMAP4_IOPAD(0x070, PIN_OUTPUT_PULLUP | MUX_MODE3)	/* gpmc_a24.gpio_48 */
	>;
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pins &bt_pins>;
	bluetooth: tiwi {
		compatible = "ti,wl1271-st";
		enable-gpios = <&gpio2 14 GPIO_ACTIVE_HIGH>;	/* GPIO_46 */
	};
};
