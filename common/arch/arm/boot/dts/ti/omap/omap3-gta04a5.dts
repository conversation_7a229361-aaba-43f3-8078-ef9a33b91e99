// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014-18 <PERSON><PERSON> <<EMAIL>>
 */

#include "omap3-gta04.dtsi"

/ {
	model = "Goldelico GTA04A5/Letux 2804";

	sound {
		ti,jack-det-gpio = <&twl_gpio 2 GPIO_ACTIVE_HIGH>;	/* GTA04A5 only */
	};

	wlan_en: wlan_en_regulator {
		compatible = "regulator-fixed";
		pinctrl-names = "default";
		pinctrl-0 = <&wlan_pins>;
		regulator-name = "wlan-en-regulator";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;

		gpio = <&gpio5 10 GPIO_ACTIVE_HIGH>;	/* GPIO_138 */

		startup-delay-us = <70000>;
		enable-active-high;
	};

	pps {
		compatible = "pps-gpio";
		pinctrl-names = "default";
		pinctrl-0 = <&pps_pins>;

		gpios = <&gpio4 18 GPIO_ACTIVE_HIGH>; /* GPIN_114 */
	};

};

&gpio5 {
	irda-en-hog {
		gpio-hog;
		gpios = <(175-160) GPIO_ACTIVE_HIGH>;
		output-high;	/* activate gpio_175 to disable IrDA receiver */
	};
};

&omap3_pmx_core {
	bt_pins: bt-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2166, PIN_OUTPUT | MUX_MODE4)	/* mmc2_dat5 = mmc3_dat1 = gpio137 */
		>;
	};

	wlan_pins: wlan-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2168, PIN_OUTPUT | MUX_MODE4)	/* mmc2_dat6 = mmc3_dat2 = gpio138 */
		>;
	};

	wlan_irq_pin: wlan-irq-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x216a, PIN_INPUT_PULLUP | MUX_MODE4)	/* mmc2_dat7 = mmc3_dat3 = gpio139 */
		>;
	};

	irda_pins: irda-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21d0, PIN_OUTPUT_PULLUP | MUX_MODE4)	/* mcspi1_cs1 = gpio175 */
		>;
	};

	pps_pins: pps-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2138, PIN_INPUT | MUX_MODE4) /* gpin114 */
		>;
	};

	bno050_pins: pinmux-bno050-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2136, PIN_INPUT | MUX_MODE4) /* gpin113 */
		>;
	};
};

/*
 * for WL183x module see
 * Documentation/devicetree/bindings/net/wireless/ti,wlcore.yaml
 */

&wifi_pwrseq {
	/delete-property/ reset-gpios;
};

&mmc2 {
	vmmc-supply = <&wlan_en>;
	bus-width = <4>;
	cap-power-off-card;
	non-removable;

	pinctrl-names = "default";
	pinctrl-0 = <&wlan_irq_pin>;

	#address-cells = <1>;
	#size-cells = <0>;

	/delete-property/ mmc-pwrseq;

	wlcore: wlcore@2 {
		compatible = "ti,wl1837";
		reg = <2>;
		interrupt-parent = <&gpio5>;
		interrupts = <11 IRQ_TYPE_LEVEL_HIGH>;	/* GPIO_139 */
		ref-clock-frequency = <26000000>;
	};
};

&i2c2 {
	/delete-node/ bmp085@77;
	/delete-node/ bma180@41;
	/delete-node/ itg3200@68;
	/delete-node/ hmc5843@1e;

	gyrometer@69 {
		compatible = "bosch,bmg160";
		reg = <0x69>;
	};

	accelerometer@10 {
		compatible = "bosch,bmc150_accel";
		reg = <0x10>;
	};

	magnetometer@12 {
		compatible = "bosch,bmc150_magn";
		reg = <0x12>;
	};

	bme280@76 {
		compatible = "bosch,bme280";
		reg = <0x76>;
		vdda-supply = <&vio>;
		vddd-supply = <&vio>;
	};

	imu@29 {
		compatible = "bosch,bno055";
		reg = <0x29>;
		pinctrl-names = "default";
		pinctrl-0 = <&bno050_pins>;
		/* interrupt at &gpio4 17 */
	};
};
