// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for OMAP34xx/OMAP35xx SoC
 *
 * Copyright (C) 2013 Texas Instruments Incorporated - https://www.ti.com/
 */

#include <dt-bindings/bus/ti-sysc.h>
#include <dt-bindings/media/omap3-isp.h>

#include "omap3.dtsi"

/ {
	cpus {
		cpu: cpu@0 {
			/* OMAP343x/OMAP35xx variants OPP1-6 */
			operating-points-v2 = <&cpu0_opp_table>;

			clock-latency = <300000>; /* From legacy driver */
			#cooling-cells = <2>;
		};
	};

	cpu0_opp_table: opp-table {
		compatible = "operating-points-v2-ti-cpu";
		syscon = <&scm_conf>;

		opp-125000000 {
			opp-hz = /bits/ 64 <125000000>;
			/*
			 * we currently only select the max voltage from table
			 * Table 3-3 of the omap3530 Data sheet (SPRS507F).
			 * Format is: <target min max>
			 */
			opp-microvolt = <975000 975000 975000>;
			/*
			 * first value is silicon revision bit mask
			 * second one 720MHz Device Identification bit mask
			 */
			opp-supported-hw = <0xffffffff 3>;
		};

		opp-250000000 {
			opp-hz = /bits/ 64 <250000000>;
			opp-microvolt = <1075000 1075000 1075000>;
			opp-supported-hw = <0xffffffff 3>;
			opp-suspend;
		};

		opp-500000000 {
			opp-hz = /bits/ 64 <500000000>;
			opp-microvolt = <1200000 1200000 1200000>;
			opp-supported-hw = <0xffffffff 3>;
		};

		opp-550000000 {
			opp-hz = /bits/ 64 <550000000>;
			opp-microvolt = <1275000 1275000 1275000>;
			opp-supported-hw = <0xffffffff 3>;
		};

		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <1350000 1350000 1350000>;
			opp-supported-hw = <0xffffffff 3>;
		};

		opp-720000000 {
			opp-hz = /bits/ 64 <720000000>;
			opp-microvolt = <1350000 1350000 1350000>;
			/* only high-speed grade omap3530 devices */
			opp-supported-hw = <0xffffffff 2>;
			turbo-mode;
		};
	};

	ocp@68000000 {
		omap3_pmx_core2: pinmux@480025d8 {
			compatible = "ti,omap3-padconf", "pinctrl-single";
			reg = <0x480025d8 0x24>;
			#address-cells = <1>;
			#size-cells = <0>;
			#pinctrl-cells = <1>;
			#interrupt-cells = <1>;
			interrupt-controller;
			pinctrl-single,register-width = <16>;
			pinctrl-single,function-mask = <0xff1f>;
		};

		isp: isp@480bc000 {
			compatible = "ti,omap3-isp";
			reg = <0x480bc000 0x12fc
			       0x480bd800 0x017c>;
			interrupts = <24>;
			iommus = <&mmu_isp>;
			syscon = <&scm_conf 0x6c>;
			ti,phy-type = <OMAP3ISP_PHY_TYPE_COMPLEX_IO>;
			#clock-cells = <1>;
			ports {
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};

		bandgap: bandgap@48002524 {
			reg = <0x48002524 0x4>;
			compatible = "ti,omap34xx-bandgap";
			#thermal-sensor-cells = <0>;
		};

		target-module@480cb000 {
			compatible = "ti,sysc-omap3430-sr", "ti,sysc";
			ti,hwmods = "smartreflex_core";
			reg = <0x480cb024 0x4>;
			reg-names = "sysc";
			ti,sysc-mask = <SYSC_OMAP2_CLOCKACTIVITY>;
			clocks = <&sr2_fck>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x480cb000 0x001000>;

			smartreflex_core: smartreflex@0 {
				compatible = "ti,omap3-smartreflex-core";
				reg = <0 0x400>;
				interrupts = <19>;
			};
		};

		target-module@480c9000 {
			compatible = "ti,sysc-omap3430-sr", "ti,sysc";
			ti,hwmods = "smartreflex_mpu_iva";
			reg = <0x480c9024 0x4>;
			reg-names = "sysc";
			ti,sysc-mask = <SYSC_OMAP2_CLOCKACTIVITY>;
			clocks = <&sr1_fck>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x480c9000 0x001000>;

			smartreflex_mpu_iva: smartreflex@480c9000 {
				compatible = "ti,omap3-smartreflex-mpu-iva";
				reg = <0 0x400>;
				interrupts = <18>;
			};
		};

		/*
		 * On omap34xx the OCP registers do not seem to be accessible
		 * at all unlike on 36xx. Maybe SGX is permanently set to
		 * "OCP bypass mode", or maybe there is OCP_SYSCONFIG that is
		 * write-only at 0x50000e10. We detect SGX based on the SGX
		 * revision register instead of the unreadable OCP revision
		 * register. Also note that on early 34xx es1 revision there
		 * are also different clocks, but we do not have any dts users
		 * for it.
		 */
		sgx_module: target-module@50000000 {
			compatible = "ti,sysc-omap2", "ti,sysc";
			reg = <0x50000014 0x4>;
			reg-names = "rev";
			clocks = <&sgx_fck>, <&sgx_ick>;
			clock-names = "fck", "ick";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x50000000 0x4000>;

			/*
			 * Closed source PowerVR driver, no child device
			 * binding or driver in mainline
			 */
		};
	};

	thermal_zones: thermal-zones {
		#include "omap3-cpu-thermal.dtsi"
	};
};

&ssi {
	status = "okay";

	clocks = <&ssi_ssr_fck>,
		 <&ssi_sst_fck>,
		 <&ssi_ick>;
	clock-names = "ssi_ssr_fck",
		      "ssi_sst_fck",
		      "ssi_ick";
};

&usb_otg_target {
	clocks = <&hsotgusb_ick_3430es2>;
};

/include/ "omap34xx-omap36xx-clocks.dtsi"
/include/ "omap36xx-omap3430es2plus-clocks.dtsi"
/include/ "omap36xx-am35xx-omap3430es2plus-clocks.dtsi"
