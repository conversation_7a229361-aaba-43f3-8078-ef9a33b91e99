// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for OMAP5 clock data
 *
 * Copyright (C) 2013 Texas Instruments, Inc.
 */
&cm_core_aon_clocks {
	pad_clks_src_ck: pad_clks_src_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "pad_clks_src_ck";
		clock-frequency = <12000000>;
	};

	pad_clks_ck: pad_clks_ck@108 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "pad_clks_ck";
		clocks = <&pad_clks_src_ck>;
		ti,bit-shift = <8>;
		reg = <0x0108>;
	};

	secure_32k_clk_src_ck: secure_32k_clk_src_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "secure_32k_clk_src_ck";
		clock-frequency = <32768>;
	};

	slimbus_src_clk: slimbus_src_clk {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "slimbus_src_clk";
		clock-frequency = <12000000>;
	};

	slimbus_clk: slimbus_clk@108 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "slimbus_clk";
		clocks = <&slimbus_src_clk>;
		ti,bit-shift = <10>;
		reg = <0x0108>;
	};

	sys_32k_ck: sys_32k_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "sys_32k_ck";
		clock-frequency = <32768>;
	};

	virt_12000000_ck: virt_12000000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_12000000_ck";
		clock-frequency = <12000000>;
	};

	virt_13000000_ck: virt_13000000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_13000000_ck";
		clock-frequency = <13000000>;
	};

	virt_16800000_ck: virt_16800000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_16800000_ck";
		clock-frequency = <16800000>;
	};

	virt_19200000_ck: virt_19200000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_19200000_ck";
		clock-frequency = <19200000>;
	};

	virt_26000000_ck: virt_26000000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_26000000_ck";
		clock-frequency = <26000000>;
	};

	virt_27000000_ck: virt_27000000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_27000000_ck";
		clock-frequency = <27000000>;
	};

	virt_38400000_ck: virt_38400000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_38400000_ck";
		clock-frequency = <38400000>;
	};

	xclk60mhsp1_ck: xclk60mhsp1_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "xclk60mhsp1_ck";
		clock-frequency = <60000000>;
	};

	xclk60mhsp2_ck: xclk60mhsp2_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "xclk60mhsp2_ck";
		clock-frequency = <60000000>;
	};

	dpll_abe_ck: dpll_abe_ck@1e0 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-m4xen-clock";
		clock-output-names = "dpll_abe_ck";
		clocks = <&abe_dpll_clk_mux>, <&abe_dpll_bypass_clk_mux>;
		reg = <0x01e0>, <0x01e4>, <0x01ec>, <0x01e8>;
	};

	dpll_abe_x2_ck: dpll_abe_x2_ck {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-x2-clock";
		clock-output-names = "dpll_abe_x2_ck";
		clocks = <&dpll_abe_ck>;
	};

	dpll_abe_m2x2_ck: dpll_abe_m2x2_ck@1f0 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_abe_m2x2_ck";
		clocks = <&dpll_abe_x2_ck>;
		ti,max-div = <31>;
		reg = <0x01f0>;
		ti,index-starts-at-one;
	};

	abe_24m_fclk: abe_24m_fclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "abe_24m_fclk";
		clocks = <&dpll_abe_m2x2_ck>;
		clock-mult = <1>;
		clock-div = <8>;
	};

	abe_clk: abe_clk@108 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "abe_clk";
		clocks = <&dpll_abe_m2x2_ck>;
		ti,max-div = <4>;
		reg = <0x0108>;
		ti,index-power-of-two;
	};

	abe_iclk: abe_iclk@528 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "abe_iclk";
		clocks = <&aess_fclk>;
		ti,bit-shift = <24>;
		reg = <0x0528>;
		ti,dividers = <2>, <1>;
	};

	abe_lp_clk_div: abe_lp_clk_div {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "abe_lp_clk_div";
		clocks = <&dpll_abe_m2x2_ck>;
		clock-mult = <1>;
		clock-div = <16>;
	};

	dpll_abe_m3x2_ck: dpll_abe_m3x2_ck@1f4 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_abe_m3x2_ck";
		clocks = <&dpll_abe_x2_ck>;
		ti,max-div = <31>;
		reg = <0x01f4>;
		ti,index-starts-at-one;
	};

	dpll_core_byp_mux: dpll_core_byp_mux@12c {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "dpll_core_byp_mux";
		clocks = <&sys_clkin>, <&dpll_abe_m3x2_ck>;
		ti,bit-shift = <23>;
		reg = <0x012c>;
	};

	dpll_core_ck: dpll_core_ck@120 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-core-clock";
		clock-output-names = "dpll_core_ck";
		clocks = <&sys_clkin>, <&dpll_core_byp_mux>;
		reg = <0x0120>, <0x0124>, <0x012c>, <0x0128>;
	};

	dpll_core_x2_ck: dpll_core_x2_ck {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-x2-clock";
		clock-output-names = "dpll_core_x2_ck";
		clocks = <&dpll_core_ck>;
	};

	dpll_core_h21x2_ck: dpll_core_h21x2_ck@150 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_h21x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <63>;
		reg = <0x0150>;
		ti,index-starts-at-one;
	};

	c2c_fclk: c2c_fclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "c2c_fclk";
		clocks = <&dpll_core_h21x2_ck>;
		clock-mult = <1>;
		clock-div = <1>;
	};

	c2c_iclk: c2c_iclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "c2c_iclk";
		clocks = <&c2c_fclk>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	dpll_core_h11x2_ck: dpll_core_h11x2_ck@138 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_h11x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <63>;
		reg = <0x0138>;
		ti,index-starts-at-one;
	};

	dpll_core_h12x2_ck: dpll_core_h12x2_ck@13c {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_h12x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <63>;
		reg = <0x013c>;
		ti,index-starts-at-one;
	};

	dpll_core_h13x2_ck: dpll_core_h13x2_ck@140 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_h13x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <63>;
		reg = <0x0140>;
		ti,index-starts-at-one;
	};

	dpll_core_h14x2_ck: dpll_core_h14x2_ck@144 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_h14x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <63>;
		reg = <0x0144>;
		ti,index-starts-at-one;
	};

	dpll_core_h22x2_ck: dpll_core_h22x2_ck@154 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_h22x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <63>;
		reg = <0x0154>;
		ti,index-starts-at-one;
	};

	dpll_core_h23x2_ck: dpll_core_h23x2_ck@158 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_h23x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <63>;
		reg = <0x0158>;
		ti,index-starts-at-one;
	};

	dpll_core_h24x2_ck: dpll_core_h24x2_ck@15c {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_h24x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <63>;
		reg = <0x015c>;
		ti,index-starts-at-one;
	};

	dpll_core_m2_ck: dpll_core_m2_ck@130 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_m2_ck";
		clocks = <&dpll_core_ck>;
		ti,max-div = <31>;
		reg = <0x0130>;
		ti,index-starts-at-one;
	};

	dpll_core_m3x2_ck: dpll_core_m3x2_ck@134 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_m3x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <31>;
		reg = <0x0134>;
		ti,index-starts-at-one;
	};

	iva_dpll_hs_clk_div: iva_dpll_hs_clk_div {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "iva_dpll_hs_clk_div";
		clocks = <&dpll_core_h12x2_ck>;
		clock-mult = <1>;
		clock-div = <1>;
	};

	dpll_iva_byp_mux: dpll_iva_byp_mux@1ac {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "dpll_iva_byp_mux";
		clocks = <&sys_clkin>, <&iva_dpll_hs_clk_div>;
		ti,bit-shift = <23>;
		reg = <0x01ac>;
	};

	dpll_iva_ck: dpll_iva_ck@1a0 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-clock";
		clock-output-names = "dpll_iva_ck";
		clocks = <&sys_clkin>, <&dpll_iva_byp_mux>;
		reg = <0x01a0>, <0x01a4>, <0x01ac>, <0x01a8>;
		assigned-clocks = <&dpll_iva_ck>;
		assigned-clock-rates = <1165000000>;
	};

	dpll_iva_x2_ck: dpll_iva_x2_ck {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-x2-clock";
		clock-output-names = "dpll_iva_x2_ck";
		clocks = <&dpll_iva_ck>;
	};

	dpll_iva_h11x2_ck: dpll_iva_h11x2_ck@1b8 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_iva_h11x2_ck";
		clocks = <&dpll_iva_x2_ck>;
		ti,max-div = <63>;
		reg = <0x01b8>;
		ti,index-starts-at-one;
		assigned-clocks = <&dpll_iva_h11x2_ck>;
		assigned-clock-rates = <465920000>;
	};

	dpll_iva_h12x2_ck: dpll_iva_h12x2_ck@1bc {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_iva_h12x2_ck";
		clocks = <&dpll_iva_x2_ck>;
		ti,max-div = <63>;
		reg = <0x01bc>;
		ti,index-starts-at-one;
		assigned-clocks = <&dpll_iva_h12x2_ck>;
		assigned-clock-rates = <388300000>;
	};

	mpu_dpll_hs_clk_div: mpu_dpll_hs_clk_div {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "mpu_dpll_hs_clk_div";
		clocks = <&dpll_core_h12x2_ck>;
		clock-mult = <1>;
		clock-div = <1>;
	};

	dpll_mpu_ck: dpll_mpu_ck@160 {
		#clock-cells = <0>;
		compatible = "ti,omap5-mpu-dpll-clock";
		clock-output-names = "dpll_mpu_ck";
		clocks = <&sys_clkin>, <&mpu_dpll_hs_clk_div>;
		reg = <0x0160>, <0x0164>, <0x016c>, <0x0168>;
	};

	dpll_mpu_m2_ck: dpll_mpu_m2_ck@170 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_mpu_m2_ck";
		clocks = <&dpll_mpu_ck>;
		ti,max-div = <31>;
		reg = <0x0170>;
		ti,index-starts-at-one;
	};

	per_dpll_hs_clk_div: per_dpll_hs_clk_div {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "per_dpll_hs_clk_div";
		clocks = <&dpll_abe_m3x2_ck>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	usb_dpll_hs_clk_div: usb_dpll_hs_clk_div {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "usb_dpll_hs_clk_div";
		clocks = <&dpll_abe_m3x2_ck>;
		clock-mult = <1>;
		clock-div = <3>;
	};

	l3_iclk_div: l3_iclk_div@100 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "l3_iclk_div";
		ti,max-div = <2>;
		ti,bit-shift = <4>;
		reg = <0x100>;
		clocks = <&dpll_core_h12x2_ck>;
		ti,index-power-of-two;
	};

	gpu_l3_iclk: gpu_l3_iclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "gpu_l3_iclk";
		clocks = <&l3_iclk_div>;
		clock-mult = <1>;
		clock-div = <1>;
	};

	l4_root_clk_div: l4_root_clk_div@100 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "l4_root_clk_div";
		ti,max-div = <2>;
		ti,bit-shift = <8>;
		reg = <0x100>;
		clocks = <&l3_iclk_div>;
		ti,index-power-of-two;
	};

	slimbus1_slimbus_clk: slimbus1_slimbus_clk@560 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "slimbus1_slimbus_clk";
		clocks = <&slimbus_clk>;
		ti,bit-shift = <11>;
		reg = <0x0560>;
	};

	aess_fclk: aess_fclk@528 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "aess_fclk";
		clocks = <&abe_clk>;
		ti,bit-shift = <24>;
		ti,max-div = <2>;
		reg = <0x0528>;
	};

	mcasp_sync_mux_ck: mcasp_sync_mux_ck@540 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "mcasp_sync_mux_ck";
		clocks = <&abe_24m_fclk>, <&dss_syc_gfclk_div>, <&func_24m_clk>;
		ti,bit-shift = <26>;
		reg = <0x0540>;
	};

	mcasp_gfclk: mcasp_gfclk@540 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "mcasp_gfclk";
		clocks = <&mcasp_sync_mux_ck>, <&pad_clks_ck>, <&slimbus_clk>;
		ti,bit-shift = <24>;
		reg = <0x0540>;
	};

	dummy_ck: dummy_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "dummy_ck";
		clock-frequency = <0>;
	};
};
&prm_clocks {
	sys_clkin: sys_clkin@110 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "sys_clkin";
		clocks = <&virt_12000000_ck>, <&virt_13000000_ck>, <&virt_16800000_ck>, <&virt_19200000_ck>, <&virt_26000000_ck>, <&virt_27000000_ck>, <&virt_38400000_ck>;
		reg = <0x0110>;
		ti,index-starts-at-one;
	};

	abe_dpll_bypass_clk_mux: abe_dpll_bypass_clk_mux@108 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "abe_dpll_bypass_clk_mux";
		clocks = <&sys_clkin>, <&sys_32k_ck>;
		reg = <0x0108>;
	};

	abe_dpll_clk_mux: abe_dpll_clk_mux@10c {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "abe_dpll_clk_mux";
		clocks = <&sys_clkin>, <&sys_32k_ck>;
		reg = <0x010c>;
	};

	custefuse_sys_gfclk_div: custefuse_sys_gfclk_div {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "custefuse_sys_gfclk_div";
		clocks = <&sys_clkin>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	dss_syc_gfclk_div: dss_syc_gfclk_div {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "dss_syc_gfclk_div";
		clocks = <&sys_clkin>;
		clock-mult = <1>;
		clock-div = <1>;
	};

	wkupaon_iclk_mux: wkupaon_iclk_mux@108 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "wkupaon_iclk_mux";
		clocks = <&sys_clkin>, <&abe_lp_clk_div>;
		reg = <0x0108>;
	};

	l3instr_ts_gclk_div: l3instr_ts_gclk_div {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "l3instr_ts_gclk_div";
		clocks = <&wkupaon_iclk_mux>;
		clock-mult = <1>;
		clock-div = <1>;
	};
};

&cm_core_clocks {

	dpll_per_byp_mux: dpll_per_byp_mux@14c {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "dpll_per_byp_mux";
		clocks = <&sys_clkin>, <&per_dpll_hs_clk_div>;
		ti,bit-shift = <23>;
		reg = <0x014c>;
	};

	dpll_per_ck: dpll_per_ck@140 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-clock";
		clock-output-names = "dpll_per_ck";
		clocks = <&sys_clkin>, <&dpll_per_byp_mux>;
		reg = <0x0140>, <0x0144>, <0x014c>, <0x0148>;
	};

	dpll_per_x2_ck: dpll_per_x2_ck {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-x2-clock";
		clock-output-names = "dpll_per_x2_ck";
		clocks = <&dpll_per_ck>;
	};

	dpll_per_h11x2_ck: dpll_per_h11x2_ck@158 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_h11x2_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,max-div = <63>;
		reg = <0x0158>;
		ti,index-starts-at-one;
	};

	dpll_per_h12x2_ck: dpll_per_h12x2_ck@15c {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_h12x2_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,max-div = <63>;
		reg = <0x015c>;
		ti,index-starts-at-one;
	};

	dpll_per_h14x2_ck: dpll_per_h14x2_ck@164 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_h14x2_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,max-div = <63>;
		reg = <0x0164>;
		ti,index-starts-at-one;
	};

	dpll_per_m2_ck: dpll_per_m2_ck@150 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_m2_ck";
		clocks = <&dpll_per_ck>;
		ti,max-div = <31>;
		reg = <0x0150>;
		ti,index-starts-at-one;
	};

	dpll_per_m2x2_ck: dpll_per_m2x2_ck@150 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_m2x2_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,max-div = <31>;
		reg = <0x0150>;
		ti,index-starts-at-one;
	};

	dpll_per_m3x2_ck: dpll_per_m3x2_ck@154 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_m3x2_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,max-div = <31>;
		reg = <0x0154>;
		ti,index-starts-at-one;
	};

	dpll_unipro1_ck: dpll_unipro1_ck@200 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-clock";
		clock-output-names = "dpll_unipro1_ck";
		clocks = <&sys_clkin>, <&sys_clkin>;
		reg = <0x0200>, <0x0204>, <0x020c>, <0x0208>;
	};

	dpll_unipro1_clkdcoldo: dpll_unipro1_clkdcoldo {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "dpll_unipro1_clkdcoldo";
		clocks = <&dpll_unipro1_ck>;
		clock-mult = <1>;
		clock-div = <1>;
	};

	dpll_unipro1_m2_ck: dpll_unipro1_m2_ck@210 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_unipro1_m2_ck";
		clocks = <&dpll_unipro1_ck>;
		ti,max-div = <127>;
		reg = <0x0210>;
		ti,index-starts-at-one;
	};

	dpll_unipro2_ck: dpll_unipro2_ck@1c0 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-clock";
		clock-output-names = "dpll_unipro2_ck";
		clocks = <&sys_clkin>, <&sys_clkin>;
		reg = <0x01c0>, <0x01c4>, <0x01cc>, <0x01c8>;
	};

	dpll_unipro2_clkdcoldo: dpll_unipro2_clkdcoldo {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "dpll_unipro2_clkdcoldo";
		clocks = <&dpll_unipro2_ck>;
		clock-mult = <1>;
		clock-div = <1>;
	};

	dpll_unipro2_m2_ck: dpll_unipro2_m2_ck@1d0 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_unipro2_m2_ck";
		clocks = <&dpll_unipro2_ck>;
		ti,max-div = <127>;
		reg = <0x01d0>;
		ti,index-starts-at-one;
	};

	dpll_usb_byp_mux: dpll_usb_byp_mux@18c {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "dpll_usb_byp_mux";
		clocks = <&sys_clkin>, <&usb_dpll_hs_clk_div>;
		ti,bit-shift = <23>;
		reg = <0x018c>;
	};

	dpll_usb_ck: dpll_usb_ck@180 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-j-type-clock";
		clock-output-names = "dpll_usb_ck";
		clocks = <&sys_clkin>, <&dpll_usb_byp_mux>;
		reg = <0x0180>, <0x0184>, <0x018c>, <0x0188>;
	};

	dpll_usb_clkdcoldo: dpll_usb_clkdcoldo {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "dpll_usb_clkdcoldo";
		clocks = <&dpll_usb_ck>;
		clock-mult = <1>;
		clock-div = <1>;
	};

	dpll_usb_m2_ck: dpll_usb_m2_ck@190 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_usb_m2_ck";
		clocks = <&dpll_usb_ck>;
		ti,max-div = <127>;
		reg = <0x0190>;
		ti,index-starts-at-one;
	};

	func_128m_clk: func_128m_clk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "func_128m_clk";
		clocks = <&dpll_per_h11x2_ck>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	func_12m_fclk: func_12m_fclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "func_12m_fclk";
		clocks = <&dpll_per_m2x2_ck>;
		clock-mult = <1>;
		clock-div = <16>;
	};

	func_24m_clk: func_24m_clk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "func_24m_clk";
		clocks = <&dpll_per_m2_ck>;
		clock-mult = <1>;
		clock-div = <4>;
	};

	func_48m_fclk: func_48m_fclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "func_48m_fclk";
		clocks = <&dpll_per_m2x2_ck>;
		clock-mult = <1>;
		clock-div = <4>;
	};

	func_96m_fclk: func_96m_fclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "func_96m_fclk";
		clocks = <&dpll_per_m2x2_ck>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	l3init_60m_fclk: l3init_60m_fclk@104 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "l3init_60m_fclk";
		clocks = <&dpll_usb_m2_ck>;
		reg = <0x0104>;
		ti,dividers = <1>, <8>;
	};

	iss_ctrlclk: iss_ctrlclk@1320 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "iss_ctrlclk";
		clocks = <&func_96m_fclk>;
		ti,bit-shift = <8>;
		reg = <0x1320>;
	};

	lli_txphy_clk: lli_txphy_clk@f20 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "lli_txphy_clk";
		clocks = <&dpll_unipro1_clkdcoldo>;
		ti,bit-shift = <8>;
		reg = <0x0f20>;
	};

	lli_txphy_ls_clk: lli_txphy_ls_clk@f20 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "lli_txphy_ls_clk";
		clocks = <&dpll_unipro1_m2_ck>;
		ti,bit-shift = <9>;
		reg = <0x0f20>;
	};

	usb_phy_cm_clk32k: usb_phy_cm_clk32k@640 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "usb_phy_cm_clk32k";
		clocks = <&sys_32k_ck>;
		ti,bit-shift = <8>;
		reg = <0x0640>;
	};

	fdif_fclk: fdif_fclk@1328 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "fdif_fclk";
		clocks = <&dpll_per_h11x2_ck>;
		ti,bit-shift = <24>;
		ti,max-div = <2>;
		reg = <0x1328>;
	};

	gpu_core_gclk_mux: gpu_core_gclk_mux@1520 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "gpu_core_gclk_mux";
		clocks = <&dpll_core_h14x2_ck>, <&dpll_per_h14x2_ck>;
		ti,bit-shift = <24>;
		reg = <0x1520>;
	};

	gpu_hyd_gclk_mux: gpu_hyd_gclk_mux@1520 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "gpu_hyd_gclk_mux";
		clocks = <&dpll_core_h14x2_ck>, <&dpll_per_h14x2_ck>;
		ti,bit-shift = <25>;
		reg = <0x1520>;
	};

	hsi_fclk: hsi_fclk@1638 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "hsi_fclk";
		clocks = <&dpll_per_m2x2_ck>;
		ti,bit-shift = <24>;
		ti,max-div = <2>;
		reg = <0x1638>;
	};
};

&cm_core_clockdomains {
	l3init_clkdm: l3init_clkdm {
		compatible = "ti,clockdomain";
		clock-output-names = "l3init_clkdm";
		clocks = <&dpll_usb_ck>;
	};
};

&scrm_clocks {
	auxclk0_src_gate_ck: auxclk0_src_gate_ck@310 {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "auxclk0_src_gate_ck";
		clocks = <&dpll_core_m3x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x0310>;
	};

	auxclk0_src_mux_ck: auxclk0_src_mux_ck@310 {
		#clock-cells = <0>;
		compatible = "ti,composite-mux-clock";
		clock-output-names = "auxclk0_src_mux_ck";
		clocks = <&sys_clkin>, <&dpll_core_m3x2_ck>, <&dpll_per_m3x2_ck>;
		ti,bit-shift = <1>;
		reg = <0x0310>;
	};

	auxclk0_src_ck: auxclk0_src_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "auxclk0_src_ck";
		clocks = <&auxclk0_src_gate_ck>, <&auxclk0_src_mux_ck>;
	};

	auxclk0_ck: auxclk0_ck@310 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "auxclk0_ck";
		clocks = <&auxclk0_src_ck>;
		ti,bit-shift = <16>;
		ti,max-div = <16>;
		reg = <0x0310>;
	};

	auxclk1_src_gate_ck: auxclk1_src_gate_ck@314 {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "auxclk1_src_gate_ck";
		clocks = <&dpll_core_m3x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x0314>;
	};

	auxclk1_src_mux_ck: auxclk1_src_mux_ck@314 {
		#clock-cells = <0>;
		compatible = "ti,composite-mux-clock";
		clock-output-names = "auxclk1_src_mux_ck";
		clocks = <&sys_clkin>, <&dpll_core_m3x2_ck>, <&dpll_per_m3x2_ck>;
		ti,bit-shift = <1>;
		reg = <0x0314>;
	};

	auxclk1_src_ck: auxclk1_src_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "auxclk1_src_ck";
		clocks = <&auxclk1_src_gate_ck>, <&auxclk1_src_mux_ck>;
	};

	auxclk1_ck: auxclk1_ck@314 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "auxclk1_ck";
		clocks = <&auxclk1_src_ck>;
		ti,bit-shift = <16>;
		ti,max-div = <16>;
		reg = <0x0314>;
	};

	auxclk2_src_gate_ck: auxclk2_src_gate_ck@318 {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "auxclk2_src_gate_ck";
		clocks = <&dpll_core_m3x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x0318>;
	};

	auxclk2_src_mux_ck: auxclk2_src_mux_ck@318 {
		#clock-cells = <0>;
		compatible = "ti,composite-mux-clock";
		clock-output-names = "auxclk2_src_mux_ck";
		clocks = <&sys_clkin>, <&dpll_core_m3x2_ck>, <&dpll_per_m3x2_ck>;
		ti,bit-shift = <1>;
		reg = <0x0318>;
	};

	auxclk2_src_ck: auxclk2_src_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "auxclk2_src_ck";
		clocks = <&auxclk2_src_gate_ck>, <&auxclk2_src_mux_ck>;
	};

	auxclk2_ck: auxclk2_ck@318 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "auxclk2_ck";
		clocks = <&auxclk2_src_ck>;
		ti,bit-shift = <16>;
		ti,max-div = <16>;
		reg = <0x0318>;
	};

	auxclk3_src_gate_ck: auxclk3_src_gate_ck@31c {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "auxclk3_src_gate_ck";
		clocks = <&dpll_core_m3x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x031c>;
	};

	auxclk3_src_mux_ck: auxclk3_src_mux_ck@31c {
		#clock-cells = <0>;
		compatible = "ti,composite-mux-clock";
		clock-output-names = "auxclk3_src_mux_ck";
		clocks = <&sys_clkin>, <&dpll_core_m3x2_ck>, <&dpll_per_m3x2_ck>;
		ti,bit-shift = <1>;
		reg = <0x031c>;
	};

	auxclk3_src_ck: auxclk3_src_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "auxclk3_src_ck";
		clocks = <&auxclk3_src_gate_ck>, <&auxclk3_src_mux_ck>;
	};

	auxclk3_ck: auxclk3_ck@31c {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "auxclk3_ck";
		clocks = <&auxclk3_src_ck>;
		ti,bit-shift = <16>;
		ti,max-div = <16>;
		reg = <0x031c>;
	};

	auxclk4_src_gate_ck: auxclk4_src_gate_ck@320 {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "auxclk4_src_gate_ck";
		clocks = <&dpll_core_m3x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x0320>;
	};

	auxclk4_src_mux_ck: auxclk4_src_mux_ck@320 {
		#clock-cells = <0>;
		compatible = "ti,composite-mux-clock";
		clock-output-names = "auxclk4_src_mux_ck";
		clocks = <&sys_clkin>, <&dpll_core_m3x2_ck>, <&dpll_per_m3x2_ck>;
		ti,bit-shift = <1>;
		reg = <0x0320>;
	};

	auxclk4_src_ck: auxclk4_src_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "auxclk4_src_ck";
		clocks = <&auxclk4_src_gate_ck>, <&auxclk4_src_mux_ck>;
	};

	auxclk4_ck: auxclk4_ck@320 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "auxclk4_ck";
		clocks = <&auxclk4_src_ck>;
		ti,bit-shift = <16>;
		ti,max-div = <16>;
		reg = <0x0320>;
	};

	auxclkreq0_ck: auxclkreq0_ck@210 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "auxclkreq0_ck";
		clocks = <&auxclk0_ck>, <&auxclk1_ck>, <&auxclk2_ck>, <&auxclk3_ck>, <&auxclk4_ck>;
		ti,bit-shift = <2>;
		reg = <0x0210>;
	};

	auxclkreq1_ck: auxclkreq1_ck@214 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "auxclkreq1_ck";
		clocks = <&auxclk0_ck>, <&auxclk1_ck>, <&auxclk2_ck>, <&auxclk3_ck>, <&auxclk4_ck>;
		ti,bit-shift = <2>;
		reg = <0x0214>;
	};

	auxclkreq2_ck: auxclkreq2_ck@218 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "auxclkreq2_ck";
		clocks = <&auxclk0_ck>, <&auxclk1_ck>, <&auxclk2_ck>, <&auxclk3_ck>, <&auxclk4_ck>;
		ti,bit-shift = <2>;
		reg = <0x0218>;
	};

	auxclkreq3_ck: auxclkreq3_ck@21c {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "auxclkreq3_ck";
		clocks = <&auxclk0_ck>, <&auxclk1_ck>, <&auxclk2_ck>, <&auxclk3_ck>, <&auxclk4_ck>;
		ti,bit-shift = <2>;
		reg = <0x021c>;
	};
};

&cm_core_aon {
	mpu_cm: mpu_cm@300 {
		compatible = "ti,omap4-cm";
		clock-output-names = "mpu_cm";
		reg = <0x300 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x300 0x100>;

		mpu_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "mpu_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	dsp_cm: dsp_cm@400 {
		compatible = "ti,omap4-cm";
		clock-output-names = "dsp_cm";
		reg = <0x400 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x400 0x100>;

		dsp_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "dsp_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	abe_cm: abe_cm@500 {
		compatible = "ti,omap4-cm";
		clock-output-names = "abe_cm";
		reg = <0x500 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x500 0x100>;

		abe_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "abe_clkctrl";
			reg = <0x20 0x64>;
			#clock-cells = <2>;
		};
	};

};

&cm_core {
	l3main1_cm: l3main1_cm@700 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3main1_cm";
		reg = <0x700 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x700 0x100>;

		l3main1_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3main1_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	l3main2_cm: l3main2_cm@800 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3main2_cm";
		reg = <0x800 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x800 0x100>;

		l3main2_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3main2_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	ipu_cm: ipu_cm@900 {
		compatible = "ti,omap4-cm";
		clock-output-names = "ipu_cm";
		reg = <0x900 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x900 0x100>;

		ipu_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "ipu_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	dma_cm: dma_cm@a00 {
		compatible = "ti,omap4-cm";
		clock-output-names = "dma_cm";
		reg = <0xa00 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xa00 0x100>;

		dma_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "dma_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	emif_cm: emif_cm@b00 {
		compatible = "ti,omap4-cm";
		clock-output-names = "emif_cm";
		reg = <0xb00 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xb00 0x100>;

		emif_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "emif_clkctrl";
			reg = <0x20 0x1c>;
			#clock-cells = <2>;
		};
	};

	l4cfg_cm: l4cfg_cm@d00 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l4cfg_cm";
		reg = <0xd00 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xd00 0x100>;

		l4cfg_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l4cfg_clkctrl";
			reg = <0x20 0x14>;
			#clock-cells = <2>;
		};
	};

	l3instr_cm: l3instr_cm@e00 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3instr_cm";
		reg = <0xe00 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xe00 0x100>;

		l3instr_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3instr_clkctrl";
			reg = <0x20 0xc>;
			#clock-cells = <2>;
		};
	};

	l4per_cm: clock@1000 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l4per_cm";
		reg = <0x1000 0x200>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1000 0x200>;

		l4per_clkctrl: clock@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l4per_clkctrl";
			reg = <0x20 0x15c>;
			#clock-cells = <2>;
		};

		l4sec_clkctrl: clock@1a0 {
			compatible = "ti,clkctrl";
			clock-output-names = "l4sec_clkctrl";
			reg = <0x1a0 0x3c>;
			#clock-cells = <2>;
		};
	};

	dss_cm: dss_cm@1400 {
		compatible = "ti,omap4-cm";
		clock-output-names = "dss_cm";
		reg = <0x1400 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1400 0x100>;

		dss_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "dss_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	gpu_cm: gpu_cm@1500 {
		compatible = "ti,omap4-cm";
		clock-output-names = "gpu_cm";
		reg = <0x1500 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1500 0x100>;

		gpu_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "gpu_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	l3init_cm: l3init_cm@1600 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3init_cm";
		reg = <0x1600 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1600 0x100>;

		l3init_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3init_clkctrl";
			reg = <0x20 0xd4>;
			#clock-cells = <2>;
		};
	};
};

&prm {
	wkupaon_cm: wkupaon_cm@1900 {
		compatible = "ti,omap4-cm";
		clock-output-names = "wkupaon_cm";
		reg = <0x1900 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1900 0x100>;

		wkupaon_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "wkupaon_clkctrl";
			reg = <0x20 0x5c>;
			#clock-cells = <2>;
		};
	};
};

&scm_wkup_pad_conf_clocks {
	fref_xtal_ck: fref_xtal_ck {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "fref_xtal_ck";
		clocks = <&sys_clkin>;
		ti,bit-shift = <28>;
		reg = <0x14>;
	};
};
