// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for OMAP34xx/OMAP36xx clock data
 *
 * Copyright (C) 2013 Texas Instruments, Inc.
 */
&cm_clocks {
	clock@a00 {
		compatible = "ti,clksel";
		reg = <0xa00>;
		#clock-cells = <2>;
		#address-cells = <0>;

		ssi_ssr_gate_fck_3430es2: clock-ssi-ssr-gate-fck-3430es2 {
			#clock-cells = <0>;
			compatible = "ti,composite-no-wait-gate-clock";
			clock-output-names = "ssi_ssr_gate_fck_3430es2";
			clocks = <&corex2_fck>;
			ti,bit-shift = <0>;
		};
	};

	clock@a40 {
		compatible = "ti,clksel";
		reg = <0xa40>;
		#clock-cells = <2>;
		#address-cells = <0>;

		ssi_ssr_div_fck_3430es2: clock-ssi-ssr-div-fck-3430es2 {
			#clock-cells = <0>;
			compatible = "ti,composite-divider-clock";
			clock-output-names = "ssi_ssr_div_fck_3430es2";
			clocks = <&corex2_fck>;
			ti,bit-shift = <8>;
			ti,dividers = <0>, <1>, <2>, <3>, <4>, <0>, <6>, <0>, <8>;
		};
	};

	ssi_ssr_fck: ssi_ssr_fck_3430es2 {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clocks = <&ssi_ssr_gate_fck_3430es2>, <&ssi_ssr_div_fck_3430es2>;
	};

	ssi_sst_fck: ssi_sst_fck_3430es2 {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&ssi_ssr_fck>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	clock@a10 {
		compatible = "ti,clksel";
		reg = <0xa10>;
		#clock-cells = <2>;
		#address-cells = <0>;

		hsotgusb_ick_3430es2: clock-hsotgusb-ick-3430es2 {
			#clock-cells = <0>;
			compatible = "ti,omap3-hsotgusb-interface-clock";
			clock-output-names = "hsotgusb_ick_3430es2";
			clocks = <&core_l3_ick>;
			ti,bit-shift = <4>;
		};

		ssi_ick: clock-ssi-ick-3430es2 {
			#clock-cells = <0>;
			compatible = "ti,omap3-ssi-interface-clock";
			clock-output-names = "ssi_ick_3430es2";
			clocks = <&ssi_l4_ick>;
			ti,bit-shift = <0>;
		};
	};

	ssi_l4_ick: ssi_l4_ick {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&l4_ick>;
		clock-mult = <1>;
		clock-div = <1>;
	};

	clock@c00 {
		compatible = "ti,clksel";
		reg = <0xc00>;
		#clock-cells = <2>;
		#address-cells = <0>;

		usim_gate_fck: clock-usim-gate-fck {
			#clock-cells = <0>;
			compatible = "ti,composite-gate-clock";
			clock-output-names = "usim_gate_fck";
			clocks = <&omap_96m_fck>;
			ti,bit-shift = <9>;
		};
	};

	sys_d2_ck: sys_d2_ck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&sys_ck>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	omap_96m_d2_fck: omap_96m_d2_fck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&omap_96m_fck>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	omap_96m_d4_fck: omap_96m_d4_fck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&omap_96m_fck>;
		clock-mult = <1>;
		clock-div = <4>;
	};

	omap_96m_d8_fck: omap_96m_d8_fck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&omap_96m_fck>;
		clock-mult = <1>;
		clock-div = <8>;
	};

	omap_96m_d10_fck: omap_96m_d10_fck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&omap_96m_fck>;
		clock-mult = <1>;
		clock-div = <10>;
	};

	dpll5_m2_d4_ck: dpll5_m2_d4_ck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&dpll5_m2_ck>;
		clock-mult = <1>;
		clock-div = <4>;
	};

	dpll5_m2_d8_ck: dpll5_m2_d8_ck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&dpll5_m2_ck>;
		clock-mult = <1>;
		clock-div = <8>;
	};

	dpll5_m2_d16_ck: dpll5_m2_d16_ck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&dpll5_m2_ck>;
		clock-mult = <1>;
		clock-div = <16>;
	};

	dpll5_m2_d20_ck: dpll5_m2_d20_ck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&dpll5_m2_ck>;
		clock-mult = <1>;
		clock-div = <20>;
	};

	clock@c40 {
		compatible = "ti,clksel";
		reg = <0xc40>;
		#clock-cells = <2>;
		#address-cells = <0>;

		usim_mux_fck: clock-usim-mux-fck {
			#clock-cells = <0>;
			compatible = "ti,composite-mux-clock";
			clock-output-names = "usim_mux_fck";
			clocks = <&sys_ck>, <&sys_d2_ck>, <&omap_96m_d2_fck>, <&omap_96m_d4_fck>, <&omap_96m_d8_fck>, <&omap_96m_d10_fck>, <&dpll5_m2_d4_ck>, <&dpll5_m2_d8_ck>, <&dpll5_m2_d16_ck>, <&dpll5_m2_d20_ck>;
			ti,bit-shift = <3>;
			ti,index-starts-at-one;
		};
	};

	usim_fck: usim_fck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clocks = <&usim_gate_fck>, <&usim_mux_fck>;
	};

	clock@c10 {
		compatible = "ti,clksel";
		reg = <0xc10>;
		#clock-cells = <2>;
		#address-cells = <0>;

		usim_ick: clock-usim-ick {
			#clock-cells = <0>;
			compatible = "ti,omap3-interface-clock";
			clock-output-names = "usim_ick";
			clocks = <&wkup_l4_ick>;
			ti,bit-shift = <9>;
		};
	};
};

&cm_clockdomains {
	core_l3_clkdm: core_l3_clkdm {
		compatible = "ti,clockdomain";
		clocks = <&sdrc_ick>, <&hsotgusb_ick_3430es2>;
	};

	wkup_clkdm: wkup_clkdm {
		compatible = "ti,clockdomain";
		clocks = <&gpio1_dbck>, <&wdt2_fck>, <&wdt2_ick>, <&wdt1_ick>,
			 <&gpio1_ick>, <&omap_32ksync_ick>, <&gpt12_ick>,
			 <&gpt1_ick>, <&usim_ick>;
	};

	core_l4_clkdm: core_l4_clkdm {
		compatible = "ti,clockdomain";
		clocks = <&cpefuse_fck>, <&ts_fck>, <&usbtll_fck>,
			 <&usbtll_ick>, <&mmchs3_ick>, <&mmchs3_fck>,
			 <&mmchs2_fck>, <&mmchs1_fck>, <&i2c3_fck>, <&i2c2_fck>,
			 <&i2c1_fck>, <&mcspi4_fck>, <&mcspi3_fck>,
			 <&mcspi2_fck>, <&mcspi1_fck>, <&uart2_fck>,
			 <&uart1_fck>, <&hdq_fck>, <&mmchs2_ick>, <&mmchs1_ick>,
			 <&hdq_ick>, <&mcspi4_ick>, <&mcspi3_ick>,
			 <&mcspi2_ick>, <&mcspi1_ick>, <&i2c3_ick>, <&i2c2_ick>,
			 <&i2c1_ick>, <&uart2_ick>, <&uart1_ick>, <&gpt11_ick>,
			 <&gpt10_ick>, <&mcbsp5_ick>, <&mcbsp1_ick>,
			 <&omapctrl_ick>, <&aes2_ick>, <&sha12_ick>,
			 <&ssi_ick>;
	};
};
