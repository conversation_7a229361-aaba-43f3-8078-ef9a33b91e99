// SPDX-License-Identifier: GPL-2.0-only
/*
 * omap3-n950-n9.dtsi - Device Tree file for Nokia N950 & N9 (common stuff)
 *
 * Written by: <PERSON><PERSON> <<EMAIL>>
 */

#include "omap36xx.dtsi"

/ {
	cpus {
		cpu@0 {
			cpu0-supply = <&vcc>;
		};
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>; /* 1 GB */
	};

	vemmc: fixedregulator0 {
		compatible = "regulator-fixed";
		regulator-name = "VEMMC";
		regulator-min-microvolt = <2900000>;
		regulator-max-microvolt = <2900000>;
		gpio = <&gpio5 29 GPIO_ACTIVE_HIGH>; /* gpio line 157 */
		startup-delay-us = <150>;
		enable-active-high;
	};

	vwlan_fixed: fixedregulator2 {
		compatible = "regulator-fixed";
		regulator-name = "VWLAN";
		gpio = <&gpio2 3 GPIO_ACTIVE_HIGH>; /* gpio 35 */
		enable-active-high;
	};

	leds {
		compatible = "gpio-leds";

		heartbeat {
			label = "debug::sleep";
			gpios = <&gpio3 28 GPIO_ACTIVE_HIGH>;  /* gpio92 */
			linux,default-trigger = "default-on";
			pinctrl-names = "default";
			pinctrl-0 = <&debug_leds>;
		};
	};

	/* controlled (enabled/disabled) directly by wl1271 */
	vctcxo: vctcxo {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <38400000>;
	};
};

&omap3_pmx_core {
	accelerator_pins: accelerator-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21da, PIN_INPUT | MUX_MODE4)	/* mcspi2_somi.gpio_180 -> LIS302 INT1 */
			OMAP3_CORE1_IOPAD(0x21dc, PIN_INPUT | MUX_MODE4)	/* mcspi2_cs0.gpio_181 -> LIS302 INT2 */
		>;
	};

	debug_leds: debug-led-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2108, PIN_OUTPUT | MUX_MODE4)       /* dss_data22.gpio_92 */
		>;
	};

	mmc2_pins: mmc2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2158, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc2_clk */
			OMAP3_CORE1_IOPAD(0x215a, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc2_cmd */
			OMAP3_CORE1_IOPAD(0x215c, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc2_dat0 */
			OMAP3_CORE1_IOPAD(0x215e, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc2_dat1 */
			OMAP3_CORE1_IOPAD(0x2160, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc2_dat2 */
			OMAP3_CORE1_IOPAD(0x2162, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc2_dat3 */
		>;
	};

	wlan_pins: wlan-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x207c, PIN_OUTPUT | MUX_MODE4) /* gpio 35 - wlan enable */
			OMAP3_CORE1_IOPAD(0x208a, PIN_INPUT | MUX_MODE4) /* gpio 42 - wlan irq */
		>;
	};

	ssi_pins: ssi-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x217c, PIN_OUTPUT | MUX_MODE1)            /* ssi1_dat_tx */
			OMAP3_CORE1_IOPAD(0x217e, PIN_OUTPUT | MUX_MODE1)            /* ssi1_flag_tx */
			OMAP3_CORE1_IOPAD(0x2180, PIN_INPUT_PULLUP | MUX_MODE1)      /* ssi1_rdy_tx */
			OMAP3_CORE1_IOPAD(0x2182, PIN_INPUT | MUX_MODE4)	/* ssi1_wake_tx (cawake) */
			OMAP3_CORE1_IOPAD(0x2184, PIN_INPUT | MUX_MODE1)             /* ssi1_dat_rx */
			OMAP3_CORE1_IOPAD(0x2186, PIN_INPUT | MUX_MODE1)             /* ssi1_flag_rx */
			OMAP3_CORE1_IOPAD(0x2188, PIN_OUTPUT | MUX_MODE1)            /* ssi1_rdy_rx */
			OMAP3_CORE1_IOPAD(0x218a, PIN_OUTPUT | MUX_MODE1)            /* ssi1_wake */
		>;
	};

	ssi_pins_idle: ssi-idle-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x217c, PIN_OUTPUT | MUX_MODE7)            /* ssi1_dat_tx */
			OMAP3_CORE1_IOPAD(0x217e, PIN_OUTPUT | MUX_MODE7)            /* ssi1_flag_tx */
			OMAP3_CORE1_IOPAD(0x2180, PIN_INPUT_PULLDOWN | MUX_MODE7)    /* ssi1_rdy_tx */
			OMAP3_CORE1_IOPAD(0x2182, PIN_INPUT | MUX_MODE4)	/* ssi1_wake_tx (cawake) */
			OMAP3_CORE1_IOPAD(0x2184, PIN_INPUT | MUX_MODE7)             /* ssi1_dat_rx */
			OMAP3_CORE1_IOPAD(0x2186, PIN_INPUT | MUX_MODE7)             /* ssi1_flag_rx */
			OMAP3_CORE1_IOPAD(0x2188, PIN_OUTPUT | MUX_MODE4)            /* ssi1_rdy_rx */
			OMAP3_CORE1_IOPAD(0x218a, PIN_OUTPUT | MUX_MODE7)            /* ssi1_wake */
		>;
	};

	modem_pins1: modem-core1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x207a, PIN_INPUT | MUX_MODE4)	/* gpio_34 (ape_rst_rq) */
			OMAP3_CORE1_IOPAD(0x2100, PIN_OUTPUT | MUX_MODE4)            /* gpio_88 (cmt_rst_rq) */
			OMAP3_CORE1_IOPAD(0x210a, PIN_OUTPUT | MUX_MODE4)            /* gpio_93 (cmt_apeslpx) */
		>;
	};

	uart2_pins: uart2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2174, PIN_INPUT_PULLUP | MUX_MODE0)		/* uart2_cts */
			OMAP3_CORE1_IOPAD(0x2176, PIN_OUTPUT | MUX_MODE0)		/* uart2_rts */
			OMAP3_CORE1_IOPAD(0x2178, PIN_OUTPUT | MUX_MODE0)		/* uart2_tx */
			OMAP3_CORE1_IOPAD(0x217a, PIN_INPUT | MUX_MODE0)		/* uart2_rx */
		>;
	};
};

&omap3_pmx_core2 {
	modem_pins2: modem-core2-pins {
		pinctrl-single,pins = <
			OMAP3630_CORE2_IOPAD(0x25ec, PIN_OUTPUT | MUX_MODE4)         /* gpio_23 (cmt_en) */
		>;
	};
};

&i2c1 {
	clock-frequency = <2900000>;

	twl: twl@48 {
		reg = <0x48>;
		interrupts = <7>; /* SYS_NIRQ cascaded to intc */
		interrupt-parent = <&intc>;
	};
};

/include/ "twl4030.dtsi"

&twl {
	compatible = "ti,twl5031";

	twl_power: power {
		compatible = "ti,twl4030-power";
		ti,use_poweroff;
	};
};

&twl_gpio {
	ti,pullups = <0x000001>; /* BIT(0) */
	ti,pulldowns = <0x008106>; /* BIT(1) | BIT(2) | BIT(8) | BIT(15) */
};

&vdac {
	regulator-name = "vdac";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
};

&vpll1 {
	regulator-name = "vpll1";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
};

&vpll2 {
	regulator-name = "vpll2";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
};

&vaux1 {
	regulator-name = "vaux1";
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
};

/* CSI-2 receiver */
&vaux2 {
	regulator-name = "vaux2";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
};

/* Cameras */
&vaux3 {
	regulator-name = "vaux3";
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
};

&vaux4 {
	regulator-name = "vaux4";
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
};

&vmmc1 {
	regulator-name = "vmmc1";
	regulator-min-microvolt = <1850000>;
	regulator-max-microvolt = <3150000>;
};

&vmmc2 {
	regulator-name = "vmmc2";
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
};

&vintana1 {
	regulator-name = "vintana1";
	regulator-min-microvolt = <1500000>;
	regulator-max-microvolt = <1500000>;
};

&vintana2 {
	regulator-name = "vintana2";
	regulator-min-microvolt = <2750000>;
	regulator-max-microvolt = <2750000>;
};

&vintdig {
	regulator-name = "vintdig";
	regulator-min-microvolt = <1500000>;
	regulator-max-microvolt = <1500000>;
};

&vsim {
	regulator-name = "vsim";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
};

&vio {
	regulator-name = "vio";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
};

&i2c2 {
	clock-frequency = <400000>;

	as3645a@30 {
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x30>;
		compatible = "ams,as3645a";
		as3645a_flash: flash@0 {
			reg = <0x0>;
			flash-timeout-us = <150000>;
			flash-max-microamp = <320000>;
			led-max-microamp = <60000>;
			ams,input-max-microamp = <1750000>;
		};
		as3645a_indicator: indicator@1 {
			reg = <0x1>;
			led-max-microamp = <10000>;
		};
	};
};

&i2c3 {
	clock-frequency = <400000>;

	lis302: lis302@1d {
		compatible = "st,lis3lv02d";
		reg = <0x1d>;

		Vdd-supply = <&vaux1>;
		Vdd_IO-supply = <&vio>;

		pinctrl-names = "default";
		pinctrl-0 = <&accelerator_pins>;

                interrupts-extended = <&gpio6 20 IRQ_TYPE_EDGE_FALLING>, <&gpio6 21 IRQ_TYPE_EDGE_FALLING>; /* 180, 181 */

		/* click flags */
		st,click-single-x;
		st,click-single-y;
		st,click-single-z;

		/* Limits are 0.5g * value */
		st,click-threshold-x = <8>;
		st,click-threshold-y = <8>;
		st,click-threshold-z = <10>;

		/* Click must be longer than time limit */
		st,click-time-limit = <9>;

		/* Kind of debounce filter */
		st,click-latency = <50>;

		st,wakeup-x-hi;
		st,wakeup-y-hi;
		st,wakeup-threshold = <(800/18)>; /* millig-value / 18 to get HW values */

		st,wakeup2-z-hi;
		st,wakeup2-threshold = <(1000/18)>; /* millig-value / 18 to get HW values */

		st,highpass-cutoff-hz = <2>;

		/* Interrupt line 1 for thresholds */
		st,irq1-ff-wu-1;
		st,irq1-ff-wu-2;
		/* Interrupt line 2 for click detection */
		st,irq2-click;

		st,wu-duration-1 = <8>;
		st,wu-duration-2 = <8>;
	};
};

&mmc1 {
	status = "disabled";
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_pins>;
	vmmc-supply = <&vemmc>;
	bus-width = <4>;
	ti,non-removable;
};

&mmc3 {
	status = "disabled";
};

/* RNG not directly accessible on N950/N9. */
&rng_target {
	status = "disabled";
};

&usb_otg_hs {
	interface-type = <0>;
	usb-phy = <&usb2_phy>;
	phys = <&usb2_phy>;
	phy-names = "usb2-phy";
	mode = <3>;
	power = <50>;
};

&gpmc {
	ranges = <0 0 0x04000000 0x1000000>;	/* CS0: 16MB for OneNAND */

	onenand@0,0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "ti,omap2-onenand";
		reg = <0 0 0x20000>;	/* CS0, offset 0, IO size 128K */

		/*
		 * These timings are based on CONFIG_OMAP_GPMC_DEBUG=y reported
		 * bootloader set values when booted with v4.19 using both N950
		 * and N9 devices (OneNAND Manufacturer: Samsung):
		 *
		 *   gpmc cs0 before gpmc_cs_program_settings:
		 *   cs0 GPMC_CS_CONFIG1: 0xfd001202
		 *   cs0 GPMC_CS_CONFIG2: 0x00181800
		 *   cs0 GPMC_CS_CONFIG3: 0x00030300
		 *   cs0 GPMC_CS_CONFIG4: 0x18001804
		 *   cs0 GPMC_CS_CONFIG5: 0x03171d1d
		 *   cs0 GPMC_CS_CONFIG6: 0x97080000
		 */
		gpmc,sync-read;
		gpmc,sync-write;
		gpmc,burst-length = <16>;
		gpmc,burst-read;
		gpmc,burst-wrap;
		gpmc,burst-write;
		gpmc,device-width = <2>;
		gpmc,mux-add-data = <2>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <122>;
		gpmc,cs-wr-off-ns = <122>;
		gpmc,adv-on-ns = <0>;
		gpmc,adv-rd-off-ns = <15>;
		gpmc,adv-wr-off-ns = <15>;
		gpmc,oe-on-ns = <20>;
		gpmc,oe-off-ns = <122>;
		gpmc,we-on-ns = <0>;
		gpmc,we-off-ns = <122>;
		gpmc,rd-cycle-ns = <148>;
		gpmc,wr-cycle-ns = <148>;
		gpmc,access-ns = <117>;
		gpmc,page-burst-access-ns = <15>;
		gpmc,bus-turnaround-ns = <0>;
		gpmc,cycle2cycle-delay-ns = <0>;
		gpmc,wait-monitoring-ns = <0>;
		gpmc,clk-activation-ns = <10>;
		gpmc,wr-data-mux-bus-ns = <40>;
		gpmc,wr-access-ns = <117>;

		gpmc,sync-clk-ps = <15000>; /* TBC; Where this value came? */

		/*
		 * MTD partition table corresponding to Nokia's MeeGo 1.2
		 * Harmattan release.
		 */
		partition@0 {
			label = "bootloader";
			reg = <0x00000000 0x00100000>;
		};
		partition@1 {
			label = "config";
			reg = <0x00100000 0x002c0000>;
		};
		partition@2 {
			label = "kernel";
			reg = <0x003c0000 0x01000000>;
		};
		partition@3 {
			label = "log";
			reg = <0x013c0000 0x00200000>;
		};
		partition@4 {
			label = "var";
			reg = <0x015c0000 0x1ca40000>;
		};
		partition@5 {
			label = "moslo";
			reg = <0x1e000000 0x02000000>;
		};
		partition@6 {
			label = "omap2-onenand";
			reg = <0x00000000 0x20000000>;
		};
	};
};

&ssi_port1 {
	pinctrl-names = "default", "idle";
	pinctrl-0 = <&ssi_pins>;
	pinctrl-1 = <&ssi_pins_idle>;

	ti,ssi-cawake-gpio = <&gpio5 23 GPIO_ACTIVE_HIGH>; /* 151 */

	modem: hsi-client {
		pinctrl-names = "default";
		pinctrl-0 = <&modem_pins1 &modem_pins2>;

		hsi-channel-ids = <0>, <1>, <2>, <3>;
		hsi-channel-names = "mcsaab-control",
				    "speech-control",
				    "speech-data",
				    "mcsaab-data";
		hsi-speed-kbps = <96000>;
		hsi-mode = "frame";
		hsi-flow = "synchronized";
		hsi-arb-mode = "round-robin";

		interrupts-extended = <&gpio2 2 IRQ_TYPE_EDGE_RISING>; /* gpio 34 */

		gpios = <&gpio3 29 GPIO_ACTIVE_HIGH>, /* gpio 93 */
			<&gpio3 24 GPIO_ACTIVE_HIGH>, /* gpio 88 */
			<&gpio1 23 GPIO_ACTIVE_HIGH>; /* gpio 23 */
		gpio-names = "cmt_apeslpx",
			     "cmt_rst_rq",
			     "cmt_en";
	};
};

&ssi_port2 {
	status = "disabled";
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pins>;

	bluetooth {
		compatible = "ti,wl1271-bluetooth-nokia", "nokia,h4p-bluetooth";

		reset-gpios = <&gpio1 26 GPIO_ACTIVE_LOW>; /* 26 */
		host-wakeup-gpios = <&gpio4 5 GPIO_ACTIVE_HIGH>; /* 101 */
		bluetooth-wakeup-gpios = <&gpio2 5 GPIO_ACTIVE_HIGH>; /* 37 */

		clocks = <&vctcxo>;
		clock-names = "sysclk";
	};
};

&aes1_target {
	status = "disabled";
};

&aes2_target {
	status = "disabled";
};
