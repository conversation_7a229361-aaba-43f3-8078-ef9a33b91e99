// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 <PERSON><PERSON> <<EMAIL>>
 */

#include "omap3-gta04.dtsi"

/ {
	model = "Goldelico GTA04A3/Letux 2804";
};

&i2c2 {

	/* alternate accelerometer that might be installed on some GTA04A3 boards */
	lis302@1d {
		compatible = "st,lis331dlh", "st,lis3lv02d";
		reg = <0x1d>;
		interrupt-parent = <&gpio3>;
		interrupts = <18 (IRQ_TYPE_LEVEL_HIGH | IRQ_TYPE_EDGE_RISING)>;
		Vdd-supply = <&vaux2>;
		Vdd_IO-supply = <&vaux2>;

		st,click-single-x;
		st,click-single-y;
		st,click-single-z;
		st,click-thresh-x = <8>;
		st,click-thresh-y = <8>;
		st,click-thresh-z = <10>;
		st,click-click-time-limit = <9>;
		st,click-latency = <50>;
		st,irq1-click;
		st,wakeup-x-lo;
		st,wakeup-x-hi;
		st,wakeup-y-lo;
		st,wakeup-y-hi;
		st,wakeup-z-lo;
		st,wakeup-z-hi;
		st,min-limit-x = <32>;
		st,min-limit-y = <3>;
		st,min-limit-z = <3>;
		st,max-limit-x = <3>;
		st,max-limit-y = <32>;
		st,max-limit-z = <32>;
	};
};
