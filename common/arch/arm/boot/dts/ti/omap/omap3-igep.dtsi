// SPDX-License-Identifier: GPL-2.0-only
/*
 * Common device tree for IGEP boards based on AM/DM37x
 *
 * Copyright (C) 2012 <PERSON> <<EMAIL>>
 * Copyright (C) 2012 Enric Balletbo i Serra <<EMAIL>>
 */
/dts-v1/;

#include "omap36xx.dtsi"

/ {
	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x20000000>; /* 512 MB */
	};

	chosen {
		stdout-path = &uart3;
	};

	sound {
		compatible = "ti,omap-twl4030";
		ti,model = "igep2";
		ti,mcbsp = <&mcbsp2>;
	};

	vdd33: regulator-vdd33 {
		compatible = "regulator-fixed";
		regulator-name = "vdd33";
		regulator-always-on;
	};

};

&omap3_pmx_core {
	gpmc_pins: gpmc-pins {
		pinctrl-single,pins = <
			/* OneNAND seems to require PIN_INPUT on clock. */
                        OMAP3_CORE1_IOPAD(0x20be, PIN_INPUT | MUX_MODE0)        /* gpmc_clk.gpmc_clk */
		>;
	};

	uart1_pins: uart1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2182, PIN_INPUT | MUX_MODE0)	/* uart1_rx.uart1_rx */
			OMAP3_CORE1_IOPAD(0x217c, PIN_OUTPUT | MUX_MODE0)	/* uart1_tx.uart1_tx */
		>;
	};

	uart3_pins: uart3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x219e, PIN_INPUT | MUX_MODE0)	/* uart3_rx.uart3_rx */
			OMAP3_CORE1_IOPAD(0x21a0, PIN_OUTPUT | MUX_MODE0)	/* uart3_tx.uart3_tx */
		>;
	};

	mcbsp2_pins: mcbsp2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x213c, PIN_INPUT | MUX_MODE0)	/* mcbsp2_fsx.mcbsp2_fsx */
			OMAP3_CORE1_IOPAD(0x213e, PIN_INPUT | MUX_MODE0)	/* mcbsp2_clkx.mcbsp2_clkx */
			OMAP3_CORE1_IOPAD(0x2140, PIN_INPUT | MUX_MODE0)	/* mcbsp2_dr.mcbsp2.dr */
			OMAP3_CORE1_IOPAD(0x2142, PIN_OUTPUT | MUX_MODE0)	/* mcbsp2_dx.mcbsp2_dx */
		>;
	};

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2144, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_clk.sdmmc1_clk */
			OMAP3_CORE1_IOPAD(0x2146, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_cmd.sdmmc1_cmd */
			OMAP3_CORE1_IOPAD(0x2148, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc1_dat0.sdmmc1_dat0 */
			OMAP3_CORE1_IOPAD(0x214a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat1.sdmmc1_dat1 */
			OMAP3_CORE1_IOPAD(0x214c, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat2.sdmmc1_dat2 */
			OMAP3_CORE1_IOPAD(0x214e, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat3.sdmmc1_dat3 */
		>;
	};

	mmc2_pins: mmc2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2158, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_clk.sdmmc2_clk */
			OMAP3_CORE1_IOPAD(0x215a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_cmd.sdmmc2_cmd */
			OMAP3_CORE1_IOPAD(0x215c, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc2_dat0.sdmmc2_dat0 */
			OMAP3_CORE1_IOPAD(0x215e, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat1.sdmmc2_dat1 */
			OMAP3_CORE1_IOPAD(0x2160, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat2.sdmmc2_dat2 */
			OMAP3_CORE1_IOPAD(0x2162, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat3.sdmmc2_dat3 */
		>;
	};

	i2c1_pins: i2c1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21ba, PIN_INPUT | MUX_MODE0)	/* i2c1_scl.i2c1_scl */
			OMAP3_CORE1_IOPAD(0x21bc, PIN_INPUT | MUX_MODE0)	/* i2c1_sda.i2c1_sda */
		>;
	};

	i2c3_pins: i2c3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21c2, PIN_INPUT | MUX_MODE0)	/* i2c3_scl.i2c3_scl */
			OMAP3_CORE1_IOPAD(0x21c4, PIN_INPUT | MUX_MODE0)	/* i2c3_sda.i2c3_sda */
		>;
	};
};

&gpmc {
	pinctrl-names = "default";
	pinctrl-0 = <&gpmc_pins>;

	nand@0,0 {
		compatible = "ti,omap2-nand";
		reg = <0 0 4>; /* CS0, offset 0, IO size 4 */
		interrupt-parent = <&gpmc>;
		interrupts = <0 IRQ_TYPE_NONE>, /* fifoevent */
			     <1 IRQ_TYPE_NONE>;	/* termcount */
		linux,mtd-name = "micron,mt29c4g96maz";
		nand-bus-width = <16>;
		gpmc,device-width = <2>;
		ti,nand-ecc-opt = "bch8";

		gpmc,sync-clk-ps = <0>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <44>;
		gpmc,cs-wr-off-ns = <44>;
		gpmc,adv-on-ns = <6>;
		gpmc,adv-rd-off-ns = <34>;
		gpmc,adv-wr-off-ns = <44>;
		gpmc,we-off-ns = <40>;
		gpmc,oe-off-ns = <54>;
		gpmc,access-ns = <64>;
		gpmc,rd-cycle-ns = <82>;
		gpmc,wr-cycle-ns = <82>;
		gpmc,wr-access-ns = <40>;
		gpmc,wr-data-mux-bus-ns = <0>;

		#address-cells = <1>;
		#size-cells = <1>;

		status = "okay";
	};

	onenand@0,0 {
		compatible = "ti,omap2-onenand";
		reg = <0 0 0x20000>;	/* CS0, offset 0, IO size 128K */

		gpmc,sync-read;
		gpmc,sync-write;
		gpmc,burst-length = <16>;
		gpmc,burst-wrap;
		gpmc,burst-read;
		gpmc,burst-write;
		gpmc,device-width = <2>; /* GPMC_DEVWIDTH_16BIT */
		gpmc,mux-add-data = <2>; /* GPMC_MUX_AD */
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <96>;
		gpmc,cs-wr-off-ns = <96>;
		gpmc,adv-on-ns = <0>;
		gpmc,adv-rd-off-ns = <12>;
		gpmc,adv-wr-off-ns = <12>;
		gpmc,oe-on-ns = <18>;
		gpmc,oe-off-ns = <96>;
		gpmc,we-on-ns = <0>;
		gpmc,we-off-ns = <96>;
		gpmc,rd-cycle-ns = <114>;
		gpmc,wr-cycle-ns = <114>;
		gpmc,access-ns = <90>;
		gpmc,page-burst-access-ns = <12>;
		gpmc,bus-turnaround-ns = <0>;
		gpmc,cycle2cycle-delay-ns = <0>;
		gpmc,wait-monitoring-ns = <0>;
		gpmc,clk-activation-ns = <6>;
		gpmc,wr-data-mux-bus-ns = <30>;
		gpmc,wr-access-ns = <90>;
		gpmc,sync-clk-ps = <12000>;

		#address-cells = <1>;
		#size-cells = <1>;

		status = "disabled";
	};
};

&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins>;
	clock-frequency = <2600000>;

	twl: twl@48 {
		reg = <0x48>;
		interrupts = <7>; /* SYS_NIRQ cascaded to intc */
		interrupt-parent = <&intc>;

		twl_audio: audio {
			compatible = "ti,twl4030-audio";
			codec {
			};
		};
	};
};

#include "twl4030.dtsi"
#include "twl4030_omap3.dtsi"

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;
};

&mcbsp2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mcbsp2_pins>;
	status = "okay";
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
	vmmc-supply = <&vmmc1>;
	vmmc_aux-supply = <&vsim>;
	bus-width = <4>;
	cd-gpios = <&twl_gpio 0 GPIO_ACTIVE_LOW>;
};

&mmc3 {
	status = "disabled";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pins>;
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>;
};

&twl_gpio {
	ti,use-leds;
};

&usb_otg_hs {
	interface-type = <0>;
	usb-phy = <&usb2_phy>;
	phys = <&usb2_phy>;
	phy-names = "usb2-phy";
	mode = <3>;
	power = <50>;
};
