// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for OMAP543x SoC GPU thermal
 *
 * Copyright (C) 2013 Texas Instruments Incorporated - https://www.ti.com/
 * Contact: <PERSON> <<EMAIL>>
 */

#include <dt-bindings/thermal/thermal.h>

gpu_thermal: gpu_thermal {
	polling-delay-passive = <250>; /* milliseconds */
	polling-delay = <500>; /* milliseconds */

			/* sensor       ID */
	thermal-sensors = <&bandgap     1>;

	trips {
		gpu_crit: gpu_crit {
			temperature = <125000>; /* milliCelsius */
			hysteresis = <2000>; /* milliCelsius */
			type = "critical";
		};
	};
};
