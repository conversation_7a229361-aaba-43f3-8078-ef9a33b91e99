// SPDX-License-Identifier: GPL-2.0
/*
 * Suppport for CompuLab SBC-T3517 with CM-T3517
 */

#include "omap3-cm-t3517.dts"
#include "omap3-sb-t35.dtsi"

/ {
	model = "CompuLab SBC-T3517 with CM-T3517";
	compatible = "compulab,omap3-sbc-t3517", "compulab,omap3-cm-t3517", "ti,am3517", "ti,omap3";

	aliases {
		display0 = &dvi0;
		display1 = &tv0;
	};

	/* Only one GPMC smsc9220 on SBC-T3517, CM-T3517 uses am35x Ethernet */
	vddvario: regulator-vddvario-sb-t35 {
		compatible = "regulator-fixed";
		regulator-name = "vddvario";
		regulator-always-on;
	};

	vdd33a: regulator-vdd33a-sb-t35 {
		compatible = "regulator-fixed";
		regulator-name = "vdd33a";
		regulator-always-on;
	};
};

&omap3_pmx_core {
	pinctrl-names = "default";
	pinctrl-0 = <
			&sb_t35_usb_hub_pins
			&usb_hub_pins
		    >;

	mmc1_aux_pins: mmc1-aux-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20c0, PIN_INPUT_PULLUP | MUX_MODE4) /* gpmc_clk.gpio_59   */
			OMAP3_CORE1_IOPAD(0x2174, PIN_INPUT_PULLUP | MUX_MODE4) /* uart2_cts.gpio_144 */
		>;
	};

	sb_t35_usb_hub_pins: sb-t35-usb-hub-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21ec, PIN_OUTPUT | MUX_MODE4) /* ccdc_wen.gpio_98 - SB-T35 USB HUB RST */
		>;
	};
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <
		&mmc1_pins
		&mmc1_aux_pins
	>;

	wp-gpios =  <&gpio2 27 GPIO_ACTIVE_HIGH>; /* gpio_59  */
	cd-gpios =  <&gpio5 16 GPIO_ACTIVE_HIGH>; /* gpio_144 */
};

&dss {
	port {
		dpi_out: endpoint {
			remote-endpoint = <&tfp410_in>;
			data-lines = <24>;
		};
	};
};

&gpmc {
	ranges = <4 0 0x2d000000 0x01000000>,	/* SB-T35 SMSC9x Eth */
		 <0 0 0x00000000 0x01000000>;	/* CM-T3x NAND */
};
