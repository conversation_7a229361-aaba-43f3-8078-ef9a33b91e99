// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2012 Texas Instruments Incorporated - https://www.ti.com/
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 */
/dts-v1/;

#include "omap34xx.dtsi"

/* Secure omaps have some devices inaccessible depending on the firmware */
&aes1_target {
	status = "disabled";
};

&aes2_target {
	status = "disabled";
};

&sham {
	status = "disabled";
};

/ {
	cpus {
		cpu@0 {
			cpu0-supply = <&vcc>;
		};
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x10000000>; /* 256 MB */
	};

	/* HS USB Port 2 Power */
	hsusb2_power: hsusb2_power_reg {
		compatible = "regulator-fixed";
		regulator-name = "hsusb2_vbus";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&twl_gpio 18 GPIO_ACTIVE_HIGH>;	/* GPIO LEDA */
		startup-delay-us = <70000>;
	};

	/* HS USB Host PHY on PORT 2 */
	hsusb2_phy: hsusb2-phy-pins {
		compatible = "usb-nop-xceiv";
		reset-gpios = <&gpio6 2 GPIO_ACTIVE_LOW>;	/* gpio_162 */
		vcc-supply = <&hsusb2_power>;
		#phy-cells = <0>;
	};

	sound {
		compatible = "ti,omap-twl4030";
		ti,model = "omap3beagle";

		/* McBSP2 is used for onboard sound, same as on beagle */
		ti,mcbsp = <&mcbsp2>;
	};

	/* Regulator to enable/switch the vcc of the Wifi module */
	mmc2_sdio_poweron: regulator-mmc2-sdio-poweron {
		compatible = "regulator-fixed";
		regulator-name = "regulator-mmc2-sdio-poweron";
		regulator-min-microvolt = <3150000>;
		regulator-max-microvolt = <3150000>;
		gpio = <&gpio5 29 GPIO_ACTIVE_LOW>;		/* gpio_157 */
		startup-delay-us = <10000>;
	};
};

&omap3_pmx_core {
	hsusbb2_pins: hsusbb2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x25f0, PIN_OUTPUT | MUX_MODE3)		/* etk_d10.hsusb2_clk */
			OMAP3_CORE1_IOPAD(0x25f2, PIN_OUTPUT | MUX_MODE3)		/* etk_d11.hsusb2_stp */
			OMAP3_CORE1_IOPAD(0x25f4, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d12.hsusb2_dir */
			OMAP3_CORE1_IOPAD(0x25f6, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d13.hsusb2_nxt */
			OMAP3_CORE1_IOPAD(0x25f8, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d14.hsusb2_data0 */
			OMAP3_CORE1_IOPAD(0x25fa, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d15.hsusb2_data1 */
			OMAP3_CORE1_IOPAD(0x21d4, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi1_cs3.hsusb2_data2 */
			OMAP3_CORE1_IOPAD(0x21d6, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_clk.hsusb2_data7 */
			OMAP3_CORE1_IOPAD(0x21d8, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_simo.hsusb2_data4 */
			OMAP3_CORE1_IOPAD(0x21da, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_somi.hsusb2_data5 */
			OMAP3_CORE1_IOPAD(0x21dc, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_cs0.hsusb2_data6 */
			OMAP3_CORE1_IOPAD(0x21de, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_cs1.hsusb2_data3 */
		>;
	};

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2144, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_clk.sdmmc1_clk */
			OMAP3_CORE1_IOPAD(0x2146, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_cmd.sdmmc1_cmd */
			OMAP3_CORE1_IOPAD(0x2148, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat0.sdmmc1_dat0 */
			OMAP3_CORE1_IOPAD(0x214a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat1.sdmmc1_dat1 */
			OMAP3_CORE1_IOPAD(0x214c, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat2.sdmmc1_dat2 */
			OMAP3_CORE1_IOPAD(0x214e, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat3.sdmmc1_dat3 */
			OMAP3_CORE1_IOPAD(0x2150, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc1_dat4.sdmmc1_dat4 */
			OMAP3_CORE1_IOPAD(0x2152, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc1_dat5.sdmmc1_dat5 */
			OMAP3_CORE1_IOPAD(0x2154, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc1_dat6.sdmmc1_dat6 */
			OMAP3_CORE1_IOPAD(0x2156, PIN_INPUT_PULLUP | MUX_MODE0) /* sdmmc1_dat7.sdmmc1_dat7 */
		>;
	};

	mmc2_pins: mmc2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2158, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_clk.sdmmc2_clk */
			OMAP3_CORE1_IOPAD(0x215a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_cmd.sdmmc2_cmd */
			OMAP3_CORE1_IOPAD(0x215c, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat0.sdmmc2_dat0 */
			OMAP3_CORE1_IOPAD(0x215e, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat1.sdmmc2_dat1 */
			OMAP3_CORE1_IOPAD(0x2160, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat2.sdmmc2_dat2 */
			OMAP3_CORE1_IOPAD(0x2162, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat3.sdmmc2_dat3 */
		>;
	};

	/* wlan GPIO output for WLAN_EN */
	wlan_gpio: wlan-gpio-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x218e, PIN_OUTPUT | MUX_MODE4)	/* mcbsp1_fsr gpio_157 */
		>;
	};

	uart3_pins: uart3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x219e, PIN_INPUT | PIN_OFF_WAKEUPENABLE | MUX_MODE0) /* uart3_rx_irrx.uart3_rx_irrx */
			OMAP3_CORE1_IOPAD(0x21a0, PIN_OUTPUT | MUX_MODE0)	/* uart3_tx_irtx.uart3_tx_irtx */
		>;
	};

	i2c3_pins: i2c3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21c2, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c3_scl.i2c3_scl */
			OMAP3_CORE1_IOPAD(0x21c4, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c3_sda.i2c3_sda */
		>;
	};

	mcspi1_pins: mcspi1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21c8, PIN_INPUT | MUX_MODE0)	/* mcspi1_clk.mcspi1_clk */
			OMAP3_CORE1_IOPAD(0x21ca, PIN_OUTPUT | MUX_MODE0)	/* mcspi1_simo.mcspi1_simo */
			OMAP3_CORE1_IOPAD(0x21cc, PIN_INPUT_PULLUP | MUX_MODE0)	/* mcspi1_somi.mcspi1_somi */
			OMAP3_CORE1_IOPAD(0x21ce, PIN_OUTPUT | MUX_MODE0)	/* mcspi1_cs0.mcspi1_cs0 */
		>;
	};

	mcspi3_pins: mcspi3-pins {
		pinctrl-single,pins = <
                        OMAP3_CORE1_IOPAD(0x25dc, PIN_OUTPUT | MUX_MODE1)	/* etk_d0.mcspi3_simo gpio14 INPUT | MODE1 */
                        OMAP3_CORE1_IOPAD(0x25de, PIN_INPUT_PULLUP | MUX_MODE1)	/* etk_d1.mcspi3_somi gpio15 INPUT | MODE1 */
                        OMAP3_CORE1_IOPAD(0x25e0, PIN_OUTPUT | MUX_MODE1)	/* etk_d2.mcspi3_cs0 gpio16 INPUT | MODE1 */
                        OMAP3_CORE1_IOPAD(0x25e2, PIN_INPUT | MUX_MODE1)	/* etk_d3.mcspi3_clk gpio17 INPUT | MODE1 */
		>;
	};

	mcbsp3_pins: mcbsp3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x216c, PIN_OUTPUT | MUX_MODE0)	/* mcbsp3_dx.uart2_cts */
			OMAP3_CORE1_IOPAD(0x216e, PIN_INPUT | MUX_MODE0)	/* mcbsp3_dr.uart2_rts */
			OMAP3_CORE1_IOPAD(0x2170, PIN_INPUT | MUX_MODE0)	/* mcbsp3_clk.uart2_tx */
			OMAP3_CORE1_IOPAD(0x2172, PIN_INPUT | MUX_MODE0)	/* mcbsp3_fsx.uart2_rx */
		>;
	};
};

/* McBSP1: mux'ed with GPIO158 as clock for HA-DSP */
&mcbsp1 {
	status = "disabled";
};

&mcbsp2 {
	status = "okay";
};

&i2c1 {
	clock-frequency = <2600000>;

	twl: twl@48 {
		reg = <0x48>;
		interrupts = <7>; /* SYS_NIRQ cascaded to intc */
		interrupt-parent = <&intc>;

		twl_audio: audio {
			compatible = "ti,twl4030-audio";
			codec {
			};
		};
	};
};

&i2c3 {
	clock-frequency = <100000>;

	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;
};

&mcspi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mcspi1_pins>;
};

&mcspi3 {
	pinctrl-names = "default";
	pinctrl-0 = <&mcspi3_pins>;
};

#include "twl4030.dtsi"
#include "twl4030_omap3.dtsi"

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
	vmmc-supply = <&vmmc1>;
	vqmmc-supply = <&vsim>;
	cd-gpios = <&twl_gpio 0 GPIO_ACTIVE_LOW>;
	bus-width = <8>;
};

// WiFi (Marvell 88W8686) on MMC2/SDIO
&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_pins>;
	vmmc-supply = <&mmc2_sdio_poweron>;
	non-removable;
	bus-width = <4>;
	cap-power-off-card;
};

&mmc3 {
	status = "disabled";
};

&usbhshost {
	port2-mode = "ehci-phy";
};

&usbhsehci {
	phys = <0 &hsusb2_phy>;
};

&twl_gpio {
	ti,use-leds;
	/* pullups: BIT(1) */
	ti,pullups = <0x000002>;
	/*
	 * pulldowns:
	 * BIT(2), BIT(6), BIT(7), BIT(8), BIT(13)
	 * BIT(15), BIT(16), BIT(17)
	 */
	ti,pulldowns = <0x03a1c4>;
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>;
};

&mcbsp3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&mcbsp3_pins>;
};

&gpmc {
	ranges = <0 0 0x30000000 0x01000000>;	/* CS0: 16MB for NAND */

	nand@0,0 {
		compatible = "ti,omap2-nand";
		reg = <0 0 4>; /* CS0, offset 0, IO size 4 */
		interrupt-parent = <&gpmc>;
		interrupts = <0 IRQ_TYPE_NONE>, /* fifoevent */
			     <1 IRQ_TYPE_NONE>;	/* termcount */
		nand-bus-width = <16>;
		gpmc,device-width = <2>;	/* GPMC_DEVWIDTH_16BIT */
		ti,nand-ecc-opt = "sw";

		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <36>;
		gpmc,cs-wr-off-ns = <36>;
		gpmc,adv-on-ns = <6>;
		gpmc,adv-rd-off-ns = <24>;
		gpmc,adv-wr-off-ns = <36>;
		gpmc,oe-on-ns = <6>;
		gpmc,oe-off-ns = <48>;
		gpmc,we-on-ns = <6>;
		gpmc,we-off-ns = <30>;
		gpmc,rd-cycle-ns = <72>;
		gpmc,wr-cycle-ns = <72>;
		gpmc,access-ns = <54>;
		gpmc,wr-access-ns = <30>;

		#address-cells = <1>;
		#size-cells = <1>;

		x-loader@0 {
			label = "X-Loader";
			reg = <0 0x80000>;
		};

		bootloaders@80000 {
			label = "U-Boot";
			reg = <0x80000 0x1e0000>;
		};

		bootloaders_env@260000 {
			label = "U-Boot Env";
			reg = <0x260000 0x20000>;
		};

		kernel@280000 {
			label = "Kernel";
			reg = <0x280000 0x400000>;
		};

		filesystem@680000 {
			label = "File System";
			reg = <0x680000 0xf980000>;
		};
	};
};

&usb_otg_hs {
	interface-type = <0>;
	usb-phy = <&usb2_phy>;
	phys = <&usb2_phy>;
	phy-names = "usb2-phy";
	mode = <3>;
	power = <50>;
};

&vaux2 {
	regulator-name = "vdd_ehci";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-always-on;
};
