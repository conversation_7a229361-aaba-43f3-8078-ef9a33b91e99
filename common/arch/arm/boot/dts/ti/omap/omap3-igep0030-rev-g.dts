// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for IGEP COM MODULE Rev. G (TI OMAP AM/DM37x)
 *
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 * Copyright (C) 2014 Enric Balletbo i Serra <<EMAIL>>
 */

#include "omap3-igep0030-common.dtsi"

/ {
	model = "IGEP COM MODULE Rev. G (TI OMAP AM/DM37x)";
	compatible = "isee,omap3-igep0030-rev-g", "ti,omap3630", "ti,omap3";

	/* Regulator to trigger the WL_EN signal of the Wifi module */
	lbep5clwmc_wlen: regulator-lbep5clwmc-wlen {
		compatible = "regulator-fixed";
		regulator-name = "regulator-lbep5clwmc-wlen";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio5 11 GPIO_ACTIVE_HIGH>;		/* gpio_139 - WL_EN */
		enable-active-high;
	};
};

&omap3_pmx_core {
	lbep5clwmc_pins: lbep5clwmc-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2164, PIN_INPUT | MUX_MODE4)	/* sdmmc2_dat4.gpio_136 - W_IRQ */
			OMAP3_CORE1_IOPAD(0x2166, PIN_OUTPUT | MUX_MODE4)	/* sdmmc2_dat5.gpio_137 - BT_EN */
			OMAP3_CORE1_IOPAD(0x216a, PIN_OUTPUT | MUX_MODE4)	/* sdmmc2_dat7.gpio_139 - WL_EN */
		>;
	};

	leds_pins: leds-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21be, PIN_OUTPUT | MUX_MODE4)	/* i2c2_scl.gpio_168 */
		>;
	};

};

&i2c2 {
	status = "disabled";
};

&leds {
	pinctrl-names = "default";
	pinctrl-0 = <&leds_pins &leds_core2_pins>;

	boot {
		label = "omap3:green:boot";
		gpios = <&gpio6 8 GPIO_ACTIVE_HIGH>;
		default-state = "on";
	};
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_pins &lbep5clwmc_pins>;
	vmmc-supply = <&lbep5clwmc_wlen>;
	bus-width = <4>;
	non-removable;

	#address-cells = <1>;
	#size-cells = <0>;
	wlcore: wlcore@2 {
		compatible = "ti,wl1835";
		reg = <2>;
		interrupt-parent = <&gpio5>;
		interrupts = <8 IRQ_TYPE_EDGE_RISING>; /* gpio 136 */
	};
};

&uart2 {
	bluetooth {
		compatible = "ti,wl1835-st";
		enable-gpios = <&gpio5 9 GPIO_ACTIVE_HIGH>; /* gpio 137 */
		max-speed = <300000>;
	};
};
