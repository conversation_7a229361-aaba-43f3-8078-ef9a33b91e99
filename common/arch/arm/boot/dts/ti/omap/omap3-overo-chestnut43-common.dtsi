// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 <PERSON>lor<PERSON>, EPFL Mobots group
 */

/*
 * Chestnut43 expansion board is manufactured by Gumstix Inc.
 */

#include "omap3-overo-common-peripherals.dtsi"
#include "omap3-overo-common-lcd43.dtsi"

#include <dt-bindings/input/input.h>

/ {
	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&led_pins>;
		led-heartbeat {
			label = "overo:red:gpio21";
			gpios = <&gpio1 21 GPIO_ACTIVE_LOW>;		/* gpio_21 */
			linux,default-trigger = "heartbeat";
		};
		led-gpio22 {
			label = "overo:blue:gpio22";
			gpios = <&gpio1 22 GPIO_ACTIVE_LOW>;		/* gpio_22 */
		};
	};

	gpio_keys {
		compatible = "gpio-keys";
		pinctrl-names = "default";
		pinctrl-0 = <&button_pins>;
		#address-cells = <1>;
		#size-cells = <0>;
		button0 {
			label = "button0";
			linux,code = <BTN_0>;
			gpios = <&gpio1 23 GPIO_ACTIVE_LOW>;		/* gpio_23 */
			wakeup-source;
		};
		button1 {
			label = "button1";
			linux,code = <BTN_1>;
			gpios = <&gpio1 14 GPIO_ACTIVE_LOW>;		/* gpio_14 */
			wakeup-source;
		};
	};
};

#include "omap-gpmc-smsc9221.dtsi"

&gpmc {
	ethernet@gpmc {
		reg = <5 0 0xff>;
		interrupt-parent = <&gpio6>;
		interrupts = <16 IRQ_TYPE_LEVEL_LOW>;	/* GPIO 176 */
	};
};

&lis33de {
	status = "disabled";
};

