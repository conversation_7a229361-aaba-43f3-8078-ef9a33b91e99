// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 Flor<PERSON>, EPFL Mobots group
 */

#include "omap34xx.dtsi"
#include "omap3-overo-base.dtsi"

&omap3_pmx_core2 {
	pinctrl-names = "default";
	pinctrl-0 = <
			&hsusb2_2_pins
	>;

	hsusb2_2_pins: hsusb2-2-pins {
		pinctrl-single,pins = <
			OMAP3430_CORE2_IOPAD(0x25f0, PIN_OUTPUT | MUX_MODE3)		/* etk_d10.hsusb2_clk */
			OMAP3430_CORE2_IOPAD(0x25f2, PIN_OUTPUT | MUX_MODE3)		/* etk_d11.hsusb2_stp */
			OMAP3430_CORE2_IOPAD(0x25f4, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d12.hsusb2_dir */
			OMAP3430_CORE2_IOPAD(0x25f6, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d13.hsusb2_nxt */
			OMAP3430_CORE2_IOPAD(0x25f8, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d14.hsusb2_data0 */
			OMAP3430_CORE2_IOPAD(0x25fa, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d15.hsusb2_data1 */
		>;
	};

	w3cbw003c_2_pins: w3cbw003c-2-pins {
		pinctrl-single,pins = <
			OMAP3430_CORE2_IOPAD(0x25e0, PIN_OUTPUT | MUX_MODE4)		/* etk_d2.gpio_16 */
		>;
	};
};
