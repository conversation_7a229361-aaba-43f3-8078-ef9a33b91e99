// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for OMAP4/5 SoC CPU thermal
 *
 * Copyright (C) 2013 Texas Instruments Incorporated - https://www.ti.com/
 * Contact: <PERSON> <<EMAIL>>
 */

#include <dt-bindings/thermal/thermal.h>

cpu_thermal: cpu_thermal {
	polling-delay-passive = <250>; /* milliseconds */
	polling-delay = <1000>; /* milliseconds */

	/*
	 * See 44xx files for single sensor addressing, omap5 and dra7 need
	 * also sensor ID for addressing.
	 */
	thermal-sensors = <&bandgap     0>;

	cpu_trips: trips {
		cpu_alert0: cpu_alert {
			temperature = <100000>; /* millicelsius */
			hysteresis = <2000>; /* millicelsius */
			type = "passive";
		};
		cpu_crit: cpu_crit {
			temperature = <125000>; /* millicelsius */
			hysteresis = <2000>; /* millicelsius */
			type = "critical";
		};
	};

	cpu_cooling_maps: cooling-maps {
		map0 {
			trip = <&cpu_alert0>;
			cooling-device =
				<&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
		};
	};
};
