// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2013 ISEE 2007 SL - http://www.isee.biz/
 */
/dts-v1/;

#include <dt-bindings/input/input.h>
#include "omap5-board-common.dtsi"

/ {
	model = "IGEPv5";
	compatible = "isee,omap5-igep0050", "ti,omap5";

	memory@80000000 {
		device_type = "memory";
		reg = <0x0 0x80000000 0 0x7f000000>;	/* 2032 MB */
	};

	aliases {
		ethernet = &ethernet;
	};

	gpio_keys {
		compatible = "gpio-keys";
		pinctrl-0 = <&power_button_pin>;
		pinctrl-names = "default";

		power-button {
			label = "Power Button";
			linux,code = <KEY_POWER>;
			gpios = <&gpio4 22 GPIO_ACTIVE_LOW>;
		};
	};

	leds {
		compatible = "gpio-leds";
		led@1 {
			label = "board:green:usr0";
			gpios = <&tca6416 1 0>;
			default-state = "off";
		};
		led@2 {
			label = "board:red:usr1";
			gpios = <&tca6416 2 0>;
			default-state = "off";
		};
		led@3 {
			label = "board:blue:usr1";
			gpios = <&tca6416 3 0>;
			default-state = "off";
		};
	};
};

&hdmi {
	vdda-supply = <&ldo7_reg>;
};

&i2c4 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4_pins>;

	tca6416: tca6416@21 {
		compatible = "ti,tca6416";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};
};

/* LDO4 is VPP1 - ball AD9 */
&ldo4_reg {
	regulator-min-microvolt = <2000000>;
	regulator-max-microvolt = <2000000>;
};

/*
 * LDO7 is used for HDMI: VDDA_DSIPORTA - ball AA33, VDDA_DSIPORTC - ball AE33,
 * VDDA_HDMI - ball AN25
 */
&ldo7_reg {
	status = "okay";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
};

&omap5_pmx_core {
	i2c4_pins: i2c4-pins {
		pinctrl-single,pins = <
			OMAP5_IOPAD(0x0f8, PIN_INPUT | MUX_MODE0)	/* i2c4_scl */
			OMAP5_IOPAD(0x0fa, PIN_INPUT | MUX_MODE0)	/* i2c4_sda */
		>;
	};

	power_button_pin: power-button-pins {
		pinctrl-single,pins = <
			OMAP5_IOPAD(0x086, PIN_INPUT | MUX_MODE6)	/* gpio4_118 */
		>;
	};
};

&tpd12s015 {
	gpios = <&tca6416 11 0>,	/* TCA6416 P01, CT_CP_HDP */
		<&tca6416 12 0>,	/* TCA6416 P00, LS_OE*/
		<&gpio7 1 0>,		/* 193, HPD */
		<&gpio7 2 0>,		/* 194, SCL */
		<&gpio7 3 0>;		/* 195, SDA */
};

&twl6040 {
	ti,audpwron-gpio = <&gpio5 16 GPIO_ACTIVE_HIGH>;  /* gpio line 144 */
};

&twl6040_pins {
	pinctrl-single,pins = <
		OMAP5_IOPAD(0x1c4, PIN_OUTPUT | MUX_MODE6)	/* mcspi1_somi.gpio5_144 */
		OMAP5_IOPAD(0x1ca, PIN_OUTPUT | MUX_MODE6)	/* perslimbus2_clock.gpio5_145 */
	>;
};

&usbhsehci {
	#address-cells = <1>;
	#size-cells = <0>;

	hub@2 {
		compatible = "usb424,3503";
		reg = <2>;
		#address-cells = <1>;
		#size-cells = <0>;

		ethernet: ethernet@3 {
			compatible = "usb424,7500";
			reg = <3>;
		};
	};
};
