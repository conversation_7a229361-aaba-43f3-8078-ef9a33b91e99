// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 <PERSON>lor<PERSON>, EPFL Mobots group
 */

/*
 * Peripherals common to all Gumstix Overo boards (Tobi, Summit, Palo43,...)
 */

/ {
	lis33_3v3: lis33-3v3-reg {
		compatible = "regulator-fixed";
		regulator-name = "lis33-3v3-reg";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	lis33_1v8: lis33-1v8-reg {
		compatible = "regulator-fixed";
		regulator-name = "lis33-1v8-reg";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
	};
};

&omap3_pmx_core {
	i2c3_pins: i2c3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21c2, PIN_INPUT | MUX_MODE0)	/* i2c3_scl.i2c3_scl */
			OMAP3_CORE1_IOPAD(0x21c4, PIN_INPUT | MUX_MODE0)	/* i2c3_sda.i2c3_sda */
		>;
	};

	uart3_pins: uart3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x219e, PIN_INPUT | MUX_MODE0)	/* uart3_rx_irrx.uart3_rx_irrx */
			OMAP3_CORE1_IOPAD(0x21a0, PIN_OUTPUT | MUX_MODE0)	/* uart3_tx_irtx.uart3_tx_irtx */
		>;
	};
};

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;
	clock-frequency = <100000>;

	/* optional 1K EEPROM with revision information */
	eeprom@51 {
		compatible = "atmel,24c01";
		reg = <0x51>;
		pagesize = <8>;
	};

	lis33de: lis33de@1d {
		compatible = "st,lis33de", "st,lis3lv02d";
		reg = <0x1d>;
		Vdd-supply = <&lis33_1v8>;
		Vdd_IO-supply = <&lis33_3v3>;

		st,click-single-x;
		st,click-single-y;
		st,click-single-z;
		st,click-thresh-x = <10>;
		st,click-thresh-y = <10>;
		st,click-thresh-z = <10>;
		st,irq1-click;
		st,irq2-click;
		st,wakeup-x-lo;
		st,wakeup-x-hi;
		st,wakeup-y-lo;
		st,wakeup-y-hi;
		st,wakeup-z-lo;
		st,wakeup-z-hi;
		st,min-limit-x = <120>;
		st,min-limit-y = <120>;
		st,min-limit-z = <140>;
		st,max-limit-x = <550>;
		st,max-limit-y = <550>;
		st,max-limit-z = <750>;
	};
};

&mmc3 {
	status = "disabled";
};

&uart3 {
	interrupts-extended = <&intc 74 &omap3_pmx_core OMAP3_UART3_RX>;
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>;
};

