// SPDX-License-Identifier: GPL-2.0-only
/*
 * Common Device Tree Source for IGEP COM MODULE
 *
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 * Copyright (C) 2014 Enric Balletbo i Serra <<EMAIL>>
 */

#include "omap3-igep.dtsi"

/ {
	leds: gpio_leds {
		compatible = "gpio-leds";

		user0 {
			 label = "omap3:red:user0";
			 gpios = <&twl_gpio 18 GPIO_ACTIVE_LOW>;	/* LEDA */
			 default-state = "off";
		};

		user1 {
			 label = "omap3:green:user1";
			 gpios = <&twl_gpio 19 GPIO_ACTIVE_LOW>;	/* LEDB */
			 default-state = "off";
		};

		user2 {
			 label = "omap3:red:user1";
			 gpios = <&gpio1 16 GPIO_ACTIVE_LOW>;		/* gpio_16 */
			 default-state = "off";
		};
	};

	hsusb2_phy: hsusb2-phy-pins {
		compatible = "usb-nop-xceiv";
		reset-gpios = <&gpio2 22 GPIO_ACTIVE_LOW>;		/* gpio_54 */
		#phy-cells = <0>;
	};
};

&omap3_pmx_core {
	pinctrl-names = "default";
	pinctrl-0 = <&hsusb2_pins>;

	hsusb2_pins: hsusb2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21d4, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi1_cs3.hsusb2_data2 */
			OMAP3_CORE1_IOPAD(0x21d6, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_clk.hsusb2_data7 */
			OMAP3_CORE1_IOPAD(0x21d8, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_simo.hsusb2_data4 */
			OMAP3_CORE1_IOPAD(0x21da, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_somi.hsusb2_data5 */
			OMAP3_CORE1_IOPAD(0x21dc, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_cs0.hsusb2_data6 */
			OMAP3_CORE1_IOPAD(0x21de, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* mcspi2_cs1.hsusb2_data3 */
		>;
	};

	uart2_pins: uart2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x216c, PIN_INPUT | MUX_MODE1)	/* mcbsp3_dx.uart2_cts */
			OMAP3_CORE1_IOPAD(0x216e, PIN_OUTPUT | MUX_MODE1)	/* mcbsp3_dr.uart2_rts */
			OMAP3_CORE1_IOPAD(0x2170, PIN_OUTPUT | MUX_MODE1)	/* mcbsp3_clk.uart2_tx */
			OMAP3_CORE1_IOPAD(0x2172, PIN_INPUT | MUX_MODE1)	/* mcbsp3_fsx.uart2_rx */
		>;
	};
};

&omap3_pmx_core2 {
	pinctrl-names = "default";
	pinctrl-0 = <&hsusb2_core2_pins>;

	hsusb2_core2_pins: hsusb2-core2-pins {
		pinctrl-single,pins = <
			OMAP3630_CORE2_IOPAD(0x25f0, PIN_OUTPUT | MUX_MODE3)		      /* etk_d10.hsusb2_clk */
			OMAP3630_CORE2_IOPAD(0x25f2, PIN_OUTPUT | MUX_MODE3)		      /* etk_d11.hsusb2_stp */
			OMAP3630_CORE2_IOPAD(0x25f4, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d12.hsusb2_dir */
			OMAP3630_CORE2_IOPAD(0x25f6, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d13.hsusb2_nxt */
			OMAP3630_CORE2_IOPAD(0x25f8, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d14.hsusb2_data0 */
			OMAP3630_CORE2_IOPAD(0x25fa, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d15.hsusb2_data1 */
		>;
	};

	leds_core2_pins: leds-core2-pins {
		pinctrl-single,pins = <
			OMAP3630_CORE2_IOPAD(0x25e0, PIN_OUTPUT | MUX_MODE4)	/* etk_d2.gpio_16 */
		>;
	};
};

&usbhshost {
	port2-mode = "ehci-phy";
};

&usbhsehci {
	phys = <0 &hsusb2_phy>;
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pins>;
};

&gpmc {
	ranges = <0 0 0x30000000 0x01000000>;   /* CS0: 16MB for NAND */
};
