// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 */

#include "omap36xx.dtsi"

/ {
	model = "INCOstartec LILLY-A83X module (DM3730)";
	compatible = "incostartec,omap3-lilly-a83x", "ti,omap3630", "ti,omap36xx", "ti,omap3";

	chosen {
			bootargs = "console=ttyO0,115200n8 vt.global_cursor_default=0 consoleblank=0";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x8000000>;   /* 128 MB */
	};

	leds {
		compatible = "gpio-leds";

		led1 {
			label = "lilly-a83x::led1";
			gpios = <&gpio1 29 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "default-on";
		};

	};

	sound {
		compatible = "ti,omap-twl4030";
		ti,model = "lilly-a83x";

		ti,mcbsp = <&mcbsp2>;
	};

	reg_vcc3: vcc3 {
		compatible = "regulator-fixed";
		regulator-name = "VCC3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	hsusb1_phy: hsusb1_phy {
		compatible = "usb-nop-xceiv";
		vcc-supply = <&reg_vcc3>;
		#phy-cells = <0>;
	};
};

&omap3_pmx_wkup {
	pinctrl-names = "default";

	lan9221_pins: lan9221-pins {
		pinctrl-single,pins = <
			OMAP3_WKUP_IOPAD(0x2a5a, PIN_INPUT | MUX_MODE4)   /* reserved.gpio_129 */
		>;
	};

	tsc2048_pins: tsc2048-pins {
		pinctrl-single,pins = <
			OMAP3_WKUP_IOPAD(0x2a16, PIN_INPUT_PULLUP | MUX_MODE4)   /* sys_boot6.gpio_8 */
		>;
	};

	mmc1cd_pins: mmc1cd-pins {
		pinctrl-single,pins = <
			OMAP3_WKUP_IOPAD(0x2a56, PIN_INPUT | MUX_MODE4)   /* reserved.gpio_126 */
		>;
	};
};

&omap3_pmx_core {
	pinctrl-names = "default";

	uart1_pins: uart1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x217c, PIN_OUTPUT | MUX_MODE0)   /* uart1_tx.uart1_tx */
			OMAP3_CORE1_IOPAD(0x217e, PIN_OUTPUT | MUX_MODE0)   /* uart1_rts.uart1_rts */
			OMAP3_CORE1_IOPAD(0x2180, PIN_INPUT | MUX_MODE0)    /* uart1_cts.uart1_cts */
			OMAP3_CORE1_IOPAD(0x2182, PIN_INPUT | MUX_MODE0)    /* uart1_rx.uart1_rx */
		>;
	};

	uart2_pins: uart2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2170, PIN_OUTPUT | MUX_MODE1)   /* mcbsp3_clkx.uart2_tx */
			OMAP3_CORE1_IOPAD(0x2172, PIN_INPUT | MUX_MODE1)    /* mcbsp3_fsx.uart2_rx */
		>;
	};

	uart3_pins: uart3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x219e, PIN_INPUT | MUX_MODE0)    /* uart3_rx_irrx.uart3_rx_irrx */
			OMAP3_CORE1_IOPAD(0x21a0, PIN_OUTPUT | MUX_MODE0)   /* uart3_tx_irtx.uart3_tx_irtx */
		>;
	};

	i2c1_pins: i2c1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21ba ,PIN_INPUT_PULLUP | MUX_MODE0)    /* i2c1_scl.i2c1_scl */
			OMAP3_CORE1_IOPAD(0x21bc ,PIN_INPUT_PULLUP | MUX_MODE0)    /* i2c1_sda.i2c1_sda */
		>;
	};

	i2c2_pins: i2c2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21be, PIN_INPUT | MUX_MODE0)   /* i2c2_scl.i2c2_scl */
			OMAP3_CORE1_IOPAD(0x21c0, PIN_INPUT | MUX_MODE0)   /* i2c2_sda.i2c2_sda */
		>;
	};

	i2c3_pins: i2c3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21c2, PIN_INPUT | MUX_MODE0)   /* i2c3_scl.i2c3_scl */
			OMAP3_CORE1_IOPAD(0x21c4, PIN_INPUT | MUX_MODE0)   /* i2c3_sda.i2c3_sda */
		>;
	};

	hsusb1_pins: hsusb1-pins {
		pinctrl-single,pins = <

			/* GPIO 182 controls USB-Hub reset. But USB-Phy its
			 * reset can't be controlled. So we clamp this GPIO to
			 * high (PIN_OFF_OUTPUT_HIGH) to always enable USB-Hub.
			 */

			OMAP3_CORE1_IOPAD(0x21de, PIN_OUTPUT_PULLUP | PIN_OFF_OUTPUT_HIGH | MUX_MODE4)   /* mcspi2_cs1.gpio_182 */
		>;
	};

	hsusb_otg_pins: hsusb-otg-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21a2, PIN_INPUT | MUX_MODE0)   /* hsusb0_clk.hsusb0_clk */
			OMAP3_CORE1_IOPAD(0x21a4, PIN_OUTPUT | MUX_MODE0)  /* hsusb0_stp.hsusb0_stp */
			OMAP3_CORE1_IOPAD(0x21a6, PIN_INPUT | MUX_MODE0)   /* hsusb0_dir.hsusb0_dir */
			OMAP3_CORE1_IOPAD(0x21a8, PIN_INPUT | MUX_MODE0)   /* hsusb0_nxt.hsusb0_nxt */
			OMAP3_CORE1_IOPAD(0x21aa, PIN_INPUT | MUX_MODE0)   /* hsusb0_data0.hsusb0_data0 */
			OMAP3_CORE1_IOPAD(0x21ac, PIN_INPUT | MUX_MODE0)   /* hsusb0_data1.hsusb0_data1 */
			OMAP3_CORE1_IOPAD(0x21ae, PIN_INPUT | MUX_MODE0)   /* hsusb0_data2.hsusb0_data2 */
			OMAP3_CORE1_IOPAD(0x21b0, PIN_INPUT | MUX_MODE0)   /* hsusb0_data3.hsusb0_data3 */
			OMAP3_CORE1_IOPAD(0x21b2, PIN_INPUT | MUX_MODE0)   /* hsusb0_data4.hsusb0_data4 */
			OMAP3_CORE1_IOPAD(0x21b4, PIN_INPUT | MUX_MODE0)   /* hsusb0_data5.hsusb0_data5 */
			OMAP3_CORE1_IOPAD(0x21b6, PIN_INPUT | MUX_MODE0)   /* hsusb0_data6.hsusb0_data6 */
			OMAP3_CORE1_IOPAD(0x21b8, PIN_INPUT | MUX_MODE0)   /* hsusb0_data7.hsusb0_data7 */
		>;
	};

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2144, PIN_INPUT_PULLUP | MUX_MODE0)   /* sdmmc1_clk.sdmmc1_clk */
			OMAP3_CORE1_IOPAD(0x2146, PIN_INPUT_PULLUP | MUX_MODE0)   /* sdmmc1_cmd.sdmmc1_cmd */
			OMAP3_CORE1_IOPAD(0x2148, PIN_INPUT_PULLUP | MUX_MODE0)   /* sdmmc1_dat0.sdmmc1_dat0 */
			OMAP3_CORE1_IOPAD(0x214a, PIN_INPUT_PULLUP | MUX_MODE0)   /* sdmmc1_dat1.sdmmc1_dat1 */
			OMAP3_CORE1_IOPAD(0x214c, PIN_INPUT_PULLUP | MUX_MODE0)   /* sdmmc1_dat2.sdmmc1_dat2 */
			OMAP3_CORE1_IOPAD(0x214e, PIN_INPUT_PULLUP | MUX_MODE0)   /* sdmmc1_dat3.sdmmc1_dat3 */
		>;
	};

	spi2_pins: spi2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21d6, PIN_INPUT_PULLDOWN | MUX_MODE0)   /* mcspi2_clk.mcspi2_clk */
			OMAP3_CORE1_IOPAD(0x21d8, PIN_INPUT_PULLDOWN | MUX_MODE0)   /* mcspi2_simo.mcspi2_simo */
			OMAP3_CORE1_IOPAD(0x21da, PIN_INPUT_PULLDOWN | MUX_MODE0)   /* mcspi2_somi.mcspi2_somi */
			OMAP3_CORE1_IOPAD(0x21dc, PIN_OUTPUT | MUX_MODE0)   /* mcspi2_cs0.mcspi2_cs0 */
		>;
	};
};

&omap3_pmx_core2 {
	pinctrl-names = "default";

	hsusb1_2_pins: hsusb1-2-pins {
		pinctrl-single,pins = <
			OMAP3630_CORE2_IOPAD(0x25d8, PIN_OUTPUT | MUX_MODE3)  /* etk_clk.hsusb1_stp */
			OMAP3630_CORE2_IOPAD(0x25da, PIN_INPUT | MUX_MODE3)   /* etk_ctl.hsusb1_clk */
			OMAP3630_CORE2_IOPAD(0x25dc, PIN_INPUT | MUX_MODE3)   /* etk_d0.hsusb1_data0 */
			OMAP3630_CORE2_IOPAD(0x25de, PIN_INPUT | MUX_MODE3)   /* etk_d1.hsusb1_data1 */
			OMAP3630_CORE2_IOPAD(0x25e0, PIN_INPUT | MUX_MODE3)   /* etk_d2.hsusb1_data2 */
			OMAP3630_CORE2_IOPAD(0x25e2, PIN_INPUT | MUX_MODE3)   /* etk_d3.hsusb1_data7 */
			OMAP3630_CORE2_IOPAD(0x25e4, PIN_INPUT | MUX_MODE3)   /* etk_d4.hsusb1_data4 */
			OMAP3630_CORE2_IOPAD(0x25e6, PIN_INPUT | MUX_MODE3)   /* etk_d5.hsusb1_data5 */
			OMAP3630_CORE2_IOPAD(0x25e8, PIN_INPUT | MUX_MODE3)   /* etk_d6.hsusb1_data6 */
			OMAP3630_CORE2_IOPAD(0x25ea, PIN_INPUT | MUX_MODE3)   /* etk_d7.hsusb1_data3 */
			OMAP3630_CORE2_IOPAD(0x25ec, PIN_INPUT | MUX_MODE3)   /* etk_d8.hsusb1_dir */
			OMAP3630_CORE2_IOPAD(0x25ee, PIN_INPUT | MUX_MODE3)   /* etk_d9.hsusb1_nxt */
		>;
	};

	gpio1_pins: gpio1-pins {
		pinctrl-single,pins = <
			OMAP3630_CORE2_IOPAD(0x25fa, PIN_OUTPUT_PULLDOWN | MUX_MODE4)   /* etk_d15.gpio_29 */
		>;
	};

};

&gpio1 {
	pinctrl-names = "default";
	pinctrl-0 = <&gpio1_pins>;
};

&gpio6 {
	pinctrl-names = "default";
	pinctrl-0 = <&hsusb1_pins>;
};

&i2c1 {
	clock-frequency = <2600000>;
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins>;

	twl: twl@48 {
		reg = <0x48>;
		interrupts = <7>;   /* SYS_NIRQ cascaded to intc */
		interrupt-parent = <&intc>;

		twl_audio: audio {
			compatible = "ti,twl4030-audio";
			codec {
			};
		};
	};
};

#include "twl4030.dtsi"
#include "twl4030_omap3.dtsi"

&twl {
	vmmc1: regulator-vmmc1 {
		regulator-always-on;
	};

	vdd1: regulator-vdd1 {
		regulator-always-on;
	};

	vdd2: regulator-vdd2 {
		regulator-always-on;
	};
};

&i2c2 {
	clock-frequency = <2600000>;
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_pins>;
};

&i2c3 {
	clock-frequency = <2600000>;
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;
		gpiom1: gpio@20 {
			compatible = "microchip,mcp23017";
			gpio-controller;
			#gpio-cells = <2>;
			reg = <0x20>;
		};
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pins>;
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pins>;
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>;
};

&uart4 {
	status = "disabled";
};

&mmc1 {
	cd-gpios = <&gpio4 30 GPIO_ACTIVE_LOW>;
	cd-inverted;
	vmmc-supply = <&vmmc1>;
	bus-width = <4>;
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins &mmc1cd_pins>;
	cap-sdio-irq;
	cap-sd-highspeed;
	cap-mmc-highspeed;
};

&mmc2 {
	status = "disabled";
};

&mmc3 {
	status = "disabled";
};

&mcspi2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&spi2_pins>;

	tsc2046@0 {
		reg = <0>;   /* CS0 */
		compatible = "ti,tsc2046";
		interrupt-parent = <&gpio1>;
		interrupts = <8 0>;   /* boot6 / gpio_8 */
		spi-max-frequency = <1000000>;
		pendown-gpio = <&gpio1 8 GPIO_ACTIVE_LOW>;
		vcc-supply = <&reg_vcc3>;
		pinctrl-names = "default";
		pinctrl-0 = <&tsc2048_pins>;

		ti,x-min = /bits/ 16 <300>;
		ti,x-max = /bits/ 16 <3000>;
		ti,y-min = /bits/ 16 <600>;
		ti,y-max = /bits/ 16 <3600>;
		ti,x-plate-ohms = /bits/ 16 <80>;
		ti,pressure-max = /bits/ 16 <255>;
		ti,swap-xy;

		wakeup-source;
	};
};

&usbhsehci {
	phys = <&hsusb1_phy>;
};

&usbhshost {
	pinctrl-names = "default";
	pinctrl-0 = <&hsusb1_2_pins>;
	num-ports = <2>;
	port1-mode = "ehci-phy";
};

&usb_otg_hs {
	pinctrl-names = "default";
	pinctrl-0 = <&hsusb_otg_pins>;
	interface-type = <0>;
	usb-phy = <&usb2_phy>;
	phys = <&usb2_phy>;
	phy-names = "usb2-phy";
	mode = <3>;
	power = <50>;
};

&mcbsp2 {
	status = "okay";
};

&gpmc {
	ranges = <0 0 0x30000000 0x1000000>,
		<7 0 0x15000000 0x01000000>;

	nand@0,0 {
		compatible = "ti,omap2-nand";
		reg = <0 0 4>; /* CS0, offset 0, IO size 4 */
		interrupt-parent = <&gpmc>;
		interrupts = <0 IRQ_TYPE_NONE>, /* fifoevent */
			     <1 IRQ_TYPE_NONE>;	/* termcount */
		nand-bus-width = <16>;
		ti,nand-ecc-opt = "bch8";
		/* no elm on omap3 */

		gpmc,mux-add-data = <0>;
		gpmc,device-width = <2>;
		gpmc,wait-pin = <0>;
		gpmc,wait-monitoring-ns = <0>;
		gpmc,burst-length = <4>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <100>;
		gpmc,cs-wr-off-ns = <100>;
		gpmc,adv-on-ns = <0>;
		gpmc,adv-rd-off-ns = <100>;
		gpmc,adv-wr-off-ns = <100>;
		gpmc,oe-on-ns = <5>;
		gpmc,oe-off-ns = <75>;
		gpmc,we-on-ns = <5>;
		gpmc,we-off-ns = <75>;
		gpmc,rd-cycle-ns = <100>;
		gpmc,wr-cycle-ns = <100>;
		gpmc,access-ns = <60>;
		gpmc,page-burst-access-ns = <5>;
		gpmc,bus-turnaround-ns = <0>;
		gpmc,cycle2cycle-samecsen;
		gpmc,cycle2cycle-delay-ns = <50>;
		gpmc,wr-data-mux-bus-ns = <75>;
		gpmc,wr-access-ns = <155>;

		#address-cells = <1>;
		#size-cells = <1>;

		partition@0 {
			label = "MLO";
			reg = <0 0x80000>;
		};

		partition@80000 {
			label = "u-boot";
			reg = <0x80000 0x1e0000>;
		};

		partition@260000 {
			label = "u-boot-environment";
			reg = <0x260000 0x20000>;
		};

		partition@280000 {
			label = "kernel";
			reg = <0x280000 0x500000>;
		};

		partition@780000 {
			label = "filesystem";
			reg = <0x780000 0xf880000>;
		};
	};

	ethernet@7,0 {
		compatible = "smsc,lan9221", "smsc,lan9115";
		bank-width = <2>;
		gpmc,mux-add-data = <2>;
		gpmc,cs-on-ns = <10>;
		gpmc,cs-rd-off-ns = <60>;
		gpmc,cs-wr-off-ns = <60>;
		gpmc,adv-on-ns = <0>;
		gpmc,adv-rd-off-ns = <10>;
		gpmc,adv-wr-off-ns = <10>;
		gpmc,oe-on-ns = <10>;
		gpmc,oe-off-ns = <60>;
		gpmc,we-on-ns = <10>;
		gpmc,we-off-ns = <60>;
		gpmc,rd-cycle-ns = <100>;
		gpmc,wr-cycle-ns = <100>;
		gpmc,access-ns = <50>;
		gpmc,page-burst-access-ns = <5>;
		gpmc,bus-turnaround-ns = <0>;
		gpmc,cycle2cycle-delay-ns = <75>;
		gpmc,wr-data-mux-bus-ns = <15>;
		gpmc,wr-access-ns = <75>;
		gpmc,cycle2cycle-samecsen;
		gpmc,cycle2cycle-diffcsen;
		vddvario-supply = <&reg_vcc3>;
		vdd33a-supply = <&reg_vcc3>;
		reg-io-width = <4>;
		interrupt-parent = <&gpio5>;
		interrupts = <1 0x2>;
		reg = <7 0 0xff>;
		pinctrl-names = "default";
		pinctrl-0 = <&lan9221_pins>;
		phy-mode = "mii";
	};
};
