// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for OMAP4 clock data
 *
 * Copyright (C) 2013 Texas Instruments, Inc.
 */
&cm1_clocks {
	extalt_clkin_ck: extalt_clkin_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "extalt_clkin_ck";
		clock-frequency = <59000000>;
	};

	pad_clks_src_ck: pad_clks_src_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "pad_clks_src_ck";
		clock-frequency = <12000000>;
	};

	pad_clks_ck: pad_clks_ck@108 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "pad_clks_ck";
		clocks = <&pad_clks_src_ck>;
		ti,bit-shift = <8>;
		reg = <0x0108>;
	};

	pad_slimbus_core_clks_ck: pad_slimbus_core_clks_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "pad_slimbus_core_clks_ck";
		clock-frequency = <12000000>;
	};

	secure_32k_clk_src_ck: secure_32k_clk_src_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "secure_32k_clk_src_ck";
		clock-frequency = <32768>;
	};

	slimbus_src_clk: slimbus_src_clk {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "slimbus_src_clk";
		clock-frequency = <12000000>;
	};

	slimbus_clk: slimbus_clk@108 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "slimbus_clk";
		clocks = <&slimbus_src_clk>;
		ti,bit-shift = <10>;
		reg = <0x0108>;
	};

	sys_32k_ck: sys_32k_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "sys_32k_ck";
		clock-frequency = <32768>;
	};

	virt_12000000_ck: virt_12000000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_12000000_ck";
		clock-frequency = <12000000>;
	};

	virt_13000000_ck: virt_13000000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_13000000_ck";
		clock-frequency = <13000000>;
	};

	virt_16800000_ck: virt_16800000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_16800000_ck";
		clock-frequency = <16800000>;
	};

	virt_19200000_ck: virt_19200000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_19200000_ck";
		clock-frequency = <19200000>;
	};

	virt_26000000_ck: virt_26000000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_26000000_ck";
		clock-frequency = <26000000>;
	};

	virt_27000000_ck: virt_27000000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_27000000_ck";
		clock-frequency = <27000000>;
	};

	virt_38400000_ck: virt_38400000_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "virt_38400000_ck";
		clock-frequency = <38400000>;
	};

	tie_low_clock_ck: tie_low_clock_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "tie_low_clock_ck";
		clock-frequency = <0>;
	};

	utmi_phy_clkout_ck: utmi_phy_clkout_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "utmi_phy_clkout_ck";
		clock-frequency = <60000000>;
	};

	xclk60mhsp1_ck: xclk60mhsp1_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "xclk60mhsp1_ck";
		clock-frequency = <60000000>;
	};

	xclk60mhsp2_ck: xclk60mhsp2_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "xclk60mhsp2_ck";
		clock-frequency = <60000000>;
	};

	xclk60motg_ck: xclk60motg_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "xclk60motg_ck";
		clock-frequency = <60000000>;
	};

	dpll_abe_ck: dpll_abe_ck@1e0 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-m4xen-clock";
		clock-output-names = "dpll_abe_ck";
		clocks = <&abe_dpll_refclk_mux_ck>, <&abe_dpll_bypass_clk_mux_ck>;
		reg = <0x01e0>, <0x01e4>, <0x01ec>, <0x01e8>;
	};

	dpll_abe_x2_ck: dpll_abe_x2_ck@1f0 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-x2-clock";
		clock-output-names = "dpll_abe_x2_ck";
		clocks = <&dpll_abe_ck>;
		reg = <0x01f0>;
	};

	dpll_abe_m2x2_ck: dpll_abe_m2x2_ck@1f0 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_abe_m2x2_ck";
		clocks = <&dpll_abe_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x01f0>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	abe_24m_fclk: abe_24m_fclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "abe_24m_fclk";
		clocks = <&dpll_abe_m2x2_ck>;
		clock-mult = <1>;
		clock-div = <8>;
	};

	abe_clk: abe_clk@108 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "abe_clk";
		clocks = <&dpll_abe_m2x2_ck>;
		ti,max-div = <4>;
		reg = <0x0108>;
		ti,index-power-of-two;
	};


	dpll_abe_m3x2_ck: dpll_abe_m3x2_ck@1f4 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_abe_m3x2_ck";
		clocks = <&dpll_abe_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x01f4>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	core_hsd_byp_clk_mux_ck: core_hsd_byp_clk_mux_ck@12c {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "core_hsd_byp_clk_mux_ck";
		clocks = <&sys_clkin_ck>, <&dpll_abe_m3x2_ck>;
		ti,bit-shift = <23>;
		reg = <0x012c>;
	};

	dpll_core_ck: dpll_core_ck@120 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-core-clock";
		clock-output-names = "dpll_core_ck";
		clocks = <&sys_clkin_ck>, <&core_hsd_byp_clk_mux_ck>;
		reg = <0x0120>, <0x0124>, <0x012c>, <0x0128>;
	};

	dpll_core_x2_ck: dpll_core_x2_ck {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-x2-clock";
		clock-output-names = "dpll_core_x2_ck";
		clocks = <&dpll_core_ck>;
	};

	dpll_core_m6x2_ck: dpll_core_m6x2_ck@140 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_m6x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x0140>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	dpll_core_m2_ck: dpll_core_m2_ck@130 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_m2_ck";
		clocks = <&dpll_core_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x0130>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	ddrphy_ck: ddrphy_ck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "ddrphy_ck";
		clocks = <&dpll_core_m2_ck>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	dpll_core_m5x2_ck: dpll_core_m5x2_ck@13c {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_m5x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x013c>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	div_core_ck: div_core_ck@100 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "div_core_ck";
		clocks = <&dpll_core_m5x2_ck>;
		reg = <0x0100>;
		ti,max-div = <2>;
	};

	div_iva_hs_clk: div_iva_hs_clk@1dc {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "div_iva_hs_clk";
		clocks = <&dpll_core_m5x2_ck>;
		ti,max-div = <4>;
		reg = <0x01dc>;
		ti,index-power-of-two;
	};

	div_mpu_hs_clk: div_mpu_hs_clk@19c {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "div_mpu_hs_clk";
		clocks = <&dpll_core_m5x2_ck>;
		ti,max-div = <4>;
		reg = <0x019c>;
		ti,index-power-of-two;
	};

	dpll_core_m4x2_ck: dpll_core_m4x2_ck@138 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_m4x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x0138>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	dll_clk_div_ck: dll_clk_div_ck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "dll_clk_div_ck";
		clocks = <&dpll_core_m4x2_ck>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	dpll_abe_m2_ck: dpll_abe_m2_ck@1f0 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_abe_m2_ck";
		clocks = <&dpll_abe_ck>;
		ti,max-div = <31>;
		reg = <0x01f0>;
		ti,index-starts-at-one;
	};

	dpll_core_m3x2_gate_ck: dpll_core_m3x2_gate_ck@134 {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "dpll_core_m3x2_gate_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x0134>;
	};

	dpll_core_m3x2_div_ck: dpll_core_m3x2_div_ck@134 {
		#clock-cells = <0>;
		compatible = "ti,composite-divider-clock";
		clock-output-names = "dpll_core_m3x2_div_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <31>;
		reg = <0x0134>;
		ti,index-starts-at-one;
	};

	dpll_core_m3x2_ck: dpll_core_m3x2_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "dpll_core_m3x2_ck";
		clocks = <&dpll_core_m3x2_gate_ck>, <&dpll_core_m3x2_div_ck>;
	};

	dpll_core_m7x2_ck: dpll_core_m7x2_ck@144 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_core_m7x2_ck";
		clocks = <&dpll_core_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x0144>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	iva_hsd_byp_clk_mux_ck: iva_hsd_byp_clk_mux_ck@1ac {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "iva_hsd_byp_clk_mux_ck";
		clocks = <&sys_clkin_ck>, <&div_iva_hs_clk>;
		ti,bit-shift = <23>;
		reg = <0x01ac>;
	};

	dpll_iva_ck: dpll_iva_ck@1a0 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-clock";
		clock-output-names = "dpll_iva_ck";
		clocks = <&sys_clkin_ck>, <&iva_hsd_byp_clk_mux_ck>;
		reg = <0x01a0>, <0x01a4>, <0x01ac>, <0x01a8>;
		assigned-clocks = <&dpll_iva_ck>;
		assigned-clock-rates = <931200000>;
	};

	dpll_iva_x2_ck: dpll_iva_x2_ck {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-x2-clock";
		clock-output-names = "dpll_iva_x2_ck";
		clocks = <&dpll_iva_ck>;
	};

	dpll_iva_m4x2_ck: dpll_iva_m4x2_ck@1b8 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_iva_m4x2_ck";
		clocks = <&dpll_iva_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x01b8>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
		assigned-clocks = <&dpll_iva_m4x2_ck>;
		assigned-clock-rates = <465600000>;
	};

	dpll_iva_m5x2_ck: dpll_iva_m5x2_ck@1bc {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_iva_m5x2_ck";
		clocks = <&dpll_iva_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x01bc>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
		assigned-clocks = <&dpll_iva_m5x2_ck>;
		assigned-clock-rates = <266100000>;
	};

	dpll_mpu_ck: dpll_mpu_ck@160 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-clock";
		clock-output-names = "dpll_mpu_ck";
		clocks = <&sys_clkin_ck>, <&div_mpu_hs_clk>;
		reg = <0x0160>, <0x0164>, <0x016c>, <0x0168>;
	};

	dpll_mpu_m2_ck: dpll_mpu_m2_ck@170 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_mpu_m2_ck";
		clocks = <&dpll_mpu_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x0170>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	per_hs_clk_div_ck: per_hs_clk_div_ck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "per_hs_clk_div_ck";
		clocks = <&dpll_abe_m3x2_ck>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	usb_hs_clk_div_ck: usb_hs_clk_div_ck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "usb_hs_clk_div_ck";
		clocks = <&dpll_abe_m3x2_ck>;
		clock-mult = <1>;
		clock-div = <3>;
	};

	l3_div_ck: l3_div_ck@100 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "l3_div_ck";
		clocks = <&div_core_ck>;
		ti,bit-shift = <4>;
		ti,max-div = <2>;
		reg = <0x0100>;
	};

	l4_div_ck: l4_div_ck@100 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "l4_div_ck";
		clocks = <&l3_div_ck>;
		ti,bit-shift = <8>;
		ti,max-div = <2>;
		reg = <0x0100>;
	};

	lp_clk_div_ck: lp_clk_div_ck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "lp_clk_div_ck";
		clocks = <&dpll_abe_m2x2_ck>;
		clock-mult = <1>;
		clock-div = <16>;
	};

	mpu_periphclk: mpu_periphclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "mpu_periphclk";
		clocks = <&dpll_mpu_ck>;
		clock-mult = <1>;
		clock-div = <2>;
	};

	ocp_abe_iclk: ocp_abe_iclk@528 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "ocp_abe_iclk";
		clocks = <&abe_clkctrl OMAP4_AESS_CLKCTRL 24>;
		ti,bit-shift = <24>;
		reg = <0x0528>;
		ti,dividers = <2>, <1>;
	};

	per_abe_24m_fclk: per_abe_24m_fclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "per_abe_24m_fclk";
		clocks = <&dpll_abe_m2_ck>;
		clock-mult = <1>;
		clock-div = <4>;
	};

	dummy_ck: dummy_ck {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-output-names = "dummy_ck";
		clock-frequency = <0>;
	};
};

&prm_clocks {
	sys_clkin_ck: sys_clkin_ck@110 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "sys_clkin_ck";
		clocks = <&virt_12000000_ck>, <&virt_13000000_ck>, <&virt_16800000_ck>, <&virt_19200000_ck>, <&virt_26000000_ck>, <&virt_27000000_ck>, <&virt_38400000_ck>;
		reg = <0x0110>;
		ti,index-starts-at-one;
	};

	abe_dpll_bypass_clk_mux_ck: abe_dpll_bypass_clk_mux_ck@108 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "abe_dpll_bypass_clk_mux_ck";
		clocks = <&sys_clkin_ck>, <&sys_32k_ck>;
		ti,bit-shift = <24>;
		reg = <0x0108>;
	};

	abe_dpll_refclk_mux_ck: abe_dpll_refclk_mux_ck@10c {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "abe_dpll_refclk_mux_ck";
		clocks = <&sys_clkin_ck>, <&sys_32k_ck>;
		reg = <0x010c>;
	};

	dbgclk_mux_ck: dbgclk_mux_ck {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "dbgclk_mux_ck";
		clocks = <&sys_clkin_ck>;
		clock-mult = <1>;
		clock-div = <1>;
	};

	l4_wkup_clk_mux_ck: l4_wkup_clk_mux_ck@108 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "l4_wkup_clk_mux_ck";
		clocks = <&sys_clkin_ck>, <&lp_clk_div_ck>;
		reg = <0x0108>;
	};

	syc_clk_div_ck: syc_clk_div_ck@100 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "syc_clk_div_ck";
		clocks = <&sys_clkin_ck>;
		reg = <0x0100>;
		ti,max-div = <2>;
	};

	usim_ck: usim_ck@1858 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "usim_ck";
		clocks = <&dpll_per_m4x2_ck>;
		ti,bit-shift = <24>;
		reg = <0x1858>;
		ti,dividers = <14>, <18>;
	};

	usim_fclk: usim_fclk@1858 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "usim_fclk";
		clocks = <&usim_ck>;
		ti,bit-shift = <8>;
		reg = <0x1858>;
	};

	trace_clk_div_ck: trace_clk_div_ck {
		#clock-cells = <0>;
		compatible = "ti,clkdm-gate-clock";
		clock-output-names = "trace_clk_div_ck";
		clocks = <&emu_sys_clkctrl OMAP4_DEBUGSS_CLKCTRL 24>;
	};
};

&prm_clockdomains {
	emu_sys_clkdm: emu_sys_clkdm {
		compatible = "ti,clockdomain";
		clock-output-names = "emu_sys_clkdm";
		clocks = <&trace_clk_div_ck>;
	};
};

&cm2_clocks {
	per_hsd_byp_clk_mux_ck: per_hsd_byp_clk_mux_ck@14c {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "per_hsd_byp_clk_mux_ck";
		clocks = <&sys_clkin_ck>, <&per_hs_clk_div_ck>;
		ti,bit-shift = <23>;
		reg = <0x014c>;
	};

	dpll_per_ck: dpll_per_ck@140 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-clock";
		clock-output-names = "dpll_per_ck";
		clocks = <&sys_clkin_ck>, <&per_hsd_byp_clk_mux_ck>;
		reg = <0x0140>, <0x0144>, <0x014c>, <0x0148>;
	};

	dpll_per_m2_ck: dpll_per_m2_ck@150 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_m2_ck";
		clocks = <&dpll_per_ck>;
		ti,max-div = <31>;
		reg = <0x0150>;
		ti,index-starts-at-one;
	};

	dpll_per_x2_ck: dpll_per_x2_ck@150 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-x2-clock";
		clock-output-names = "dpll_per_x2_ck";
		clocks = <&dpll_per_ck>;
		reg = <0x0150>;
	};

	dpll_per_m2x2_ck: dpll_per_m2x2_ck@150 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_m2x2_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x0150>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	dpll_per_m3x2_gate_ck: dpll_per_m3x2_gate_ck@154 {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "dpll_per_m3x2_gate_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x0154>;
	};

	dpll_per_m3x2_div_ck: dpll_per_m3x2_div_ck@154 {
		#clock-cells = <0>;
		compatible = "ti,composite-divider-clock";
		clock-output-names = "dpll_per_m3x2_div_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,max-div = <31>;
		reg = <0x0154>;
		ti,index-starts-at-one;
	};

	dpll_per_m3x2_ck: dpll_per_m3x2_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "dpll_per_m3x2_ck";
		clocks = <&dpll_per_m3x2_gate_ck>, <&dpll_per_m3x2_div_ck>;
	};

	dpll_per_m4x2_ck: dpll_per_m4x2_ck@158 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_m4x2_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x0158>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	dpll_per_m5x2_ck: dpll_per_m5x2_ck@15c {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_m5x2_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x015c>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	dpll_per_m6x2_ck: dpll_per_m6x2_ck@160 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_m6x2_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x0160>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	dpll_per_m7x2_ck: dpll_per_m7x2_ck@164 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_per_m7x2_ck";
		clocks = <&dpll_per_x2_ck>;
		ti,max-div = <31>;
		ti,autoidle-shift = <8>;
		reg = <0x0164>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	dpll_usb_ck: dpll_usb_ck@180 {
		#clock-cells = <0>;
		compatible = "ti,omap4-dpll-j-type-clock";
		clock-output-names = "dpll_usb_ck";
		clocks = <&sys_clkin_ck>, <&usb_hs_clk_div_ck>;
		reg = <0x0180>, <0x0184>, <0x018c>, <0x0188>;
	};

	dpll_usb_clkdcoldo_ck: dpll_usb_clkdcoldo_ck@1b4 {
		#clock-cells = <0>;
		compatible = "ti,fixed-factor-clock";
		clock-output-names = "dpll_usb_clkdcoldo_ck";
		clocks = <&dpll_usb_ck>;
		ti,clock-div = <1>;
		ti,autoidle-shift = <8>;
		reg = <0x01b4>;
		ti,clock-mult = <1>;
		ti,invert-autoidle-bit;
	};

	dpll_usb_m2_ck: dpll_usb_m2_ck@190 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "dpll_usb_m2_ck";
		clocks = <&dpll_usb_ck>;
		ti,max-div = <127>;
		ti,autoidle-shift = <8>;
		reg = <0x0190>;
		ti,index-starts-at-one;
		ti,invert-autoidle-bit;
	};

	ducati_clk_mux_ck: ducati_clk_mux_ck@100 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "ducati_clk_mux_ck";
		clocks = <&div_core_ck>, <&dpll_per_m6x2_ck>;
		reg = <0x0100>;
	};

	func_12m_fclk: func_12m_fclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "func_12m_fclk";
		clocks = <&dpll_per_m2x2_ck>;
		clock-mult = <1>;
		clock-div = <16>;
	};

	func_24m_clk: func_24m_clk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "func_24m_clk";
		clocks = <&dpll_per_m2_ck>;
		clock-mult = <1>;
		clock-div = <4>;
	};

	func_24mc_fclk: func_24mc_fclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "func_24mc_fclk";
		clocks = <&dpll_per_m2x2_ck>;
		clock-mult = <1>;
		clock-div = <8>;
	};

	func_48m_fclk: func_48m_fclk@108 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "func_48m_fclk";
		clocks = <&dpll_per_m2x2_ck>;
		reg = <0x0108>;
		ti,dividers = <4>, <8>;
	};

	func_48mc_fclk: func_48mc_fclk {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clock-output-names = "func_48mc_fclk";
		clocks = <&dpll_per_m2x2_ck>;
		clock-mult = <1>;
		clock-div = <4>;
	};

	func_64m_fclk: func_64m_fclk@108 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "func_64m_fclk";
		clocks = <&dpll_per_m4x2_ck>;
		reg = <0x0108>;
		ti,dividers = <2>, <4>;
	};

	func_96m_fclk: func_96m_fclk@108 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "func_96m_fclk";
		clocks = <&dpll_per_m2x2_ck>;
		reg = <0x0108>;
		ti,dividers = <2>, <4>;
	};

	init_60m_fclk: init_60m_fclk@104 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "init_60m_fclk";
		clocks = <&dpll_usb_m2_ck>;
		reg = <0x0104>;
		ti,dividers = <1>, <8>;
	};

	per_abe_nc_fclk: per_abe_nc_fclk@108 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "per_abe_nc_fclk";
		clocks = <&dpll_abe_m2_ck>;
		reg = <0x0108>;
		ti,max-div = <2>;
	};

	usb_phy_cm_clk32k: usb_phy_cm_clk32k@640 {
		#clock-cells = <0>;
		compatible = "ti,gate-clock";
		clock-output-names = "usb_phy_cm_clk32k";
		clocks = <&sys_32k_ck>;
		ti,bit-shift = <8>;
		reg = <0x0640>;
	};
};

&cm2_clockdomains {
	l3_init_clkdm: l3_init_clkdm {
		compatible = "ti,clockdomain";
		clock-output-names = "l3_init_clkdm";
		clocks = <&dpll_usb_ck>;
	};
};

&scrm_clocks {
	auxclk0_src_gate_ck: auxclk0_src_gate_ck@310 {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "auxclk0_src_gate_ck";
		clocks = <&dpll_core_m3x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x0310>;
	};

	auxclk0_src_mux_ck: auxclk0_src_mux_ck@310 {
		#clock-cells = <0>;
		compatible = "ti,composite-mux-clock";
		clock-output-names = "auxclk0_src_mux_ck";
		clocks = <&sys_clkin_ck>, <&dpll_core_m3x2_ck>, <&dpll_per_m3x2_ck>;
		ti,bit-shift = <1>;
		reg = <0x0310>;
	};

	auxclk0_src_ck: auxclk0_src_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "auxclk0_src_ck";
		clocks = <&auxclk0_src_gate_ck>, <&auxclk0_src_mux_ck>;
	};

	auxclk0_ck: auxclk0_ck@310 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "auxclk0_ck";
		clocks = <&auxclk0_src_ck>;
		ti,bit-shift = <16>;
		ti,max-div = <16>;
		reg = <0x0310>;
	};

	auxclk1_src_gate_ck: auxclk1_src_gate_ck@314 {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "auxclk1_src_gate_ck";
		clocks = <&dpll_core_m3x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x0314>;
	};

	auxclk1_src_mux_ck: auxclk1_src_mux_ck@314 {
		#clock-cells = <0>;
		compatible = "ti,composite-mux-clock";
		clock-output-names = "auxclk1_src_mux_ck";
		clocks = <&sys_clkin_ck>, <&dpll_core_m3x2_ck>, <&dpll_per_m3x2_ck>;
		ti,bit-shift = <1>;
		reg = <0x0314>;
	};

	auxclk1_src_ck: auxclk1_src_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "auxclk1_src_ck";
		clocks = <&auxclk1_src_gate_ck>, <&auxclk1_src_mux_ck>;
	};

	auxclk1_ck: auxclk1_ck@314 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "auxclk1_ck";
		clocks = <&auxclk1_src_ck>;
		ti,bit-shift = <16>;
		ti,max-div = <16>;
		reg = <0x0314>;
	};

	auxclk2_src_gate_ck: auxclk2_src_gate_ck@318 {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "auxclk2_src_gate_ck";
		clocks = <&dpll_core_m3x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x0318>;
	};

	auxclk2_src_mux_ck: auxclk2_src_mux_ck@318 {
		#clock-cells = <0>;
		compatible = "ti,composite-mux-clock";
		clock-output-names = "auxclk2_src_mux_ck";
		clocks = <&sys_clkin_ck>, <&dpll_core_m3x2_ck>, <&dpll_per_m3x2_ck>;
		ti,bit-shift = <1>;
		reg = <0x0318>;
	};

	auxclk2_src_ck: auxclk2_src_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "auxclk2_src_ck";
		clocks = <&auxclk2_src_gate_ck>, <&auxclk2_src_mux_ck>;
	};

	auxclk2_ck: auxclk2_ck@318 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "auxclk2_ck";
		clocks = <&auxclk2_src_ck>;
		ti,bit-shift = <16>;
		ti,max-div = <16>;
		reg = <0x0318>;
	};

	auxclk3_src_gate_ck: auxclk3_src_gate_ck@31c {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "auxclk3_src_gate_ck";
		clocks = <&dpll_core_m3x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x031c>;
	};

	auxclk3_src_mux_ck: auxclk3_src_mux_ck@31c {
		#clock-cells = <0>;
		compatible = "ti,composite-mux-clock";
		clock-output-names = "auxclk3_src_mux_ck";
		clocks = <&sys_clkin_ck>, <&dpll_core_m3x2_ck>, <&dpll_per_m3x2_ck>;
		ti,bit-shift = <1>;
		reg = <0x031c>;
	};

	auxclk3_src_ck: auxclk3_src_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "auxclk3_src_ck";
		clocks = <&auxclk3_src_gate_ck>, <&auxclk3_src_mux_ck>;
	};

	auxclk3_ck: auxclk3_ck@31c {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "auxclk3_ck";
		clocks = <&auxclk3_src_ck>;
		ti,bit-shift = <16>;
		ti,max-div = <16>;
		reg = <0x031c>;
	};

	auxclk4_src_gate_ck: auxclk4_src_gate_ck@320 {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "auxclk4_src_gate_ck";
		clocks = <&dpll_core_m3x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x0320>;
	};

	auxclk4_src_mux_ck: auxclk4_src_mux_ck@320 {
		#clock-cells = <0>;
		compatible = "ti,composite-mux-clock";
		clock-output-names = "auxclk4_src_mux_ck";
		clocks = <&sys_clkin_ck>, <&dpll_core_m3x2_ck>, <&dpll_per_m3x2_ck>;
		ti,bit-shift = <1>;
		reg = <0x0320>;
	};

	auxclk4_src_ck: auxclk4_src_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "auxclk4_src_ck";
		clocks = <&auxclk4_src_gate_ck>, <&auxclk4_src_mux_ck>;
	};

	auxclk4_ck: auxclk4_ck@320 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "auxclk4_ck";
		clocks = <&auxclk4_src_ck>;
		ti,bit-shift = <16>;
		ti,max-div = <16>;
		reg = <0x0320>;
	};

	auxclk5_src_gate_ck: auxclk5_src_gate_ck@324 {
		#clock-cells = <0>;
		compatible = "ti,composite-no-wait-gate-clock";
		clock-output-names = "auxclk5_src_gate_ck";
		clocks = <&dpll_core_m3x2_ck>;
		ti,bit-shift = <8>;
		reg = <0x0324>;
	};

	auxclk5_src_mux_ck: auxclk5_src_mux_ck@324 {
		#clock-cells = <0>;
		compatible = "ti,composite-mux-clock";
		clock-output-names = "auxclk5_src_mux_ck";
		clocks = <&sys_clkin_ck>, <&dpll_core_m3x2_ck>, <&dpll_per_m3x2_ck>;
		ti,bit-shift = <1>;
		reg = <0x0324>;
	};

	auxclk5_src_ck: auxclk5_src_ck {
		#clock-cells = <0>;
		compatible = "ti,composite-clock";
		clock-output-names = "auxclk5_src_ck";
		clocks = <&auxclk5_src_gate_ck>, <&auxclk5_src_mux_ck>;
	};

	auxclk5_ck: auxclk5_ck@324 {
		#clock-cells = <0>;
		compatible = "ti,divider-clock";
		clock-output-names = "auxclk5_ck";
		clocks = <&auxclk5_src_ck>;
		ti,bit-shift = <16>;
		ti,max-div = <16>;
		reg = <0x0324>;
	};

	auxclkreq0_ck: auxclkreq0_ck@210 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "auxclkreq0_ck";
		clocks = <&auxclk0_ck>, <&auxclk1_ck>, <&auxclk2_ck>, <&auxclk3_ck>, <&auxclk4_ck>, <&auxclk5_ck>;
		ti,bit-shift = <2>;
		reg = <0x0210>;
	};

	auxclkreq1_ck: auxclkreq1_ck@214 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "auxclkreq1_ck";
		clocks = <&auxclk0_ck>, <&auxclk1_ck>, <&auxclk2_ck>, <&auxclk3_ck>, <&auxclk4_ck>, <&auxclk5_ck>;
		ti,bit-shift = <2>;
		reg = <0x0214>;
	};

	auxclkreq2_ck: auxclkreq2_ck@218 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "auxclkreq2_ck";
		clocks = <&auxclk0_ck>, <&auxclk1_ck>, <&auxclk2_ck>, <&auxclk3_ck>, <&auxclk4_ck>, <&auxclk5_ck>;
		ti,bit-shift = <2>;
		reg = <0x0218>;
	};

	auxclkreq3_ck: auxclkreq3_ck@21c {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "auxclkreq3_ck";
		clocks = <&auxclk0_ck>, <&auxclk1_ck>, <&auxclk2_ck>, <&auxclk3_ck>, <&auxclk4_ck>, <&auxclk5_ck>;
		ti,bit-shift = <2>;
		reg = <0x021c>;
	};

	auxclkreq4_ck: auxclkreq4_ck@220 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "auxclkreq4_ck";
		clocks = <&auxclk0_ck>, <&auxclk1_ck>, <&auxclk2_ck>, <&auxclk3_ck>, <&auxclk4_ck>, <&auxclk5_ck>;
		ti,bit-shift = <2>;
		reg = <0x0220>;
	};

	auxclkreq5_ck: auxclkreq5_ck@224 {
		#clock-cells = <0>;
		compatible = "ti,mux-clock";
		clock-output-names = "auxclkreq5_ck";
		clocks = <&auxclk0_ck>, <&auxclk1_ck>, <&auxclk2_ck>, <&auxclk3_ck>, <&auxclk4_ck>, <&auxclk5_ck>;
		ti,bit-shift = <2>;
		reg = <0x0224>;
	};
};

&cm1 {
	mpuss_cm: mpuss_cm@300 {
		compatible = "ti,omap4-cm";
		clock-output-names = "mpuss_cm";
		reg = <0x300 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x300 0x100>;

		mpuss_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "mpuss_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	tesla_cm: tesla_cm@400 {
		compatible = "ti,omap4-cm";
		clock-output-names = "tesla_cm";
		reg = <0x400 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x400 0x100>;

		tesla_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "tesla_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	abe_cm: abe_cm@500 {
		compatible = "ti,omap4-cm";
		clock-output-names = "abe_cm";
		reg = <0x500 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x500 0x100>;

		abe_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "abe_clkctrl";
			reg = <0x20 0x6c>;
			#clock-cells = <2>;
		};
	};

};

&cm2 {
	l4_ao_cm: l4_ao_cm@600 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l4_ao_cm";
		reg = <0x600 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x600 0x100>;

		l4_ao_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l4_ao_clkctrl";
			reg = <0x20 0x1c>;
			#clock-cells = <2>;
		};
	};

	l3_1_cm: l3_1_cm@700 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3_1_cm";
		reg = <0x700 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x700 0x100>;

		l3_1_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3_1_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	l3_2_cm: l3_2_cm@800 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3_2_cm";
		reg = <0x800 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x800 0x100>;

		l3_2_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3_2_clkctrl";
			reg = <0x20 0x14>;
			#clock-cells = <2>;
		};
	};

	ducati_cm: ducati_cm@900 {
		compatible = "ti,omap4-cm";
		clock-output-names = "ducati_cm";
		reg = <0x900 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x900 0x100>;

		ducati_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "ducati_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	l3_dma_cm: l3_dma_cm@a00 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3_dma_cm";
		reg = <0xa00 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xa00 0x100>;

		l3_dma_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3_dma_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	l3_emif_cm: l3_emif_cm@b00 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3_emif_cm";
		reg = <0xb00 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xb00 0x100>;

		l3_emif_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3_emif_clkctrl";
			reg = <0x20 0x1c>;
			#clock-cells = <2>;
		};
	};

	d2d_cm: d2d_cm@c00 {
		compatible = "ti,omap4-cm";
		clock-output-names = "d2d_cm";
		reg = <0xc00 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xc00 0x100>;

		d2d_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "d2d_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	l4_cfg_cm: l4_cfg_cm@d00 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l4_cfg_cm";
		reg = <0xd00 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xd00 0x100>;

		l4_cfg_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l4_cfg_clkctrl";
			reg = <0x20 0x14>;
			#clock-cells = <2>;
		};
	};

	l3_instr_cm: l3_instr_cm@e00 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3_instr_cm";
		reg = <0xe00 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xe00 0x100>;

		l3_instr_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3_instr_clkctrl";
			reg = <0x20 0x24>;
			#clock-cells = <2>;
		};
	};

	ivahd_cm: ivahd_cm@f00 {
		compatible = "ti,omap4-cm";
		clock-output-names = "ivahd_cm";
		reg = <0xf00 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0xf00 0x100>;

		ivahd_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "ivahd_clkctrl";
			reg = <0x20 0xc>;
			#clock-cells = <2>;
		};
	};

	iss_cm: iss_cm@1000 {
		compatible = "ti,omap4-cm";
		clock-output-names = "iss_cm";
		reg = <0x1000 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1000 0x100>;

		iss_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "iss_clkctrl";
			reg = <0x20 0xc>;
			#clock-cells = <2>;
		};
	};

	l3_dss_cm: l3_dss_cm@1100 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3_dss_cm";
		reg = <0x1100 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1100 0x100>;

		l3_dss_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3_dss_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	l3_gfx_cm: l3_gfx_cm@1200 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3_gfx_cm";
		reg = <0x1200 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1200 0x100>;

		l3_gfx_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3_gfx_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};

	l3_init_cm: l3_init_cm@1300 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l3_init_cm";
		reg = <0x1300 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1300 0x100>;

		l3_init_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l3_init_clkctrl";
			reg = <0x20 0xc4>;
			#clock-cells = <2>;
		};
	};

	l4_per_cm: clock@1400 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l4_per_cm";
		reg = <0x1400 0x200>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1400 0x200>;

		l4_per_clkctrl: clock@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l4_per_clkctrl";
			reg = <0x20 0x144>;
			#clock-cells = <2>;
		};

		l4_secure_clkctrl: clock@1a0 {
			compatible = "ti,clkctrl";
			clock-output-names = "l4_secure_clkctrl";
			reg = <0x1a0 0x3c>;
			#clock-cells = <2>;
		};
	};
};

&prm {
	l4_wkup_cm: l4_wkup_cm@1800 {
		compatible = "ti,omap4-cm";
		clock-output-names = "l4_wkup_cm";
		reg = <0x1800 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1800 0x100>;

		l4_wkup_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "l4_wkup_clkctrl";
			reg = <0x20 0x5c>;
			#clock-cells = <2>;
		};
	};

	emu_sys_cm: emu_sys_cm@1a00 {
		compatible = "ti,omap4-cm";
		clock-output-names = "emu_sys_cm";
		reg = <0x1a00 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x1a00 0x100>;

		emu_sys_clkctrl: clk@20 {
			compatible = "ti,clkctrl";
			clock-output-names = "emu_sys_clkctrl";
			reg = <0x20 0x4>;
			#clock-cells = <2>;
		};
	};
};
