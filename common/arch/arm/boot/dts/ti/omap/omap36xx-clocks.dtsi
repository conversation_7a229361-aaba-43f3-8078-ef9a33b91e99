// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for OMAP36xx clock data
 *
 * Copyright (C) 2013 Texas Instruments, Inc.
 */
&cm_clocks {
	dpll4_ck: dpll4_ck@d00 {
		#clock-cells = <0>;
		compatible = "ti,omap3-dpll-per-j-type-clock";
		clocks = <&sys_ck>, <&sys_ck>;
		reg = <0x0d00>, <0x0d20>, <0x0d44>, <0x0d30>;
	};

	dpll4_m5x2_ck: dpll4_m5x2_ck@d00 {
		#clock-cells = <0>;
		compatible = "ti,hsdiv-gate-clock";
		clocks = <&dpll4_m5x2_mul_ck>;
		ti,bit-shift = <0x1e>;
		reg = <0x0d00>;
		ti,set-rate-parent;
		ti,set-bit-to-disable;
	};

	dpll4_m2x2_ck: dpll4_m2x2_ck@d00 {
		#clock-cells = <0>;
		compatible = "ti,hsdiv-gate-clock";
		clocks = <&dpll4_m2x2_mul_ck>;
		ti,bit-shift = <0x1b>;
		reg = <0x0d00>;
		ti,set-bit-to-disable;
	};

	dpll3_m3x2_ck: dpll3_m3x2_ck@d00 {
		#clock-cells = <0>;
		compatible = "ti,hsdiv-gate-clock";
		clocks = <&dpll3_m3x2_mul_ck>;
		ti,bit-shift = <0xc>;
		reg = <0x0d00>;
		ti,set-bit-to-disable;
	};

	dpll4_m3x2_ck: dpll4_m3x2_ck@d00 {
		#clock-cells = <0>;
		compatible = "ti,hsdiv-gate-clock";
		clocks = <&dpll4_m3x2_mul_ck>;
		ti,bit-shift = <0x1c>;
		reg = <0x0d00>;
		ti,set-bit-to-disable;
	};

	dpll4_m6x2_ck: dpll4_m6x2_ck@d00 {
		#clock-cells = <0>;
		compatible = "ti,hsdiv-gate-clock";
		clocks = <&dpll4_m6x2_mul_ck>;
		ti,bit-shift = <0x1f>;
		reg = <0x0d00>;
		ti,set-bit-to-disable;
	};

	clock@1000 {
		compatible = "ti,clksel";
		reg = <0x1000>;
		#clock-cells = <2>;
		#address-cells = <0>;

		uart4_fck: clock-uart4-fck {
			#clock-cells = <0>;
			compatible = "ti,wait-gate-clock";
			clock-output-names = "uart4_fck";
			clocks = <&per_48m_fck>;
			ti,bit-shift = <18>;
		};
	};
};

&dpll4_m2x2_mul_ck {
	clock-mult = <1>;
};

&dpll4_m3x2_mul_ck {
	clock-mult = <1>;
};

&dpll4_m4x2_mul_ck {
	ti,clock-mult = <1>;
};

&dpll4_m5x2_mul_ck {
	ti,clock-mult = <1>;
};

&dpll4_m6x2_mul_ck {
	clock-mult = <1>;
};

&cm_clockdomains {
	dpll4_clkdm: dpll4_clkdm {
		compatible = "ti,clockdomain";
		clocks = <&dpll4_ck>;
	};

	per_clkdm: per_clkdm {
		compatible = "ti,clockdomain";
		clocks = <&uart3_fck>, <&gpio6_dbck>, <&gpio5_dbck>,
			 <&gpio4_dbck>, <&gpio3_dbck>, <&gpio2_dbck>,
			 <&wdt3_fck>, <&gpio6_ick>, <&gpio5_ick>, <&gpio4_ick>,
			 <&gpio3_ick>, <&gpio2_ick>, <&wdt3_ick>, <&uart3_ick>,
			 <&uart4_ick>, <&gpt9_ick>, <&gpt8_ick>, <&gpt7_ick>,
			 <&gpt6_ick>, <&gpt5_ick>, <&gpt4_ick>, <&gpt3_ick>,
			 <&gpt2_ick>, <&mcbsp2_ick>, <&mcbsp3_ick>,
			 <&mcbsp4_ick>, <&uart4_fck>;
	};
};

&dpll4_m4_ck {
	ti,max-div = <31>;
};
