// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2015
 *   <PERSON><PERSON> <<EMAIL>>
 *
 * Common device tree include for OpenPandora devices.
 */

#include <dt-bindings/input/input.h>

/ {
	cpus {
		cpu@0 {
			cpu0-supply = <&vcc>;
		};
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x20000000>; /* 512 MB */
	};

	aliases {
		display0 = &lcd;
	};

	/* fixed 26MHz oscillator */
	hfclk_26m: oscillator {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <26000000>;
	};

	tv: connector {
		compatible = "connector-analog-tv";
		label = "tv";

		port {
			tv_connector_in: endpoint {
				remote-endpoint = <&venc_out>;
			};
		};
	};

	gpio-leds {

		compatible = "gpio-leds";

		pinctrl-names = "default";
		pinctrl-0 = <&led_pins>;

		led1 {
			label = "pandora::sd1";
			gpios = <&gpio5 0 GPIO_ACTIVE_HIGH>;	/* GPIO_128 */
			linux,default-trigger = "mmc0";
			default-state = "off";
		};

		led2 {
			label = "pandora::sd2";
			gpios = <&gpio5 1 GPIO_ACTIVE_HIGH>;	/* GPIO_129 */
			linux,default-trigger = "mmc1";
			default-state = "off";
		};

		led3 {
			label = "pandora::bluetooth";
			gpios = <&gpio5 30 GPIO_ACTIVE_HIGH>;	/* GPIO_158 */
			linux,default-trigger = "heartbeat";
			default-state = "off";
		};

		led4 {
			label = "pandora::wifi";
			gpios = <&gpio5 31 GPIO_ACTIVE_HIGH>;	/* GPIO_159 */
			linux,default-trigger = "mmc2";
			default-state = "off";
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		pinctrl-names = "default";
		pinctrl-0 = <&button_pins>;

		up-button {
			label = "up";
			linux,code = <KEY_UP>;
			gpios = <&gpio4 14 GPIO_ACTIVE_LOW>;	/* GPIO_110 */
			wakeup-source;
		};

		down-button {
			label = "down";
			linux,code = <KEY_DOWN>;
			gpios = <&gpio4 7 GPIO_ACTIVE_LOW>;	/* GPIO_103 */
			wakeup-source;
		};

		left-button {
			label = "left";
			linux,code = <KEY_LEFT>;
			gpios = <&gpio4 0 GPIO_ACTIVE_LOW>;	/* GPIO_96 */
			wakeup-source;
		};

		right-button {
			label = "right";
			linux,code = <KEY_RIGHT>;
			gpios = <&gpio4 2 GPIO_ACTIVE_LOW>;	/* GPIO_98 */
			wakeup-source;
		};

		pageup-button {
			label = "game 1";
			linux,code = <KEY_PAGEUP>;
			gpios = <&gpio4 13 GPIO_ACTIVE_LOW>;	/* GPIO_109 */
			wakeup-source;
		};

		pagedown-button {
			label = "game 3";
			linux,code = <KEY_PAGEDOWN>;
			gpios = <&gpio4 10 GPIO_ACTIVE_LOW>;	/* GPIO_106 */
			wakeup-source;
		};

		home-button {
			label = "game 4";
			linux,code = <KEY_HOME>;
			gpios = <&gpio4 5 GPIO_ACTIVE_LOW>;	/* GPIO_101 */
			wakeup-source;
		};

		end-button {
			label = "game 2";
			linux,code = <KEY_END>;
			gpios = <&gpio4 15 GPIO_ACTIVE_LOW>;	/* GPIO_111 */
			wakeup-source;
		};

		right-shift {
			label = "l";
			linux,code = <KEY_RIGHTSHIFT>;
			gpios = <&gpio4 6 GPIO_ACTIVE_LOW>;	/* GPIO_102 */
			wakeup-source;
		};

		kp-plus {
			label = "l2";
			linux,code = <KEY_KPPLUS>;
			gpios = <&gpio4 1 GPIO_ACTIVE_LOW>;	/* GPIO_97 */
			wakeup-source;
		};

		right-ctrl {
			label = "r";
			linux,code = <KEY_RIGHTCTRL>;
			gpios = <&gpio4 9 GPIO_ACTIVE_LOW>;	/* GPIO_105 */
			wakeup-source;
		};

		kp-minus {
			label = "r2";
			linux,code = <KEY_KPMINUS>;
			gpios = <&gpio4 11 GPIO_ACTIVE_LOW>;	/* GPIO_107 */
			wakeup-source;
		};

		left-ctrl {
			label = "ctrl";
			linux,code = <KEY_LEFTCTRL>;
			gpios = <&gpio4 8 GPIO_ACTIVE_LOW>;	/* GPIO_104 */
			wakeup-source;
		};

		menu {
			label = "menu";
			linux,code = <KEY_MENU>;
			gpios = <&gpio4 3 GPIO_ACTIVE_LOW>;	/* GPIO_99 */
			wakeup-source;
		};

		hold {
			label = "hold";
			linux,code = <KEY_COFFEE>;
			gpios = <&gpio6 16 GPIO_ACTIVE_LOW>;	/* GPIO_176 */
			wakeup-source;
		};

		left-alt {
			label = "alt";
			linux,code = <KEY_LEFTALT>;
			gpios = <&gpio4 4 GPIO_ACTIVE_HIGH>;	/* GPIO_100 */
			wakeup-source;
		};

		lid {
			label = "lid";
			linux,code = <0x00>;    /* SW_LID lid shut */
			linux,input-type = <0x05>;    /* EV_SW */
			gpios = <&gpio4 12 GPIO_ACTIVE_HIGH>;   /* GPIO_108 */
		};
	};

	/* HS USB Host PHY on PORT 2 */
	hsusb2_phy: hsusb2-phy-pins {
		compatible = "usb-nop-xceiv";
		reset-gpios = <&gpio1 16 GPIO_ACTIVE_LOW>; /* GPIO_16 */
		vcc-supply = <&vaux2>;
		#phy-cells = <0>;
	};

	/* HS USB Host VBUS supply
	 * disabling this regulator causes current leakage, and LCD flicker
	 * on earlier (CC) board revisions, so keep it always on */
	usb_host_5v: fixed-regulator-usb_host_5v {
		compatible = "regulator-fixed";
		regulator-name = "usb_host_5v";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
		enable-active-high;
		gpio = <&gpio6 4 GPIO_ACTIVE_HIGH>;	/* GPIO_164 */
	};

	/* wl1251 wifi+bt module */
	wlan_en: fixed-regulator-wg7210_en {
		compatible = "regulator-fixed";
		regulator-name = "vwlan";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		startup-delay-us = <50000>;
		enable-active-high;
		gpio = <&gpio1 23 GPIO_ACTIVE_HIGH>;
	};

	/* wg7210 (wifi+bt module) 32k clock buffer */
	wg7210_32k: fixed-regulator-wg7210_32k {
		compatible = "regulator-fixed";
		regulator-name = "wg7210_32k";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
		enable-active-high;
		gpio = <&twl_gpio 13 GPIO_ACTIVE_HIGH>;
	};
};

&omap3_pmx_core {

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2144, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_clk.sdmmc1_clk */
			OMAP3_CORE1_IOPAD(0x2146, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_cmd.sdmmc1_cmd */
			OMAP3_CORE1_IOPAD(0x2148, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_dat0.sdmmc1_dat0 */
			OMAP3_CORE1_IOPAD(0x214a, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_dat1.sdmmc1_dat1 */
			OMAP3_CORE1_IOPAD(0x214c, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_dat2.sdmmc1_dat2 */
			OMAP3_CORE1_IOPAD(0x214e, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc1_dat3.sdmmc1_dat3 */
		>;
	};

	mmc2_pins: mmc2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2158, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_clk.sdmmc2_clk */
			OMAP3_CORE1_IOPAD(0x215a, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_cmd.sdmmc2_cmd */
			OMAP3_CORE1_IOPAD(0x215c, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_dat0.sdmmc2_dat0 */
			OMAP3_CORE1_IOPAD(0x215e, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_dat1.sdmmc2_dat1 */
			OMAP3_CORE1_IOPAD(0x2160, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_dat2.sdmmc2_dat2 */
			OMAP3_CORE1_IOPAD(0x2162, PIN_INPUT_PULLUP | MUX_MODE0)		/* sdmmc2_dat3.sdmmc2_dat3 */
			OMAP3_CORE1_IOPAD(0x2164, PIN_OUTPUT_PULLUP | MUX_MODE1)	/* sdmmc2_dat4.sdmmc2_dirdat0 */
			OMAP3_CORE1_IOPAD(0x2166, PIN_OUTPUT_PULLUP | MUX_MODE1)	/* sdmmc2_dat5.sdmmc2_dirdat1 */
			OMAP3_CORE1_IOPAD(0x2168, PIN_OUTPUT_PULLUP | MUX_MODE1)	/* sdmmc2_dat6.sdmmc2_dircmd */
			OMAP3_CORE1_IOPAD(0x216a, PIN_INPUT_PULLUP | MUX_MODE1)		/* sdmmc2_dat7.sdmmc2_clkin */
		>;
	};

	dss_dpi_pins: dss-dpi-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20d4, PIN_OUTPUT | MUX_MODE0)	/* dss_pclk.dss_pclk */
			OMAP3_CORE1_IOPAD(0x20d6, PIN_OUTPUT | MUX_MODE0)	/* dss_hsync.dss_hsync */
			OMAP3_CORE1_IOPAD(0x20d8, PIN_OUTPUT | MUX_MODE0)	/* dss_vsync.dss_vsync */
			OMAP3_CORE1_IOPAD(0x20da, PIN_OUTPUT | MUX_MODE0)	/* dss_acbias.dss_acbias */
			OMAP3_CORE1_IOPAD(0x20dc, PIN_OUTPUT | MUX_MODE0)	/* dss_data0.dss_data0 */
			OMAP3_CORE1_IOPAD(0x20de, PIN_OUTPUT | MUX_MODE0)	/* dss_data1.dss_data1 */
			OMAP3_CORE1_IOPAD(0x20e0, PIN_OUTPUT | MUX_MODE0)	/* dss_data2.dss_data2 */
			OMAP3_CORE1_IOPAD(0x20e2, PIN_OUTPUT | MUX_MODE0)	/* dss_data3.dss_data3 */
			OMAP3_CORE1_IOPAD(0x20e4, PIN_OUTPUT | MUX_MODE0)	/* dss_data4.dss_data4 */
			OMAP3_CORE1_IOPAD(0x20e6, PIN_OUTPUT | MUX_MODE0)	/* dss_data5.dss_data5 */
			OMAP3_CORE1_IOPAD(0x20e8, PIN_OUTPUT | MUX_MODE0)	/* dss_data6.dss_data6 */
			OMAP3_CORE1_IOPAD(0x20ea, PIN_OUTPUT | MUX_MODE0)	/* dss_data7.dss_data7 */
			OMAP3_CORE1_IOPAD(0x20ec, PIN_OUTPUT | MUX_MODE0)	/* dss_data8.dss_data8 */
			OMAP3_CORE1_IOPAD(0x20ee, PIN_OUTPUT | MUX_MODE0)	/* dss_data9.dss_data9 */
			OMAP3_CORE1_IOPAD(0x20f0, PIN_OUTPUT | MUX_MODE0)	/* dss_data10.dss_data10 */
			OMAP3_CORE1_IOPAD(0x20f2, PIN_OUTPUT | MUX_MODE0)	/* dss_data11.dss_data11 */
			OMAP3_CORE1_IOPAD(0x20f4, PIN_OUTPUT | MUX_MODE0)	/* dss_data12.dss_data12 */
			OMAP3_CORE1_IOPAD(0x20f6, PIN_OUTPUT | MUX_MODE0)	/* dss_data13.dss_data13 */
			OMAP3_CORE1_IOPAD(0x20f8, PIN_OUTPUT | MUX_MODE0)	/* dss_data14.dss_data14 */
			OMAP3_CORE1_IOPAD(0x20fa, PIN_OUTPUT | MUX_MODE0)	/* dss_data15.dss_data15 */
			OMAP3_CORE1_IOPAD(0x20fc, PIN_OUTPUT | MUX_MODE0)	/* dss_data16.dss_data16 */
			OMAP3_CORE1_IOPAD(0x20fe, PIN_OUTPUT | MUX_MODE0)	/* dss_data17.dss_data17 */
			OMAP3_CORE1_IOPAD(0x2100, PIN_OUTPUT | MUX_MODE0)	/* dss_data18.dss_data18 */
			OMAP3_CORE1_IOPAD(0x2102, PIN_OUTPUT | MUX_MODE0)	/* dss_data19.dss_data19 */
			OMAP3_CORE1_IOPAD(0x2104, PIN_OUTPUT | MUX_MODE0)	/* dss_data20.dss_data20 */
			OMAP3_CORE1_IOPAD(0x2106, PIN_OUTPUT | MUX_MODE0)	/* dss_data21.dss_data21 */
			OMAP3_CORE1_IOPAD(0x2108, PIN_OUTPUT | MUX_MODE0)	/* dss_data22.dss_data22 */
			OMAP3_CORE1_IOPAD(0x210a, PIN_OUTPUT | MUX_MODE0)	/* dss_data23.dss_data23 */
			OMAP3_CORE1_IOPAD(0x218e, PIN_OUTPUT | MUX_MODE4)	/* GPIO_157 = lcd reset */
		>;
	};

	uart3_pins: uart3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x219e, PIN_INPUT | PIN_OFF_WAKEUPENABLE | MUX_MODE0)	/* uart3_rx_irrx.uart3_rx_irrx */
			OMAP3_CORE1_IOPAD(0x21a0, PIN_OUTPUT | MUX_MODE0) /* uart3_tx_irtx.uart3_tx_irtx */
		>;
	};

	led_pins: leds-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2154, PIN_OUTPUT | MUX_MODE4)	/* GPIO_128 */
			OMAP3_CORE1_IOPAD(0x2156, PIN_OUTPUT | MUX_MODE4)	/* GPIO_129 */
			OMAP3_CORE1_IOPAD(0x2190, PIN_OUTPUT | MUX_MODE4)	/* GPIO_158 */
			OMAP3_CORE1_IOPAD(0x2192, PIN_OUTPUT | MUX_MODE4)	/* GPIO_159 */
		>;
	};

	button_pins: button-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2110, PIN_INPUT | MUX_MODE4)	/* GPIO_96 */
			OMAP3_CORE1_IOPAD(0x2112, PIN_INPUT | MUX_MODE4)	/* GPIO_97 */
			OMAP3_CORE1_IOPAD(0x2114, PIN_INPUT | MUX_MODE4)	/* GPIO_98 */
			OMAP3_CORE1_IOPAD(0x2116, PIN_INPUT | MUX_MODE4)	/* GPIO_99 */
			OMAP3_CORE1_IOPAD(0x2118, PIN_INPUT | MUX_MODE4)	/* GPIO_100 */
			OMAP3_CORE1_IOPAD(0x211a, PIN_INPUT | MUX_MODE4)	/* GPIO_101 */
			OMAP3_CORE1_IOPAD(0x211c, PIN_INPUT | MUX_MODE4)	/* GPIO_102 */
			OMAP3_CORE1_IOPAD(0x211e, PIN_INPUT | MUX_MODE4)	/* GPIO_103 */
			OMAP3_CORE1_IOPAD(0x2120, PIN_INPUT | MUX_MODE4)	/* GPIO_104 */
			OMAP3_CORE1_IOPAD(0x2122, PIN_INPUT | MUX_MODE4)	/* GPIO_105 */
			OMAP3_CORE1_IOPAD(0x2124, PIN_INPUT | MUX_MODE4)	/* GPIO_106 */
			OMAP3_CORE1_IOPAD(0x2126, PIN_INPUT | MUX_MODE4)	/* GPIO_107 */
			OMAP3_CORE1_IOPAD(0x2128, PIN_INPUT | MUX_MODE4)	/* GPIO_108 */
			OMAP3_CORE1_IOPAD(0x212a, PIN_INPUT | MUX_MODE4)	/* GPIO_109 */
			OMAP3_CORE1_IOPAD(0x212c, PIN_INPUT | MUX_MODE4)	/* GPIO_110 */
			OMAP3_CORE1_IOPAD(0x212e, PIN_INPUT | MUX_MODE4)	/* GPIO_111 */
			OMAP3_CORE1_IOPAD(0x21d2, PIN_INPUT | MUX_MODE4)	/* GPIO_176 */
		>;
	};

	penirq_pins: penirq-pins {
		pinctrl-single,pins = <
			/* here we could enable to wakeup the cpu from suspend by a pen touch */
			OMAP3_CORE1_IOPAD(0x210c, PIN_INPUT | MUX_MODE4)	/* GPIO_94 */
		>;
	};

};

&omap3_pmx_core2 {
	/* define in CPU specific file that includes this one
	 * use either OMAP3430_CORE2_IOPAD() or OMAP3630_CORE2_IOPAD()
	 */
};

&i2c1 {
	clock-frequency = <2600000>;

	twl: twl@48 {
		reg = <0x48>;
		interrupts = <7>; /* SYS_NIRQ cascaded to intc */
		interrupt-parent = <&intc>;

		clocks = <&hfclk_26m>;
		clock-names = "fck";

		twl_power: power {
			compatible = "ti,twl4030-power-reset";
			ti,use_poweroff;
		};

		twl_audio: audio {
			compatible = "ti,twl4030-audio";

			codec {
				ti,ramp_delay_value = <3>;
			};
		};
	};
};

#include "twl4030.dtsi"
#include "twl4030_omap3.dtsi"

&twl_keypad {
	keypad,num-rows = <8>;
	keypad,num-columns = <6>;
	linux,keymap = <
		MATRIX_KEY(0, 0, KEY_9)
		MATRIX_KEY(0, 1, KEY_8)
		MATRIX_KEY(0, 2, KEY_I)
		MATRIX_KEY(0, 3, KEY_J)
		MATRIX_KEY(0, 4, KEY_N)
		MATRIX_KEY(0, 5, KEY_M)
		MATRIX_KEY(1, 0, KEY_0)
		MATRIX_KEY(1, 1, KEY_7)
		MATRIX_KEY(1, 2, KEY_U)
		MATRIX_KEY(1, 3, KEY_H)
		MATRIX_KEY(1, 4, KEY_B)
		MATRIX_KEY(1, 5, KEY_SPACE)
		MATRIX_KEY(2, 0, KEY_BACKSPACE)
		MATRIX_KEY(2, 1, KEY_6)
		MATRIX_KEY(2, 2, KEY_Y)
		MATRIX_KEY(2, 3, KEY_G)
		MATRIX_KEY(2, 4, KEY_V)
		MATRIX_KEY(2, 5, KEY_FN)
		MATRIX_KEY(3, 0, KEY_O)
		MATRIX_KEY(3, 1, KEY_5)
		MATRIX_KEY(3, 2, KEY_T)
		MATRIX_KEY(3, 3, KEY_F)
		MATRIX_KEY(3, 4, KEY_C)
		MATRIX_KEY(4, 0, KEY_P)
		MATRIX_KEY(4, 1, KEY_4)
		MATRIX_KEY(4, 2, KEY_R)
		MATRIX_KEY(4, 3, KEY_D)
		MATRIX_KEY(4, 4, KEY_X)
		MATRIX_KEY(5, 0, KEY_K)
		MATRIX_KEY(5, 1, KEY_3)
		MATRIX_KEY(5, 2, KEY_E)
		MATRIX_KEY(5, 3, KEY_S)
		MATRIX_KEY(5, 4, KEY_Z)
		MATRIX_KEY(6, 0, KEY_L)
		MATRIX_KEY(6, 1, KEY_2)
		MATRIX_KEY(6, 2, KEY_W)
		MATRIX_KEY(6, 3, KEY_A)
		MATRIX_KEY(6, 4, KEY_RIGHTBRACE)
		MATRIX_KEY(7, 0, KEY_ENTER)
		MATRIX_KEY(7, 1, KEY_1)
		MATRIX_KEY(7, 2, KEY_Q)
		MATRIX_KEY(7, 3, KEY_LEFTSHIFT)
		MATRIX_KEY(7, 4, KEY_LEFTBRACE )
	 >;
};

/* backup battery charger */
&charger {
	ti,bb-uvolt = <3200000>;
	ti,bb-uamp = <150>;
};

/* MMC2 */
&vmmc2 {
	regulator-min-microvolt = <1850000>;
	regulator-max-microvolt = <3150000>;
};

/* LCD */
&vaux1 {
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
};

/* USB Host PHY */
&vaux2 {
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
};

/* available on expansion connector */
&vaux3 {
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
};

/* ADS7846 and nubs */
&vaux4 {
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
};

/* power audio DAC and LID sensor */
&vsim {
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
	regulator-always-on;
};

&i2c2 {
	clock-frequency = <100000>;
	/* no clients so we should disable clock */
};

&i2c3 {
	clock-frequency = <100000>;

	bq27500@55 {
		compatible = "ti,bq27500";
		reg = <0x55>;
	};

};

&usb_otg_hs {
	interface-type = <0>;
	usb-phy = <&usb2_phy>;
	phys = <&usb2_phy>;
	phy-names = "usb2-phy";
	mode = <3>;
	power = <50>;
};

/*
 * Many pandora boards have been produced with defective write-protect switches
 * on either slot, so it was decided not to use this feature. If you know
 * your board has good switches, feel free to uncomment wp-gpios below.
 */
&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
	vmmc-supply = <&vmmc1>;
	bus-width = <4>;
	cd-gpios = <&twl_gpio 0 GPIO_ACTIVE_LOW>;
	/*wp-gpios = <&gpio4 30 GPIO_ACTIVE_HIGH>;*/	/* GPIO_126 */
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_pins>;
	vmmc-supply = <&vmmc2>;
	bus-width = <4>;
	cd-gpios = <&twl_gpio 1 GPIO_ACTIVE_LOW>;
	/*wp-gpios = <&gpio4 31 GPIO_ACTIVE_HIGH>;*/	/* GPIO_127 */
};

&mmc3 {
	vmmc-supply = <&wlan_en>;

	bus-width = <4>;
	non-removable;
	ti,non-removable;
	cap-power-off-card;

	pinctrl-names = "default";
	pinctrl-0 = <&mmc3_pins>;

	#address-cells = <1>;
	#size-cells = <0>;

	wlan: wifi@1 {
		compatible = "ti,wl1251";

		reg = <1>;

		interrupt-parent = <&gpio1>;
		interrupts = <21 IRQ_TYPE_LEVEL_HIGH>;	/* GPIO_21 */

		ti,wl1251-has-eeprom;
	};
};

/* bluetooth*/
&uart1 {
};

/* spare (expansion connector) */
&uart2 {
};

/* console (expansion connector) */
&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>;
	interrupts-extended = <&intc 74 &omap3_pmx_core OMAP3_UART3_RX>;
};

&usbhshost {
	port2-mode = "ehci-phy";
};

&usbhsehci {
	phys = <0 &hsusb2_phy>;
};

&gpmc {
	ranges = <0 0 0x30000000 0x1000000>; /* CS0: 16MB for NAND */

	nand@0,0 {
		compatible = "ti,omap2-nand";
		reg = <0 0 4>; /* CS0, offset 0, IO size 4 */
		interrupt-parent = <&gpmc>;
		interrupts = <0 IRQ_TYPE_NONE>, /* fifoevent */
			     <1 IRQ_TYPE_NONE>;	/* termcount */
		nand-bus-width = <16>;
		ti,nand-ecc-opt = "sw";

		gpmc,sync-clk-ps = <0>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <44>;
		gpmc,cs-wr-off-ns = <44>;
		gpmc,adv-on-ns = <6>;
		gpmc,adv-rd-off-ns = <34>;
		gpmc,adv-wr-off-ns = <44>;
		gpmc,we-off-ns = <40>;
		gpmc,oe-off-ns = <54>;
		gpmc,access-ns = <64>;
		gpmc,rd-cycle-ns = <82>;
		gpmc,wr-cycle-ns = <82>;
		gpmc,wr-access-ns = <40>;
		gpmc,wr-data-mux-bus-ns = <0>;
		gpmc,device-width = <2>;

		#address-cells = <1>;
		#size-cells = <1>;

		/* u-boot uses mtdparts=nand:512k(xloader),1920k(uboot),128k(uboot-env),10m(boot),-(rootfs) */

		x-loader@0 {
			label = "xloader";
			reg = <0 0x80000>;
		};

		bootloaders@80000 {
			label = "uboot";
			reg = <0x80000 0x1e0000>;
		};

		bootloaders_env@260000 {
			label = "uboot-env";
			reg = <0x260000 0x20000>;
		};

		kernel@280000 {
			label = "boot";
			reg = <0x280000 0xa00000>;
		};

		filesystem@c80000 {
			label = "rootfs";
			reg = <0xc80000 0>;	/* 0 = MTDPART_SIZ_FULL */
		};
	};
};

&mcspi1 {
	tsc2046@0 {
		reg = <0>;	/* CS0 */
		compatible = "ti,tsc2046";
		spi-max-frequency = <1000000>;
		pinctrl-names = "default";
		pinctrl-0 = <&penirq_pins>;
		interrupt-parent = <&gpio3>;
		interrupts = <30 IRQ_TYPE_NONE>;	/* GPIO_94 */
		pendown-gpio = <&gpio3 30 GPIO_ACTIVE_LOW>;
		vcc-supply = <&vaux4>;

		ti,x-min = /bits/ 16 <0>;
		ti,x-max = /bits/ 16 <8000>;
		ti,y-min = /bits/ 16 <0>;
		ti,y-max = /bits/ 16 <4800>;
		ti,x-plate-ohms = /bits/ 16 <40>;
		ti,pressure-max = /bits/ 16 <255>;

		wakeup-source;
	};

	lcd: lcd@1 {
		reg = <1>;	/* CS1 */
		compatible = "tpo,td043mtea1";
		spi-max-frequency = <100000>;
		spi-cpol;
		spi-cpha;

		label = "lcd";
		reset-gpios = <&gpio5 29 GPIO_ACTIVE_LOW>;	/* GPIO_157 */
		vcc-supply = <&vaux1>;

		port {
			lcd_in: endpoint {
				remote-endpoint = <&dpi_out>;
			};
		};
	};


};

/* n/a - used as GPIOs */
&mcbsp1 {
};

/* audio DAC */
&mcbsp2 {
};

/* bluetooth */
&mcbsp3 {
};

/* to twl4030*/
&mcbsp4 {
};

&venc {
	status = "okay";

	vdda-supply = <&vdac>;

	port {
		venc_out: endpoint {
			remote-endpoint = <&tv_connector_in>;
			ti,channels = <2>;
		};
	};
};

&dss {
	pinctrl-names = "default";
	pinctrl-0 = < &dss_dpi_pins >;

	status = "okay";
	vdds_dsi-supply = <&vpll2>;

	port {
		dpi_out: endpoint {
			remote-endpoint = <&lcd_in>;
			data-lines = <24>;
		};
	};
};
