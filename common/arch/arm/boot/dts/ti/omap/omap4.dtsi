// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2011 Texas Instruments Incorporated - https://www.ti.com/
 */

#include <dt-bindings/bus/ti-sysc.h>
#include <dt-bindings/clock/omap4.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/pinctrl/omap.h>
#include <dt-bindings/clock/omap4.h>

/ {
	compatible = "ti,omap4430", "ti,omap4";
	interrupt-parent = <&wakeupgen>;
	#address-cells = <1>;
	#size-cells = <1>;
	chosen { };

	aliases {
		i2c0 = &i2c1;
		i2c1 = &i2c2;
		i2c2 = &i2c3;
		i2c3 = &i2c4;
		mmc0 = &mmc1;
		mmc1 = &mmc2;
		mmc2 = &mmc3;
		mmc3 = &mmc4;
		mmc4 = &mmc5;
		serial0 = &uart1;
		serial1 = &uart2;
		serial2 = &uart3;
		serial3 = &uart4;
		rproc0 = &dsp;
		rproc1 = &ipu;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			compatible = "arm,cortex-a9";
			device_type = "cpu";
			next-level-cache = <&L2>;
			reg = <0x0>;

			clocks = <&dpll_mpu_ck>;
			clock-names = "cpu";

			clock-latency = <300000>; /* From omap-cpufreq driver */
		};
		cpu@1 {
			compatible = "arm,cortex-a9";
			device_type = "cpu";
			next-level-cache = <&L2>;
			reg = <0x1>;
		};
	};

	/*
	 * Needed early by omap4_sram_init() for barrier, do not move to l3
	 * interconnect as simple-pm-bus probes at module_init() time.
	 */
	ocmcram: sram@40304000 {
		compatible = "mmio-sram";
		reg = <0x40304000 0xa000>; /* 40k */
	};

	gic: interrupt-controller@48241000 {
		compatible = "arm,cortex-a9-gic";
		interrupt-controller;
		#interrupt-cells = <3>;
		reg = <0x48241000 0x1000>,
		      <0x48240100 0x0100>;
		interrupt-parent = <&gic>;
	};

	L2: cache-controller@48242000 {
		compatible = "arm,pl310-cache";
		reg = <0x48242000 0x1000>;
		cache-unified;
		cache-level = <2>;
	};

	local-timer@48240600 {
		compatible = "arm,cortex-a9-twd-timer";
		clocks = <&mpu_periphclk>;
		reg = <0x48240600 0x20>;
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_RAW(3) | IRQ_TYPE_EDGE_RISING)>;
		interrupt-parent = <&gic>;
	};

	wakeupgen: interrupt-controller@48281000 {
		compatible = "ti,omap4-wugen-mpu";
		interrupt-controller;
		#interrupt-cells = <3>;
		reg = <0x48281000 0x1000>;
		interrupt-parent = <&gic>;
	};

	/*
	 * XXX: Use a flat representation of the OMAP4 interconnect.
	 * The real OMAP interconnect network is quite complex.
	 * Since it will not bring real advantage to represent that in DT for
	 * the moment, just use a fake OCP bus entry to represent the whole bus
	 * hierarchy.
	 */
	ocp {
		compatible = "simple-pm-bus";
		power-domains = <&prm_l4per>;
		clocks = <&l3_1_clkctrl OMAP4_L3_MAIN_1_CLKCTRL 0>,
			 <&l3_2_clkctrl OMAP4_L3_MAIN_2_CLKCTRL 0>,
			 <&l3_instr_clkctrl OMAP4_L3_MAIN_3_CLKCTRL 0>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		l3-noc@44000000 {
			compatible = "ti,omap4-l3-noc";
			reg = <0x44000000 0x1000>,
			      <0x44800000 0x2000>,
			      <0x45000000 0x1000>;
			interrupts = <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>;
		};

		l4_wkup: interconnect@4a300000 {
		};

		l4_cfg: interconnect@4a000000 {
		};

		l4_per: interconnect@48000000 {
		};

		target-module@48210000 {
			compatible = "ti,sysc-omap4-simple", "ti,sysc";
			power-domains = <&prm_mpu>;
			clocks = <&mpuss_clkctrl OMAP4_MPU_CLKCTRL 0>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x48210000 0x1f0000>;

			mpu {
				compatible = "ti,omap4-mpu";
				sram = <&ocmcram>;
			};
		};

		l4_abe: interconnect@40100000 {
		};

		target-module@50000000 {
			compatible = "ti,sysc-omap2", "ti,sysc";
			reg = <0x50000000 4>,
			      <0x50000010 4>,
			      <0x50000014 4>;
			reg-names = "rev", "sysc", "syss";
			ti,sysc-sidle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>;
			ti,syss-mask = <1>;
			ti,no-idle-on-init;
			clocks = <&l3_2_clkctrl OMAP4_GPMC_CLKCTRL 0>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x50000000 0x50000000 0x00001000>, /* regs */
				 <0x00000000 0x00000000 0x40000000>; /* data */

			gpmc: gpmc@50000000 {
				compatible = "ti,omap4430-gpmc";
				reg = <0x50000000 0x1000>;
				#address-cells = <2>;
				#size-cells = <1>;
				interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
				dmas = <&sdma 4>;
				dma-names = "rxtx";
				gpmc,num-cs = <8>;
				gpmc,num-waitpins = <4>;
				clocks = <&l3_div_ck>;
				clock-names = "fck";
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-controller;
				#gpio-cells = <2>;
			};
		};

		target-module@52000000 {
			compatible = "ti,sysc-omap4", "ti,sysc";
			reg = <0x52000000 0x4>,
			      <0x52000010 0x4>;
			reg-names = "rev", "sysc";
			ti,sysc-mask = <SYSC_OMAP4_SOFTRESET>;
			ti,sysc-midle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>,
					<SYSC_IDLE_SMART_WKUP>;
			ti,sysc-sidle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>,
					<SYSC_IDLE_SMART_WKUP>;
			ti,sysc-delay-us = <2>;
			power-domains = <&prm_cam>;
			clocks = <&iss_clkctrl OMAP4_ISS_CLKCTRL 0>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x52000000 0x1000000>;

			/* No child device binding, driver in staging */
		};

		/*
		 * Note that 4430 needs cross trigger interface (CTI) supported
		 * before we can configure the interrupts. This means sampling
		 * events are not supported for pmu. Note that 4460 does not use
		 * CTI, see also 4460.dtsi.
		 */
		target-module@54000000 {
			compatible = "ti,sysc-omap4-simple", "ti,sysc";
			power-domains = <&prm_emu>;
			clocks = <&emu_sys_clkctrl OMAP4_DEBUGSS_CLKCTRL 0>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x54000000 0x1000000>;

			pmu: pmu {
				compatible = "arm,cortex-a9-pmu";
			};
		};

		target-module@55082000 {
			compatible = "ti,sysc-omap2", "ti,sysc";
			reg = <0x55082000 0x4>,
			      <0x55082010 0x4>,
			      <0x55082014 0x4>;
			reg-names = "rev", "sysc", "syss";
			ti,sysc-sidle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>;
			ti,sysc-mask = <(SYSC_OMAP2_CLOCKACTIVITY |
					 SYSC_OMAP2_SOFTRESET |
					 SYSC_OMAP2_AUTOIDLE)>;
			clocks = <&ducati_clkctrl OMAP4_IPU_CLKCTRL 0>;
			clock-names = "fck";
			resets = <&prm_core 2>;
			reset-names = "rstctrl";
			ranges = <0x0 0x55082000 0x100>;
			#size-cells = <1>;
			#address-cells = <1>;

			mmu_ipu: mmu@0 {
				compatible = "ti,omap4-iommu";
				reg = <0x0 0x100>;
				interrupts = <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>;
				#iommu-cells = <0>;
				ti,iommu-bus-err-back;
			};
		};

		target-module@4012c000 {
			compatible = "ti,sysc-omap4", "ti,sysc";
			reg = <0x4012c000 0x4>,
			      <0x4012c010 0x4>;
			reg-names = "rev", "sysc";
			ti,sysc-mask = <SYSC_OMAP4_SOFTRESET>;
			ti,sysc-sidle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>,
					<SYSC_IDLE_SMART_WKUP>;
			clocks = <&abe_clkctrl OMAP4_SLIMBUS1_CLKCTRL 0>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x00000000 0x4012c000 0x1000>, /* MPU */
				 <0x4902c000 0x4902c000 0x1000>; /* L3 */

			/* No child device binding or driver in mainline */
		};

		target-module@4e000000 {
			compatible = "ti,sysc-omap2", "ti,sysc";
			reg = <0x4e000000 0x4>,
			      <0x4e000010 0x4>;
			reg-names = "rev", "sysc";
			ti,sysc-sidle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>;
			ranges = <0x0 0x4e000000 0x2000000>;
			#size-cells = <1>;
			#address-cells = <1>;

			dmm@0 {
				compatible = "ti,omap4-dmm";
				reg = <0 0x800>;
				interrupts = <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		target-module@4c000000 {
			compatible = "ti,sysc-omap4-simple", "ti,sysc";
			reg = <0x4c000000 0x4>;
			reg-names = "rev";
			clocks = <&l3_emif_clkctrl OMAP4_EMIF1_CLKCTRL 0>;
			clock-names = "fck";
			ti,no-idle;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x4c000000 0x1000000>;

			emif1: emif@0 {
				compatible = "ti,emif-4d";
				reg = <0 0x100>;
				interrupts = <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>;
				phy-type = <1>;
				hw-caps-read-idle-ctrl;
				hw-caps-ll-interface;
				hw-caps-temp-alert;
			};
		};

		target-module@4d000000 {
			compatible = "ti,sysc-omap4-simple", "ti,sysc";
			reg = <0x4d000000 0x4>;
			reg-names = "rev";
			clocks = <&l3_emif_clkctrl OMAP4_EMIF2_CLKCTRL 0>;
			clock-names = "fck";
			ti,no-idle;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x4d000000 0x1000000>;

			emif2: emif@0 {
				compatible = "ti,emif-4d";
				reg = <0 0x100>;
				interrupts = <GIC_SPI 111 IRQ_TYPE_LEVEL_HIGH>;
				phy-type = <1>;
				hw-caps-read-idle-ctrl;
				hw-caps-ll-interface;
				hw-caps-temp-alert;
			};
		};

		dsp: dsp {
			compatible = "ti,omap4-dsp";
			ti,bootreg = <&scm_conf 0x304 0>;
			iommus = <&mmu_dsp>;
			resets = <&prm_tesla 0>;
			clocks = <&tesla_clkctrl OMAP4_DSP_CLKCTRL 0>;
			firmware-name = "omap4-dsp-fw.xe64T";
			mboxes = <&mailbox &mbox_dsp>;
			status = "disabled";
		};

		ipu: ipu@55020000 {
			compatible = "ti,omap4-ipu";
			reg = <0x55020000 0x10000>;
			reg-names = "l2ram";
			iommus = <&mmu_ipu>;
			resets = <&prm_core 0>, <&prm_core 1>;
			clocks = <&ducati_clkctrl OMAP4_IPU_CLKCTRL 0>;
			firmware-name = "omap4-ipu-fw.xem3";
			mboxes = <&mailbox &mbox_ipu>;
			status = "disabled";
		};

		aes1_target: target-module@4b501000 {
			compatible = "ti,sysc-omap2", "ti,sysc";
			reg = <0x4b501080 0x4>,
			      <0x4b501084 0x4>,
			      <0x4b501088 0x4>;
			reg-names = "rev", "sysc", "syss";
			ti,sysc-mask = <(SYSC_OMAP2_SOFTRESET |
					 SYSC_OMAP2_AUTOIDLE)>;
			ti,sysc-sidle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>,
					<SYSC_IDLE_SMART_WKUP>;
			ti,syss-mask = <1>;
			/* Domains (P, C): l4per_pwrdm, l4_secure_clkdm */
			clocks = <&l4_secure_clkctrl OMAP4_AES1_CLKCTRL 0>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x4b501000 0x1000>;

			aes1: aes@0 {
				compatible = "ti,omap4-aes";
				reg = <0 0xa0>;
				interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
				dmas = <&sdma 111>, <&sdma 110>;
				dma-names = "tx", "rx";
			};
		};

		aes2_target: target-module@4b701000 {
			compatible = "ti,sysc-omap2", "ti,sysc";
			reg = <0x4b701080 0x4>,
			      <0x4b701084 0x4>,
			      <0x4b701088 0x4>;
			reg-names = "rev", "sysc", "syss";
			ti,sysc-mask = <(SYSC_OMAP2_SOFTRESET |
					 SYSC_OMAP2_AUTOIDLE)>;
			ti,sysc-sidle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>,
					<SYSC_IDLE_SMART_WKUP>;
			ti,syss-mask = <1>;
			/* Domains (P, C): l4per_pwrdm, l4_secure_clkdm */
			clocks = <&l4_secure_clkctrl OMAP4_AES2_CLKCTRL 0>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x4b701000 0x1000>;

			aes2: aes@0 {
				compatible = "ti,omap4-aes";
				reg = <0 0xa0>;
				interrupts = <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>;
				dmas = <&sdma 114>, <&sdma 113>;
				dma-names = "tx", "rx";
			};
		};

		sham_target: target-module@4b100000 {
			compatible = "ti,sysc-omap3-sham", "ti,sysc";
			reg = <0x4b100100 0x4>,
			      <0x4b100110 0x4>,
			      <0x4b100114 0x4>;
			reg-names = "rev", "sysc", "syss";
			ti,sysc-mask = <(SYSC_OMAP2_SOFTRESET |
					 SYSC_OMAP2_AUTOIDLE)>;
			ti,sysc-sidle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>;
			ti,syss-mask = <1>;
			/* Domains (P, C): l4per_pwrdm, l4_secure_clkdm */
			clocks = <&l4_secure_clkctrl OMAP4_SHA2MD5_CLKCTRL 0>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x4b100000 0x1000>;

			sham: sham@0 {
				compatible = "ti,omap4-sham";
				reg = <0 0x300>;
				interrupts = <GIC_SPI 51 IRQ_TYPE_LEVEL_HIGH>;
				dmas = <&sdma 119>;
				dma-names = "rx";
			};
		};

		abb_mpu: regulator-abb-mpu {
			compatible = "ti,abb-v2";
			regulator-name = "abb_mpu";
			#address-cells = <0>;
			#size-cells = <0>;
			ti,tranxdone-status-mask = <0x80>;
			clocks = <&sys_clkin_ck>;
			ti,settling-time = <50>;
			ti,clock-cycles = <16>;

			status = "disabled";
		};

		abb_iva: regulator-abb-iva {
			compatible = "ti,abb-v2";
			regulator-name = "abb_iva";
			#address-cells = <0>;
			#size-cells = <0>;
			ti,tranxdone-status-mask = <0x80000000>;
			clocks = <&sys_clkin_ck>;
			ti,settling-time = <50>;
			ti,clock-cycles = <16>;

			status = "disabled";
		};

		sgx_module: target-module@56000000 {
			compatible = "ti,sysc-omap4", "ti,sysc";
			reg = <0x5600fe00 0x4>,
			      <0x5600fe10 0x4>;
			reg-names = "rev", "sysc";
			ti,sysc-midle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>,
					<SYSC_IDLE_SMART_WKUP>;
			ti,sysc-sidle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>,
					<SYSC_IDLE_SMART_WKUP>;
			power-domains = <&prm_gfx>;
			clocks = <&l3_gfx_clkctrl OMAP4_GPU_CLKCTRL 0>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x56000000 0x2000000>;

			/*
			 * Closed source PowerVR driver, no child device
			 * binding or driver in mainline
			 */
		};

		/*
		 * DSS is only using l3 mapping without l4 as noted in the TRM
		 * "10.1.3 DSS Register Manual" for omap4460.
		 */
		target-module@58000000 {
			compatible = "ti,sysc-omap2", "ti,sysc";
			reg = <0x58000000 4>,
			      <0x58000014 4>;
			reg-names = "rev", "syss";
			ti,syss-mask = <1>;
			power-domains = <&prm_dss>;
			clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 0>,
				 <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 9>,
				 <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 10>,
				 <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 11>;
			clock-names = "fck", "hdmi_clk", "sys_clk", "tv_clk";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x58000000 0x1000000>;

			dss: dss@0 {
				compatible = "ti,omap4-dss";
				reg = <0 0x80>;
				status = "disabled";
				clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 8>;
				clock-names = "fck";
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 0 0x1000000>;

				target-module@1000 {
					compatible = "ti,sysc-omap2", "ti,sysc";
					reg = <0x1000 0x4>,
					      <0x1010 0x4>,
					      <0x1014 0x4>;
					reg-names = "rev", "sysc", "syss";
					ti,sysc-sidle = <SYSC_IDLE_FORCE>,
							<SYSC_IDLE_NO>,
							<SYSC_IDLE_SMART>;
					ti,sysc-midle = <SYSC_IDLE_FORCE>,
							<SYSC_IDLE_NO>,
							<SYSC_IDLE_SMART>;
					ti,sysc-mask = <(SYSC_OMAP2_CLOCKACTIVITY |
							 SYSC_OMAP2_ENAWAKEUP |
							 SYSC_OMAP2_SOFTRESET |
							 SYSC_OMAP2_AUTOIDLE)>;
					ti,syss-mask = <1>;
					clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 8>,
						 <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 10>;
					clock-names = "fck", "sys_clk";
					#address-cells = <1>;
					#size-cells = <1>;
					ranges = <0 0x1000 0x1000>;

					dispc@0 {
						compatible = "ti,omap4-dispc";
						reg = <0 0x1000>;
						interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
						clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 8>;
						clock-names = "fck";
					};
				};

				target-module@2000 {
					compatible = "ti,sysc-omap2", "ti,sysc";
					reg = <0x2000 0x4>,
					      <0x2010 0x4>,
					      <0x2014 0x4>;
					reg-names = "rev", "sysc", "syss";
					ti,sysc-sidle = <SYSC_IDLE_FORCE>,
							<SYSC_IDLE_NO>,
							<SYSC_IDLE_SMART>;
					ti,sysc-mask = <(SYSC_OMAP2_SOFTRESET |
							 SYSC_OMAP2_AUTOIDLE)>;
					ti,syss-mask = <1>;
					clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 8>,
						 <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 10>;
					clock-names = "fck", "sys_clk";
					#address-cells = <1>;
					#size-cells = <1>;
					ranges = <0 0x2000 0x1000>;

					rfbi: encoder@0  {
						reg = <0 0x1000>;
						status = "disabled";
						clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 8>, <&l3_div_ck>;
						clock-names = "fck", "ick";
					};
				};

				target-module@3000 {
					compatible = "ti,sysc-omap2", "ti,sysc";
					reg = <0x3000 0x4>;
					reg-names = "rev";
					clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 10>;
					clock-names = "sys_clk";
					#address-cells = <1>;
					#size-cells = <1>;
					ranges = <0 0x3000 0x1000>;

					venc: encoder@0 {
						compatible = "ti,omap4-venc";
						reg = <0 0x1000>;
						status = "disabled";
						clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 11>;
						clock-names = "fck";
					};
				};

				target-module@4000 {
					compatible = "ti,sysc-omap2", "ti,sysc";
					reg = <0x4000 0x4>,
					      <0x4010 0x4>,
					      <0x4014 0x4>;
					reg-names = "rev", "sysc", "syss";
					ti,sysc-sidle = <SYSC_IDLE_FORCE>,
							<SYSC_IDLE_NO>,
							<SYSC_IDLE_SMART>;
					ti,sysc-mask = <(SYSC_OMAP2_CLOCKACTIVITY |
							 SYSC_OMAP2_ENAWAKEUP |
							 SYSC_OMAP2_SOFTRESET |
							 SYSC_OMAP2_AUTOIDLE)>;
					ti,syss-mask = <1>;
					#address-cells = <1>;
					#size-cells = <1>;
					ranges = <0 0x4000 0x1000>;

					dsi1: encoder@0 {
						compatible = "ti,omap4-dsi";
						reg = <0 0x200>,
						      <0x200 0x40>,
						      <0x300 0x20>;
						reg-names = "proto", "phy", "pll";
						interrupts = <GIC_SPI 53 IRQ_TYPE_LEVEL_HIGH>;
						status = "disabled";
						clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 8>,
							 <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 10>;
						clock-names = "fck", "sys_clk";

						#address-cells = <1>;
						#size-cells = <0>;
					};
				};

				target-module@5000 {
					compatible = "ti,sysc-omap2", "ti,sysc";
					reg = <0x5000 0x4>,
					      <0x5010 0x4>,
					      <0x5014 0x4>;
					reg-names = "rev", "sysc", "syss";
					ti,sysc-sidle = <SYSC_IDLE_FORCE>,
							<SYSC_IDLE_NO>,
							<SYSC_IDLE_SMART>;
					ti,sysc-mask = <(SYSC_OMAP2_CLOCKACTIVITY |
							 SYSC_OMAP2_ENAWAKEUP |
							 SYSC_OMAP2_SOFTRESET |
							 SYSC_OMAP2_AUTOIDLE)>;
					ti,syss-mask = <1>;
					#address-cells = <1>;
					#size-cells = <1>;
					ranges = <0 0x5000 0x1000>;

					dsi2: encoder@0 {
						compatible = "ti,omap4-dsi";
						reg = <0 0x200>,
						      <0x200 0x40>,
						      <0x300 0x20>;
						reg-names = "proto", "phy", "pll";
						interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
						status = "disabled";
						clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 8>,
						         <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 10>;
						clock-names = "fck", "sys_clk";

						#address-cells = <1>;
						#size-cells = <0>;
					};
				};

				target-module@6000 {
					compatible = "ti,sysc-omap4", "ti,sysc";
					reg = <0x6000 0x4>,
					      <0x6010 0x4>;
					reg-names = "rev", "sysc";
					/*
					 * Has SYSC_IDLE_SMART and SYSC_IDLE_SMART_WKUP
					 * but HDMI audio will fail with them.
					 */
					ti,sysc-sidle = <SYSC_IDLE_FORCE>,
							<SYSC_IDLE_NO>;
					ti,sysc-mask = <(SYSC_OMAP4_SOFTRESET)>;
					clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 9>,
						 <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 8>;
					clock-names = "fck", "dss_clk";
					#address-cells = <1>;
					#size-cells = <1>;
					ranges = <0 0x6000 0x2000>;

					hdmi: encoder@0 {
					compatible = "ti,omap4-hdmi";
						reg = <0 0x200>,
						      <0x200 0x100>,
						      <0x300 0x100>,
						      <0x400 0x1000>;
						reg-names = "wp", "pll", "phy", "core";
						interrupts = <GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>;
						status = "disabled";
						clocks = <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 9>,
						         <&l3_dss_clkctrl OMAP4_DSS_CORE_CLKCTRL 10>;
						clock-names = "fck", "sys_clk";
						dmas = <&sdma 76>;
						dma-names = "audio_tx";
					};
				};
			};
		};

		iva_hd_target: target-module@5a000000 {
			compatible = "ti,sysc-omap4", "ti,sysc";
			reg = <0x5a05a400 0x4>,
			      <0x5a05a410 0x4>;
			reg-names = "rev", "sysc";
			ti,sysc-midle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>;
			ti,sysc-sidle = <SYSC_IDLE_FORCE>,
					<SYSC_IDLE_NO>,
					<SYSC_IDLE_SMART>;
			power-domains = <&prm_ivahd>;
			resets = <&prm_ivahd 2>;
			reset-names = "rstctrl";
			clocks = <&ivahd_clkctrl OMAP4_IVA_CLKCTRL 0>;
			clock-names = "fck";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x5a000000 0x5a000000 0x1000000>,
				 <0x5b000000 0x5b000000 0x1000000>;

			iva {
				compatible = "ti,ivahd";
			};
		};
	};
};

#include "omap4-l4.dtsi"
#include "omap4-l4-abe.dtsi"
#include "omap44xx-clocks.dtsi"

&prm {
	prm_mpu: prm@300 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x300 0x100>;
		#power-domain-cells = <0>;
	};

	prm_tesla: prm@400 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x400 0x100>;
		#reset-cells = <1>;
		#power-domain-cells = <0>;
	};

	prm_abe: prm@500 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x500 0x100>;
		#power-domain-cells = <0>;
	};

	prm_always_on_core: prm@600 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x600 0x100>;
		#power-domain-cells = <0>;
	};

	prm_core: prm@700 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x700 0x100>;
		#reset-cells = <1>;
		#power-domain-cells = <0>;
	};

	prm_ivahd: prm@f00 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0xf00 0x100>;
		#reset-cells = <1>;
		#power-domain-cells = <0>;
	};

	prm_cam: prm@1000 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x1000 0x100>;
		#power-domain-cells = <0>;
	};

	prm_dss: prm@1100 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x1100 0x100>;
		#power-domain-cells = <0>;
	};

	prm_gfx: prm@1200 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x1200 0x100>;
		#power-domain-cells = <0>;
	};

	prm_l3init: prm@1300 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x1300 0x100>;
		#power-domain-cells = <0>;
	};

	prm_l4per: prm@1400 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x1400 0x100>;
		#power-domain-cells = <0>;
	};

	prm_cefuse: prm@1600 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x1600 0x100>;
		#power-domain-cells = <0>;
	};

	prm_wkup: prm@1700 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x1700 0x100>;
		#power-domain-cells = <0>;
	};

	prm_emu: prm@1900 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x1900 0x100>;
		#power-domain-cells = <0>;
	};

	prm_dss: prm@1100 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x1100 0x40>;
		#power-domain-cells = <0>;
	};

	prm_device: prm@1b00 {
		compatible = "ti,omap4-prm-inst", "ti,omap-prm-inst";
		reg = <0x1b00 0x40>;
		#reset-cells = <1>;
	};
};

/* Preferred always-on timer for clockevent */
&timer1_target {
	ti,no-reset-on-init;
	ti,no-idle;
	timer@0 {
		assigned-clocks = <&l4_wkup_clkctrl OMAP4_TIMER1_CLKCTRL 24>;
		assigned-clock-parents = <&sys_32k_ck>;
	};
};
