// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for IGEP COM MODULE Rev. E (TI OMAP AM/DM37x)
 *
 * Copyright (C) 2012 <PERSON> <<EMAIL>>
 * Copyright (C) 2012 Enric Balletbo i Serra <<EMAIL>>
 */

#include "omap3-igep0030-common.dtsi"

/ {
	model = "IGEP COM MODULE Rev. E (TI OMAP AM/DM37x)";
	compatible = "isee,omap3-igep0030", "ti,omap3630", "ti,omap3";

	vmmcsdio_fixed: fixedregulator-mmcsdio {
		compatible = "regulator-fixed";
		regulator-name = "vmmcsdio_fixed";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	mmc2_pwrseq: mmc2_pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&gpio5 11 GPIO_ACTIVE_LOW>,	/* gpio_139 - RESET_N_W */
			      <&gpio5 10 GPIO_ACTIVE_LOW>;	/* gpio_138 - WIFI_PDN */
	};
};

&omap3_pmx_core {
	lbee1usjyc_pins: lbee1usjyc-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2166, PIN_OUTPUT | MUX_MODE4)	/* sdmmc2_dat5.gpio_137 - RESET_N_W */
			OMAP3_CORE1_IOPAD(0x2168, PIN_OUTPUT | MUX_MODE4)	/* sdmmc2_dat6.gpio_138 - WIFI_PDN */
			OMAP3_CORE1_IOPAD(0x216a, PIN_OUTPUT | MUX_MODE4)	/* sdmmc2_dat7.gpio_139 - RST_N_B */
		>;
	};
};

&leds {
	pinctrl-names = "default";
	pinctrl-0 = <&leds_core2_pins>;

	boot {
		label = "omap3:green:boot";
		gpios = <&twl_gpio 13 GPIO_ACTIVE_LOW>;	/* LEDSYNC */
		default-state = "on";
	};
};

/* On board Wifi module */
&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_pins &lbee1usjyc_pins>;
	vmmc-supply = <&vmmcsdio_fixed>;
	mmc-pwrseq = <&mmc2_pwrseq>;
	bus-width = <4>;
	non-removable;
};

