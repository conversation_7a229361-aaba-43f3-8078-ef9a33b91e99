// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2012 Texas Instruments Incorporated - https://www.ti.com/
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 */

#include "omap3-tao3530.dtsi"

/ {
	gpio_poweroff {
		pinctrl-names = "default";
		pinctrl-0 = <&poweroff_pins>;

		compatible = "gpio-poweroff";
		gpios = <&gpio6 8 GPIO_ACTIVE_LOW>;	/* GPIO 168 */
	};
};

&omap3_pmx_core {
	sound2_pins: sound2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x209e, PIN_OUTPUT | MUX_MODE4)	/* gpmc_d8 gpio_44 */
		>;
	};

	led_blue_pins: led-blue-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2110, PIN_OUTPUT | MUX_MODE4)	/* cam_xclka gpio_96, LED blue */
		>;
	};

	led_green_pins: led-green-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2126, PIN_OUTPUT | MUX_MODE4)	/* cam_d8 gpio_107, LED green */
		>;
	};

	led_red_pins: led-red-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x212e, PIN_OUTPUT_PULLUP | MUX_MODE4)	/* cam_xclkb gpio_111, LED red */
		>;
	};

	poweroff_pins: poweroff-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21be, PIN_OUTPUT_PULLUP | MUX_MODE4)	/* i2c2_scl gpio_168 */
		>;
	};

	powerdown_input_pins: powerdown-input-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21c0, PIN_INPUT_PULLUP | MUX_MODE4)	/* i2c2_sda gpio_183 */
		>;
	};

	fpga_boot0_pins: fpga-boot0-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x211a, PIN_INPUT | MUX_MODE4)	/* cam_d2 gpio_101 */
			OMAP3_CORE1_IOPAD(0x211c, PIN_OUTPUT | MUX_MODE4)	/* cam_d3 gpio_102 */
			OMAP3_CORE1_IOPAD(0x211e, PIN_OUTPUT | MUX_MODE4)	/* cam_d4 gpio_103 */
			OMAP3_CORE1_IOPAD(0x2120, PIN_INPUT_PULLUP | MUX_MODE4)	/* cam_d5 gpio_104 */
		>;
	};

	fpga_boot1_pins: fpga-boot1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20a2, PIN_INPUT | MUX_MODE4)	/* gpmc_d10 gpio_46 */
			OMAP3_CORE1_IOPAD(0x20a4, PIN_OUTPUT | MUX_MODE4)	/* gpmc_d11 gpio_47 */
			OMAP3_CORE1_IOPAD(0x20a6, PIN_OUTPUT | MUX_MODE4)	/* gpmc_d12 gpio_48 */
			OMAP3_CORE1_IOPAD(0x20a8, PIN_INPUT_PULLUP | MUX_MODE4)	/* gpmc_d13 gpio_49 */
		>;
	};
};

/* I2C2: mux'ed with GPIO168 which is connected to nKILL_POWER */
&i2c2 {
	status = "disabled";
};

&i2c3 {
	clock-frequency = <100000>;

	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;
};
