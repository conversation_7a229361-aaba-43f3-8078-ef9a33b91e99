// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2013 Texas Instruments Incorporated - https://www.ti.com/
 */
/dts-v1/;

#include "omap36xx.dtsi"
#include "omap-zoom-common.dtsi"

/ {
	model = "TI Zoom3";
	compatible = "ti,omap3-zoom3", "ti,omap3630", "ti,omap3";

	cpus {
		cpu@0 {
			cpu0-supply = <&vcc>;
		};
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x20000000>; /* 512 MB */
	};

	vddvario: regulator-vddvario {
		compatible = "regulator-fixed";
		regulator-name = "vddvario";
		regulator-always-on;
	};

	vdd33a: regulator-vdd33a {
		compatible = "regulator-fixed";
		regulator-name = "vdd33a";
		regulator-always-on;
	};

	wl12xx_vmmc: wl12xx_vmmc {
		pinctrl-names = "default";
		pinctrl-0 = <&wl12xx_gpio>;
		compatible = "regulator-fixed";
		regulator-name = "vwl1271";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		gpio = <&gpio4 5 GPIO_ACTIVE_HIGH>;	/* gpio101 */
		startup-delay-us = <70000>;
		enable-active-high;
	};
};

&omap3_pmx_core {
	/* REVISIT: twl gpio0 is mmc0_cd */
	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2144, PIN_OUTPUT_PULLUP | MUX_MODE0)	/* sdmmc1_clk.sdmmc1_clk */
			OMAP3_CORE1_IOPAD(0x2146, PIN_OUTPUT_PULLUP | MUX_MODE0)	/* sdmmc1_cmd.sdmmc1_cmd */
			OMAP3_CORE1_IOPAD(0x2148, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat0.sdmmc1_dat0 */
			OMAP3_CORE1_IOPAD(0x214a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat1.sdmmc1_dat1 */
			OMAP3_CORE1_IOPAD(0x214c, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat2.sdmmc1_dat2 */
			OMAP3_CORE1_IOPAD(0x214e, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat3.sdmmc1_dat3 */
		>;
	};

	mmc2_pins: mmc2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2158, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_clk.sdmmc2_clk */
			OMAP3_CORE1_IOPAD(0x215a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_cmd.sdmmc2_cmd */
			OMAP3_CORE1_IOPAD(0x215c, PIN_INPUT | MUX_MODE0)		/* sdmmc2_dat0.sdmmc2_dat0 */
			OMAP3_CORE1_IOPAD(0x215e, PIN_INPUT | MUX_MODE0)		/* sdmmc2_dat1.sdmmc2_dat1 */
			OMAP3_CORE1_IOPAD(0x2160, PIN_INPUT | MUX_MODE0)		/* sdmmc2_dat2.sdmmc2_dat2 */
			OMAP3_CORE1_IOPAD(0x2162, PIN_INPUT | MUX_MODE0)		/* sdmmc2_dat3.sdmmc2_dat3 */
			OMAP3_CORE1_IOPAD(0x2164, PIN_INPUT | MUX_MODE0)		/* sdmmc2_dat4.sdmmc2_dat4 */
			OMAP3_CORE1_IOPAD(0x2166, PIN_INPUT | MUX_MODE0)		/* sdmmc2_dat5.sdmmc2_dat5 */
			OMAP3_CORE1_IOPAD(0x2168, PIN_INPUT | MUX_MODE0)		/* sdmmc2_dat6.sdmmc2_dat6 */
			OMAP3_CORE1_IOPAD(0x216a, PIN_INPUT | MUX_MODE0)		/* sdmmc2_dat7.sdmmc2_dat7 */
		>;
	};

	mmc3_pins: mmc3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2198, PIN_INPUT | MUX_MODE4)	/* mcbsp1_clkx.gpio_162 WLAN IRQ */
			OMAP3_CORE1_IOPAD(0x21d0, PIN_INPUT_PULLUP | MUX_MODE3)	/* mcspi1_cs1.sdmmc3_cmd */
		>;
	};

	uart1_pins: uart1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2180, PIN_INPUT | MUX_MODE0)		/* uart1_cts.uart1_cts */
			OMAP3_CORE1_IOPAD(0x217e, PIN_OUTPUT | MUX_MODE0)		/* uart1_rts.uart1_rts */
			OMAP3_CORE1_IOPAD(0x2182, WAKEUP_EN | PIN_INPUT | MUX_MODE0) /* uart1_rx.uart1_rx */
			OMAP3_CORE1_IOPAD(0x217c, PIN_OUTPUT | MUX_MODE0)		/* uart1_tx.uart1_tx */
		>;
	};

	uart2_pins: uart2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2174, PIN_INPUT_PULLUP | MUX_MODE0)	/* uart2_cts.uart2_cts */
			OMAP3_CORE1_IOPAD(0x2176, PIN_OUTPUT | MUX_MODE0)		/* uart2_rts.uart2_rts */
			OMAP3_CORE1_IOPAD(0x217a, PIN_INPUT | MUX_MODE0)		/* uart2_rx.uart2_rx */
			OMAP3_CORE1_IOPAD(0x2178, PIN_OUTPUT | MUX_MODE0)		/* uart2_tx.uart2_tx */
		>;
	};

	uart3_pins: uart3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x219a, PIN_INPUT_PULLDOWN | MUX_MODE0)	/* uart3_cts_rctx.uart3_cts_rctx */
			OMAP3_CORE1_IOPAD(0x219c, PIN_OUTPUT | MUX_MODE0)		/* uart3_rts_sd.uart3_rts_sd */
			OMAP3_CORE1_IOPAD(0x219e, PIN_INPUT | MUX_MODE0)		/* uart3_rx_irrx.uart3_rx_irrx */
			OMAP3_CORE1_IOPAD(0x21a0, PIN_OUTPUT | MUX_MODE0)		/* uart3_tx_irtx.uart3_tx_irtx */
		>;
	};

	/* wl12xx GPIO output for WLAN_EN */
	wl12xx_gpio: wl12xx-gpio-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x211a, PIN_OUTPUT| MUX_MODE4)		/* cam_d2.gpio_101 */
		>;
	};
};

&omap3_pmx_core2 {
	mmc3_2_pins: mmc3-2-pins {
		pinctrl-single,pins = <
			OMAP3630_CORE2_IOPAD(0x25d8, PIN_INPUT_PULLUP | MUX_MODE2)	/* etk_clk.sdmmc3_clk */
			OMAP3630_CORE2_IOPAD(0x25e4, PIN_INPUT_PULLUP | MUX_MODE2)	/* etk_d4.sdmmc3_dat0 */
			OMAP3630_CORE2_IOPAD(0x25e6, PIN_INPUT_PULLUP | MUX_MODE2)	/* etk_d5.sdmmc3_dat1 */
			OMAP3630_CORE2_IOPAD(0x25e8, PIN_INPUT_PULLUP | MUX_MODE2)	/* etk_d6.sdmmc3_dat2 */
			OMAP3630_CORE2_IOPAD(0x25e2, PIN_INPUT_PULLUP | MUX_MODE2)	/* etk_d3.sdmmc3_dat3 */
		>;
	};
};

&omap3_pmx_wkup {
	wlan_host_wkup: wlan-host-wkup-pins {
		pinctrl-single,pins = <
			OMAP3_WKUP_IOPAD(0x2a1a, PIN_INPUT_PULLUP | MUX_MODE4)	/* sys_clkout1.gpio_10 WLAN_HOST_WKUP */
		>;
	};
};

&i2c1 {
	clock-frequency = <2600000>;

	twl: twl@48 {
		reg = <0x48>;
		interrupts = <7>; /* SYS_NIRQ cascaded to intc */
		interrupt-parent = <&intc>;
	};
};

#include "twl4030.dtsi"

&i2c2 {
	clock-frequency = <400000>;
};

&i2c3 {
	clock-frequency = <400000>;

	/*
	 * TVP5146 Video decoder-in for analog input support.
	 */
	tvp5146@5c {
		compatible = "ti,tvp5146m2";
		reg = <0x5c>;
	};
};

&twl_gpio {
	ti,use-leds;
};

&mmc1 {
	vmmc-supply = <&vmmc1>;
	vqmmc-supply = <&vsim>;
	bus-width = <4>;
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
};
/*
&mmc2 {
	vmmc-supply = <&vmmc2>;
	ti,non-removable;
	bus-width = <8>;
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_pins>;
};
*/
&mmc3 {
	vmmc-supply = <&wl12xx_vmmc>;
	non-removable;
	bus-width = <4>;
	cap-power-off-card;
	pinctrl-names = "default";
	pinctrl-0 = <&mmc3_pins &mmc3_2_pins>;

	#address-cells = <1>;
	#size-cells = <0>;
	wlcore: wlcore@2 {
		compatible = "ti,wl1271";
		reg = <2>;
		interrupt-parent = <&gpio6>;
		interrupts = <2 IRQ_TYPE_EDGE_RISING>; /* gpio 162 */
		ref-clock-frequency = <26000000>;
	};
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pins>;
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pins>;
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>;
};

&uart4 {
	status = "disabled";
};

&usb_otg_hs {
	interface-type = <0>;
	usb-phy = <&usb2_phy>;
	mode = <3>;
	power = <50>;
};
