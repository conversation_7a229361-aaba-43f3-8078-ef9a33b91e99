// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2011 Texas Instruments Incorporated - https://www.ti.com/
 */
/dts-v1/;

#include "omap34xx.dtsi"
#include "omap3-evm-common.dtsi"
#include "omap3-evm-processor-common.dtsi"

/ {
	model = "TI OMAP35XX EVM (TMDSEVM3530)";
	compatible = "ti,omap3-evm", "ti,omap3430", "ti,omap3";
};

&omap3_pmx_core2 {
	pinctrl-names = "default";
	pinctrl-0 = <&hsusb2_2_pins>;

	ehci_phy_pins: ehci-phy-pins {
		pinctrl-single,pins = <

		/* EHCI PHY reset GPIO etk_d7.gpio_21 */
		OMAP3430_CORE2_IOPAD(0x25ea, PIN_OUTPUT | MUX_MODE4)

		/* EHCI VBUS etk_d8.gpio_22 */
		OMAP3430_CORE2_IOPAD(0x25ec, PIN_OUTPUT | MUX_MODE4)
		>;
	};

	/* Used by OHCI and EHCI. OHCI won't work without external phy */
	hsusb2_2_pins: hsusb2-2-pins {
		pinctrl-single,pins = <

		/* etk_d10.hsusb2_clk */
		OMAP3430_CORE2_IOPAD(0x25f0, PIN_OUTPUT | MUX_MODE3)

		/* etk_d11.hsusb2_stp */
		OMAP3430_CORE2_IOPAD(0x25f2, PIN_OUTPUT | MUX_MODE3)

		/* etk_d12.hsusb2_dir */
		OMAP3430_CORE2_IOPAD(0x25f4, PIN_INPUT_PULLDOWN | MUX_MODE3)

		/* etk_d13.hsusb2_nxt */
		OMAP3430_CORE2_IOPAD(0x25f6, PIN_INPUT_PULLDOWN | MUX_MODE3)

		/* etk_d14.hsusb2_data0 */
		OMAP3430_CORE2_IOPAD(0x25f8, PIN_INPUT_PULLDOWN | MUX_MODE3)

		/* etk_d15.hsusb2_data1 */
		OMAP3430_CORE2_IOPAD(0x25fa, PIN_INPUT_PULLDOWN | MUX_MODE3)
		>;
	};
};

&gpmc {
	nand@0,0 {
		compatible = "ti,omap2-nand";
		reg = <0 0 4>; /* CS0, offset 0, IO size 4 */
		interrupt-parent = <&gpmc>;
		interrupts = <0 IRQ_TYPE_NONE>, /* fifoevent */
			     <1 IRQ_TYPE_NONE>;	/* termcount */
		linux,mtd-name = "micron,mt29f2g16abdhc";
		nand-bus-width = <16>;
		gpmc,device-width = <2>;
		ti,nand-ecc-opt = "bch8";

		gpmc,sync-clk-ps = <0>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <44>;
		gpmc,cs-wr-off-ns = <44>;
		gpmc,adv-on-ns = <6>;
		gpmc,adv-rd-off-ns = <34>;
		gpmc,adv-wr-off-ns = <44>;
		gpmc,we-off-ns = <40>;
		gpmc,oe-off-ns = <54>;
		gpmc,access-ns = <64>;
		gpmc,rd-cycle-ns = <82>;
		gpmc,wr-cycle-ns = <82>;
		gpmc,wr-access-ns = <40>;
		gpmc,wr-data-mux-bus-ns = <0>;

		#address-cells = <1>;
		#size-cells = <1>;
	};
};
