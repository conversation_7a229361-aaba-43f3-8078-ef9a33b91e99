// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree Source for OMAP543x SoC CORE thermal
 *
 * Copyright (C) 2013 Texas Instruments Incorporated - https://www.ti.com/
 * Contact: <PERSON> <<EMAIL>>
 */

#include <dt-bindings/thermal/thermal.h>

core_thermal: core_thermal {
	polling-delay-passive = <250>; /* milliseconds */
	polling-delay = <500>; /* milliseconds */

			/* sensor       ID */
	thermal-sensors = <&bandgap     2>;

	trips {
		core_crit: core_crit {
			temperature = <125000>; /* milliCelsius */
			hysteresis = <2000>; /* milliCelsius */
			type = "critical";
		};
	};
};
