// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2011-2013 Texas Instruments Incorporated - https://www.ti.com/
 */
#include <dt-bindings/input/input.h>
#include "elpida_ecb240abacn.dtsi"
#include "omap4-mcpdm.dtsi"

/ {
	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x40000000>; /* 1 GB */
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		dsp_memory_region: dsp-memory@98000000 {
			compatible = "shared-dma-pool";
			reg = <0x98000000 0x800000>;
			reusable;
			status = "okay";
		};

		ipu_memory_region: ipu-memory@98800000 {
			compatible = "shared-dma-pool";
			reg = <0x98800000 0x7000000>;
			reusable;
			status = "okay";
		};
	};

	chosen {
		stdout-path = &uart3;
	};

	aliases {
		display0 = &dvi0;
		display1 = &hdmi0;
		ethernet = &ethernet;
	};

	leds: leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <
			&led_wkgpio_pins
		>;

		led-heartbeat {
			label = "pandaboard::status1";
			gpios = <&gpio1 7 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
		};

		led-mmc {
			label = "pandaboard::status2";
			gpios = <&gpio1 8 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "mmc0";
		};
	};

	gpio_keys: gpio_keys {
		compatible = "gpio-keys";
		pinctrl-names = "default";
		pinctrl-0 = <
			&button_pins
		>;

		buttonS2 {
			label = "button S2";
			gpios = <&gpio4 25 GPIO_ACTIVE_LOW>;	/* gpio_121 */
			linux,code = <BTN_0>;
			wakeup-source;
		};
	};

	sound: sound {
		compatible = "ti,abe-twl6040";
		ti,model = "PandaBoard";

		ti,mclk-freq = <********>;

		ti,mcpdm = <&mcpdm>;

		ti,twl6040 = <&twl6040>;

		/* Audio routing */
		ti,audio-routing =
			"Headset Stereophone", "HSOL",
			"Headset Stereophone", "HSOR",
			"Ext Spk", "HFL",
			"Ext Spk", "HFR",
			"Line Out", "AUXL",
			"Line Out", "AUXR",
			"HSMIC", "Headset Mic",
			"Headset Mic", "Headset Mic Bias",
			"AFML", "Line In",
			"AFMR", "Line In";
	};

	/* HS USB Port 1 Power */
	hsusb1_power: hsusb1_power_reg {
		compatible = "regulator-fixed";
		regulator-name = "hsusb1_vbus";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio1 1 GPIO_ACTIVE_HIGH>;	/* gpio_1 */
		startup-delay-us = <70000>;
		enable-active-high;
		/*
		 * boot-on is required along with always-on as the
		 * regulator framework doesn't enable the regulator
		 * if boot-on is not there.
		 */
		regulator-always-on;
		regulator-boot-on;
	};

	/* HS USB Host PHY on PORT 1 */
	hsusb1_phy: hsusb1_phy {
		compatible = "usb-nop-xceiv";
		reset-gpios = <&gpio2 30 GPIO_ACTIVE_LOW>;   /* gpio_62 */
		#phy-cells = <0>;
		vcc-supply = <&hsusb1_power>;
		clocks = <&auxclk3_ck>;
		clock-names = "main_clk";
		clock-frequency = <19200000>;
	};

	/* regulator for wl12xx on sdio5 */
	wl12xx_vmmc: wl12xx_vmmc {
		pinctrl-names = "default";
		pinctrl-0 = <&wl12xx_gpio>;
		compatible = "regulator-fixed";
		regulator-name = "vwl1271";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		gpio = <&gpio2 11 GPIO_ACTIVE_HIGH>;
		startup-delay-us = <70000>;
		enable-active-high;
	};

	tfp410: encoder0 {
		compatible = "ti,tfp410";
		powerdown-gpios = <&gpio1 0 GPIO_ACTIVE_LOW>;	/* gpio_0 */

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				tfp410_in: endpoint {
					remote-endpoint = <&dpi_out>;
				};
			};

			port@1 {
				reg = <1>;

				tfp410_out: endpoint {
					remote-endpoint = <&dvi_connector_in>;
				};
			};
		};
	};

	dvi0: connector0 {
		compatible = "dvi-connector";
		label = "dvi";

		digital;

		ddc-i2c-bus = <&i2c3>;

		port {
			dvi_connector_in: endpoint {
				remote-endpoint = <&tfp410_out>;
			};
		};
	};

	tpd12s015: encoder1 {
		compatible = "ti,tpd12s015";

		gpios = <&gpio2 28 GPIO_ACTIVE_HIGH>,	/* 60, CT CP HPD */
			<&gpio2 9 GPIO_ACTIVE_HIGH>,	/* 41, LS OE */
			<&gpio2 31 GPIO_ACTIVE_HIGH>;	/* 63, HPD */

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				tpd12s015_in: endpoint {
					remote-endpoint = <&hdmi_out>;
				};
			};

			port@1 {
				reg = <1>;

				tpd12s015_out: endpoint {
					remote-endpoint = <&hdmi_connector_in>;
				};
			};
		};
	};

	hdmi0: connector1 {
		compatible = "hdmi-connector";
		label = "hdmi";

		type = "a";

		port {
			hdmi_connector_in: endpoint {
				remote-endpoint = <&tpd12s015_out>;
			};
		};
	};
};

&omap4_pmx_core {
	pinctrl-names = "default";
	pinctrl-0 = <
			&dss_dpi_pins
			&tfp410_pins
			&dss_hdmi_pins
			&tpd12s015_pins
			&hsusbb1_pins
	>;

	twl6040_pins: twl6040-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x120, PIN_OUTPUT | MUX_MODE3)	/* hdq_sio.gpio_127 */
			OMAP4_IOPAD(0x1a0, PIN_INPUT | MUX_MODE0)	/* sys_nirq2.sys_nirq2 */
		>;
	};

	mcbsp1_pins: mcbsp1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x0fe, PIN_INPUT | MUX_MODE0)		/* abe_mcbsp1_clkx.abe_mcbsp1_clkx */
			OMAP4_IOPAD(0x100, PIN_INPUT_PULLDOWN | MUX_MODE0)	/* abe_mcbsp1_dr.abe_mcbsp1_dr */
			OMAP4_IOPAD(0x102, PIN_OUTPUT_PULLDOWN | MUX_MODE0)	/* abe_mcbsp1_dx.abe_mcbsp1_dx */
			OMAP4_IOPAD(0x104, PIN_INPUT | MUX_MODE0)		/* abe_mcbsp1_fsx.abe_mcbsp1_fsx */
		>;
	};

	dss_dpi_pins: dss-dpi-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x162, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data23 */
			OMAP4_IOPAD(0x164, PIN_OUTPUT | MUX_MODE5) 	/* dispc2_data22 */
			OMAP4_IOPAD(0x166, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data21 */
			OMAP4_IOPAD(0x168, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data20 */
			OMAP4_IOPAD(0x16a, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data19 */
			OMAP4_IOPAD(0x16c, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data18 */
			OMAP4_IOPAD(0x16e, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data15 */
			OMAP4_IOPAD(0x170, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data14 */
			OMAP4_IOPAD(0x172, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data13 */
			OMAP4_IOPAD(0x174, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data12 */
			OMAP4_IOPAD(0x176, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data11 */

			OMAP4_IOPAD(0x1b4, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data10 */
			OMAP4_IOPAD(0x1b6, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data9 */
			OMAP4_IOPAD(0x1b8, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data16 */
			OMAP4_IOPAD(0x1ba, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data17 */
			OMAP4_IOPAD(0x1bc, PIN_OUTPUT | MUX_MODE5)	/* dispc2_hsync */
			OMAP4_IOPAD(0x1be, PIN_OUTPUT | MUX_MODE5)	/* dispc2_pclk */
			OMAP4_IOPAD(0x1c0, PIN_OUTPUT | MUX_MODE5)	/* dispc2_vsync */
			OMAP4_IOPAD(0x1c2, PIN_OUTPUT | MUX_MODE5)	/* dispc2_de */
			OMAP4_IOPAD(0x1c4, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data8 */
			OMAP4_IOPAD(0x1c6, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data7 */
			OMAP4_IOPAD(0x1c8, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data6 */
			OMAP4_IOPAD(0x1ca, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data5 */
			OMAP4_IOPAD(0x1cc, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data4 */
			OMAP4_IOPAD(0x1ce, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data3 */

			OMAP4_IOPAD(0x1d0, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data2 */
			OMAP4_IOPAD(0x1d2, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data1 */
			OMAP4_IOPAD(0x1d4, PIN_OUTPUT | MUX_MODE5)	/* dispc2_data0 */
		>;
	};

	tfp410_pins: tfp410-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x184, PIN_OUTPUT | MUX_MODE3)	/* gpio_0 */
		>;
	};

	dss_hdmi_pins: dss-hdmi-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x09a, PIN_INPUT | MUX_MODE0)		/* hdmi_cec.hdmi_cec */
			OMAP4_IOPAD(0x09c, PIN_INPUT_PULLUP | MUX_MODE0)	/* hdmi_scl.hdmi_scl */
			OMAP4_IOPAD(0x09e, PIN_INPUT_PULLUP | MUX_MODE0)	/* hdmi_sda.hdmi_sda */
		>;
	};

	tpd12s015_pins: tpd12s015-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x062, PIN_OUTPUT | MUX_MODE3)		/* gpmc_a17.gpio_41 */
			OMAP4_IOPAD(0x088, PIN_OUTPUT | MUX_MODE3)		/* gpmc_nbe1.gpio_60 */
			OMAP4_IOPAD(0x098, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* hdmi_hpd.gpio_63 */
		>;
	};

	hsusbb1_pins: hsusbb1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x0c2, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_clk.usbb1_ulpiphy_clk */
			OMAP4_IOPAD(0x0c4, PIN_OUTPUT | MUX_MODE4)		/* usbb1_ulpitll_stp.usbb1_ulpiphy_stp */
			OMAP4_IOPAD(0x0c6, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dir.usbb1_ulpiphy_dir */
			OMAP4_IOPAD(0x0c8, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_nxt.usbb1_ulpiphy_nxt */
			OMAP4_IOPAD(0x0ca, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat0.usbb1_ulpiphy_dat0 */
			OMAP4_IOPAD(0x0cc, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat1.usbb1_ulpiphy_dat1 */
			OMAP4_IOPAD(0x0ce, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat2.usbb1_ulpiphy_dat2 */
			OMAP4_IOPAD(0x0d0, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat3.usbb1_ulpiphy_dat3 */
			OMAP4_IOPAD(0x0d2, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat4.usbb1_ulpiphy_dat4 */
			OMAP4_IOPAD(0x0d4, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat5.usbb1_ulpiphy_dat5 */
			OMAP4_IOPAD(0x0d6, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat6.usbb1_ulpiphy_dat6 */
			OMAP4_IOPAD(0x0d8, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* usbb1_ulpitll_dat7.usbb1_ulpiphy_dat7 */
		>;
	};

	i2c1_pins: i2c1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x122, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c1_scl */
			OMAP4_IOPAD(0x124, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c1_sda */
		>;
	};

	i2c2_pins: i2c2-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x126, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c2_scl */
			OMAP4_IOPAD(0x128, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c2_sda */
		>;
	};

	i2c3_pins: i2c3-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x12a, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c3_scl */
			OMAP4_IOPAD(0x12c, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c3_sda */
		>;
	};

	i2c4_pins: i2c4-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x12e, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c4_scl */
			OMAP4_IOPAD(0x130, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c4_sda */
		>;
	};

	/*
	 * wl12xx GPIO outputs for WLAN_EN, BT_EN, FM_EN, BT_WAKEUP
	 * REVISIT: Are the pull-ups needed for GPIO 48 and 49?
	 */
	wl12xx_gpio: wl12xx-gpio-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x066, PIN_OUTPUT | MUX_MODE3)		/* gpmc_a19.gpio_43 */
			OMAP4_IOPAD(0x06c, PIN_OUTPUT | MUX_MODE3)		/* gpmc_a22.gpio_46 */
			OMAP4_IOPAD(0x070, PIN_OUTPUT_PULLUP | MUX_MODE3)	/* gpmc_a24.gpio_48 */
			OMAP4_IOPAD(0x072, PIN_OUTPUT_PULLUP | MUX_MODE3)	/* gpmc_a25.gpio_49 */
		>;
	};

	/* wl12xx GPIO inputs and SDIO pins */
	wl12xx_pins: wl12xx-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x078, PIN_INPUT | MUX_MODE3)		/* gpmc_ncs2.gpio_52 */
			OMAP4_IOPAD(0x07a, PIN_INPUT | MUX_MODE3)		/* gpmc_ncs3.gpio_53 */
			OMAP4_IOPAD(0x148, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_clk.sdmmc5_clk */
			OMAP4_IOPAD(0x14a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_cmd.sdmmc5_cmd */
			OMAP4_IOPAD(0x14c, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_dat0.sdmmc5_dat0 */
			OMAP4_IOPAD(0x14e, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_dat1.sdmmc5_dat1 */
			OMAP4_IOPAD(0x150, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_dat2.sdmmc5_dat2 */
			OMAP4_IOPAD(0x152, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_dat3.sdmmc5_dat3 */
		>;
	};

	button_pins: button-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x114, PIN_INPUT_PULLUP | MUX_MODE3)	/* gpio_121 */
		>;
	};
};

&omap4_pmx_wkup {
	led_wkgpio_pins: leds-wkpins-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x05a, PIN_OUTPUT | MUX_MODE3)	/* gpio_wk7 */
			OMAP4_IOPAD(0x05c, PIN_OUTPUT | MUX_MODE3)	/* gpio_wk8 */
		>;
	};
};

&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins>;

	clock-frequency = <400000>;

	twl: twl@48 {
		reg = <0x48>;
		/* IRQ# = 7 */
		interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>; /* IRQ_SYS_1N cascaded to gic */
	};

	twl6040: twl@4b {
		compatible = "ti,twl6040";
		#clock-cells = <0>;
		reg = <0x4b>;

		pinctrl-names = "default";
		pinctrl-0 = <&twl6040_pins>;

		/* IRQ# = 119 */
		interrupts = <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>; /* IRQ_SYS_2N cascaded to gic */
		ti,audpwron-gpio = <&gpio4 31 GPIO_ACTIVE_HIGH>;  /* gpio line 127 */

		vio-supply = <&v1v8>;
		v2v1-supply = <&v2v1>;
		enable-active-high;
	};
};

#include "twl6030.dtsi"
#include "twl6030_omap4.dtsi"

&i2c2 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_pins>;

	clock-frequency = <400000>;
};

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;

	clock-frequency = <100000>;

	/*
	 * Display monitor features are burnt in their EEPROM as EDID data.
	 * The EEPROM is connected as I2C slave device.
	 */
	eeprom@50 {
		compatible = "ti,eeprom";
		reg = <0x50>;
	};
};

&i2c4 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4_pins>;

	clock-frequency = <400000>;
};

&mmc1 {
	vmmc-supply = <&vmmc>;
	bus-width = <8>;
};

&mmc2 {
	status = "disabled";
};

&mmc3 {
	status = "disabled";
};

&mmc4 {
	status = "disabled";
};

&mmc5 {
	pinctrl-names = "default";
	pinctrl-0 = <&wl12xx_pins>;
	vmmc-supply = <&wl12xx_vmmc>;
	interrupts-extended = <&wakeupgen GIC_SPI 59 IRQ_TYPE_LEVEL_HIGH
			       &omap4_pmx_core 0x10e>;
	non-removable;
	bus-width = <4>;
	cap-power-off-card;

	#address-cells = <1>;
	#size-cells = <0>;
	wlcore: wlcore@2 {
		compatible = "ti,wl1271";
		reg = <2>;
		/* gpio_53 with gpmc_ncs3 pad as wakeup */
		interrupts-extended = <&gpio2 21 IRQ_TYPE_LEVEL_HIGH>,
				      <&omap4_pmx_core 0x3a>;
		interrupt-names = "irq", "wakeup";
		ref-clock-frequency = <********>;
	};
};

&emif1 {
	cs1-used;
	device-handle = <&elpida_ECB240ABACN>;
};

&emif2 {
	cs1-used;
	device-handle = <&elpida_ECB240ABACN>;
};

&mcbsp1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mcbsp1_pins>;
	status = "okay";
};

&twl_usb_comparator {
	usb-supply = <&vusb>;
};

&uart2 {
	interrupts-extended = <&wakeupgen GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH
			       &omap4_pmx_core OMAP4_UART2_RX>;
};

&uart3 {
	interrupts-extended = <&wakeupgen GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH
			       &omap4_pmx_core OMAP4_UART3_RX>;
};

&uart4 {
	interrupts-extended = <&wakeupgen GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH
			       &omap4_pmx_core OMAP4_UART4_RX>;
};

&usb_otg_hs {
	interface-type = <1>;
	mode = <3>;
	power = <50>;
};

&usbhshost {
	port1-mode = "ehci-phy";
};

&usbhsehci {
	phys = <&hsusb1_phy>;

	#address-cells = <1>;
	#size-cells = <0>;

	hub@1 {
		compatible = "usb424,9514";
		reg = <1>;
		#address-cells = <1>;
		#size-cells = <0>;

		ethernet: ethernet@1 {
			compatible = "usb424,ec00";
			reg = <1>;
		};
	};
};

&dss {
	status = "okay";

	port {
		dpi_out: endpoint {
			remote-endpoint = <&tfp410_in>;
			data-lines = <24>;
		};
	};
};

&dsi2 {
	status = "okay";
	vdd-supply = <&vcxio>;
};

&hdmi {
	status = "okay";
	vdda-supply = <&vdac>;

	port {
		hdmi_out: endpoint {
			remote-endpoint = <&tpd12s015_in>;
		};
	};
};

&dsp {
	status = "okay";
	memory-region = <&dsp_memory_region>;
	ti,timers = <&timer5>;
	ti,watchdog-timers = <&timer6>;
};

&ipu {
	status = "okay";
	memory-region = <&ipu_memory_region>;
	ti,timers = <&timer3>;
	ti,watchdog-timers = <&timer9>, <&timer11>;
};
