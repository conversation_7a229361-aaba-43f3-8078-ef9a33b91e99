// SPDX-License-Identifier: GPL-2.0
/*
 * Common support for CompuLab SB-T35 used on SBC-T3530, SBC-T3517 and SBC-T3730
 */

/ {
	tfp410: encoder {
		compatible = "ti,tfp410";

		powerdown-gpios = <&gpio2 22 GPIO_ACTIVE_LOW>;  /* gpio_54 */

		pinctrl-names = "default";
		pinctrl-0 = <&tfp410_pins>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				tfp410_in: endpoint {
					remote-endpoint = <&dpi_out>;
				};
			};

			port@1 {
				reg = <1>;

				tfp410_out: endpoint {
					remote-endpoint = <&dvi_connector_in>;
				};
			};
		};
	};

	dvi0: dvi-connector {
		compatible = "dvi-connector";
		label = "dvi";

		port {
			dvi_connector_in: endpoint {
				remote-endpoint = <&tfp410_out>;
			};
		};
	};

	audio_amp: audio_amp {
		compatible = "regulator-fixed";
		regulator-name = "audio_amp";
		pinctrl-names = "default";
		pinctrl-0 = <&sb_t35_audio_amp>;
		gpio = <&gpio2 29 GPIO_ACTIVE_LOW>;   /* gpio_61 */
		regulator-always-on;
	};
};

&omap3_pmx_core {
	smsc2_pins: smsc2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20b6, PIN_OUTPUT | MUX_MODE0)	/* gpmc_ncs4.gpmc_ncs4 */
			OMAP3_CORE1_IOPAD(0x20d2, PIN_INPUT_PULLUP | MUX_MODE4)	/* gpmc_wait3.gpio_65 */
		>;
	};

	tfp410_pins: tfp410-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20b4, PIN_OUTPUT | MUX_MODE4)	/* gpmc_ncs3.gpio_54 */
		>;
	};

	i2c3_pins: i2c3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21c2, PIN_INPUT_PULLUP | MUX_MODE0) /* i2c3_scl */
			OMAP3_CORE1_IOPAD(0x21c4, PIN_INPUT_PULLUP | MUX_MODE0) /* i2c3_sda */
		>;
	};

	sb_t35_audio_amp: sb-t35-audio-amp-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20c8, PIN_OUTPUT | MUX_MODE4) /* gpmc_nbe1.gpio_61 */
		>;
	};
};

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;

	clock-frequency = <400000>;

	at24@50 {
		compatible = "atmel,24c02";
		pagesize = <16>;
		reg = <0x50>;
	};
};

&gpmc {
	ranges = <4 0 0x2d000000 0x01000000>;

	smsc2: ethernet@4,0 {
		compatible = "smsc,lan9221", "smsc,lan9115";
		pinctrl-names = "default";
		pinctrl-0 = <&smsc2_pins>;
		interrupt-parent = <&gpio3>;
		interrupts = <1 IRQ_TYPE_LEVEL_LOW>;
		reg = <4 0 0xff>;
		bank-width = <2>;
		gpmc,device-width = <1>;
		gpmc,cycle2cycle-samecsen;
		gpmc,cycle2cycle-diffcsen;
		gpmc,cs-on-ns = <5>;
		gpmc,cs-rd-off-ns = <150>;
		gpmc,cs-wr-off-ns = <150>;
		gpmc,adv-on-ns = <0>;
		gpmc,adv-rd-off-ns = <15>;
		gpmc,adv-wr-off-ns = <40>;
		gpmc,oe-on-ns = <45>;
		gpmc,oe-off-ns = <140>;
		gpmc,we-on-ns = <45>;
		gpmc,we-off-ns = <140>;
		gpmc,rd-cycle-ns = <155>;
		gpmc,wr-cycle-ns = <155>;
		gpmc,access-ns = <120>;
		gpmc,page-burst-access-ns = <20>;
		gpmc,bus-turnaround-ns = <75>;
		gpmc,cycle2cycle-delay-ns = <75>;
		gpmc,wait-monitoring-ns = <0>;
		gpmc,clk-activation-ns = <0>;
		gpmc,wr-data-mux-bus-ns = <0>;
		gpmc,wr-access-ns = <0>;
		vddvario-supply = <&vddvario>;
		vdd33a-supply = <&vdd33a>;
		reg-io-width = <4>;
		smsc,save-mac-address;
	};
};
