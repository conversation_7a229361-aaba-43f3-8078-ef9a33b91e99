// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright (C) 2013 <PERSON> <<EMAIL>>
 * Copyright (C) 2013-2014 <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "omap34xx.dtsi"
#include <dt-bindings/input/input.h>
#include <dt-bindings/leds/common.h>
#include <dt-bindings/media/video-interfaces.h>

/*
 * Default secure signed bootloader (Nokia X-Loader) does not enable L3 firewall
 * for omap AES HW crypto support. When linux kernel try to access memory of AES
 * blocks then kernel receive "Unhandled fault: external abort on non-linefetch"
 * and crash. Until somebody fix omap-aes.c and omap_hwmod_3xxx_data.c code (no
 * crash anymore) omap AES support will be disabled for all Nokia N900 devices.
 * There is "unofficial" version of bootloader which enables AES in L3 firewall
 * but it is not widely used and to prevent kernel crash rather <PERSON>ES is disabled.
 * There is also no runtime detection code if <PERSON><PERSON> is disabled in L3 firewall...
 */
&aes1_target {
	status = "disabled";
};

&aes2_target {
	status = "disabled";
};

/ {
	model = "Nokia N900";
	compatible = "nokia,omap3-n900", "ti,omap3430", "ti,omap3";

	aliases {
		i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		i2c3 = &i2c3;
		display0 = &lcd;
		display1 = &tv;
	};

	cpus {
		cpu@0 {
			cpu0-supply = <&vcc>;
		};
	};

	leds {
		compatible = "gpio-leds";
		led-heartbeat {
			label = "debug::sleep";
			gpios = <&gpio6 2 GPIO_ACTIVE_HIGH>;  /* 162 */
			linux,default-trigger = "default-on";
			pinctrl-names = "default";
			pinctrl-0 = <&debug_leds>;
		};
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x10000000>; /* 256 MB */
	};

	gpio_keys {
		compatible = "gpio-keys";

		camera_lens_cover {
			label = "Camera Lens Cover";
			gpios = <&gpio4 14 GPIO_ACTIVE_LOW>; /* 110 */
			linux,input-type = <EV_SW>;
			linux,code = <SW_CAMERA_LENS_COVER>;
			linux,can-disable;
		};

		camera_focus {
			label = "Camera Focus";
			gpios = <&gpio3 4 GPIO_ACTIVE_LOW>; /* 68 */
			linux,code = <KEY_CAMERA_FOCUS>;
			linux,can-disable;
		};

		camera_capture {
			label = "Camera Capture";
			gpios = <&gpio3 5 GPIO_ACTIVE_LOW>; /* 69 */
			linux,code = <KEY_CAMERA>;
			linux,can-disable;
		};

		lock_button {
			label = "Lock Button";
			gpios = <&gpio4 17 GPIO_ACTIVE_LOW>; /* 113 */
			linux,code = <KEY_SCREENLOCK>;
			linux,can-disable;
		};

		keypad_slide {
			label = "Keypad Slide";
			gpios = <&gpio3 7 GPIO_ACTIVE_LOW>; /* 71 */
			linux,input-type = <EV_SW>;
			linux,code = <SW_KEYPAD_SLIDE>;
			linux,can-disable;
		};

		proximity_sensor {
			label = "Proximity Sensor";
			gpios = <&gpio3 25 GPIO_ACTIVE_HIGH>; /* 89 */
			linux,input-type = <EV_SW>;
			linux,code = <SW_FRONT_PROXIMITY>;
			linux,can-disable;
		};

		machine_cover {
			label = "Machine Cover";
			gpios = <&gpio6 0 GPIO_ACTIVE_LOW>; /* 160 */
			linux,input-type = <EV_SW>;
			linux,code = <SW_MACHINE_COVER>;
			linux,can-disable;
		};
	};

	isp1707: isp1707 {
		compatible = "nxp,isp1707";
		nxp,enable-gpio = <&gpio3 3 GPIO_ACTIVE_HIGH>;
		usb-phy = <&usb2_phy>;
	};

	tv: connector {
		compatible = "composite-video-connector";
		label = "tv";

		port {
			tv_connector_in: endpoint {
				remote-endpoint = <&venc_out>;
			};
		};
	};

	sound: n900-audio {
		compatible = "nokia,n900-audio";

		nokia,cpu-dai = <&mcbsp2>;
		nokia,audio-codec = <&tlv320aic3x>, <&tlv320aic3x_aux>;
		nokia,headphone-amplifier = <&tpa6130a2>;

		tvout-selection-gpios = <&gpio2 8 GPIO_ACTIVE_HIGH>; /* 40 */
		jack-detection-gpios = <&gpio6 17 GPIO_ACTIVE_HIGH>; /* 177 */
		eci-switch-gpios = <&gpio6 22 GPIO_ACTIVE_HIGH>; /* 182 */
		speaker-amplifier-gpios = <&twl_gpio 7 GPIO_ACTIVE_HIGH>;
	};

	battery: n900-battery {
		compatible = "nokia,n900-battery";
		io-channels = <&twl_madc 0>, <&twl_madc 4>, <&twl_madc 12>;
		io-channel-names = "temp", "bsi", "vbat";
	};

	pwm9: pwm-9 {
		compatible = "ti,omap-dmtimer-pwm";
		#pwm-cells = <3>;
		ti,timers = <&timer9>;
		ti,clock-source = <0x00>; /* timer_sys_ck */
	};

	ir: n900-ir {
		compatible = "nokia,n900-ir";
		pwms = <&pwm9 0 26316 0>; /* 38000 Hz */
	};

	rom_rng: rng {
		compatible = "nokia,n900-rom-rng";
		clocks = <&rng_ick>;
		clock-names = "ick";
	};

	/* controlled (enabled/disabled) directly by bcm2048 and wl1251 */
	vctcxo: vctcxo {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <38400000>;
	};
};

&isp {
	vdds_csib-supply = <&vaux2>;

	pinctrl-names = "default";
	pinctrl-0 = <&camera_pins>;

	ports {
		port@1 {
			reg = <1>;

			csi_isp: endpoint {
				remote-endpoint = <&csi_cam1>;
				bus-type = <MEDIA_BUS_TYPE_CCP2>;
				clock-lanes = <1>;
				data-lanes = <0>;
				lane-polarity = <0 0>;
				/* Select strobe = <1> for back camera, <0> for front camera */
				strobe = <1>;
			};
		};
	};
};

&omap3_pmx_core {
	pinctrl-names = "default";

	uart2_pins: uart2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2174, PIN_INPUT_PULLUP | MUX_MODE0)		/* uart2_cts */
			OMAP3_CORE1_IOPAD(0x2176, PIN_OUTPUT | MUX_MODE0)		/* uart2_rts */
			OMAP3_CORE1_IOPAD(0x2178, PIN_OUTPUT | MUX_MODE0)		/* uart2_tx */
			OMAP3_CORE1_IOPAD(0x217a, PIN_INPUT | MUX_MODE0)		/* uart2_rx */
		>;
	};

	uart3_pins: uart3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x219e, PIN_INPUT | MUX_MODE0)		/* uart3_rx */
			OMAP3_CORE1_IOPAD(0x21a0, PIN_OUTPUT | MUX_MODE0)		/* uart3_tx */
		>;
	};

	ethernet_pins: ethernet-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20b4, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* gpmc_ncs3.gpio_54 */
			OMAP3_CORE1_IOPAD(0x20fc, PIN_OUTPUT | MUX_MODE4)		/* dss_data16.gpio_86 */
			OMAP3_CORE1_IOPAD(0x219c, PIN_OUTPUT | MUX_MODE4)		/* uart3_rts_sd.gpio_164 */
		>;
	};

	gpmc_pins: gpmc-pins {
		pinctrl-single,pins = <

			/* address lines */
			OMAP3_CORE1_IOPAD(0x207a, PIN_OUTPUT | MUX_MODE0)       /* gpmc_a1.gpmc_a1 */
			OMAP3_CORE1_IOPAD(0x207c, PIN_OUTPUT | MUX_MODE0)       /* gpmc_a2.gpmc_a2 */
			OMAP3_CORE1_IOPAD(0x207e, PIN_OUTPUT | MUX_MODE0)       /* gpmc_a3.gpmc_a3 */

			/* data lines, gpmc_d0..d7 not muxable according to TRM */
			OMAP3_CORE1_IOPAD(0x209e, PIN_INPUT | MUX_MODE0)        /* gpmc_d8.gpmc_d8 */
			OMAP3_CORE1_IOPAD(0x20a0, PIN_INPUT | MUX_MODE0)        /* gpmc_d9.gpmc_d9 */
			OMAP3_CORE1_IOPAD(0x20a2, PIN_INPUT | MUX_MODE0)        /* gpmc_d10.gpmc_d10 */
			OMAP3_CORE1_IOPAD(0x20a4, PIN_INPUT | MUX_MODE0)        /* gpmc_d11.gpmc_d11 */
			OMAP3_CORE1_IOPAD(0x20a6, PIN_INPUT | MUX_MODE0)        /* gpmc_d12.gpmc_d12 */
			OMAP3_CORE1_IOPAD(0x20a8, PIN_INPUT | MUX_MODE0)        /* gpmc_d13.gpmc_d13 */
			OMAP3_CORE1_IOPAD(0x20aa, PIN_INPUT | MUX_MODE0)        /* gpmc_d14.gpmc_d14 */
			OMAP3_CORE1_IOPAD(0x20ac, PIN_INPUT | MUX_MODE0)        /* gpmc_d15.gpmc_d15 */

			/*
			 * gpmc_ncs0, gpmc_nadv_ale, gpmc_noe, gpmc_nwe, gpmc_wait0 not muxable
			 * according to TRM. OneNAND seems to require PIN_INPUT on clock.
			 */
			OMAP3_CORE1_IOPAD(0x20b0, PIN_OUTPUT | MUX_MODE0)       /* gpmc_ncs1.gpmc_ncs1 */
			OMAP3_CORE1_IOPAD(0x20be, PIN_INPUT | MUX_MODE0)        /* gpmc_clk.gpmc_clk */
			>;
	};

	i2c1_pins: i2c1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21ba, PIN_INPUT | MUX_MODE0)		/* i2c1_scl */
			OMAP3_CORE1_IOPAD(0x21bc, PIN_INPUT | MUX_MODE0)		/* i2c1_sda */
		>;
	};

	i2c2_pins: i2c2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21be, PIN_INPUT | MUX_MODE0)		/* i2c2_scl */
			OMAP3_CORE1_IOPAD(0x21c0, PIN_INPUT | MUX_MODE0)		/* i2c2_sda */
		>;
	};

	i2c3_pins: i2c3-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21c2, PIN_INPUT | MUX_MODE0)		/* i2c3_scl */
			OMAP3_CORE1_IOPAD(0x21c4, PIN_INPUT | MUX_MODE0)		/* i2c3_sda */
		>;
	};

	debug_leds: debug-led-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2198, PIN_OUTPUT | MUX_MODE4)	/* mcbsp1_clkx.gpio_162 */
		>;
	};

	mcspi4_pins: mcspi4-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x218c, PIN_INPUT_PULLDOWN | MUX_MODE1) /* mcspi4_clk */
			OMAP3_CORE1_IOPAD(0x2192, PIN_INPUT_PULLDOWN | MUX_MODE1) /* mcspi4_somi */
			OMAP3_CORE1_IOPAD(0x2190, PIN_OUTPUT | MUX_MODE1) /* mcspi4_simo */
			OMAP3_CORE1_IOPAD(0x2196, PIN_OUTPUT | MUX_MODE1) /* mcspi4_cs0 */
		>;
	};

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2144, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_clk */
			OMAP3_CORE1_IOPAD(0x2146, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_cmd */
			OMAP3_CORE1_IOPAD(0x2148, PIN_INPUT_PULLUP | MUX_MODE0) 	/* sdmmc1_dat0 */
			OMAP3_CORE1_IOPAD(0x214a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat1 */
			OMAP3_CORE1_IOPAD(0x214c, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat2 */
			OMAP3_CORE1_IOPAD(0x214e, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc1_dat3 */
		>;
	};

	mmc2_pins: mmc2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2158, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_clk */
			OMAP3_CORE1_IOPAD(0x215a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_cmd */
			OMAP3_CORE1_IOPAD(0x215c, PIN_INPUT_PULLUP | MUX_MODE0) 	/* sdmmc2_dat0 */
			OMAP3_CORE1_IOPAD(0x215e, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat1 */
			OMAP3_CORE1_IOPAD(0x2160, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat2 */
			OMAP3_CORE1_IOPAD(0x2162, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat3 */
			OMAP3_CORE1_IOPAD(0x2164, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat4 */
			OMAP3_CORE1_IOPAD(0x2166, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat5 */
			OMAP3_CORE1_IOPAD(0x2168, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat6 */
			OMAP3_CORE1_IOPAD(0x216a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc2_dat7 */
		>;
	};

	acx565akm_pins: acx565akm-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2104, PIN_OUTPUT | MUX_MODE4)		/* RX51_LCD_RESET_GPIO */
		>;
	};

	dss_sdi_pins: dss-sdi-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20f0, PIN_OUTPUT | MUX_MODE1)   /* dss_data10.sdi_dat1n */
			OMAP3_CORE1_IOPAD(0x20f2, PIN_OUTPUT | MUX_MODE1)   /* dss_data11.sdi_dat1p */
			OMAP3_CORE1_IOPAD(0x20f4, PIN_OUTPUT | MUX_MODE1)   /* dss_data12.sdi_dat2n */
			OMAP3_CORE1_IOPAD(0x20f6, PIN_OUTPUT | MUX_MODE1)   /* dss_data13.sdi_dat2p */

			OMAP3_CORE1_IOPAD(0x2108, PIN_OUTPUT | MUX_MODE1)   /* dss_data22.sdi_clkp */
			OMAP3_CORE1_IOPAD(0x210a, PIN_OUTPUT | MUX_MODE1)   /* dss_data23.sdi_clkn */
		>;
	};

	wl1251_pins: wl1251-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20fe, PIN_OUTPUT | MUX_MODE4)		/* gpio 87 => wl1251 enable */
			OMAP3_CORE1_IOPAD(0x208a, PIN_INPUT | MUX_MODE4)		/* gpio 42 => wl1251 irq */
		>;
	};

	ssi_pins: ssi-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2180, PIN_INPUT_PULLUP | MUX_MODE1)	/* ssi1_rdy_tx */
			OMAP3_CORE1_IOPAD(0x217e, PIN_OUTPUT | MUX_MODE1)		/* ssi1_flag_tx */
			OMAP3_CORE1_IOPAD(0x2182, PIN_INPUT | MUX_MODE4)		/* ssi1_wake_tx (cawake) */
			OMAP3_CORE1_IOPAD(0x217c, PIN_OUTPUT | MUX_MODE1)		/* ssi1_dat_tx */
			OMAP3_CORE1_IOPAD(0x2184, PIN_INPUT | MUX_MODE1)		/* ssi1_dat_rx */
			OMAP3_CORE1_IOPAD(0x2186, PIN_INPUT | MUX_MODE1)		/* ssi1_flag_rx */
			OMAP3_CORE1_IOPAD(0x2188, PIN_OUTPUT | MUX_MODE1)		/* ssi1_rdy_rx */
			OMAP3_CORE1_IOPAD(0x218a, PIN_OUTPUT | MUX_MODE1)		/* ssi1_wake */
		>;
	};

	modem_pins: modem-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20dc, PIN_OUTPUT | MUX_MODE4)		/* gpio 70 => cmt_apeslpx */
			OMAP3_CORE1_IOPAD(0x20e0, PIN_INPUT | MUX_MODE4)		/* gpio 72 => ape_rst_rq */
			OMAP3_CORE1_IOPAD(0x20e2, PIN_OUTPUT | MUX_MODE4)		/* gpio 73 => cmt_rst_rq */
			OMAP3_CORE1_IOPAD(0x20e4, PIN_OUTPUT | MUX_MODE4)		/* gpio 74 => cmt_en */
			OMAP3_CORE1_IOPAD(0x20e6, PIN_OUTPUT | MUX_MODE4)		/* gpio 75 => cmt_rst */
			OMAP3_CORE1_IOPAD(0x218e, PIN_OUTPUT | MUX_MODE4)		/* gpio 157 => cmt_bsi */
		>;
	};

	camera_pins: camera-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x210c, PIN_OUTPUT | MUX_MODE7)       /* cam_hs */
			OMAP3_CORE1_IOPAD(0x210e, PIN_OUTPUT | MUX_MODE7)       /* cam_vs */
			OMAP3_CORE1_IOPAD(0x2110, PIN_OUTPUT | MUX_MODE0)       /* cam_xclka */
			OMAP3_CORE1_IOPAD(0x211e, PIN_OUTPUT | MUX_MODE7)       /* cam_d4 */
			OMAP3_CORE1_IOPAD(0x2122, PIN_INPUT | MUX_MODE0)        /* cam_d6 */
			OMAP3_CORE1_IOPAD(0x2124, PIN_INPUT | MUX_MODE0)        /* cam_d7 */
			OMAP3_CORE1_IOPAD(0x2126, PIN_INPUT | MUX_MODE0)        /* cam_d8 */
			OMAP3_CORE1_IOPAD(0x2128, PIN_INPUT | MUX_MODE0)        /* cam_d9 */
			OMAP3_CORE1_IOPAD(0x212a, PIN_OUTPUT | MUX_MODE7)       /* cam_d10 */
			OMAP3_CORE1_IOPAD(0x212e, PIN_OUTPUT | MUX_MODE7)       /* cam_xclkb */
			OMAP3_CORE1_IOPAD(0x2132, PIN_OUTPUT | MUX_MODE0)       /* cam_strobe */
		>;
	};
};

&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins>;

	clock-frequency = <2200000>;

	twl: twl@48 {
		reg = <0x48>;
		interrupts = <7>; /* SYS_NIRQ cascaded to intc */
		interrupt-parent = <&intc>;
	};
};

#include "twl4030.dtsi"
#include "twl4030_omap3.dtsi"

&vaux1 {
	regulator-name = "V28";
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
	regulator-initial-mode = <0x0e>; /* RES_STATE_ACTIVE */
	regulator-always-on; /* due to battery cover sensor */
};

&vaux2 {
	regulator-name = "VCSI";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-initial-mode = <0x0e>; /* RES_STATE_ACTIVE */
};

&vaux3 {
	regulator-name = "VMMC2_30";
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <3000000>;
	regulator-initial-mode = <0x0e>; /* RES_STATE_ACTIVE */
};

&vaux4 {
	regulator-name = "VCAM_ANA_28";
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
	regulator-initial-mode = <0x0e>; /* RES_STATE_ACTIVE */
};

&vmmc1 {
	regulator-name = "VMMC1";
	regulator-min-microvolt = <1850000>;
	regulator-max-microvolt = <3150000>;
	regulator-initial-mode = <0x0e>; /* RES_STATE_ACTIVE */
};

&vmmc2 {
	regulator-name = "V28_A";
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <3000000>;
	regulator-initial-mode = <0x0e>; /* RES_STATE_ACTIVE */
	regulator-always-on; /* due VIO leak to AIC34 VDDs */
};

&vpll1 {
	regulator-name = "VPLL";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-initial-mode = <0x0e>; /* RES_STATE_ACTIVE */
	regulator-always-on;
};

&vpll2 {
	regulator-name = "VSDI_CSI";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-initial-mode = <0x0e>; /* RES_STATE_ACTIVE */
	regulator-always-on;
};

&vsim {
	regulator-name = "VMMC2_IO_18";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-initial-mode = <0x0e>; /* RES_STATE_ACTIVE */
};

&vio {
	regulator-name = "VIO";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
};

&vintana1 {
	regulator-name = "VINTANA1";
	/* fixed to 1500000 */
	regulator-always-on;
};

&vintana2 {
	regulator-name = "VINTANA2";
	regulator-min-microvolt = <2750000>;
	regulator-max-microvolt = <2750000>;
	regulator-always-on;
};

&vintdig {
	regulator-name = "VINTDIG";
	/* fixed to 1500000 */
	regulator-always-on;
};

/* First two dma channels are reserved on secure omap3 */
&sdma {
	dma-channel-mask = <0xfffffffc>;
};

&twl {
	twl_audio: audio {
		compatible = "ti,twl4030-audio";
		ti,enable-vibra = <1>;
	};

	twl_power: power {
		compatible = "ti,twl4030-power-n900", "ti,twl4030-power-idle-osc-off";
		ti,use_poweroff;
	};
};

&twl_keypad {
	linux,keymap = < MATRIX_KEY(0x00, 0x00, KEY_Q)
			 MATRIX_KEY(0x00, 0x01, KEY_O)
			 MATRIX_KEY(0x00, 0x02, KEY_P)
			 MATRIX_KEY(0x00, 0x03, KEY_COMMA)
			 MATRIX_KEY(0x00, 0x04, KEY_BACKSPACE)
			 MATRIX_KEY(0x00, 0x06, KEY_A)
			 MATRIX_KEY(0x00, 0x07, KEY_S)

			 MATRIX_KEY(0x01, 0x00, KEY_W)
			 MATRIX_KEY(0x01, 0x01, KEY_D)
			 MATRIX_KEY(0x01, 0x02, KEY_F)
			 MATRIX_KEY(0x01, 0x03, KEY_G)
			 MATRIX_KEY(0x01, 0x04, KEY_H)
			 MATRIX_KEY(0x01, 0x05, KEY_J)
			 MATRIX_KEY(0x01, 0x06, KEY_K)
			 MATRIX_KEY(0x01, 0x07, KEY_L)

			 MATRIX_KEY(0x02, 0x00, KEY_E)
			 MATRIX_KEY(0x02, 0x01, KEY_DOT)
			 MATRIX_KEY(0x02, 0x02, KEY_UP)
			 MATRIX_KEY(0x02, 0x03, KEY_ENTER)
			 MATRIX_KEY(0x02, 0x05, KEY_Z)
			 MATRIX_KEY(0x02, 0x06, KEY_X)
			 MATRIX_KEY(0x02, 0x07, KEY_C)
			 MATRIX_KEY(0x02, 0x08, KEY_F9)

			 MATRIX_KEY(0x03, 0x00, KEY_R)
			 MATRIX_KEY(0x03, 0x01, KEY_V)
			 MATRIX_KEY(0x03, 0x02, KEY_B)
			 MATRIX_KEY(0x03, 0x03, KEY_N)
			 MATRIX_KEY(0x03, 0x04, KEY_M)
			 MATRIX_KEY(0x03, 0x05, KEY_SPACE)
			 MATRIX_KEY(0x03, 0x06, KEY_SPACE)
			 MATRIX_KEY(0x03, 0x07, KEY_LEFT)

			 MATRIX_KEY(0x04, 0x00, KEY_T)
			 MATRIX_KEY(0x04, 0x01, KEY_DOWN)
			 MATRIX_KEY(0x04, 0x02, KEY_RIGHT)
			 MATRIX_KEY(0x04, 0x04, KEY_LEFTCTRL)
			 MATRIX_KEY(0x04, 0x05, KEY_RIGHTALT)
			 MATRIX_KEY(0x04, 0x06, KEY_LEFTSHIFT)
			 MATRIX_KEY(0x04, 0x08, KEY_F10)

			 MATRIX_KEY(0x05, 0x00, KEY_Y)
			 MATRIX_KEY(0x05, 0x08, KEY_F11)

			 MATRIX_KEY(0x06, 0x00, KEY_U)

			 MATRIX_KEY(0x07, 0x00, KEY_I)
			 MATRIX_KEY(0x07, 0x01, KEY_F7)
			 MATRIX_KEY(0x07, 0x02, KEY_F8)
			 >;
};

&twl_gpio {
	ti,pullups = <0x0>;
	ti,pulldowns = <0x03ff3f>; /* BIT(0..5) | BIT(8..17) */
};

&i2c2 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_pins>;

	clock-frequency = <100000>;

	tlv320aic3x: tlv320aic3x@18 {
		compatible = "ti,tlv320aic3x";
		reg = <0x18>;
		reset-gpios = <&gpio2 28 GPIO_ACTIVE_LOW>; /* 60 */
		ai3x-gpio-func = <
			0 /* AIC3X_GPIO1_FUNC_DISABLED */
			5 /* AIC3X_GPIO2_FUNC_DIGITAL_MIC_INPUT */
		>;

		AVDD-supply = <&vmmc2>;
		DRVDD-supply = <&vmmc2>;
		IOVDD-supply = <&vio>;
		DVDD-supply = <&vio>;

		ai3x-micbias-vg = <1>;
	};

	tlv320aic3x_aux: tlv320aic3x@19 {
		compatible = "ti,tlv320aic3x";
		reg = <0x19>;
		reset-gpios = <&gpio2 28 GPIO_ACTIVE_LOW>; /* 60 */

		AVDD-supply = <&vmmc2>;
		DRVDD-supply = <&vmmc2>;
		IOVDD-supply = <&vio>;
		DVDD-supply = <&vio>;

		ai3x-micbias-vg = <2>;
	};

	tsl2563: tsl2563@29 {
		compatible = "amstaos,tsl2563";
		reg = <0x29>;

		amstaos,cover-comp-gain = <16>;
	};

	adp1653: led-controller@30 {
		compatible = "adi,adp1653";
		reg = <0x30>;
		enable-gpios = <&gpio3 24 GPIO_ACTIVE_HIGH>; /* 88 */

		flash {
			flash-timeout-us = <500000>;
			flash-max-microamp = <320000>;
			led-max-microamp = <50000>;
		};
		indicator {
			led-max-microamp = <17500>;
		};
	};

	lp5523: lp5523@32 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "national,lp5523";
		reg = <0x32>;
		clock-mode = /bits/ 8 <0>; /* LP55XX_CLOCK_AUTO */
		enable-gpios = <&gpio2 9 GPIO_ACTIVE_HIGH>; /* 41 */

		led@0 {
			reg = <0>;
			chan-name = "lp5523:kb1";
			led-cur = /bits/ 8 <50>;
			max-cur = /bits/ 8 <100>;
			color = <LED_COLOR_ID_WHITE>;
			function = LED_FUNCTION_KBD_BACKLIGHT;
		};

		led@1 {
			reg = <1>;
			chan-name = "lp5523:kb2";
			led-cur = /bits/ 8 <50>;
			max-cur = /bits/ 8 <100>;
			color = <LED_COLOR_ID_WHITE>;
			function = LED_FUNCTION_KBD_BACKLIGHT;
		};

		led@2 {
			reg = <2>;
			chan-name = "lp5523:kb3";
			led-cur = /bits/ 8 <50>;
			max-cur = /bits/ 8 <100>;
			color = <LED_COLOR_ID_WHITE>;
			function = LED_FUNCTION_KBD_BACKLIGHT;
		};

		led@3 {
			reg = <3>;
			chan-name = "lp5523:kb4";
			led-cur = /bits/ 8 <50>;
			max-cur = /bits/ 8 <100>;
			color = <LED_COLOR_ID_WHITE>;
			function = LED_FUNCTION_KBD_BACKLIGHT;
		};

		led@4 {
			reg = <4>;
			chan-name = "lp5523:b";
			led-cur = /bits/ 8 <50>;
			max-cur = /bits/ 8 <100>;
			color = <LED_COLOR_ID_BLUE>;
			function = LED_FUNCTION_STATUS;
		};

		led@5 {
			reg = <5>;
			chan-name = "lp5523:g";
			led-cur = /bits/ 8 <50>;
			max-cur = /bits/ 8 <100>;
			color = <LED_COLOR_ID_GREEN>;
			function = LED_FUNCTION_STATUS;
		};

		led@6 {
			reg = <6>;
			chan-name = "lp5523:r";
			led-cur = /bits/ 8 <50>;
			max-cur = /bits/ 8 <100>;
			color = <LED_COLOR_ID_RED>;
			function = LED_FUNCTION_STATUS;
		};

		led@7 {
			reg = <7>;
			chan-name = "lp5523:kb5";
			led-cur = /bits/ 8 <50>;
			max-cur = /bits/ 8 <100>;
			color = <LED_COLOR_ID_WHITE>;
			function = LED_FUNCTION_KBD_BACKLIGHT;
		};

		led@8 {
			reg = <8>;
			chan-name = "lp5523:kb6";
			led-cur = /bits/ 8 <50>;
			max-cur = /bits/ 8 <100>;
			color = <LED_COLOR_ID_WHITE>;
			function = LED_FUNCTION_KBD_BACKLIGHT;
		};
	};

	bq27200: bq27200@55 {
		compatible = "ti,bq27200";
		reg = <0x55>;
		power-supplies = <&bq24150a>;
	};

	/* Stereo headphone amplifier */
	tpa6130a2: tpa6130a2@60 {
		compatible = "ti,tpa6130a2";
		reg = <0x60>;

		Vdd-supply = <&vmmc2>;

		power-gpio = <&gpio4 2 GPIO_ACTIVE_HIGH>; /* 98 */
	};

	si4713: si4713@63 {
		compatible = "silabs,si4713";
		reg = <0x63>;

		interrupts-extended = <&gpio2 21 IRQ_TYPE_EDGE_FALLING>; /* 53 */
		reset-gpios = <&gpio6 3 GPIO_ACTIVE_HIGH>; /* 163 */
		vio-supply = <&vio>;
		vdd-supply = <&vaux1>;
	};

	bq24150a: bq24150a@6b {
		compatible = "ti,bq24150a";
		reg = <0x6b>;

		ti,current-limit = <100>;
		ti,weak-battery-voltage = <3400>;
		ti,battery-regulation-voltage = <4200>;
		ti,charge-current = <650>;
		ti,termination-current = <100>;
		ti,resistor-sense = <68>;

		ti,usb-charger-detection = <&isp1707>;
	};
};

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;

	clock-frequency = <400000>;

	accelerometer@1d {
		compatible = "st,lis302dl";
		reg = <0x1d>;

		vdd-supply = <&vaux1>;
		vddio-supply = <&vio>;

		interrupt-parent = <&gpio6>;
		interrupts = <21 IRQ_TYPE_EDGE_RISING>,
			     <20 IRQ_TYPE_EDGE_RISING>; /* 181 and 180 */

		mount-matrix =	 "-1",  "0",  "0",
				  "0",  "1",  "0",
				  "0",  "0",  "-1";
	};

	cam1: camera@3e {
		compatible = "toshiba,et8ek8";
		reg = <0x3e>;

		vana-supply = <&vaux4>;

		clocks = <&isp 0>;
		clock-names = "extclk";
		clock-frequency = <9600000>;

		reset-gpio = <&gpio4 6 GPIO_ACTIVE_HIGH>; /* 102 */

		lens-focus = <&ad5820>;

		port {
			csi_cam1: endpoint {
				bus-type = <MEDIA_BUS_TYPE_CCP2>;
				strobe = <1>;
				clock-inv = <0>;
				crc = <1>;

				remote-endpoint = <&csi_isp>;
			};
		};
	};

	/* D/A converter for auto-focus */
	ad5820: dac@c {
		compatible = "adi,ad5820";
		reg = <0x0c>;

		VANA-supply = <&vaux4>;

		#io-channel-cells = <0>;
	};
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
	vmmc-supply = <&vmmc1>;
	bus-width = <4>;
};

/* most boards use vaux3, only some old versions use vmmc2 instead */
&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_pins>;
	vmmc-supply = <&vaux3>;
	vqmmc-supply = <&vsim>;
	bus-width = <8>;
	non-removable;
	no-sdio;
	no-sd;
};

&mmc3 {
	status = "disabled";
};

&gpmc {
	ranges = <0 0 0x01000000 0x01000000>,	/* 16 MB for OneNAND */
		 <1 0 0x02000000 0x01000000>;	/* 16 MB for smc91c96 */
	pinctrl-names = "default";
	pinctrl-0 = <&gpmc_pins>;

	/* sys_ndmareq1 could be used by the driver, not as gpio65 though */
	onenand@0,0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "ti,omap2-onenand";
		reg = <0 0 0x20000>;	/* CS0, offset 0, IO size 128K */

		/*
		 * These timings are based on CONFIG_OMAP_GPMC_DEBUG=y reported
		 * bootloader set values when booted with v5.1
		 * (OneNAND Manufacturer: Samsung):
		 *
		 *   cs0 GPMC_CS_CONFIG1: 0xfb001202
		 *   cs0 GPMC_CS_CONFIG2: 0x00111100
		 *   cs0 GPMC_CS_CONFIG3: 0x00020200
		 *   cs0 GPMC_CS_CONFIG4: 0x11001102
		 *   cs0 GPMC_CS_CONFIG5: 0x03101616
		 *   cs0 GPMC_CS_CONFIG6: 0x90060000
		 */
		gpmc,sync-read;
		gpmc,sync-write;
		gpmc,burst-length = <16>;
		gpmc,burst-read;
		gpmc,burst-wrap;
		gpmc,burst-write;
		gpmc,device-width = <2>;
		gpmc,mux-add-data = <2>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <102>;
		gpmc,cs-wr-off-ns = <102>;
		gpmc,adv-on-ns = <0>;
		gpmc,adv-rd-off-ns = <12>;
		gpmc,adv-wr-off-ns = <12>;
		gpmc,oe-on-ns = <12>;
		gpmc,oe-off-ns = <102>;
		gpmc,we-on-ns = <0>;
		gpmc,we-off-ns = <102>;
		gpmc,rd-cycle-ns = <132>;
		gpmc,wr-cycle-ns = <132>;
		gpmc,access-ns = <96>;
		gpmc,page-burst-access-ns = <18>;
		gpmc,bus-turnaround-ns = <0>;
		gpmc,cycle2cycle-delay-ns = <0>;
		gpmc,wait-monitoring-ns = <0>;
		gpmc,clk-activation-ns = <6>;
		gpmc,wr-data-mux-bus-ns = <36>;
		gpmc,wr-access-ns = <96>;
		gpmc,sync-clk-ps = <15000>;

		/*
		 * MTD partition table corresponding to Nokia's
		 * Maemo 5 (Fremantle) release.
		 */
		partition@0 {
			label = "bootloader";
			reg = <0x00000000 0x00020000>;
			read-only;
		};
		partition@1 {
			label = "config";
			reg = <0x00020000 0x00060000>;
		};
		partition@2 {
			label = "log";
			reg = <0x00080000 0x00040000>;
		};
		partition@3 {
			label = "kernel";
			reg = <0x000c0000 0x00200000>;
		};
		partition@4 {
			label = "initfs";
			reg = <0x002c0000 0x00200000>;
		};
		partition@5 {
			label = "rootfs";
			reg = <0x004c0000 0x0fb40000>;
		};
	};

	/* Ethernet is on some early development boards and qemu */
	ethernet@gpmc {
		compatible = "smsc,lan91c94";
		interrupt-parent = <&gpio2>;
		interrupts = <22 IRQ_TYPE_LEVEL_HIGH>;	/* gpio54 */
		reg = <1 0 0xf>;		/* 16 byte IO range */
		bank-width = <2>;
		pinctrl-names = "default";
		pinctrl-0 = <&ethernet_pins>;
		power-gpios = <&gpio3 22 GPIO_ACTIVE_HIGH>;	/* gpio86 */
		reset-gpios = <&gpio6 4 GPIO_ACTIVE_HIGH>;	/* gpio164 */
		gpmc,device-width = <2>;
		gpmc,sync-clk-ps = <0>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <48>;
		gpmc,cs-wr-off-ns = <24>;
		gpmc,adv-on-ns = <0>;
		gpmc,adv-rd-off-ns = <0>;
		gpmc,adv-wr-off-ns = <0>;
		gpmc,we-on-ns = <12>;
		gpmc,we-off-ns = <18>;
		gpmc,oe-on-ns = <12>;
		gpmc,oe-off-ns = <48>;
		gpmc,page-burst-access-ns = <0>;
		gpmc,access-ns = <42>;
		gpmc,rd-cycle-ns = <180>;
		gpmc,wr-cycle-ns = <180>;
		gpmc,bus-turnaround-ns = <0>;
		gpmc,cycle2cycle-delay-ns = <0>;
		gpmc,wait-monitoring-ns = <0>;
		gpmc,clk-activation-ns = <0>;
		gpmc,wr-access-ns = <0>;
		gpmc,wr-data-mux-bus-ns = <12>;
	};
};

&mcspi1 {
	/*
	 * For some reason, touchscreen is necessary for screen to work at
	 * all on real hw. It works well without it on emulator.
	 *
	 * Also... order in the device tree actually matters here.
	 */
	tsc2005@0 {
		compatible = "ti,tsc2005";
		spi-max-frequency = <6000000>;
		reg = <0>;

		vio-supply = <&vio>;

		reset-gpios = <&gpio4 8 GPIO_ACTIVE_HIGH>; /* 104 */
		interrupts-extended = <&gpio4 4 IRQ_TYPE_EDGE_RISING>; /* 100 */

		touchscreen-fuzz-x = <4>;
		touchscreen-fuzz-y = <7>;
		touchscreen-fuzz-pressure = <2>;
		touchscreen-size-x = <4096>;
		touchscreen-size-y = <4096>;
		touchscreen-max-pressure = <2048>;

		ti,x-plate-ohms = <280>;
		ti,esd-recovery-timeout-ms = <8000>;
	};

	lcd: acx565akm@2 {
		compatible = "sony,acx565akm";
		spi-max-frequency = <6000000>;
		reg = <2>;

		pinctrl-names = "default";
		pinctrl-0 = <&acx565akm_pins>;

		label = "lcd";
		reset-gpios = <&gpio3 26 GPIO_ACTIVE_HIGH>; /* 90 */

		port {
			lcd_in: endpoint {
				remote-endpoint = <&sdi_out>;
			};
		};
	};
};

&mcspi4 {
	pinctrl-names = "default";
	pinctrl-0 = <&mcspi4_pins>;

	wl1251@0 {
		pinctrl-names = "default";
		pinctrl-0 = <&wl1251_pins>;

		vio-supply = <&vio>;

		compatible = "ti,wl1251";
		reg = <0>;
		spi-max-frequency = <48000000>;

		spi-cpol;
		spi-cpha;

		ti,power-gpio = <&gpio3 23 GPIO_ACTIVE_HIGH>; /* 87 */

		interrupt-parent = <&gpio2>;
		interrupts = <10 IRQ_TYPE_NONE>; /* gpio line 42 */

		clocks = <&vctcxo>;
	};
};

/* RNG not directly accessible on n900, see omap3-rom-rng instead */
&rng_target {
	status = "disabled";
};

&usb_otg_hs {
	interface-type = <0>;
	usb-phy = <&usb2_phy>;
	phys = <&usb2_phy>;
	phy-names = "usb2-phy";
	mode = <2>;
	power = <50>;
};

&uart1 {
	status = "disabled";
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pins>;

	bcm2048: bluetooth {
		compatible = "brcm,bcm2048-nokia", "nokia,h4p-bluetooth";
		reset-gpios = <&gpio3 27 GPIO_ACTIVE_LOW>; /* 91 */
		host-wakeup-gpios = <&gpio4 5 GPIO_ACTIVE_HIGH>; /* 101 */
		bluetooth-wakeup-gpios = <&gpio2 5 GPIO_ACTIVE_HIGH>; /* 37 */
		clocks = <&vctcxo>;
		clock-names = "sysclk";
	};
};

&uart3 {
	interrupts-extended = <&intc 74 &omap3_pmx_core OMAP3_UART3_RX>;
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>;
};

&dss {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&dss_sdi_pins>;

	vdds_sdi-supply = <&vaux1>;

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@1 {
			reg = <1>;

			sdi_out: endpoint {
				remote-endpoint = <&lcd_in>;
				datapairs = <2>;
			};
		};
	};
};

&venc {
	status = "okay";

	vdda-supply = <&vdac>;

	port {
		venc_out: endpoint {
			remote-endpoint = <&tv_connector_in>;
			ti,channels = <1>;
		};
	};
};

&mcbsp2 {
	status = "okay";
};

&ssi_port1 {
	pinctrl-names = "default";
	pinctrl-0 = <&ssi_pins>;

	ti,ssi-cawake-gpio = <&gpio5 23 GPIO_ACTIVE_HIGH>; /* 151 */

	modem: hsi-client {
		compatible = "nokia,n900-modem";

		pinctrl-names = "default";
		pinctrl-0 = <&modem_pins>;

		hsi-channel-ids = <0>, <1>, <2>, <3>;
		hsi-channel-names = "mcsaab-control",
				    "speech-control",
				    "speech-data",
				    "mcsaab-data";
		hsi-speed-kbps = <55000>;
		hsi-mode = "frame";
		hsi-flow = "synchronized";
		hsi-arb-mode = "round-robin";

		interrupts-extended = <&gpio3 8 IRQ_TYPE_EDGE_FALLING>; /* 72 */

		gpios = <&gpio3  6 GPIO_ACTIVE_HIGH>, /* 70 */
			<&gpio3  9 GPIO_ACTIVE_HIGH>, /* 73 */
			<&gpio3 10 GPIO_ACTIVE_HIGH>, /* 74 */
			<&gpio3 11 GPIO_ACTIVE_HIGH>, /* 75 */
			<&gpio5 29 GPIO_ACTIVE_HIGH>; /* 157 */
		gpio-names = "cmt_apeslpx",
			     "cmt_rst_rq",
			     "cmt_en",
			     "cmt_rst",
			     "cmt_bsi";
	};
};

&ssi_port2 {
	status = "disabled";
};
