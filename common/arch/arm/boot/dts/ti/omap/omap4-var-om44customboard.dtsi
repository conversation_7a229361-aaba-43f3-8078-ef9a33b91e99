// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 */

#include <dt-bindings/input/input.h>

/ {
	aliases {
		display0 = &hdmi0;
	};

	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_led_pins>;

		led0 {
			label = "var:green:led0";
			gpios = <&gpio6 13 GPIO_ACTIVE_HIGH>; /* gpio 173 */
			linux,default-trigger = "heartbeat";
		};

		led1 {
			label = "var:green:led1";
			gpios = <&gpio6 12 GPIO_ACTIVE_HIGH>; /* gpio 172 */
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_key_pins>;
		#address-cells = <1>;
		#size-cells = <0>;

		user-key@184 {
			label = "user";
			gpios = <&gpio6 24 GPIO_ACTIVE_HIGH>; /* gpio 184 */
			linux,code = <BTN_EXTRA>;
			wakeup-source;
		};
	};

	hdmi0: connector {
		compatible = "hdmi-connector";
		pinctrl-names = "default";
		pinctrl-0 = <&hdmi_hpd_pins>;
		label = "hdmi";
		type = "a";

		hpd-gpios = <&gpio2 31 GPIO_ACTIVE_HIGH>; /* gpio_63 */

		port {
			hdmi_connector_in: endpoint {
				remote-endpoint = <&hdmi_out>;
			};
		};
	};
};

&omap4_pmx_core {
	uart1_pins: uart1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x13c, PIN_INPUT_PULLUP | MUX_MODE1)	/* mcspi1_cs2.uart1_cts */
			OMAP4_IOPAD(0x13e, PIN_OUTPUT | MUX_MODE1)		/* mcspi1_cs3.uart1_rts */
			OMAP4_IOPAD(0x126, PIN_INPUT_PULLUP | MUX_MODE1)	/* i2c2_scl.uart1_rx */
			OMAP4_IOPAD(0x128, PIN_OUTPUT | MUX_MODE1)		/* i2c2_sda.uart1_tx */
		>;
	};

	mcspi1_pins: mcspi1-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x132, PIN_INPUT | MUX_MODE0)		/*  mcspi1_clk.mcspi1_clk */
			OMAP4_IOPAD(0x134, PIN_INPUT | MUX_MODE0)		/*  mcspi1_somi.mcspi1_somi */
			OMAP4_IOPAD(0x136, PIN_INPUT | MUX_MODE0)		/*  mcspi1_simo.mcspi1_simo */
			OMAP4_IOPAD(0x138, PIN_INPUT | MUX_MODE0)		/*  mcspi1_cs0.mcspi1_cs0 */
		>;
	};

	mcasp_pins: mcsasp-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x0f8, PIN_OUTPUT | MUX_MODE2)		/*  mcbsp2_dr.abe_mcasp_axr */
		>;
	};

	dss_dpi_pins: dss-dpi-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x162, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data23 */
			OMAP4_IOPAD(0x164, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data22 */
			OMAP4_IOPAD(0x166, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data21 */
			OMAP4_IOPAD(0x168, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data20 */
			OMAP4_IOPAD(0x16a, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data19 */
			OMAP4_IOPAD(0x16c, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data18 */
			OMAP4_IOPAD(0x16e, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data15 */
			OMAP4_IOPAD(0x170, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data14 */
			OMAP4_IOPAD(0x172, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data13 */
			OMAP4_IOPAD(0x174, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data12 */
			OMAP4_IOPAD(0x176, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data11 */
			OMAP4_IOPAD(0x1b4, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data10 */
			OMAP4_IOPAD(0x1b6, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data9 */
			OMAP4_IOPAD(0x1b8, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data16 */
			OMAP4_IOPAD(0x1ba, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data17 */
			OMAP4_IOPAD(0x1bc, PIN_OUTPUT | MUX_MODE5)		/* dispc2_hsync */
			OMAP4_IOPAD(0x1be, PIN_OUTPUT | MUX_MODE5)		/* dispc2_pclk */
			OMAP4_IOPAD(0x1c0, PIN_OUTPUT | MUX_MODE5)		/* dispc2_vsync */
			OMAP4_IOPAD(0x1c2, PIN_OUTPUT | MUX_MODE5)		/* dispc2_de */
			OMAP4_IOPAD(0x1c4, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data8 */
			OMAP4_IOPAD(0x1c6, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data7 */
			OMAP4_IOPAD(0x1c8, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data6 */
			OMAP4_IOPAD(0x1ca, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data5 */
			OMAP4_IOPAD(0x1cc, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data4 */
			OMAP4_IOPAD(0x1ce, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data3 */
			OMAP4_IOPAD(0x1d0, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data2 */
			OMAP4_IOPAD(0x1d2, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data1 */
			OMAP4_IOPAD(0x1d4, PIN_OUTPUT | MUX_MODE5)		/* dispc2_data0 */
		>;
	};

	dss_hdmi_pins: dss-hdmi-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x09a, PIN_INPUT | MUX_MODE0)		/* hdmi_cec.hdmi_cec */
			OMAP4_IOPAD(0x09c, PIN_INPUT_PULLUP | MUX_MODE0)	/* hdmi_scl.hdmi_scl */
			OMAP4_IOPAD(0x09e, PIN_INPUT_PULLUP | MUX_MODE0)	/* hdmi_sda.hdmi_sda */
		>;
	};

	i2c4_pins: i2c4-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x12e, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c4_scl */
			OMAP4_IOPAD(0x130, PIN_INPUT_PULLUP | MUX_MODE0)	/* i2c4_sda */
		>;
	};

	mmc5_pins: mmc5-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x0f6, PIN_INPUT | MUX_MODE3)		/* abe_mcbsp2_clkx.gpio_110 */
			OMAP4_IOPAD(0x148, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_clk.sdmmc5_clk */
			OMAP4_IOPAD(0x14a, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_cmd.sdmmc5_cmd */
			OMAP4_IOPAD(0x14c, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_dat0.sdmmc5_dat0 */
			OMAP4_IOPAD(0x14e, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_dat1.sdmmc5_dat1 */
			OMAP4_IOPAD(0x150, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_dat2.sdmmc5_dat2 */
			OMAP4_IOPAD(0x152, PIN_INPUT_PULLUP | MUX_MODE0)	/* sdmmc5_dat3.sdmmc5_dat3 */
		>;
	};

	gpio_led_pins: gpio-led-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x17e, PIN_OUTPUT | MUX_MODE3)		/* kpd_col4.gpio_172 */
			OMAP4_IOPAD(0x180, PIN_OUTPUT | MUX_MODE3)		/* kpd_col5.gpio_173 */
		>;
	};

	gpio_key_pins: gpio-key-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x1a2, PIN_INPUT | MUX_MODE3)		/* sys_boot0.gpio_184 */
		>;
	};

	ks8851_irq_pins: ks8851-irq-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x17c, PIN_INPUT_PULLUP | MUX_MODE3)	/* kpd_col3.gpio_171 */
		>;
	};

	hdmi_hpd_pins: hdmi-hpd-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x098, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* hdmi_hpd.gpio_63 */
		>;
	};

	backlight_pins: backlight-pins {
		pinctrl-single,pins = <
			OMAP4_IOPAD(0x116, PIN_OUTPUT | MUX_MODE3)		/* abe_dmic_din3.gpio_122 */
		>;
	};
};

&i2c4 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4_pins>;
	clock-frequency = <400000>;
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pins>;
	status = "okay";
};

&mcspi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mcspi1_pins>;
	status = "okay";

	eth@0 {
		compatible = "ks8851";
		pinctrl-names = "default";
		pinctrl-0 = <&ks8851_irq_pins>;
		spi-max-frequency = <24000000>;
		reg = <0>;
		interrupt-parent = <&gpio6>;
		interrupts = <11 IRQ_TYPE_LEVEL_LOW>; /* gpio 171 */
	};
};

&mmc5 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc5_pins>;
	vmmc-supply = <&vbat>;
	bus-width = <4>;
	cd-gpios = <&gpio4 14 GPIO_ACTIVE_HIGH>; /* gpio 110 */
	status = "okay";
};

&dss {
	status = "okay";
};

&hdmi {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&dss_hdmi_pins>;
	vdda-supply = <&vdac>;

	port {
		hdmi_out: endpoint {
			remote-endpoint = <&hdmi_connector_in>;
		};
	};
};
