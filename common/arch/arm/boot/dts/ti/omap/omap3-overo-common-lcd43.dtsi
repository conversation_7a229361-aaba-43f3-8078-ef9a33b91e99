// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014 Florian <PERSON>, EPFL Mobots group
 */

/*
 * 4.3'' LCD panel output for some Gumstix Overo boards (Gallop43, Chestnut43)
 */

&omap3_pmx_core {
	dss_dpi_pins: dss-dpi-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20d4, PIN_OUTPUT | MUX_MODE0)	/* dss_pclk.dss_pclk */
			OMAP3_CORE1_IOPAD(0x20d6, PIN_OUTPUT | MUX_MODE0)	/* dss_hsync.dss_hsync */
			OMAP3_CORE1_IOPAD(0x20d8, PIN_OUTPUT | MUX_MODE0)	/* dss_vsync.dss_vsync */
			OMAP3_CORE1_IOPAD(0x20da, PIN_OUTPUT | MUX_MODE0)	/* dss_acbias.dss_acbias */
			OMAP3_CORE1_IOPAD(0x20dc, PIN_OUTPUT | MUX_MODE0)	/* dss_data0.dss_data0 */
			OMAP3_CORE1_IOPAD(0x20de, PIN_OUTPUT | MUX_MODE0)	/* dss_data1.dss_data1 */
			OMAP3_CORE1_IOPAD(0x20e0, PIN_OUTPUT | MUX_MODE0)	/* dss_data2.dss_data2 */
			OMAP3_CORE1_IOPAD(0x20e2, PIN_OUTPUT | MUX_MODE0)	/* dss_data3.dss_data3 */
			OMAP3_CORE1_IOPAD(0x20e4, PIN_OUTPUT | MUX_MODE0)	/* dss_data4.dss_data4 */
			OMAP3_CORE1_IOPAD(0x20e6, PIN_OUTPUT | MUX_MODE0)	/* dss_data5.dss_data5 */
			OMAP3_CORE1_IOPAD(0x20e8, PIN_OUTPUT | MUX_MODE0)	/* dss_data6.dss_data6 */
			OMAP3_CORE1_IOPAD(0x20ea, PIN_OUTPUT | MUX_MODE0)	/* dss_data7.dss_data7 */
			OMAP3_CORE1_IOPAD(0x20ec, PIN_OUTPUT | MUX_MODE0)	/* dss_data8.dss_data8 */
			OMAP3_CORE1_IOPAD(0x20ee, PIN_OUTPUT | MUX_MODE0)	/* dss_data9.dss_data9 */
			OMAP3_CORE1_IOPAD(0x20f0, PIN_OUTPUT | MUX_MODE0)	/* dss_data10.dss_data10 */
			OMAP3_CORE1_IOPAD(0x20f2, PIN_OUTPUT | MUX_MODE0)	/* dss_data11.dss_data11 */
			OMAP3_CORE1_IOPAD(0x20f4, PIN_OUTPUT | MUX_MODE0)	/* dss_data12.dss_data12 */
			OMAP3_CORE1_IOPAD(0x20f6, PIN_OUTPUT | MUX_MODE0)	/* dss_data13.dss_data13 */
			OMAP3_CORE1_IOPAD(0x20f8, PIN_OUTPUT | MUX_MODE0)	/* dss_data14.dss_data14 */
			OMAP3_CORE1_IOPAD(0x20fa, PIN_OUTPUT | MUX_MODE0)	/* dss_data15.dss_data15 */
			OMAP3_CORE1_IOPAD(0x20fc, PIN_OUTPUT | MUX_MODE0)	/* dss_data16.dss_data16 */
			OMAP3_CORE1_IOPAD(0x20fe, PIN_OUTPUT | MUX_MODE0)	/* dss_data17.dss_data17 */
			OMAP3_CORE1_IOPAD(0x2100, PIN_OUTPUT | MUX_MODE0)	/* dss_data18.dss_data18 */
			OMAP3_CORE1_IOPAD(0x2102, PIN_OUTPUT | MUX_MODE0)	/* dss_data19.dss_data19 */
			OMAP3_CORE1_IOPAD(0x2104, PIN_OUTPUT | MUX_MODE0)	/* dss_data20.dss_data20 */
			OMAP3_CORE1_IOPAD(0x2106, PIN_OUTPUT | MUX_MODE0)	/* dss_data21.dss_data21 */
			OMAP3_CORE1_IOPAD(0x2108, PIN_OUTPUT | MUX_MODE0)	/* dss_data22.dss_data22 */
			OMAP3_CORE1_IOPAD(0x210a, PIN_OUTPUT | MUX_MODE0)	/* dss_data23.dss_data23 */
		>;
	};

	lte430_pins: lte430-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2174, PIN_OUTPUT | MUX_MODE4)	/* uart2_cts.gpio_144 */
		>;
	};

	backlight_pins: backlight-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2176, PIN_OUTPUT | MUX_MODE4)	/* uart2_rts.gpio_145 */
		>;
	};

	mcspi1_pins: mcspi1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21c8, PIN_INPUT | MUX_MODE0)	/* mcspi1_clk.mcspi1_clk */
			OMAP3_CORE1_IOPAD(0x21ca, PIN_INPUT | MUX_MODE0)	/* mcspi1_simo.mcspi1_simo */
			OMAP3_CORE1_IOPAD(0x21cc, PIN_INPUT | MUX_MODE0)	/* mcspi1_somi.mcspi1_somi */
			OMAP3_CORE1_IOPAD(0x21ce, PIN_INPUT | MUX_MODE0)	/* mcspi1_cs0.mcspi1_cs0 */
		>;
	};

	ads7846_pins: ads7846-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2138, PIN_INPUT_PULLDOWN | MUX_MODE4)	/* csi2_dx1.gpio_114 */
		>;
	};
};

/* Needed to power the DPI pins */
&vpll2 {
	regulator-always-on;
};

&dss {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&dss_dpi_pins>;

	port {
		dpi_out: endpoint {
			remote-endpoint = <&lcd_in>;
			data-lines = <24>;
		};
	};
};

/ {
	aliases {
		display0 = &lcd0;
	};

	lcd0: display {
		compatible = "samsung,lte430wq-f0c", "panel-dpi";
		label = "lcd43";

		pinctrl-names = "default";
		pinctrl-0 = <&lte430_pins>;
		enable-gpios = <&gpio5 16 GPIO_ACTIVE_HIGH>;		/* gpio_144 */

		port {
			lcd_in: endpoint {
				remote-endpoint = <&dpi_out>;
			};
		};

		panel-timing {
			clock-frequency = <9200000>;
			hactive = <480>;
			vactive = <272>;
			hfront-porch = <8>;
			hback-porch = <4>;
			hsync-len = <41>;
			vback-porch = <2>;
			vfront-porch = <4>;
			vsync-len = <10>;

			hsync-active = <0>;
			vsync-active = <0>;
			de-active = <1>;
			pixelclk-active = <1>;
		};
	};

	ads7846reg: ads7846-reg {
		compatible = "regulator-fixed";
		regulator-name = "ads7846-reg";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	backlight {
		compatible = "gpio-backlight";
		
		pinctrl-names = "default";
		pinctrl-0 = <&backlight_pins>;
		gpios = <&gpio5 17 GPIO_ACTIVE_HIGH>;		/* gpio_145 */

		default-on;
	};
};

&mcspi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mcspi1_pins>;

	/* touch controller */
	ads7846@0 {
		pinctrl-names = "default";
		pinctrl-0 = <&ads7846_pins>;

		compatible = "ti,ads7846";
		vcc-supply = <&ads7846reg>;

		reg = <0>;				/* CS0 */
		spi-max-frequency = <1500000>;

		interrupt-parent = <&gpio4>;
		interrupts = <18 0>;			/* gpio_114 */
		pendown-gpio = <&gpio4 18 GPIO_ACTIVE_LOW>;

		ti,x-min = /bits/ 16 <0x0>;
		ti,x-max = /bits/ 16 <0x0fff>;
		ti,y-min = /bits/ 16 <0x0>;
		ti,y-max = /bits/ 16 <0x0fff>;
		ti,x-plate-ohms = /bits/ 16 <180>;
		ti,pressure-max = /bits/ 16 <255>;

		wakeup-source;
	};
};

