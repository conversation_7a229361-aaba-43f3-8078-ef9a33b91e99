// SPDX-License-Identifier: GPL-2.0-only
/*
 * Common Device Tree Source for IGEPv2
 *
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 * Copyright (C) 2014 Enric Balletbo i Serra <<EMAIL>>
 */

#include "omap3-igep.dtsi"
#include "omap-gpmc-smsc9221.dtsi"

/ {

	leds {
		pinctrl-names = "default";
		pinctrl-0 = <&leds_pins>;
		compatible = "gpio-leds";

		boot {
			 label = "omap3:green:boot";
			 gpios = <&gpio1 26 GPIO_ACTIVE_HIGH>;
			 default-state = "on";
		};

		user0 {
			 label = "omap3:red:user0";
			 gpios = <&gpio1 27 GPIO_ACTIVE_HIGH>;
			 default-state = "off";
		};

		user1 {
			 label = "omap3:red:user1";
			 gpios = <&gpio1 28 GPIO_ACTIVE_HIGH>;
			 default-state = "off";
		};

		user2 {
			label = "omap3:green:user1";
			gpios = <&twl_gpio 19 GPIO_ACTIVE_LOW>;
		};
	};

	/* HS USB Port 1 Power */
	hsusb1_power: hsusb1_power_reg {
		compatible = "regulator-fixed";
		regulator-name = "hsusb1_vbus";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&twl_gpio 18 GPIO_ACTIVE_LOW>;	/* GPIO LEDA */
		startup-delay-us = <70000>;
	};

	/* HS USB Host PHY on PORT 1 */
	hsusb1_phy: hsusb1_phy {
		compatible = "usb-nop-xceiv";
		reset-gpios = <&gpio1 24 GPIO_ACTIVE_LOW>; /* gpio_24 */
		vcc-supply = <&hsusb1_power>;
		#phy-cells = <0>;
	};

	tfp410: encoder {
		compatible = "ti,tfp410";
		powerdown-gpios = <&gpio6 10 GPIO_ACTIVE_LOW>; /* gpio_170 */

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				tfp410_in: endpoint {
					remote-endpoint = <&dpi_out>;
				};
			};

			port@1 {
				reg = <1>;

				tfp410_out: endpoint {
					remote-endpoint = <&dvi_connector_in>;
				};
			};
		};
	};

	dvi0: connector {
		compatible = "dvi-connector";
		label = "dvi";

		digital;

		ddc-i2c-bus = <&i2c3>;

		port {
			dvi_connector_in: endpoint {
				remote-endpoint = <&tfp410_out>;
			};
		};
	};
};

&omap3_pmx_core {
	pinctrl-names = "default";
	pinctrl-0 = <
		&tfp410_pins
		&dss_dpi_pins
	>;

	tfp410_pins: tfp410-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21c6, PIN_OUTPUT | MUX_MODE4)   /* hdq_sio.gpio_170 */
		>;
	};

	dss_dpi_pins: dss-dpi-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20d4, PIN_OUTPUT | MUX_MODE0)   /* dss_pclk.dss_pclk */
			OMAP3_CORE1_IOPAD(0x20d6, PIN_OUTPUT | MUX_MODE0)   /* dss_hsync.dss_hsync */
			OMAP3_CORE1_IOPAD(0x20d8, PIN_OUTPUT | MUX_MODE0)   /* dss_vsync.dss_vsync */
			OMAP3_CORE1_IOPAD(0x20da, PIN_OUTPUT | MUX_MODE0)   /* dss_acbias.dss_acbias */
			OMAP3_CORE1_IOPAD(0x20dc, PIN_OUTPUT | MUX_MODE0)   /* dss_data0.dss_data0 */
			OMAP3_CORE1_IOPAD(0x20de, PIN_OUTPUT | MUX_MODE0)   /* dss_data1.dss_data1 */
			OMAP3_CORE1_IOPAD(0x20e0, PIN_OUTPUT | MUX_MODE0)   /* dss_data2.dss_data2 */
			OMAP3_CORE1_IOPAD(0x20e2, PIN_OUTPUT | MUX_MODE0)   /* dss_data3.dss_data3 */
			OMAP3_CORE1_IOPAD(0x20e4, PIN_OUTPUT | MUX_MODE0)   /* dss_data4.dss_data4 */
			OMAP3_CORE1_IOPAD(0x20e6, PIN_OUTPUT | MUX_MODE0)   /* dss_data5.dss_data5 */
			OMAP3_CORE1_IOPAD(0x20e8, PIN_OUTPUT | MUX_MODE0)   /* dss_data6.dss_data6 */
			OMAP3_CORE1_IOPAD(0x20ea, PIN_OUTPUT | MUX_MODE0)   /* dss_data7.dss_data7 */
			OMAP3_CORE1_IOPAD(0x20ec, PIN_OUTPUT | MUX_MODE0)   /* dss_data8.dss_data8 */
			OMAP3_CORE1_IOPAD(0x20ee, PIN_OUTPUT | MUX_MODE0)   /* dss_data9.dss_data9 */
			OMAP3_CORE1_IOPAD(0x20f0, PIN_OUTPUT | MUX_MODE0)   /* dss_data10.dss_data10 */
			OMAP3_CORE1_IOPAD(0x20f2, PIN_OUTPUT | MUX_MODE0)   /* dss_data11.dss_data11 */
			OMAP3_CORE1_IOPAD(0x20f4, PIN_OUTPUT | MUX_MODE0)   /* dss_data12.dss_data12 */
			OMAP3_CORE1_IOPAD(0x20f6, PIN_OUTPUT | MUX_MODE0)   /* dss_data13.dss_data13 */
			OMAP3_CORE1_IOPAD(0x20f8, PIN_OUTPUT | MUX_MODE0)   /* dss_data14.dss_data14 */
			OMAP3_CORE1_IOPAD(0x20fa, PIN_OUTPUT | MUX_MODE0)   /* dss_data15.dss_data15 */
			OMAP3_CORE1_IOPAD(0x20fc, PIN_OUTPUT | MUX_MODE0)   /* dss_data16.dss_data16 */
			OMAP3_CORE1_IOPAD(0x20fe, PIN_OUTPUT | MUX_MODE0)   /* dss_data17.dss_data17 */
			OMAP3_CORE1_IOPAD(0x2100, PIN_OUTPUT | MUX_MODE0)   /* dss_data18.dss_data18 */
			OMAP3_CORE1_IOPAD(0x2102, PIN_OUTPUT | MUX_MODE0)   /* dss_data19.dss_data19 */
			OMAP3_CORE1_IOPAD(0x2104, PIN_OUTPUT | MUX_MODE0)   /* dss_data20.dss_data20 */
			OMAP3_CORE1_IOPAD(0x2106, PIN_OUTPUT | MUX_MODE0)   /* dss_data21.dss_data21 */
			OMAP3_CORE1_IOPAD(0x2108, PIN_OUTPUT | MUX_MODE0)   /* dss_data22.dss_data22 */
			OMAP3_CORE1_IOPAD(0x210a, PIN_OUTPUT | MUX_MODE0)   /* dss_data23.dss_data23 */
		>;
	};

	uart2_pins: uart2-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2174, PIN_INPUT | MUX_MODE0)	/* uart2_cts.uart2_cts */
			OMAP3_CORE1_IOPAD(0x2176, PIN_OUTPUT | MUX_MODE0)	/* uart2_rts .uart2_rts*/
			OMAP3_CORE1_IOPAD(0x2178, PIN_OUTPUT | MUX_MODE0)	/* uart2_tx.uart2_tx */
			OMAP3_CORE1_IOPAD(0x217a, PIN_INPUT | MUX_MODE0)	/* uart2_rx.uart2_rx */
		>;
	};

	smsc9221_pins: smsc9221-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21d2, PIN_INPUT | MUX_MODE4)	/* mcspi1_cs2.gpio_176 */
		>;
	};
};

&omap3_pmx_core2 {
	pinctrl-names = "default";
	pinctrl-0 = <
		&hsusbb1_pins
	>;

	hsusbb1_pins: hsusbb1-pins {
		pinctrl-single,pins = <
			OMAP3630_CORE2_IOPAD(0x25da, PIN_OUTPUT | MUX_MODE3)		/* etk_ctl.hsusb1_clk */
			OMAP3630_CORE2_IOPAD(0x25d8, PIN_OUTPUT | MUX_MODE3)		/* etk_clk.hsusb1_stp */
			OMAP3630_CORE2_IOPAD(0x25ec, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d8.hsusb1_dir */
			OMAP3630_CORE2_IOPAD(0x25ee, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d9.hsusb1_nxt */
			OMAP3630_CORE2_IOPAD(0x25dc, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d0.hsusb1_data0 */
			OMAP3630_CORE2_IOPAD(0x25de, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d1.hsusb1_data1 */
			OMAP3630_CORE2_IOPAD(0x25e0, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d2.hsusb1_data2 */
			OMAP3630_CORE2_IOPAD(0x25e2, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d3.hsusb1_data7 */
			OMAP3630_CORE2_IOPAD(0x25e4, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d4.hsusb1_data4 */
			OMAP3630_CORE2_IOPAD(0x25e6, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d5.hsusb1_data5 */
			OMAP3630_CORE2_IOPAD(0x25e8, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d6.hsusb1_data6 */
			OMAP3630_CORE2_IOPAD(0x25ea, PIN_INPUT_PULLDOWN | MUX_MODE3)	/* etk_d7.hsusb1_data3 */
		>;
	};

	leds_pins: leds-pins {
		pinctrl-single,pins = <
			OMAP3630_CORE2_IOPAD(0x25f4, PIN_OUTPUT | MUX_MODE4) /* etk_d12.gpio_26 */
			OMAP3630_CORE2_IOPAD(0x25f6, PIN_OUTPUT | MUX_MODE4) /* etk_d13.gpio_27 */
			OMAP3630_CORE2_IOPAD(0x25f8, PIN_OUTPUT | MUX_MODE4) /* etk_d14.gpio_28 */
		>;
	};

	mmc1_wp_pins: mmc1-cd-pins {
		pinctrl-single,pins = <
			OMAP3630_CORE2_IOPAD(0x25fa, PIN_INPUT | MUX_MODE4)   /* etk_d15.gpio_29 */
		>;
	};
};

&i2c3 {
	clock-frequency = <100000>;

	/*
	 * Display monitor features are burnt in the EEPROM
	 * as EDID data.
	 */
	eeprom@50 {
		compatible = "ti,eeprom";
		reg = <0x50>;
	};
};

&gpmc {
	ranges = <0 0 0x30000000 0x01000000>,	/* CS0: 16MB for NAND */
		 <5 0 0x2c000000 0x01000000>;	/* CS5: 16MB for ethernet */

	ethernet@gpmc {
		pinctrl-names = "default";
		pinctrl-0 = <&smsc9221_pins>;
		reg = <5 0 0xff>;
		interrupt-parent = <&gpio6>;
		interrupts = <16 IRQ_TYPE_LEVEL_LOW>;
	};
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pins>;
};

&usbhshost {
	port1-mode = "ehci-phy";
};

&usbhsehci {
	phys = <&hsusb1_phy>;
};

&vpll2 {
	/* Needed for DSS */
	regulator-name = "vdds_dsi";
};

&dss {
	status = "okay";

	port {
		dpi_out: endpoint {
			remote-endpoint = <&tfp410_in>;
			data-lines = <24>;
		};
	};
};

&mmc1 {
	pinctrl-0 = <&mmc1_pins &mmc1_wp_pins>;
	wp-gpios = <&gpio1 29 GPIO_ACTIVE_LOW>;	/* gpio_29 */
};
