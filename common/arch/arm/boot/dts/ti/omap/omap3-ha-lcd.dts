// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2012 Texas Instruments Incorporated - https://www.ti.com/
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 */

#include "omap3-ha-common.dtsi"

/ {
	model = "TI OMAP3 HEAD acoustics LCD-baseboard with TAO3530 SOM";
	compatible = "headacoustics,omap3-ha-lcd", "technexion,omap3-tao3530", "ti,omap3430", "ti,omap34xx", "ti,omap3";
};

&omap3_pmx_core {
	pinctrl-names = "default";
	pinctrl-0 = <
		&hsusbb2_pins
		&powerdown_input_pins
		&fpga_boot0_pins
		&fpga_boot1_pins
		&led_blue_pins
		&led_green_pins
		&led_red_pins
		&touchscreen_wake_pins
	>;

	touchscreen_irq_pins: touchscreen-irq-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2164, PIN_INPUT_PULLUP | MUX_MODE4)	/* gpio_136, Touchscreen IRQ */
		>;
	};

	touchscreen_wake_pins: touchscreen-wake-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x212c, PIN_OUTPUT_PULLUP | MUX_MODE4)	/* gpio_110, Touchscreen Wake */
		>;
	};

	dss_dpi_pins: dss-dpi-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x20d4, PIN_OUTPUT | MUX_MODE0)	/* dss_pclk.dss_pclk */
			OMAP3_CORE1_IOPAD(0x20d6, PIN_OUTPUT | MUX_MODE0)	/* dss_hsync.dss_hsync */
			OMAP3_CORE1_IOPAD(0x20d8, PIN_OUTPUT | MUX_MODE0)	/* dss_vsync.dss_vsync */
			OMAP3_CORE1_IOPAD(0x20da, PIN_OUTPUT | MUX_MODE0)	/* dss_acbias.dss_acbias */
			OMAP3_CORE1_IOPAD(0x20dc, PIN_OUTPUT | MUX_MODE0)	/* dss_data0.dss_data0 */
			OMAP3_CORE1_IOPAD(0x20de, PIN_OUTPUT | MUX_MODE0)	/* dss_data1.dss_data1 */
			OMAP3_CORE1_IOPAD(0x20e0, PIN_OUTPUT | MUX_MODE0)	/* dss_data2.dss_data2 */
			OMAP3_CORE1_IOPAD(0x20e2, PIN_OUTPUT | MUX_MODE0)	/* dss_data3.dss_data3 */
			OMAP3_CORE1_IOPAD(0x20e4, PIN_OUTPUT | MUX_MODE0)	/* dss_data4.dss_data4 */
			OMAP3_CORE1_IOPAD(0x20e6, PIN_OUTPUT | MUX_MODE0)	/* dss_data5.dss_data5 */
			OMAP3_CORE1_IOPAD(0x20e8, PIN_OUTPUT | MUX_MODE0)	/* dss_data6.dss_data6 */
			OMAP3_CORE1_IOPAD(0x20ea, PIN_OUTPUT | MUX_MODE0)	/* dss_data7.dss_data7 */
			OMAP3_CORE1_IOPAD(0x20ec, PIN_OUTPUT | MUX_MODE0)	/* dss_data8.dss_data8 */
			OMAP3_CORE1_IOPAD(0x20ee, PIN_OUTPUT | MUX_MODE0)	/* dss_data9.dss_data9 */
			OMAP3_CORE1_IOPAD(0x20f0, PIN_OUTPUT | MUX_MODE0)	/* dss_data10.dss_data10 */
			OMAP3_CORE1_IOPAD(0x20f2, PIN_OUTPUT | MUX_MODE0)	/* dss_data11.dss_data11 */
			OMAP3_CORE1_IOPAD(0x20f4, PIN_OUTPUT | MUX_MODE0)	/* dss_data12.dss_data12 */
			OMAP3_CORE1_IOPAD(0x20f6, PIN_OUTPUT | MUX_MODE0)	/* dss_data13.dss_data13 */
			OMAP3_CORE1_IOPAD(0x20f8, PIN_OUTPUT | MUX_MODE0)	/* dss_data14.dss_data14 */
			OMAP3_CORE1_IOPAD(0x20fa, PIN_OUTPUT | MUX_MODE0)	/* dss_data15.dss_data15 */
			OMAP3_CORE1_IOPAD(0x20fc, PIN_OUTPUT | MUX_MODE0)	/* dss_data16.dss_data16 */
			OMAP3_CORE1_IOPAD(0x20fe, PIN_OUTPUT | MUX_MODE0)	/* dss_data17.dss_data17 */
			OMAP3_CORE1_IOPAD(0x2100, PIN_OUTPUT | MUX_MODE0)	/* dss_data18.dss_data18 */
			OMAP3_CORE1_IOPAD(0x2102, PIN_OUTPUT | MUX_MODE0)	/* dss_data19.dss_data19 */
			OMAP3_CORE1_IOPAD(0x2104, PIN_OUTPUT | MUX_MODE0)	/* dss_data20.dss_data20 */
			OMAP3_CORE1_IOPAD(0x2106, PIN_OUTPUT | MUX_MODE0)	/* dss_data21.dss_data21 */
			OMAP3_CORE1_IOPAD(0x2108, PIN_OUTPUT | MUX_MODE0)	/* dss_data22.dss_data22 */
			OMAP3_CORE1_IOPAD(0x210a, PIN_OUTPUT | MUX_MODE0)	/* dss_data23.dss_data23 */
		>;
	};

	lte430_pins: lte430-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2168, PIN_OUTPUT | MUX_MODE4)	/* sdmmc2_dat6.gpio_138 */
		>;
	};

	backlight_pins: backlight-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x216a, PIN_OUTPUT | MUX_MODE4)	/* sdmmc2_dat7.gpio_139 */
		>;
	};
};

/* I2C2: mux'ed with GPIO168 which is connected to nKILL_POWER */
&i2c2 {
	status = "disabled";
};

&i2c3 {
	clock-frequency = <100000>;

	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;
};

/* Needed to power the DPI pins */
&vpll2 {
	regulator-always-on;
};

&dss {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&dss_dpi_pins>;

	port {
		dpi_out: endpoint {
			remote-endpoint = <&lcd_in>;
			data-lines = <24>;
		};
	};
};

/ {
	aliases {
		display0 = &lcd0;
	};

	lcd0: display {
		compatible = "panel-dpi";
		label = "lcd";

		pinctrl-names = "default";
		pinctrl-0 = <&lte430_pins>;
		enable-gpios = <&gpio5 10 GPIO_ACTIVE_LOW>;	/* gpio_138 */

		port {
			lcd_in: endpoint {
				remote-endpoint = <&dpi_out>;
			};
		};

		panel-timing {
			clock-frequency = <31250000>;
			hactive = <800>;
			vactive = <480>;
			hfront-porch = <40>;
			hback-porch = <86>;
			hsync-len = <1>;
			vback-porch = <30>;
			vfront-porch = <13>;
			vsync-len = <3>;

			hsync-active = <0>;
			vsync-active = <0>;
			de-active = <1>;
			pixelclk-active = <1>;
		};
	};

	backlight {
		compatible = "gpio-backlight";

		pinctrl-names = "default";
		pinctrl-0 = <&backlight_pins>;
		gpios = <&gpio5 11 GPIO_ACTIVE_HIGH>;		/* gpio_139 */

		default-on;
	};
};
