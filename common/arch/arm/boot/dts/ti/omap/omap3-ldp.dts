// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2013 Texas Instruments Incorporated - https://www.ti.com/
 */
/dts-v1/;

#include <dt-bindings/input/input.h>
#include "omap34xx.dtsi"
#include "omap-gpmc-smsc911x.dtsi"

/ {
	model = "TI OMAP3430 LDP (Zoom1 Labrador)";
	compatible = "ti,omap3-ldp", "ti,omap3430", "ti,omap3";

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x8000000>; /* 128 MB */
	};

	cpus {
		cpu@0 {
			cpu0-supply = <&vcc>;
		};
	};

	gpio_keys {
		compatible = "gpio-keys";
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_key_pins>;

		key_enter {
			label = "enter";
			gpios = <&gpio4 5 GPIO_ACTIVE_LOW>; /* gpio101 */
			linux,code = <KEY_ENTER>;
			wakeup-source;
		};

		key_f1 {
			label = "f1";
			gpios = <&gpio4 6 GPIO_ACTIVE_LOW>; /* gpio102 */
			linux,code = <KEY_F1>;
			wakeup-source;
		};

		key_f2 {
			label = "f2";
			gpios = <&gpio4 7 GPIO_ACTIVE_LOW>; /* gpio103 */
			linux,code = <KEY_F2>;
			wakeup-source;
		};

		key_f3 {
			label = "f3";
			gpios = <&gpio4 8 GPIO_ACTIVE_LOW>; /* gpio104 */
			linux,code = <KEY_F3>;
			wakeup-source;
		};

		key_f4 {
			label = "f4";
			gpios = <&gpio4 9 GPIO_ACTIVE_LOW>; /* gpio105 */
			linux,code = <KEY_F4>;
			wakeup-source;
		};

		key_left {
			label = "left";
			gpios = <&gpio4 10 GPIO_ACTIVE_LOW>; /* gpio106 */
			linux,code = <KEY_LEFT>;
			wakeup-source;
		};

		key_right {
			label = "right";
			gpios = <&gpio4 11 GPIO_ACTIVE_LOW>; /* gpio107 */
			linux,code = <KEY_RIGHT>;
			wakeup-source;
		};

		key_up {
			label = "up";
			gpios = <&gpio4 12 GPIO_ACTIVE_LOW>; /* gpio108 */
			linux,code = <KEY_UP>;
			wakeup-source;
		};

		key_down {
			label = "down";
			gpios = <&gpio4 13 GPIO_ACTIVE_LOW>; /* gpio109 */
			linux,code = <KEY_DOWN>;
			wakeup-source;
		};
	};
};

&gpmc {
	ranges = <0 0 0x30000000 0x1000000>,	/* CS0 space, 16MB */
		 <1 0 0x08000000 0x1000000>;	/* CS1 space, 16MB */

	nand@0,0 {
		compatible = "ti,omap2-nand";
		reg = <0 0 4>; /* CS0, offset 0, IO size 4 */
		interrupt-parent = <&gpmc>;
		interrupts = <0 IRQ_TYPE_NONE>, /* fifoevent */
			     <1 IRQ_TYPE_NONE>;	/* termcount */
		linux,mtd-name = "micron,nand";
		nand-bus-width = <16>;
		gpmc,device-width = <2>;
		ti,nand-ecc-opt = "bch8";

		gpmc,sync-clk-ps = <0>;
		gpmc,cs-on-ns = <0>;
		gpmc,cs-rd-off-ns = <44>;
		gpmc,cs-wr-off-ns = <44>;
		gpmc,adv-on-ns = <6>;
		gpmc,adv-rd-off-ns = <34>;
		gpmc,adv-wr-off-ns = <44>;
		gpmc,we-off-ns = <40>;
		gpmc,oe-off-ns = <54>;
		gpmc,access-ns = <64>;
		gpmc,rd-cycle-ns = <82>;
		gpmc,wr-cycle-ns = <82>;
		gpmc,wr-access-ns = <40>;
		gpmc,wr-data-mux-bus-ns = <0>;

		#address-cells = <1>;
		#size-cells = <1>;

		partition@0 {
			label = "X-Loader";
			reg = <0 0x80000>;
		};
		partition@80000 {
			label = "U-Boot";
			reg = <0x80000 0x140000>;
		};
		partition@1c0000 {
			label = "Environment";
			reg = <0x1c0000 0x40000>;
		};
		partition@200000 {
			label = "Kernel";
			reg = <0x200000 0x1e00000>;
		};
		partition@2000000 {
			label = "Filesystem";
			reg = <0x2000000 0x6000000>;
		};
	};

	ethernet@gpmc {
		interrupt-parent = <&gpio5>;
		interrupts = <24 IRQ_TYPE_LEVEL_LOW>;
		reg = <1 0 0xff>;
	};
};

&i2c1 {
	clock-frequency = <2600000>;

	twl: twl@48 {
		reg = <0x48>;
		interrupts = <7>; /* SYS_NIRQ cascaded to intc */
		interrupt-parent = <&intc>;

		twl_power: power {
			compatible = "ti,twl4030-power-idle";
			ti,use_poweroff;
		};
	};
};

#include "twl4030.dtsi"
#include "twl4030_omap3.dtsi"
#include "omap3-panel-sharp-ls037v7dw01.dtsi"

&backlight0 {
	gpios = <&twl_gpio 7 GPIO_ACTIVE_HIGH>;
};

&i2c2 {
	clock-frequency = <400000>;
};

&i2c3 {
	clock-frequency = <400000>;
};

/* tps61130rsa enabled by twl4030 regen */
&lcd_3v3 {
	regulator-always-on;
};

&lcd0 {
	enable-gpios = <&twl_gpio 15 GPIO_ACTIVE_HIGH>;	/* lcd INI */
	reset-gpios = <&gpio2 23 GPIO_ACTIVE_HIGH>;	/* gpio55, lcd RESB */
	mode-gpios = <&gpio2 24 GPIO_ACTIVE_HIGH>;	/* gpio56, lcd MO */
};

&mcspi1 {
	tsc2046@0 {
		interrupt-parent = <&gpio2>;
		interrupts = <22 0>;		/* gpio54 */
		pendown-gpio = <&gpio2 22 GPIO_ACTIVE_HIGH>;
	};
};

&mmc1 {
	/* See 35xx errata 2.1.1.128 in SPRZ278F */
	compatible = "ti,omap3-pre-es3-hsmmc";
	vmmc-supply = <&vmmc1>;
	bus-width = <4>;
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
};

&mmc2 {
	status = "disabled";
};

&mmc3 {
	status = "disabled";
};

&omap3_pmx_core {
	gpio_key_pins: gpio-key-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x211a, PIN_INPUT | MUX_MODE4)	/* cam_d2.gpio_101 */
			OMAP3_CORE1_IOPAD(0x211c, PIN_INPUT | MUX_MODE4)	/* cam_d3.gpio_102 */
			OMAP3_CORE1_IOPAD(0x211e, PIN_INPUT | MUX_MODE4)	/* cam_d4.gpio_103 */
			OMAP3_CORE1_IOPAD(0x2120, PIN_INPUT | MUX_MODE4)	/* cam_d5.gpio_104 */
			OMAP3_CORE1_IOPAD(0x2122, PIN_INPUT | MUX_MODE4)	/* cam_d6.gpio_105 */
			OMAP3_CORE1_IOPAD(0x2124, PIN_INPUT | MUX_MODE4)	/* cam_d7.gpio_106 */
			OMAP3_CORE1_IOPAD(0x2126, PIN_INPUT | MUX_MODE4)	/* cam_d8.gpio_107 */
			OMAP3_CORE1_IOPAD(0x2128, PIN_INPUT | MUX_MODE4)	/* cam_d9.gpio_108 */
			OMAP3_CORE1_IOPAD(0x212a, PIN_INPUT | MUX_MODE4)	/* cam_d10.gpio_109 */
		>;
	};

	musb_pins: musb-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x21a2, PIN_INPUT | MUX_MODE0)	/* hsusb0_clk.hsusb0_clk */
			OMAP3_CORE1_IOPAD(0x21aa, PIN_INPUT | MUX_MODE0)	/* hsusb0_data0.hsusb0_data0 */
			OMAP3_CORE1_IOPAD(0x21ac, PIN_INPUT | MUX_MODE0)	/* hsusb0_data1.hsusb0_data1 */
			OMAP3_CORE1_IOPAD(0x21ae, PIN_INPUT | MUX_MODE0)	/* hsusb0_data2.hsusb0_data2 */
			OMAP3_CORE1_IOPAD(0x21b0, PIN_INPUT | MUX_MODE0)	/* hsusb0_data3.hsusb0_data3 */
			OMAP3_CORE1_IOPAD(0x21b2, PIN_INPUT | MUX_MODE0)	/* hsusb0_data4.hsusb0_data4 */
			OMAP3_CORE1_IOPAD(0x21b4, PIN_INPUT | MUX_MODE0)	/* hsusb0_data5.hsusb0_data5 */
			OMAP3_CORE1_IOPAD(0x21b6, PIN_INPUT | MUX_MODE0)	/* hsusb0_data6.hsusb0_data6 */
			OMAP3_CORE1_IOPAD(0x21b8, PIN_INPUT | MUX_MODE0)	/* hsusb0_data7.hsusb0_data7 */
			OMAP3_CORE1_IOPAD(0x21a6, PIN_INPUT | MUX_MODE0)	/* hsusb0_dir.hsusb0_dir */
			OMAP3_CORE1_IOPAD(0x21a8, PIN_INPUT | MUX_MODE0)	/* hsusb0_nxt.hsusb0_nxt */
			OMAP3_CORE1_IOPAD(0x21a4, PIN_OUTPUT | MUX_MODE0)	/* hsusb0_stp.hsusb0_stp */
		>;
	};

	mmc1_pins: mmc1-pins {
		pinctrl-single,pins = <
			OMAP3_CORE1_IOPAD(0x2144, PIN_INPUT_PULLUP | MUX_MODE0)	/* mmc1_clk.mmc1_clk */
			OMAP3_CORE1_IOPAD(0x2146, PIN_INPUT_PULLUP | MUX_MODE0)	/* mmc1_cmd.mmc1_cmd */
			OMAP3_CORE1_IOPAD(0x2148, PIN_INPUT_PULLUP | MUX_MODE0)	/* mmc1_dat0.mmc1_dat0 */
			OMAP3_CORE1_IOPAD(0x214A, PIN_INPUT_PULLUP | MUX_MODE0)	/* mmc1_dat1.mmc1_dat1 */
			OMAP3_CORE1_IOPAD(0x214C, PIN_INPUT_PULLUP | MUX_MODE0)	/* mmc1_dat2.mmc1_dat2 */
			OMAP3_CORE1_IOPAD(0x214e, PIN_INPUT_PULLUP | MUX_MODE0)	/* mmc1_dat3.mmc1_dat3 */
		>;
	};
};

&twl_keypad {
	linux,keymap = <MATRIX_KEY(0, 0, KEY_1)
			MATRIX_KEY(0, 1, KEY_2)
			MATRIX_KEY(0, 2, KEY_3)
			MATRIX_KEY(1, 0, KEY_4)
			MATRIX_KEY(1, 1, KEY_5)
			MATRIX_KEY(1, 2, KEY_6)
			MATRIX_KEY(1, 3, KEY_F5)
			MATRIX_KEY(2, 0, KEY_7)
			MATRIX_KEY(2, 1, KEY_8)
			MATRIX_KEY(2, 2, KEY_9)
			MATRIX_KEY(2, 3, KEY_F6)
			MATRIX_KEY(3, 0, KEY_F7)
			MATRIX_KEY(3, 1, KEY_0)
			MATRIX_KEY(3, 2, KEY_F8)
			MATRIX_KEY(5, 4, KEY_RESERVED)
			MATRIX_KEY(4, 4, KEY_VOLUMEUP)
			MATRIX_KEY(5, 5, KEY_VOLUMEDOWN)>;
};

&uart3 {
	interrupts-extended = <&intc 74 &omap3_pmx_core OMAP3_UART3_RX>;
};

&usb_otg_hs {
	pinctrl-names = "default";
	pinctrl-0 = <&musb_pins>;
	interface-type = <0>;
	usb-phy = <&usb2_phy>;
	mode = <3>;
	power = <50>;
};

&vaux1 {
	/* Needed for ads7846 */
	regulator-name = "vcc";
};
