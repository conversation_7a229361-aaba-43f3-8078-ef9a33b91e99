// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright 2012 DENX Software Engineering GmbH
 * <PERSON><PERSON> <<EMAIL>>
 */
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	chosen { };
	aliases { };

	memory@c0000000 {
		device_type = "memory";
		reg = <0xc0000000 0x0>;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu: cpu@0 {
			compatible = "arm,arm926ej-s";
			device_type = "cpu";
			reg = <0>;
			clocks = <&psc0 14>;
			operating-points-v2 = <&opp_table>;
		};
	};

	opp_table: opp-table {
		compatible = "operating-points-v2";

		opp_100: opp100-100000000 {
			opp-hz = /bits/ 64 <100000000>;
			opp-microvolt = <1000000 950000 1050000>;
		};

		opp_200: opp110-200000000 {
			opp-hz = /bits/ 64 <200000000>;
			opp-microvolt = <1100000 1050000 1160000>;
		};

		opp_300: opp120-300000000 {
			opp-hz = /bits/ 64 <300000000>;
			opp-microvolt = <1200000 1140000 1320000>;
		};

		/*
		 * Original silicon was 300MHz max, so higher frequencies
		 * need to be enabled on a per-board basis if the chip is
		 * capable.
		 */

		opp_375: opp120-375000000 {
			status = "disabled";
			opp-hz = /bits/ 64 <375000000>;
			opp-microvolt = <1200000 1140000 1320000>;
		};

		opp_456: opp130-456000000 {
			status = "disabled";
			opp-hz = /bits/ 64 <456000000>;
			opp-microvolt = <1300000 1250000 1350000>;
		};
	};

	arm {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		intc: interrupt-controller@fffee000 {
			compatible = "ti,cp-intc";
			interrupt-controller;
			#interrupt-cells = <1>;
			ti,intc-size = <101>;
			reg = <0xfffee000 0x2000>;
		};
	};
	clocks: clocks {
		ref_clk: ref_clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-output-names = "ref_clk";
		};
		sata_refclk: sata_refclk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-output-names = "sata_refclk";
			status = "disabled";
		};
		usb_refclkin: usb_refclkin {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-output-names = "usb_refclkin";
			status = "disabled";
		};
	};
	dsp: dsp@11800000 {
		compatible = "ti,da850-dsp";
		reg = <0x11800000 0x40000>,
		      <0x11e00000 0x8000>,
		      <0x11f00000 0x8000>,
		      <0x01c14044 0x4>,
		      <0x01c14174 0x8>;
		reg-names = "l2sram", "l1pram", "l1dram", "host1cfg", "chipsig";
		interrupt-parent = <&intc>;
		interrupts = <28>;
		clocks = <&psc0 15>;
		resets = <&psc0 15>;
		status = "disabled";
	};
	soc@1c00000 {
		compatible = "simple-bus";
		model = "da850";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0x01c00000 0x400000>;
		interrupt-parent = <&intc>;

		psc0: clock-controller@10000 {
			compatible = "ti,da850-psc0";
			reg = <0x10000 0x1000>;
			#clock-cells = <1>;
			#reset-cells = <1>;
			#power-domain-cells = <1>;
			clocks = <&pll0_sysclk 1>, <&pll0_sysclk 2>,
				 <&pll0_sysclk 4>, <&pll0_sysclk 6>,
				 <&async1_clk>;
			clock-names = "pll0_sysclk1", "pll0_sysclk2",
				      "pll0_sysclk4", "pll0_sysclk6",
				      "async1";
		};
		pll0: clock-controller@11000 {
			compatible = "ti,da850-pll0";
			reg = <0x11000 0x1000>;
			clocks = <&ref_clk>, <&pll1_sysclk 3>;
			clock-names = "clksrc", "extclksrc";

			pll0_pllout: pllout {
				#clock-cells = <0>;
			};
			pll0_sysclk: sysclk {
				#clock-cells = <1>;
			};
			pll0_auxclk: auxclk {
				#clock-cells = <0>;
			};
			pll0_obsclk: obsclk {
				#clock-cells = <0>;
			};
		};
		pmx_core: pinmux@14120 {
			compatible = "pinctrl-single";
			reg = <0x14120 0x50>;
			#pinctrl-cells = <2>;
			pinctrl-single,bit-per-mux;
			pinctrl-single,register-width = <32>;
			pinctrl-single,function-mask = <0xf>;
			/* pin base, nr pins & gpio function */
			pinctrl-single,gpio-range = <&range   0 17 0x8>,
						    <&range  17  8 0x4>,
						    <&range  26  8 0x4>,
						    <&range  34 80 0x8>,
						    <&range 129 31 0x8>;
			status = "disabled";

			range: gpio-range {
				#pinctrl-single,gpio-range-cells = <3>;
			};

			serial0_rtscts_pins: serial0-rtscts-pins {
				pinctrl-single,bits = <
					/* UART0_RTS UART0_CTS */
					0x0c 0x22000000 0xff000000
				>;
			};
			serial0_rxtx_pins: serial0-rxtx-pins {
				pinctrl-single,bits = <
					/* UART0_TXD UART0_RXD */
					0x0c 0x00220000 0x00ff0000
				>;
			};
			serial1_rtscts_pins: serial1-rtscts-pins {
				pinctrl-single,bits = <
					/* UART1_CTS UART1_RTS */
					0x00 0x00440000 0x00ff0000
				>;
			};
			serial1_rxtx_pins: serial1-rxtx-pins {
				pinctrl-single,bits = <
					/* UART1_TXD UART1_RXD */
					0x10 0x22000000 0xff000000
				>;
			};
			serial2_rtscts_pins: serial2-rtscts-pins {
				pinctrl-single,bits = <
					/* UART2_CTS UART2_RTS */
					0x00 0x44000000 0xff000000
				>;
			};
			serial2_rxtx_pins: serial2-rxtx-pins {
				pinctrl-single,bits = <
					/* UART2_TXD UART2_RXD */
					0x10 0x00220000 0x00ff0000
				>;
			};
			i2c0_pins: i2c0-pins {
				pinctrl-single,bits = <
					/* I2C0_SDA,I2C0_SCL */
					0x10 0x00002200 0x0000ff00
				>;
			};
			i2c1_pins: i2c1-pins {
				pinctrl-single,bits = <
					/* I2C1_SDA, I2C1_SCL */
					0x10 0x00440000 0x00ff0000
				>;
			};
			mmc0_pins: mmc-pins {
				pinctrl-single,bits = <
					/* MMCSD0_DAT[3] MMCSD0_DAT[2]
					 * MMCSD0_DAT[1] MMCSD0_DAT[0]
					 * MMCSD0_CMD    MMCSD0_CLK
					 */
					0x28 0x00222222  0x00ffffff
				>;
			};
			ehrpwm0a_pins: ehrpwm0a-pins {
				pinctrl-single,bits = <
					/* EPWM0A */
					0xc 0x00000002 0x0000000f
				>;
			};
			ehrpwm0b_pins: ehrpwm0b-pins {
				pinctrl-single,bits = <
					/* EPWM0B */
					0xc 0x00000020 0x000000f0
				>;
			};
			ehrpwm1a_pins: ehrpwm1a-pins {
				pinctrl-single,bits = <
					/* EPWM1A */
					0x14 0x00000002 0x0000000f
				>;
			};
			ehrpwm1b_pins: ehrpwm1b-pins {
				pinctrl-single,bits = <
					/* EPWM1B */
					0x14 0x00000020 0x000000f0
				>;
			};
			ecap0_pins: ecap0-pins {
				pinctrl-single,bits = <
					/* ECAP0_APWM0 */
					0x8 0x20000000 0xf0000000
				>;
			};
			ecap1_pins: ecap1-pins {
				pinctrl-single,bits = <
					/* ECAP1_APWM1 */
					0x4 0x40000000 0xf0000000
				>;
			};
			ecap2_pins: ecap2-pins {
				pinctrl-single,bits = <
					/* ECAP2_APWM2 */
					0x4 0x00000004 0x0000000f
				>;
			};
			spi0_pins: spi0-pins {
				pinctrl-single,bits = <
					/* SIMO, SOMI, CLK */
					0xc 0x00001101 0x0000ff0f
				>;
			};
			spi0_cs0_pin: spi0-cs0-pins {
				pinctrl-single,bits = <
					/* CS0 */
					0x10 0x00000010 0x000000f0
				>;
			};
			spi0_cs3_pin: spi0-cs3-pins {
				pinctrl-single,bits = <
					/* CS3 */
					0xc 0x01000000 0x0f000000
				>;
			};
			spi1_pins: spi1-pins {
				pinctrl-single,bits = <
					/* SIMO, SOMI, CLK */
					0x14 0x00110100 0x00ff0f00
				>;
			};
			spi1_cs0_pin: spi1-cs0-pins {
				pinctrl-single,bits = <
					/* CS0 */
					0x14 0x00000010 0x000000f0
				>;
			};
			mdio_pins: mdio-pins {
				pinctrl-single,bits = <
					/* MDIO_CLK, MDIO_D */
					0x10 0x00000088 0x000000ff
				>;
			};
			mii_pins: mii-pins {
				pinctrl-single,bits = <
					/*
					 * MII_TXEN, MII_TXCLK, MII_COL
					 * MII_TXD_3, MII_TXD_2, MII_TXD_1
					 * MII_TXD_0
					 */
					0x8 0x88888880 0xfffffff0
					/*
					 * MII_RXER, MII_CRS, MII_RXCLK
					 * MII_RXDV, MII_RXD_3, MII_RXD_2
					 * MII_RXD_1, MII_RXD_0
					 */
					0xc 0x88888888 0xffffffff
				>;
			};
			lcd_pins: lcd-pins {
				pinctrl-single,bits = <
					/*
					 * LCD_D[2], LCD_D[3], LCD_D[4], LCD_D[5],
					 * LCD_D[6], LCD_D[7]
					 */
					0x40 0x22222200 0xffffff00
					/*
					 * LCD_D[10], LCD_D[11], LCD_D[12], LCD_D[13],
					 * LCD_D[14], LCD_D[15], LCD_D[0], LCD_D[1]
					 */
					0x44 0x22222222 0xffffffff
					/* LCD_D[8], LCD_D[9] */
					0x48 0x00000022 0x000000ff

					/* LCD_PCLK */
					0x48 0x02000000 0x0f000000
					/* LCD_AC_ENB_CS, LCD_VSYNC, LCD_HSYNC */
					0x4c 0x02000022 0x0f0000ff
				>;
			};
			vpif_capture_pins: vpif-capture-pins {
				pinctrl-single,bits = <
					/* VP_DIN[2..7], VP_CLKIN1, VP_CLKIN0 */
					0x38 0x11111111 0xffffffff
					/* VP_DIN[10..15,0..1] */
					0x3c 0x11111111 0xffffffff
					/* VP_DIN[8..9] */
					0x40 0x00000011 0x000000ff
				>;
			};
			vpif_display_pins: vpif-display-pins {
				pinctrl-single,bits = <
					/* VP_DOUT[2..7] */
					0x40 0x11111100 0xffffff00
					/* VP_DOUT[10..15,0..1] */
					0x44 0x11111111 0xffffffff
					/*  VP_DOUT[8..9] */
					0x48 0x00000011 0x000000ff
					/*
					 * VP_CLKOUT3, VP_CLKIN3,
					 * VP_CLKOUT2, VP_CLKIN2
					 */
					0x4c 0x00111100 0x00ffff00
				>;
			};
		};
		prictrl: priority-controller@14110 {
			compatible = "ti,da850-mstpri";
			reg = <0x14110 0x0c>;
			status = "disabled";
		};
		cfgchip: chip-controller@1417c {
			compatible = "ti,da830-cfgchip", "syscon", "simple-mfd";
			reg = <0x1417c 0x14>;

			usb_phy: usb-phy {
				compatible = "ti,da830-usb-phy";
				#phy-cells = <1>;
				clocks = <&usb_phy_clk 0>, <&usb_phy_clk 1>;
				clock-names = "usb0_clk48", "usb1_clk48";
				status = "disabled";
			};
			usb_phy_clk: usb-phy-clocks {
				compatible = "ti,da830-usb-phy-clocks";
				#clock-cells = <1>;
				clocks = <&psc1 1>, <&usb_refclkin>,
					 <&pll0_auxclk>;
				clock-names = "fck", "usb_refclkin", "auxclk";
			};
			ehrpwm_tbclk: ehrpwm_tbclk {
				compatible = "ti,da830-tbclksync";
				#clock-cells = <0>;
				clocks = <&psc1 17>;
				clock-names = "fck";
			};
			div4p5_clk: div4.5 {
				compatible = "ti,da830-div4p5ena";
				#clock-cells = <0>;
				clocks = <&pll0_pllout>;
				clock-names = "pll0_pllout";
			};
			async1_clk: async1 {
				compatible = "ti,da850-async1-clksrc";
				#clock-cells = <0>;
				clocks = <&pll0_sysclk 3>, <&div4p5_clk>;
				clock-names = "pll0_sysclk3", "div4.5";
			};
			async3_clk: async3 {
				compatible = "ti,da850-async3-clksrc";
				#clock-cells = <0>;
				clocks = <&pll0_sysclk 2>, <&pll1_sysclk 2>;
				clock-names = "pll0_sysclk2", "pll1_sysclk2";
			};
		};
		edma0: edma@0 {
			compatible = "ti,edma3-tpcc";
			/* eDMA3 CC0: 0x01c0 0000 - 0x01c0 7fff */
			reg = <0x0 0x8000>;
			reg-names = "edma3_cc";
			interrupts = <11>, <12>;
			interrupt-names = "edma3_ccint", "edma3_ccerrint";
			#dma-cells = <2>;

			ti,tptcs = <&edma0_tptc0 7>, <&edma0_tptc1 0>;
			power-domains = <&psc0 0>;
		};
		edma0_tptc0: tptc@8000 {
			compatible = "ti,edma3-tptc";
			reg = <0x8000 0x400>;
			interrupts = <13>;
			interrupt-names = "edm3_tcerrint";
			power-domains = <&psc0 1>;
		};
		edma0_tptc1: tptc@8400 {
			compatible = "ti,edma3-tptc";
			reg = <0x8400 0x400>;
			interrupts = <32>;
			interrupt-names = "edm3_tcerrint";
			power-domains = <&psc0 2>;
		};
		edma1: edma@230000 {
			compatible = "ti,edma3-tpcc";
			/* eDMA3 CC1: 0x01e3 0000 - 0x01e3 7fff */
			reg = <0x230000 0x8000>;
			reg-names = "edma3_cc";
			interrupts = <93>, <94>;
			interrupt-names = "edma3_ccint", "edma3_ccerrint";
			#dma-cells = <2>;

			ti,tptcs = <&edma1_tptc0 7>;
			power-domains = <&psc1 0>;
		};
		edma1_tptc0: tptc@238000 {
			compatible = "ti,edma3-tptc";
			reg = <0x238000 0x400>;
			interrupts = <95>;
			interrupt-names = "edm3_tcerrint";
			power-domains = <&psc1 21>;
		};
		serial0: serial@42000 {
			compatible = "ti,da830-uart", "ns16550a";
			reg = <0x42000 0x100>;
			reg-io-width = <4>;
			reg-shift = <2>;
			interrupts = <25>;
			clocks = <&psc0 9>;
			power-domains = <&psc0 9>;
			status = "disabled";
		};
		serial1: serial@10c000 {
			compatible = "ti,da830-uart", "ns16550a";
			reg = <0x10c000 0x100>;
			reg-io-width = <4>;
			reg-shift = <2>;
			interrupts = <53>;
			clocks = <&psc1 12>;
			power-domains = <&psc1 12>;
			status = "disabled";
		};
		serial2: serial@10d000 {
			compatible = "ti,da830-uart", "ns16550a";
			reg = <0x10d000 0x100>;
			reg-io-width = <4>;
			reg-shift = <2>;
			interrupts = <61>;
			clocks = <&psc1 13>;
			power-domains = <&psc1 13>;
			status = "disabled";
		};
		rtc0: rtc@23000 {
			compatible = "ti,da830-rtc";
			reg = <0x23000 0x1000>;
			interrupts = <19>, <19>;
			clocks = <&pll0_auxclk>;
			clock-names = "int-clk";
			status = "disabled";
		};
		i2c0: i2c@22000 {
			compatible = "ti,davinci-i2c";
			reg = <0x22000 0x1000>;
			interrupts = <15>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&pll0_auxclk>;
			status = "disabled";
		};
		i2c1: i2c@228000 {
			compatible = "ti,davinci-i2c";
			reg = <0x228000 0x1000>;
			interrupts = <51>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&psc1 11>;
			power-domains = <&psc1 11>;
			status = "disabled";
		};
		clocksource: timer@20000 {
			compatible = "ti,da830-timer";
			reg = <0x20000 0x1000>;
			interrupts = <21>, <22>;
			interrupt-names = "tint12", "tint34";
			clocks = <&pll0_auxclk>;
		};
		wdt: wdt@21000 {
			compatible = "ti,davinci-wdt";
			reg = <0x21000 0x1000>;
			clocks = <&pll0_auxclk>;
			status = "disabled";
		};
		mmc0: mmc@40000 {
			compatible = "ti,da830-mmc";
			reg = <0x40000 0x1000>;
			cap-sd-highspeed;
			cap-mmc-highspeed;
			interrupts = <16>;
			dmas = <&edma0 16 0>, <&edma0 17 0>;
			dma-names = "rx", "tx";
			clocks = <&psc0 5>;
			status = "disabled";
		};
		vpif: video@217000 {
			compatible = "ti,da850-vpif";
			reg = <0x217000 0x1000>;
			interrupts = <92>;
			power-domains = <&psc1 9>;
			status = "disabled";

			/* VPIF capture port */
			port@0 {
				#address-cells = <1>;
				#size-cells = <0>;
			};

			/* VPIF display port */
			port@1 {
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};
		mmc1: mmc@21b000 {
			compatible = "ti,da830-mmc";
			reg = <0x21b000 0x1000>;
			cap-sd-highspeed;
			cap-mmc-highspeed;
			interrupts = <72>;
			dmas = <&edma1 28 0>, <&edma1 29 0>;
			dma-names = "rx", "tx";
			clocks = <&psc1 18>;
			status = "disabled";
		};
		ehrpwm0: pwm@300000 {
			compatible = "ti,da850-ehrpwm", "ti,am3352-ehrpwm";
			#pwm-cells = <3>;
			reg = <0x300000 0x2000>;
			clocks = <&psc1 17>, <&ehrpwm_tbclk>;
			clock-names = "fck", "tbclk";
			power-domains = <&psc1 17>;
			status = "disabled";
		};
		ehrpwm1: pwm@302000 {
			compatible = "ti,da850-ehrpwm", "ti,am3352-ehrpwm";
			#pwm-cells = <3>;
			reg = <0x302000 0x2000>;
			clocks = <&psc1 17>, <&ehrpwm_tbclk>;
			clock-names = "fck", "tbclk";
			power-domains = <&psc1 17>;
			status = "disabled";
		};
		ecap0: pwm@306000 {
			compatible = "ti,da850-ecap", "ti,am3352-ecap";
			#pwm-cells = <3>;
			reg = <0x306000 0x80>;
			clocks = <&psc1 20>;
			clock-names = "fck";
			power-domains = <&psc1 20>;
			status = "disabled";
		};
		ecap1: pwm@307000 {
			compatible = "ti,da850-ecap", "ti,am3352-ecap";
			#pwm-cells = <3>;
			reg = <0x307000 0x80>;
			clocks = <&psc1 20>;
			clock-names = "fck";
			power-domains = <&psc1 20>;
			status = "disabled";
		};
		ecap2: pwm@308000 {
			compatible = "ti,da850-ecap", "ti,am3352-ecap";
			#pwm-cells = <3>;
			reg = <0x308000 0x80>;
			clocks = <&psc1 20>;
			clock-names = "fck";
			power-domains = <&psc1 20>;
			status = "disabled";
		};
		spi0: spi@41000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "ti,da830-spi";
			reg = <0x41000 0x1000>;
			num-cs = <6>;
			ti,davinci-spi-intr-line = <1>;
			interrupts = <20>;
			dmas = <&edma0 14 0>, <&edma0 15 0>;
			dma-names = "rx", "tx";
			clocks = <&psc0 4>;
			power-domains = <&psc0 4>;
			status = "disabled";
		};
		spi1: spi@30e000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "ti,da830-spi";
			reg = <0x30e000 0x1000>;
			num-cs = <4>;
			ti,davinci-spi-intr-line = <1>;
			interrupts = <56>;
			dmas = <&edma0 18 0>, <&edma0 19 0>;
			dma-names = "rx", "tx";
			clocks = <&psc1 10>;
			power-domains = <&psc1 10>;
			status = "disabled";
		};
		usb0: usb@200000 {
			compatible = "ti,da830-musb";
			reg = <0x200000 0x1000>;
			ranges;
			interrupts = <58>;
			interrupt-names = "mc";
			dr_mode = "otg";
			phys = <&usb_phy 0>;
			phy-names = "usb-phy";
			clocks = <&psc1 1>;
			clock-ranges;
			status = "disabled";

			#address-cells = <1>;
			#size-cells = <1>;

			dmas = <&cppi41dma 0 0 &cppi41dma 1 0
				&cppi41dma 2 0 &cppi41dma 3 0
				&cppi41dma 0 1 &cppi41dma 1 1
				&cppi41dma 2 1 &cppi41dma 3 1>;
			dma-names =
				"rx1", "rx2", "rx3", "rx4",
				"tx1", "tx2", "tx3", "tx4";

			cppi41dma: dma-controller@201000 {
				compatible = "ti,da830-cppi41";
				reg = <0x201000 0x1000
					0x202000 0x1000
					0x204000 0x4000>;
				reg-names = "controller",
					    "scheduler", "queuemgr";
				interrupts = <58>;
				#dma-cells = <2>;
				/* For backwards compatibility: */
				#dma-channels = <4>;
				dma-channels = <4>;
				power-domains = <&psc1 1>;
				status = "okay";
			};
		};
		sata: sata@218000 {
			compatible = "ti,da850-ahci";
			reg = <0x218000 0x2000>, <0x22c018 0x4>;
			interrupts = <67>;
			clocks = <&psc1 8>, <&sata_refclk>;
			clock-names = "fck", "refclk";
			status = "disabled";
		};
		pll1: clock-controller@21a000 {
			compatible = "ti,da850-pll1";
			reg = <0x21a000 0x1000>;
			clocks = <&ref_clk>;
			clock-names = "clksrc";

			pll1_sysclk: sysclk {
				#clock-cells = <1>;
			};
			pll1_obsclk: obsclk {
				#clock-cells = <0>;
			};
		};
		mdio: mdio@224000 {
			compatible = "ti,davinci_mdio";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x224000 0x1000>;
			clocks = <&psc1 5>;
			clock-names = "fck";
			power-domains = <&psc1 5>;
			status = "disabled";
		};
		eth0: ethernet@220000 {
			compatible = "ti,davinci-dm6467-emac";
			reg = <0x220000 0x4000>;
			ti,davinci-ctrl-reg-offset = <0x3000>;
			ti,davinci-ctrl-mod-reg-offset = <0x2000>;
			ti,davinci-ctrl-ram-offset = <0>;
			ti,davinci-ctrl-ram-size = <0x2000>;
			local-mac-address = [ 00 00 00 00 00 00 ];
			interrupts = <33>, <34>, <35>,<36>;
			clocks = <&psc1 5>;
			power-domains = <&psc1 5>;
			status = "disabled";
		};
		usb1: usb@225000 {
			compatible = "ti,da830-ohci";
			reg = <0x225000 0x1000>;
			interrupts = <59>;
			phys = <&usb_phy 1>;
			phy-names = "usb-phy";
			clocks = <&psc1 2>;
			status = "disabled";
		};
		gpio: gpio@226000 {
			compatible = "ti,dm6441-gpio";
			gpio-controller;
			#gpio-cells = <2>;
			reg = <0x226000 0x1000>;
			interrupts = <42>, <43>, <44>, <45>, <46>, <47>, <48>, <49>, <50>;
			ti,ngpio = <144>;
			ti,davinci-gpio-unbanked = <0>;
			clocks = <&psc1 3>;
			clock-names = "gpio";
			status = "disabled";
			interrupt-controller;
			#interrupt-cells = <2>;
			gpio-ranges = <&pmx_core   0  15 1>,
				      <&pmx_core   1  14 1>,
				      <&pmx_core   2  13 1>,
				      <&pmx_core   3  12 1>,
				      <&pmx_core   4  11 1>,
				      <&pmx_core   5  10 1>,
				      <&pmx_core   6   9 1>,
				      <&pmx_core   7   8 1>,
				      <&pmx_core   8   7 1>,
				      <&pmx_core   9   6 1>,
				      <&pmx_core  10   5 1>,
				      <&pmx_core  11   4 1>,
				      <&pmx_core  12   3 1>,
				      <&pmx_core  13   2 1>,
				      <&pmx_core  14   1 1>,
				      <&pmx_core  15   0 1>,
				      <&pmx_core  16  39 1>,
				      <&pmx_core  17  38 1>,
				      <&pmx_core  18  37 1>,
				      <&pmx_core  19  36 1>,
				      <&pmx_core  20  35 1>,
				      <&pmx_core  21  34 1>,
				      <&pmx_core  22  33 1>,
				      <&pmx_core  23  32 1>,
				      <&pmx_core  24  24 1>,
				      <&pmx_core  25  22 1>,
				      <&pmx_core  26  21 1>,
				      <&pmx_core  27  20 1>,
				      <&pmx_core  28  19 1>,
				      <&pmx_core  29  18 1>,
				      <&pmx_core  30  17 1>,
				      <&pmx_core  31  16 1>,
				      <&pmx_core  32  55 1>,
				      <&pmx_core  33  54 1>,
				      <&pmx_core  34  53 1>,
				      <&pmx_core  35  52 1>,
				      <&pmx_core  36  51 1>,
				      <&pmx_core  37  50 1>,
				      <&pmx_core  38  49 1>,
				      <&pmx_core  39  48 1>,
				      <&pmx_core  40  47 1>,
				      <&pmx_core  41  46 1>,
				      <&pmx_core  42  45 1>,
				      <&pmx_core  43  44 1>,
				      <&pmx_core  44  43 1>,
				      <&pmx_core  45  42 1>,
				      <&pmx_core  46  41 1>,
				      <&pmx_core  47  40 1>,
				      <&pmx_core  48  71 1>,
				      <&pmx_core  49  70 1>,
				      <&pmx_core  50  69 1>,
				      <&pmx_core  51  68 1>,
				      <&pmx_core  52  67 1>,
				      <&pmx_core  53  66 1>,
				      <&pmx_core  54  65 1>,
				      <&pmx_core  55  64 1>,
				      <&pmx_core  56  63 1>,
				      <&pmx_core  57  62 1>,
				      <&pmx_core  58  61 1>,
				      <&pmx_core  59  60 1>,
				      <&pmx_core  60  59 1>,
				      <&pmx_core  61  58 1>,
				      <&pmx_core  62  57 1>,
				      <&pmx_core  63  56 1>,
				      <&pmx_core  64  87 1>,
				      <&pmx_core  65  86 1>,
				      <&pmx_core  66  85 1>,
				      <&pmx_core  67  84 1>,
				      <&pmx_core  68  83 1>,
				      <&pmx_core  69  82 1>,
				      <&pmx_core  70  81 1>,
				      <&pmx_core  71  80 1>,
				      <&pmx_core  72  70 1>,
				      <&pmx_core  73  78 1>,
				      <&pmx_core  74  77 1>,
				      <&pmx_core  75  76 1>,
				      <&pmx_core  76  75 1>,
				      <&pmx_core  77  74 1>,
				      <&pmx_core  78  73 1>,
				      <&pmx_core  79  72 1>,
				      <&pmx_core  80 103 1>,
				      <&pmx_core  81 102 1>,
				      <&pmx_core  82 101 1>,
				      <&pmx_core  83 100 1>,
				      <&pmx_core  84  99 1>,
				      <&pmx_core  85  98 1>,
				      <&pmx_core  86  97 1>,
				      <&pmx_core  87  96 1>,
				      <&pmx_core  88  95 1>,
				      <&pmx_core  89  94 1>,
				      <&pmx_core  90  93 1>,
				      <&pmx_core  91  92 1>,
				      <&pmx_core  92  91 1>,
				      <&pmx_core  93  90 1>,
				      <&pmx_core  94  89 1>,
				      <&pmx_core  95  88 1>,
				      <&pmx_core  96 158 1>,
				      <&pmx_core  97 157 1>,
				      <&pmx_core  98 156 1>,
				      <&pmx_core  99 155 1>,
				      <&pmx_core 100 154 1>,
				      <&pmx_core 101 129 1>,
				      <&pmx_core 102 113 1>,
				      <&pmx_core 103 112 1>,
				      <&pmx_core 104 111 1>,
				      <&pmx_core 105 110 1>,
				      <&pmx_core 106 109 1>,
				      <&pmx_core 107 108 1>,
				      <&pmx_core 108 107 1>,
				      <&pmx_core 109 106 1>,
				      <&pmx_core 110 105 1>,
				      <&pmx_core 111 104 1>,
				      <&pmx_core 112 145 1>,
				      <&pmx_core 113 144 1>,
				      <&pmx_core 114 143 1>,
				      <&pmx_core 115 142 1>,
				      <&pmx_core 116 141 1>,
				      <&pmx_core 117 140 1>,
				      <&pmx_core 118 139 1>,
				      <&pmx_core 119 138 1>,
				      <&pmx_core 120 137 1>,
				      <&pmx_core 121 136 1>,
				      <&pmx_core 122 135 1>,
				      <&pmx_core 123 134 1>,
				      <&pmx_core 124 133 1>,
				      <&pmx_core 125 132 1>,
				      <&pmx_core 126 131 1>,
				      <&pmx_core 127 130 1>,
				      <&pmx_core 128 159 1>,
				      <&pmx_core 129  31 1>,
				      <&pmx_core 130  30 1>,
				      <&pmx_core 131  20 1>,
				      <&pmx_core 132  28 1>,
				      <&pmx_core 133  27 1>,
				      <&pmx_core 134  26 1>,
				      <&pmx_core 135  23 1>,
				      <&pmx_core 136 153 1>,
				      <&pmx_core 137 152 1>,
				      <&pmx_core 138 151 1>,
				      <&pmx_core 139 150 1>,
				      <&pmx_core 140 149 1>,
				      <&pmx_core 141 148 1>,
				      <&pmx_core 142 147 1>,
				      <&pmx_core 143 146 1>;
		};
		psc1: clock-controller@227000 {
			compatible = "ti,da850-psc1";
			reg = <0x227000 0x1000>;
			#clock-cells = <1>;
			#power-domain-cells = <1>;
			clocks = <&pll0_sysclk 2>, <&pll0_sysclk 4>,
				 <&async3_clk>;
			clock-names = "pll0_sysclk2", "pll0_sysclk4", "async3";
			assigned-clocks = <&async3_clk>;
			assigned-clock-parents = <&pll1_sysclk 2>;
		};
		pinconf: pin-controller@22c00c {
			compatible = "ti,da850-pupd";
			reg = <0x22c00c 0x8>;
			status = "disabled";
		};

		mcasp0: mcasp@100000 {
			compatible = "ti,da830-mcasp-audio";
			reg = <0x100000 0x2000>,
			      <0x102000 0x400000>;
			reg-names = "mpu", "dat";
			interrupts = <54>;
			interrupt-names = "common";
			power-domains = <&psc1 7>;
			status = "disabled";
			dmas = <&edma0 1 1>,
				<&edma0 0 1>;
			dma-names = "tx", "rx";
		};

		lcdc: display@213000 {
			compatible = "ti,da850-tilcdc";
			reg = <0x213000 0x1000>;
			interrupts = <52>;
			max-pixelclock = <37500>;
			clocks = <&psc1 16>;
			clock-names = "fck";
			power-domains = <&psc1 16>;
			status = "disabled";
		};
	};
	aemif: aemif@68000000 {
		compatible = "ti,da850-aemif";
		#address-cells = <2>;
		#size-cells = <1>;

		reg = <0x68000000 0x00008000>;
		ranges = <0 0 0x60000000 0x08000000
			  1 0 0x68000000 0x00008000>;
		clocks = <&psc0 3>;
		clock-names = "aemif";
		clock-ranges;
		status = "disabled";
	};
	memctrl: memory-controller@b0000000 {
		compatible = "ti,da850-ddr-controller";
		reg = <0xb0000000 0xe8>;
		status = "disabled";
	};
};
