// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device Tree for DA850 EVM board
 *
 * Copyright (C) 2012 Texas Instruments Incorporated - https://www.ti.com/
 */
/dts-v1/;
#include "da850.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	compatible = "ti,da850-evm", "ti,da850";
	model = "DA850/AM1808/OMAP-L138 EVM";

	chosen {
		stdout-path = &serial2;
	};

	aliases {
		serial0 = &serial0;
		serial1 = &serial1;
		serial2 = &serial2;
		ethernet0 = &eth0;
		spi0 = &spi1;
	};

	backlight: backlight-pwm {
		pinctrl-names = "default";
		pinctrl-0 = <&ecap2_pins>;
		power-supply = <&backlight_lcd>;
		compatible = "pwm-backlight";
		/*
		 * The PWM here corresponds to production hardware. The
		 * schematic needs to be 1015171 (15 March 2010), Rev A
		 * or newer.
		 */
		pwms = <&ecap2 0 50000 0>;
		brightness-levels = <0 10 20 30 40 50 60 70 80 90 99>;
		default-brightness-level = <7>;
	};

	panel {
		compatible = "ti,tilcdc,panel";
		pinctrl-names = "default";
		pinctrl-0 = <&lcd_pins>;
		/*
		 * The vpif and the LCD are mutually exclusive.
		 * To enable VPIF, change the status below to 'disabled' then
		 * then change the status of the vpif below to 'okay'
		 */
		status = "okay";
		enable-gpios = <&gpio 40 GPIO_ACTIVE_HIGH>; /* lcd_panel_pwr */

		panel-info {
			ac-bias = <255>;
			ac-bias-intrpt = <0>;
			dma-burst-sz = <16>;
			bpp = <16>;
			fdd = <0x80>;
			sync-edge = <0>;
			sync-ctrl = <1>;
			raster-order = <0>;
			fifo-th = <0>;
		};

		display-timings {
			native-mode = <&timing0>;
			timing0: 480x272 {
				clock-frequency = <9000000>;
				hactive = <480>;
				vactive = <272>;
				hfront-porch = <3>;
				hback-porch = <2>;
				hsync-len = <42>;
				vback-porch = <3>;
				vfront-porch = <4>;
				vsync-len = <11>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <1>;
				pixelclk-active = <1>;
			};
		};
	};

	vbat: fixedregulator0 {
		compatible = "regulator-fixed";
		regulator-name = "vbat";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
	};

	baseboard_3v3: fixedregulator-3v3 {
		/* TPS73701DCQ */
		compatible = "regulator-fixed";
		regulator-name = "baseboard_3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&vbat>;
		regulator-always-on;
		regulator-boot-on;
	};

	baseboard_1v8: fixedregulator-1v8 {
		/* TPS73701DCQ */
		compatible = "regulator-fixed";
		regulator-name = "baseboard_1v8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&vbat>;
		regulator-always-on;
		regulator-boot-on;
	};

	backlight_lcd: backlight-regulator {
		compatible = "regulator-fixed";
		regulator-name = "lcd_backlight_pwr";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio 47 GPIO_ACTIVE_HIGH>; /* lcd_backlight_pwr */
		enable-active-high;
	};

	sound {
		compatible = "simple-audio-card";
		simple-audio-card,name = "DA850-OMAPL138 EVM";
		simple-audio-card,widgets =
			"Line", "Line In",
			"Line", "Line Out";
		simple-audio-card,routing =
			"LINE1L", "Line In",
			"LINE1R", "Line In",
			"Line Out", "LLOUT",
			"Line Out", "RLOUT";
		simple-audio-card,format = "dsp_b";
		simple-audio-card,bitclock-master = <&link0_codec>;
		simple-audio-card,frame-master = <&link0_codec>;
		simple-audio-card,bitclock-inversion;

		simple-audio-card,cpu {
			sound-dai = <&mcasp0>;
			system-clock-frequency = <********>;
		};

		link0_codec: simple-audio-card,codec {
			sound-dai = <&tlv320aic3106>;
			system-clock-frequency = <********>;
		};
	};
};

&ecap2 {
	status = "okay";
};

&ref_clk {
	clock-frequency = <********>;
};

&pmx_core {
	status = "okay";

	mcasp0_pins: mcasp0-pins {
		pinctrl-single,bits = <
			/*
			 * AHCLKX, ACLKX, AFSX, AHCLKR, ACLKR,
			 * AFSR, AMUTE
			 */
			0x00 0x11111111 0xffffffff
			/* AXR11, AXR12 */
			0x04 0x00011000 0x000ff000
		>;
	};
	nand_pins: nand-pins {
		pinctrl-single,bits = <
			/* EMA_WAIT[0], EMA_OE, EMA_WE, EMA_CS[4], EMA_CS[3] */
			0x1c 0x10110110  0xf0ff0ff0
			/*
			 * EMA_D[0], EMA_D[1], EMA_D[2],
			 * EMA_D[3], EMA_D[4], EMA_D[5],
			 * EMA_D[6], EMA_D[7]
			 */
			0x24 0x11111111  0xffffffff
			/* EMA_A[1], EMA_A[2] */
			0x30 0x01100000  0x0ff00000
		>;
	};
};

&cpu {
	cpu-supply = <&vdcdc3_reg>;
};

/*
 * The standard da850-evm kits and SOM's are 375MHz so enable this operating
 * point by default. Higher frequencies must be enabled for custom boards with
 * other variants of the SoC.
 */
&opp_375 {
	status = "okay";
};

&sata {
	status = "okay";
};

&serial0 {
	status = "okay";
};

&serial1 {
	status = "okay";
};

&serial2 {
	status = "okay";
};

&rtc0 {
	status = "okay";
};

&lcdc {
	status = "okay";
};

&i2c0 {
	status = "okay";
	clock-frequency = <100000>;
	pinctrl-names = "default";
	pinctrl-0 = <&i2c0_pins>;

	tps: tps@48 {
		reg = <0x48>;
	};
	tlv320aic3106: tlv320aic3106@18 {
		#sound-dai-cells = <0>;
		compatible = "ti,tlv320aic3106";
		reg = <0x18>;
		status = "okay";

		/* Regulators */
		IOVDD-supply = <&vdcdc2_reg>;
		AVDD-supply = <&baseboard_3v3>;
		DRVDD-supply = <&baseboard_3v3>;
		DVDD-supply = <&baseboard_1v8>;
	};
	tca6416: gpio@20 {
		compatible = "ti,tca6416";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
	};
	tca6416_bb: gpio@21 {
		compatible = "ti,tca6416";
		reg = <0x21>;
		gpio-controller;
		#gpio-cells = <2>;
	};
};

&wdt {
	status = "okay";
};

&mmc0 {
	max-frequency = <50000000>;
	bus-width = <4>;
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&mmc0_pins>;
	cd-gpios = <&gpio 64 GPIO_ACTIVE_LOW>;
	wp-gpios = <&gpio 65 GPIO_ACTIVE_HIGH>;
};

&spi1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&spi1_pins &spi1_cs0_pin>;
	flash: flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "jedec,spi-nor";
		spi-max-frequency = <30000000>;
		m25p,fast-read;
		reg = <0>;
		partition@0 {
			label = "U-Boot-SPL";
			reg = <0x00000000 0x00010000>;
			read-only;
		};
		partition@1 {
			label = "U-Boot";
			reg = <0x00010000 0x00080000>;
			read-only;
		};
		partition@2 {
			label = "U-Boot-Env";
			reg = <0x00090000 0x00010000>;
			read-only;
		};
		partition@3 {
			label = "Kernel";
			reg = <0x000a0000 0x00280000>;
		};
		partition@4 {
			label = "Filesystem";
			reg = <0x00320000 0x00400000>;
		};
		partition@5 {
			label = "MAC-Address";
			reg = <0x007f0000 0x00010000>;
			read-only;
		};
	};
};

&mdio {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&mdio_pins>;
	bus_freq = <2200000>;
};

&eth0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&mii_pins>;
};

&gpio {
	status = "okay";
};

/include/ "../../tps6507x.dtsi"

&tps {
	vdcdc1_2-supply = <&vbat>;
	vdcdc3-supply = <&vbat>;
	vldo1_2-supply = <&vbat>;

	regulators {
		vdcdc1_reg: regulator@0 {
			regulator-name = "VDCDC1_3.3V";
			regulator-min-microvolt = <3150000>;
			regulator-max-microvolt = <3450000>;
			regulator-always-on;
			regulator-boot-on;
		};

		vdcdc2_reg: regulator@1 {
			regulator-name = "VDCDC2_3.3V";
			regulator-min-microvolt = <1710000>;
			regulator-max-microvolt = <3450000>;
			regulator-always-on;
			regulator-boot-on;
			ti,defdcdc_default = <1>;
		};

		vdcdc3_reg: regulator@2 {
			regulator-name = "VDCDC3_1.2V";
			regulator-min-microvolt = <950000>;
			regulator-max-microvolt = <1350000>;
			regulator-always-on;
			regulator-boot-on;
			ti,defdcdc_default = <1>;
		};

		ldo1_reg: regulator@3 {
			regulator-name = "LDO1_1.8V";
			regulator-min-microvolt = <1710000>;
			regulator-max-microvolt = <1890000>;
			regulator-always-on;
			regulator-boot-on;
		};

		ldo2_reg: regulator@4 {
			regulator-name = "LDO2_1.2V";
			regulator-min-microvolt = <1140000>;
			regulator-max-microvolt = <1320000>;
			regulator-always-on;
			regulator-boot-on;
		};
	};
};

&mcasp0 {
	#sound-dai-cells = <0>;
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&mcasp0_pins>;

	op-mode = <0>;          /* MCASP_IIS_MODE */
	tdm-slots = <2>;
	/* 4 serializer */
	serial-dir = <  /* 0: INACTIVE, 1: TX, 2: RX */
		0 0 0 0
		0 0 0 0
		0 0 0 1
		2 0 0 0
	>;
	tx-num-evt = <32>;
	rx-num-evt = <32>;
};

&edma0 {
	ti,edma-reserved-slot-ranges = <32 50>;
};

&edma1 {
	ti,edma-reserved-slot-ranges = <32 90>;
};

&aemif {
	pinctrl-names = "default";
	pinctrl-0 = <&nand_pins>;
	status = "okay";
	cs3 {
		#address-cells = <2>;
		#size-cells = <1>;
		clock-ranges;
		ranges;

		ti,cs-chipselect = <3>;

		nand@2000000,0 {
			compatible = "ti,davinci-nand";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0 0x02000000 0x02000000
			       1 0x00000000 0x00008000>;

			ti,davinci-chipselect = <1>;
			ti,davinci-mask-ale = <0>;
			ti,davinci-mask-cle = <0>;
			ti,davinci-mask-chipsel = <0>;
			ti,davinci-ecc-mode = "hw";
			ti,davinci-ecc-bits = <4>;
			ti,davinci-nand-use-bbt;
		};
	};
};

&usb_phy {
	status = "okay";
};

&usb0 {
	status = "okay";
};

&usb1 {
	status = "okay";
};

&vpif {
	pinctrl-names = "default";
	pinctrl-0 = <&vpif_capture_pins>, <&vpif_display_pins>;
	/*
	 * The vpif and the LCD are mutually exclusive.
	 * To enable VPIF, disable the ti,tilcdc,panel then
	 * change the status below to 'okay'
	 */
	status = "disabled";
};
