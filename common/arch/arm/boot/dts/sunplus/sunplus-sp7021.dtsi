// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for Sunplus SP7021
 *
 * Copyright (C) 2021 Sunplus Technology Co.
 */

#include <dt-bindings/clock/sunplus,sp7021-clkc.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/reset/sunplus,sp7021-reset.h>
#include <dt-bindings/pinctrl/sppctl-sp7021.h>
#include <dt-bindings/gpio/gpio.h>

#define XTAL	27000000

/ {
	compatible = "sunplus,sp7021";
	model = "Sunplus SP7021";

	clocks {
		extclk: osc0 {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <XTAL>;
			clock-output-names = "extclk";
		};
	};

	soc@9c000000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x9c000000 0x400000>;
		interrupt-parent = <&intc>;

		clkc: clock-controller@4 {
			compatible = "sunplus,sp7021-clkc";
			reg = <0x4 0x28>,
			      <0x200 0x44>,
			      <0x268 0x04>;
			clocks = <&extclk>;
			#clock-cells = <1>;
		};

		intc: interrupt-controller@780 {
			compatible = "sunplus,sp7021-intc";
			reg = <0x780 0x80>, <0xa80 0x80>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		otp: otp@af00 {
			compatible = "sunplus,sp7021-ocotp";
			reg = <0xaf00 0x34>, <0xaf80 0x58>;
			reg-names = "hb_gpio", "otprx";
			clocks = <&clkc CLK_OTPRX>;
			resets = <&rstc RST_OTPRX>;
			#address-cells = <1>;
			#size-cells = <1>;

			therm_calib: thermal-calibration@14 {
				reg = <0x14 0x3>;
			};
			disc_vol: disconnect-voltage@18 {
				reg = <0x18 0x2>;
			};
			mac_addr0: mac-address0@34 {
				reg = <0x34 0x6>;
			};
			mac_addr1: mac-address1@3a {
				reg = <0x3a 0x6>;
			};
		};

		pctl: pinctrl@100 {
			compatible = "sunplus,sp7021-pctl";
			reg = <0x100 0x100>,
			      <0x300 0x100>,
			      <0x32e4 0x1C>,
			      <0x80 0x20>;
			reg-names = "moon2", "gpioxt", "first", "moon1";
			gpio-controller;
			#gpio-cells = <2>;
			clocks = <&clkc CLK_GPIO>;
			resets = <&rstc RST_GPIO>;

			emac_pins: pinmux-emac-pins {
				sunplus,pins = <
					SPPCTL_IOPAD(49,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_CLK_OUT,0)
					SPPCTL_IOPAD(44,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_MAC_SMI_MDC,0)
					SPPCTL_IOPAD(43,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_MAC_SMI_MDIO,0)
					SPPCTL_IOPAD(52,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P0_MAC_RMII_TXEN,0)
					SPPCTL_IOPAD(50,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P0_MAC_RMII_TXD0,0)
					SPPCTL_IOPAD(51,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P0_MAC_RMII_TXD1,0)
					SPPCTL_IOPAD(46,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P0_MAC_RMII_CRSDV,0)
					SPPCTL_IOPAD(47,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P0_MAC_RMII_RXD0,0)
					SPPCTL_IOPAD(48,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P0_MAC_RMII_RXD1,0)
					SPPCTL_IOPAD(45,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P0_MAC_RMII_RXER,0)
					SPPCTL_IOPAD(59,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P1_MAC_RMII_TXEN,0)
					SPPCTL_IOPAD(57,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P1_MAC_RMII_TXD0,0)
					SPPCTL_IOPAD(58,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P1_MAC_RMII_TXD1,0)
					SPPCTL_IOPAD(54,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P1_MAC_RMII_CRSDV,0)
					SPPCTL_IOPAD(55,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P1_MAC_RMII_RXD0,0)
					SPPCTL_IOPAD(56,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P1_MAC_RMII_RXD1,0)
					SPPCTL_IOPAD(53,SPPCTL_PCTL_G_PMUX,MUXF_L2SW_P1_MAC_RMII_RXER,0)
				>;
				sunplus,zerofunc = <
					MUXF_L2SW_LED_FLASH0
					MUXF_L2SW_LED_FLASH1
					MUXF_L2SW_LED_ON0
					MUXF_L2SW_LED_ON1
					MUXF_DAISY_MODE
				>;
			};

			emmc_pins: pinmux-emmc-pins {
				function = "CARD0_EMMC";
				groups = "CARD0_EMMC";
			};

			leds_pins: pinmux-leds-pins {
				sunplus,pins = < SPPCTL_IOPAD(0,SPPCTL_PCTL_G_GPIO,0,SPPCTL_PCTL_L_OUT) >;
			};

			sdcard_pins: pinmux-sdcard-pins {
				function = "SD_CARD";
				groups = "SD_CARD";
				sunplus,pins = < SPPCTL_IOPAD(91, SPPCTL_PCTL_G_GPIO, 0, 0) >;
			};

			spi0_pins: pinmux-spi0-pins {
				sunplus,pins = <
					SPPCTL_IOPAD(26,SPPCTL_PCTL_G_GPIO,0,0)
					SPPCTL_IOPAD(28,SPPCTL_PCTL_G_GPIO,0,0)
					SPPCTL_IOPAD(23,SPPCTL_PCTL_G_PMUX,MUXF_SPI0S_DO,0)
					SPPCTL_IOPAD(25,SPPCTL_PCTL_G_PMUX,MUXF_SPI0S_DI,0)
					SPPCTL_IOPAD(27,SPPCTL_PCTL_G_PMUX,MUXF_SPI0S_CLK,0)
				>;
			};

			uart0_pins: pinmux-uart0-pins {
				function = "UA0";
				groups = "UA0";
			};

			uart1_pins: pinmux-uart1-pins {
				sunplus,pins = <
					SPPCTL_IOPAD(14,SPPCTL_PCTL_G_PMUX,MUXF_UA4_TX,0)
					SPPCTL_IOPAD(16,SPPCTL_PCTL_G_PMUX,MUXF_UA4_RX,0)
				>;
			};

			uart2_pins: pinmux-uart2-pins {
				sunplus,pins = <
					SPPCTL_IOPAD(16,SPPCTL_PCTL_G_PMUX,MUXF_UA2_TX,0)
					SPPCTL_IOPAD(17,SPPCTL_PCTL_G_PMUX,MUXF_UA2_RX,0)
					SPPCTL_IOPAD(18,SPPCTL_PCTL_G_PMUX,MUXF_UA2_RTS,0)
					SPPCTL_IOPAD(19,SPPCTL_PCTL_G_PMUX,MUXF_UA2_CTS,0)
				>;
			};

			uart4_pins: pinmux-uart4-pins {
				sunplus,pins = <
					SPPCTL_IOPAD(22,SPPCTL_PCTL_G_PMUX,MUXF_UA4_TX,0)
					SPPCTL_IOPAD(20,SPPCTL_PCTL_G_PMUX,MUXF_UA4_RX,0)
					SPPCTL_IOPAD(23,SPPCTL_PCTL_G_PMUX,MUXF_UA4_RTS,0)
					SPPCTL_IOPAD(21,SPPCTL_PCTL_G_PMUX,MUXF_UA4_CTS,0)
				>;
			};
		};

		rstc: reset@54 {
			compatible = "sunplus,sp7021-reset";
			reg = <0x54 0x28>;
			#reset-cells = <1>;
		};

		rtc: rtc@3a00 {
			compatible = "sunplus,sp7021-rtc";
			reg = <0x3a00 0x80>;
			reg-names = "rtc";
			clocks = <&clkc CLK_RTC>;
			resets = <&rstc RST_RTC>;
			interrupts = <163 IRQ_TYPE_EDGE_RISING>;
		};

		spi_controller0: spi@2d80 {
			compatible = "sunplus,sp7021-spi";
			reg = <0x2d80 0x80>, <0x2e00 0x80>;
			reg-names = "master", "slave";
			interrupts = <144 IRQ_TYPE_LEVEL_HIGH>,
				     <146 IRQ_TYPE_LEVEL_HIGH>,
				     <145 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "dma_w", "master_risc", "slave_risc";
			clocks = <&clkc CLK_SPI_COMBO_0>;
			resets = <&rstc RST_SPI_COMBO_0>;

			pinctrl-names = "default";
			pinctrl-0 = <&spi0_pins>;
			cs-gpios = <&pctl 26 GPIO_ACTIVE_LOW>,
				   <&pctl 28 GPIO_ACTIVE_LOW>;
		};

		spi_controller1: spi@f480 {
			compatible = "sunplus,sp7021-spi";
			reg = <0xf480 0x80>, <0xf500 0x80>;
			reg-names = "master", "slave";
			interrupts = <67 IRQ_TYPE_LEVEL_HIGH>,
				     <69 IRQ_TYPE_LEVEL_HIGH>,
				     <68 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "dma_w", "master_risc", "slave_risc";
			clocks = <&clkc CLK_SPI_COMBO_1>;
			resets = <&rstc RST_SPI_COMBO_1>;
			status = "disabled";
		};

		spi_controller2: spi@f600 {
			compatible = "sunplus,sp7021-spi";
			reg = <0xf600 0x80>, <0xf680 0x80>;
			reg-names = "master", "slave";
			interrupts = <70 IRQ_TYPE_LEVEL_HIGH>,
				     <72 IRQ_TYPE_LEVEL_HIGH>,
				     <71 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "dma_w", "master_risc", "slave_risc";
			clocks = <&clkc CLK_SPI_COMBO_2>;
			resets = <&rstc RST_SPI_COMBO_2>;
			status = "disabled";
		};

		spi_controller3: spi@f780 {
			compatible = "sunplus,sp7021-spi";
			reg = <0xf780 0x80>, <0xf800 0x80>;
			reg-names = "master", "slave";
			interrupts = <73 IRQ_TYPE_LEVEL_HIGH>,
				     <75 IRQ_TYPE_LEVEL_HIGH>,
				     <74 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "dma_w", "master_risc", "slave_risc";
			clocks = <&clkc CLK_SPI_COMBO_3>;
			resets = <&rstc RST_SPI_COMBO_3>;
			status = "disabled";
		};

		uart0: serial@900 {
			compatible = "sunplus,sp7021-uart";
			reg = <0x900 0x80>;
			interrupts = <53 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clkc CLK_UA0>;
			resets = <&rstc RST_UA0>;
			pinctrl-names = "default";
			pinctrl-0 = <&uart0_pins>;
		};

		uart1: serial@980 {
			compatible = "sunplus,sp7021-uart";
			reg = <0x980 0x80>;
			interrupts = <54 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clkc CLK_UA1>;
			resets = <&rstc RST_UA1>;
			pinctrl-names = "default";
			pinctrl-0 = <&uart1_pins>;
			status = "disabled";
		};

		uart2: serial@800 {
			compatible = "sunplus,sp7021-uart";
			reg = <0x800 0x80>;
			interrupts = <55 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clkc CLK_UA2>;
			resets = <&rstc RST_UA2>;
			pinctrl-names = "default";
			pinctrl-0 = <&uart2_pins>;
			status = "disabled";
		};

		uart3: serial@880 {
			compatible = "sunplus,sp7021-uart";
			reg = <0x880 0x80>;
			interrupts = <56 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clkc CLK_UA3>;
			resets = <&rstc RST_UA3>;
			status = "disabled";
		};

		uart4: serial@8780 {
			compatible = "sunplus,sp7021-uart";
			reg = <0x8780 0x80>;
			interrupts = <134 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clkc CLK_UA4>;
			resets = <&rstc RST_UA4>;
			pinctrl-names = "default";
			pinctrl-0 = <&uart4_pins>;
			status = "disabled";
		};
	};

	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&leds_pins>;
		system-led {
			label = "system-led";
			gpios = <&pctl 0 GPIO_ACTIVE_HIGH>;
			default-state = "off";
			linux,default-trigger = "heartbeat";
		};
	};
};
