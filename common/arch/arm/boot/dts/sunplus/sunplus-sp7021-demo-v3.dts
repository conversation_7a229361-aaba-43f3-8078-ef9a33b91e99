// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for Sunplus SP7021 Demo V3 SBC board
 *
 * Copyright (C) Sunplus Technology Co.
 */

/dts-v1/;

#include "sunplus-sp7021-achip.dtsi"

/ {
	compatible = "sunplus,sp7021-demo-v3", "sunplus,sp7021";
	model = "Sunplus SP7021/CA7/Demo_V3";
	#address-cells = <1>;
	#size-cells = <1>;

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x20000000>;
	};
};
