// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for Sunplus SP7021
 *
 * Copyright (C) 2021 Sunplus Technology Co.
 */

#include "sunplus-sp7021.dtsi"
#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	compatible = "sunplus,sp7021-achip", "sunplus,sp7021";
	model = "Sunplus SP7021 (CA7)";
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&gic>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0>;
			clock-frequency = <931000000>;
		};
		cpu1: cpu@1 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <1>;
			clock-frequency = <931000000>;
		};
		cpu2: cpu@2 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <2>;
			clock-frequency = <931000000>;
		};
		cpu3: cpu@3 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <3>;
			clock-frequency = <931000000>;
		};
	};

	gic: interrupt-controller@9f101000 {
		compatible = "arm,cortex-a7-gic";
		interrupt-controller;
		#interrupt-cells = <3>;
		reg = <0x9f101000 0x1000>,
		      <0x9f102000 0x2000>,
		      <0x9f104000 0x2000>,
		      <0x9f106000 0x2000>;
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
		clock-frequency = <XTAL>;
		arm,cpu-registers-not-fw-configured;
	};

	arm-pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 219 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 220 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 221 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 222 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
	};

	soc@9c000000 {
		intc: interrupt-controller@780 {
			interrupt-parent = <&gic>;
			interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>, /* EXT_INT0 */
				     <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>; /* EXT_INT1 */
		};
	};
};
