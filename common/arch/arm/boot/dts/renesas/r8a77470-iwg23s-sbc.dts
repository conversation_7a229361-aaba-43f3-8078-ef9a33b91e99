// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the iWave-RZ/G1C single board computer
 *
 * Copyright (C) 2018 Renesas Electronics Corp.
 */

/dts-v1/;
#include <dt-bindings/gpio/gpio.h>
#include "r8a77470.dtsi"
/ {
	model = "iWave iW-RainboW-G23S single board computer based on RZ/G1C";
	compatible = "iwave,g23s", "renesas,r8a77470";

	aliases {
		ethernet0 = &avb;
		serial1 = &scif1;
	};

	chosen {
		bootargs = "ignore_loglevel rw root=/dev/nfs ip=on";
		stdout-path = "serial1:115200n8";
	};

	hdmi-out {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con: endpoint {
				remote-endpoint = <&bridge_out>;
			};
		};
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0 0x40000000 0 0x20000000>;
	};

	reg_1p8v: reg-1p8v {
		compatible = "regulator-fixed";
		regulator-name = "fixed-1.8V";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-boot-on;
		regulator-always-on;
	};

	reg_3p3v: reg-3p3v {
		compatible = "regulator-fixed";
		regulator-name = "fixed-3.3V";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
		regulator-always-on;
	};

	vccq_sdhi2: regulator-vccq-sdhi2 {
		compatible = "regulator-gpio";

		regulator-name = "SDHI2 VccQ";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <3300000>;

		gpios = <&gpio2 24 GPIO_ACTIVE_HIGH>;
		gpios-states = <1>;
		states = <3300000 1>, <1800000 0>;
	};
};

&avb {
	pinctrl-0 = <&avb_pins>;
	pinctrl-names = "default";

	phy-handle = <&phy3>;
	phy-mode = "gmii";
	renesas,no-ether-link;
	status = "okay";

	phy3: ethernet-phy@3 {
		compatible = "ethernet-phy-id0022.1622",
			     "ethernet-phy-ieee802.3-c22";
		reg = <3>;
		interrupt-parent = <&gpio5>;
		interrupts = <16 IRQ_TYPE_LEVEL_LOW>;
		micrel,led-mode = <1>;
	};
};

&cmt0 {
	status = "okay";
};

&du {
	pinctrl-0 = <&du0_pins>;
	pinctrl-names = "default";

	status = "okay";

	ports {
		port@0 {
			endpoint {
				remote-endpoint = <&bridge_in>;
			};
		};
	};
};

&ehci1 {
	status = "okay";
};

&extal_clk {
	clock-frequency = <20000000>;
};

&gpio2 {
	interrupt-fixup-hog {
		gpio-hog;
		gpios = <29 GPIO_ACTIVE_HIGH>;
		line-name = "hdmi-hpd-int";
		input;
	};
};

&hsusb0 {
	status = "okay";
};

&i2c3 {
	pinctrl-0 = <&i2c3_pins>;
	pinctrl-names = "default";

	status = "okay";
	clock-frequency = <400000>;

	rtc@51 {
		compatible = "nxp,pcf85263";
		reg = <0x51>;
	};
};

&i2c4 {
	pinctrl-0 = <&i2c4_pins>;
	pinctrl-names = "default";

	status = "okay";
	clock-frequency = <100000>;

	hdmi@39 {
		compatible = "sil,sii9022";
		reg = <0x39>;
		interrupt-parent = <&gpio2>;
		interrupts = <29 IRQ_TYPE_LEVEL_LOW>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				bridge_in: endpoint {
					remote-endpoint = <&du_out_rgb0>;
				};
			};

			port@1 {
				reg = <1>;
				bridge_out: endpoint {
					remote-endpoint = <&hdmi_con>;
				};
			};
		};
	};
};

&ohci1 {
	status = "okay";
};

&pfc {
	avb_pins: avb {
		groups = "avb_mdio", "avb_gmii_tx_rx";
		function = "avb";
	};

	du0_pins: du0 {
		groups = "du0_rgb888", "du0_sync", "du0_disp", "du0_clk0_out";
		function = "du0";
	};

	i2c4_pins: i2c4 {
		groups = "i2c4_e";
		function = "i2c4";
	};

	i2c3_pins: i2c3 {
		groups = "i2c3_c";
		function = "i2c3";
	};

	mmc_pins_uhs: mmc_uhs {
		groups = "mmc_data8", "mmc_ctrl";
		function = "mmc";
		power-source = <1800>;
	};

	qspi0_pins: qspi0 {
		groups = "qspi0_ctrl", "qspi0_data2";
		function = "qspi0";
	};

	scif1_pins: scif1 {
		groups = "scif1_data_b";
		function = "scif1";
	};

	sdhi2_pins: sd2 {
		groups = "sdhi2_data4", "sdhi2_ctrl";
		function = "sdhi2";
		power-source = <3300>;
	};

	sdhi2_pins_uhs: sd2_uhs {
		groups = "sdhi2_data4", "sdhi2_ctrl";
		function = "sdhi2";
		power-source = <1800>;
	};

	usb0_pins: usb0 {
		groups = "usb0";
		function = "usb0";
	};

	usb1_pins: usb1 {
		groups = "usb1";
		function = "usb1";
	};
};

&qspi0 {
	pinctrl-0 = <&qspi0_pins>;
	pinctrl-names = "default";

	status = "okay";

	/* WARNING - This device contains the bootloader. Handle with care. */
	flash: flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "issi,is25lp016d", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <133000000>;
		spi-tx-bus-width = <1>;
		spi-rx-bus-width = <1>;
		m25p,fast-read;
		spi-cpol;
		spi-cpha;
	};
};

&rwdt {
	timeout-sec = <60>;
	status = "okay";
};

&scif1 {
	pinctrl-0 = <&scif1_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&sdhi1 {
	pinctrl-0 = <&mmc_pins_uhs>;
	pinctrl-names = "state_uhs";

	vmmc-supply = <&reg_3p3v>;
	vqmmc-supply = <&reg_1p8v>;
	bus-width = <8>;
	mmc-hs200-1_8v;
	non-removable;
	fixed-emmc-driver-type = <1>;
	status = "okay";
};

&sdhi2 {
	pinctrl-0 = <&sdhi2_pins>;
	pinctrl-1 = <&sdhi2_pins_uhs>;
	pinctrl-names = "default", "state_uhs";

	vmmc-supply = <&reg_3p3v>;
	vqmmc-supply = <&vccq_sdhi2>;
	bus-width = <4>;
	cd-gpios = <&gpio4 20 GPIO_ACTIVE_LOW>;
	sd-uhs-sdr50;
	status = "okay";
};

&usb2_phy0 {
	status = "okay";
};

&usb2_phy1 {
	status = "okay";
};

&usbphy0 {
	pinctrl-0 = <&usb0_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&usbphy1 {
	pinctrl-0 = <&usb1_pins>;
	pinctrl-names = "default";

	status = "okay";
};
