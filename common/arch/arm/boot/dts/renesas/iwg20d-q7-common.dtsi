// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the iWave-RZ/G1M/G1N Qseven carrier board
 *
 * Copyright (C) 2017 Renesas Electronics Corp.
 */

/*
 * SSI-SGTL5000
 *
 * This command is required when Playback/Capture
 *
 *      amixer set "DVC Out" 100%
 *      amixer set "DVC In" 100%
 *
 * You can use Mute
 *
 *      amixer set "DVC Out Mute" on
 *      amixer set "DVC In Mute" on
 *
 * You can use Volume Ramp
 *
 *      amixer set "DVC Out Ramp Up Rate"   "0.125 dB/64 steps"
 *      amixer set "DVC Out Ramp Down Rate" "0.125 dB/512 steps"
 *      amixer set "DVC Out Ramp" on
 *      aplay xxx.wav &
 *      amixer set "DVC Out"  80%  // Volume Down
 *      amixer set "DVC Out" 100%  // Volume Up
 */

/ {
	aliases {
		serial0 = &scif0;
		serial3 = &scifb1;
		ethernet0 = &avb;
	};

	chosen {
		bootargs = "ignore_loglevel rw root=/dev/nfs ip=dhcp";
		stdout-path = "serial0:115200n8";
	};

	audio_clock: audio_clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <26000000>;
	};

	lcd_backlight: backlight {
		compatible = "pwm-backlight";

		pwms = <&pwm3 0 5000000>;
		brightness-levels = <0 4 8 16 32 64 128 255>;
		default-brightness-level = <7>;
		enable-gpios = <&gpio5 14 GPIO_ACTIVE_HIGH>;
	};

	lvds-receiver {
		compatible = "ti,ds90cf384a", "lvds-decoder";
		power-supply = <&vcc_3v3_tft1>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				lvds_receiver_in: endpoint {
					remote-endpoint = <&lvds0_out>;
				};
			};
			port@1 {
				reg = <1>;
				lvds_receiver_out: endpoint {
					remote-endpoint = <&panel_in>;
				};
			};
		};
	};

	panel {
		compatible = "edt,etm0700g0dh6";
		backlight = <&lcd_backlight>;
		power-supply = <&vcc_3v3_tft1>;

		port {
			panel_in: endpoint {
				remote-endpoint = <&lvds_receiver_out>;
			};
		};
	};

	reg_1p5v: 1p5v {
		compatible = "regulator-fixed";
		regulator-name = "1P5V";
		regulator-min-microvolt = <1500000>;
		regulator-max-microvolt = <1500000>;
		regulator-always-on;
	};

	rsnd_sgtl5000: sound {
		compatible = "simple-audio-card";

		simple-audio-card,format = "i2s";
		simple-audio-card,bitclock-master = <&sndcodec>;
		simple-audio-card,frame-master = <&sndcodec>;

		sndcpu: simple-audio-card,cpu {
			sound-dai = <&rcar_sound>;
		};

		sndcodec: simple-audio-card,codec {
			sound-dai = <&sgtl5000>;
		};
	};

	vcc_3v3_tft1: regulator-panel {
		compatible = "regulator-fixed";

		regulator-name = "vcc-3v3-tft1";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		enable-active-high;
		startup-delay-us = <500>;
		gpio = <&gpio7 25 GPIO_ACTIVE_HIGH>;
	};

	vcc_sdhi1: regulator-vcc-sdhi1 {
		compatible = "regulator-fixed";

		regulator-name = "SDHI1 Vcc";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;

		gpio = <&gpio1 16 GPIO_ACTIVE_LOW>;
	};

	vccq_sdhi1: regulator-vccq-sdhi1 {
		compatible = "regulator-gpio";

		regulator-name = "SDHI1 VccQ";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <3300000>;

		gpios = <&gpio2 30 GPIO_ACTIVE_HIGH>;
		gpios-states = <1>;
		states = <3300000 1>, <1800000 0>;
	};
};

&avb {
	pinctrl-0 = <&avb_pins>;
	pinctrl-names = "default";

	phy-handle = <&phy3>;
	phy-mode = "gmii";
	renesas,no-ether-link;
	status = "okay";

	phy3: ethernet-phy@3 {
		compatible = "ethernet-phy-id0022.1622",
			     "ethernet-phy-ieee802.3-c22";
		reg = <3>;
		micrel,led-mode = <1>;
	};
};

&can0 {
	pinctrl-0 = <&can0_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&cmt0 {
	status = "okay";
};

&du {
	status = "okay";
};

&gpio2 {
	touch-interrupt-hog {
		gpio-hog;
		gpios = <12 GPIO_ACTIVE_LOW>;
		input;
	};
};

&hsusb {
	status = "okay";
	pinctrl-0 = <&usb0_pins>;
	pinctrl-names = "default";
};

&i2c2 {
	pinctrl-0 = <&i2c2_pins>;
	pinctrl-names = "default";

	status = "okay";
	clock-frequency = <400000>;

	rtc@68 {
		compatible = "ti,bq32000";
		reg = <0x68>;
	};

	sgtl5000: codec@a {
		compatible = "fsl,sgtl5000";
		#sound-dai-cells = <0>;
		reg = <0x0a>;
		clocks = <&audio_clock>;
		VDDA-supply = <&reg_3p3v>;
		VDDIO-supply = <&reg_3p3v>;
		VDDD-supply = <&reg_1p5v>;
	};

	touch: touchpanel@38 {
		compatible = "edt,edt-ft5406";
		reg = <0x38>;
		interrupt-parent = <&gpio2>;
		interrupts = <12 IRQ_TYPE_EDGE_FALLING>;
		vcc-supply = <&vcc_3v3_tft1>;
	};
};

&lvds0 {
	status = "okay";

	ports {
		port@1 {
			lvds0_out: endpoint {
				remote-endpoint = <&lvds_receiver_in>;
			};
		};
	};
};

&pci0 {
	pinctrl-0 = <&usb0_pins>;
	pinctrl-names = "default";
};

&pci1 {
	status = "okay";
	pinctrl-0 = <&usb1_pins>;
	pinctrl-names = "default";
};

&pcie_bus_clk {
	clock-frequency = <100000000>;
};

&pfc {
	can0_pins: can0 {
		groups = "can0_data_d";
		function = "can0";
	};

	avb_pins: avb {
		groups = "avb_mdio", "avb_gmii";
		function = "avb";
	};

	i2c2_pins: i2c2 {
		groups = "i2c2";
		function = "i2c2";
	};

	pwm3_pins: pwm3 {
		groups = "pwm3";
		function = "pwm3";
	};

	scif0_pins: scif0 {
		groups = "scif0_data_d";
		function = "scif0";
	};

	scifb1_pins: scifb1 {
		groups = "scifb1_data_d", "scifb1_ctrl";
		function = "scifb1";
	};

	sdhi1_pins: sd1 {
		groups = "sdhi1_data4", "sdhi1_ctrl";
		function = "sdhi1";
		power-source = <3300>;
	};

	sdhi1_pins_uhs: sd1_uhs {
		groups = "sdhi1_data4", "sdhi1_ctrl";
		function = "sdhi1";
		power-source = <1800>;
	};

	sound_pins: sound {
		groups = "ssi0129_ctrl", "ssi0_data", "ssi1_data";
		function = "ssi";
	};

	usb0_pins: usb0 {
		groups = "usb0";
		function = "usb0";
	};

	usb1_pins: usb1 {
		groups = "usb1";
		function = "usb1";
	};
};

&pwm3 {
	pinctrl-0 = <&pwm3_pins>;
	pinctrl-names = "default";
	status = "okay";
};

&rcar_sound {
	pinctrl-0 = <&sound_pins>;
	pinctrl-names = "default";
	status = "okay";

	/* Single DAI */
	#sound-dai-cells = <0>;

	rcar_sound,dai {
		dai0 {
			playback = <&ssi1>, <&src3>, <&dvc1>;
			capture = <&ssi0>, <&src2>, <&dvc0>;
		};
	};
};

&rwdt {
	timeout-sec = <60>;
	status = "okay";
};

&scif0 {
	pinctrl-0 = <&scif0_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&scifb1 {
	pinctrl-0 = <&scifb1_pins>;
	pinctrl-names = "default";

	uart-has-rtscts;
	status = "okay";
};

&sdhi1 {
	pinctrl-0 = <&sdhi1_pins>;
	pinctrl-1 = <&sdhi1_pins_uhs>;
	pinctrl-names = "default", "state_uhs";

	vmmc-supply = <&vcc_sdhi1>;
	vqmmc-supply = <&vccq_sdhi1>;
	cd-gpios = <&gpio6 14 GPIO_ACTIVE_LOW>;
	wp-gpios = <&gpio6 15 GPIO_ACTIVE_HIGH>;
	sd-uhs-sdr50;
	status = "okay";
};

&ssi1 {
	shared-pin;
};

&usbphy {
	status = "okay";
};
