// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the armadillo 800 eva board
 *
 * Copyright (C) 2012 Renesas Solutions Corp.
 */

/dts-v1/;
#include "r8a7740.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/pwm/pwm.h>

/ {
	model = "armadillo 800 eva";
	compatible = "renesas,armadillo800eva", "renesas,r8a7740";

	aliases {
		serial0 = &scifa1;
	};

	chosen {
		bootargs = "earlyprintk ignore_loglevel root=/dev/nfs ip=on rw";
		stdout-path = "serial0:115200n8";
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x20000000>;
	};

	reg_3p3v: regulator-3p3v {
		compatible = "regulator-fixed";
		regulator-name = "fixed-3.3V";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
	};

	vcc_sdhi0: regulator-vcc-sdhi0 {
		compatible = "regulator-fixed";

		regulator-name = "SDHI0 Vcc";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;

		gpio = <&pfc 75 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	vccq_sdhi0: regulator-vccq-sdhi0 {
		compatible = "regulator-gpio";

		regulator-name = "SDHI0 VccQ";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&vcc_sdhi0>;

		enable-gpios = <&pfc 74 GPIO_ACTIVE_HIGH>;
		gpios = <&pfc 17 GPIO_ACTIVE_HIGH>;
		states = <3300000 0>, <1800000 1>;

		enable-active-high;
	};

	reg_5p0v: regulator-5p0v {
		compatible = "regulator-fixed";
		regulator-name = "fixed-5.0V";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	keyboard {
		compatible = "gpio-keys";

		power-key {
			gpios = <&pfc 99 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			label = "SW3";
			wakeup-source;
		};

		back-key {
			gpios = <&pfc 100 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_BACK>;
			label = "SW4";
		};

		menu-key {
			gpios = <&pfc 97 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_MENU>;
			label = "SW5";
		};

		home-key {
			gpios = <&pfc 98 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_HOME>;
			label = "SW6";
		};
	};

	leds {
		compatible = "gpio-leds";
		led3 {
			gpios = <&pfc 102 GPIO_ACTIVE_HIGH>;
			label = "LED3";
		};
		led4 {
			gpios = <&pfc 111 GPIO_ACTIVE_HIGH>;
			label = "LED4";
		};
		led5 {
			gpios = <&pfc 110 GPIO_ACTIVE_HIGH>;
			label = "LED5";
		};
		led6 {
			gpios = <&pfc 177 GPIO_ACTIVE_HIGH>;
			label = "LED6";
		};
	};

	i2c2: i2c-2 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "i2c-gpio";
		sda-gpios = <&pfc 208 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		scl-gpios = <&pfc 91 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		i2c-gpio,delay-us = <5>;
	};

	backlight {
		compatible = "pwm-backlight";
		pwms = <&tpu 2 33333 PWM_POLARITY_INVERTED>;
		brightness-levels = <0 1 2 4 8 16 32 64 128 255>;
		default-brightness-level = <9>;
		pinctrl-0 = <&backlight_pins>;
		pinctrl-names = "default";
		power-supply = <&reg_5p0v>;
		enable-gpios = <&pfc 61 GPIO_ACTIVE_HIGH>;
	};

	sound {
		compatible = "simple-audio-card";

		simple-audio-card,format = "i2s";

		simple-audio-card,cpu {
			sound-dai = <&sh_fsi2 0>;
			bitclock-inversion;
		};

		simple-audio-card,codec {
			sound-dai = <&wm8978>;
			bitclock-master;
			frame-master;
			system-clock-frequency = <12288000>;
		};
	};
};

&ether {
	pinctrl-0 = <&ether_pins>;
	pinctrl-names = "default";

	phy-handle = <&phy0>;
	status = "okay";

	phy0: ethernet-phy@0 {
		compatible = "ethernet-phy-id0007.c0f1",
			     "ethernet-phy-ieee802.3-c22";
		reg = <0>;
		reset-gpios = <&pfc 18 GPIO_ACTIVE_LOW>;
	};
};

&extal1_clk {
	clock-frequency = <24000000>;
};
&extal2_clk {
	clock-frequency = <48000000>;
};
&fsibck_clk {
	clock-frequency = <12288000>;
};
&cpg_clocks {
	renesas,mode = <0x05>; /* MD_CK0 | MD_CK2 */
};

&cmt1 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	wm8978: codec@1a {
		#sound-dai-cells = <0>;
		compatible = "wlf,wm8978";
		reg = <0x1a>;
	};

	eeprom@50 {
		compatible = "st,24c01", "atmel,24c01";
		reg = <0x50>;
		pagesize = <16>;
	};

	touchscreen@55 {
		compatible = "sitronix,st1232";
		reg = <0x55>;
		interrupt-parent = <&irqpin1>;
		interrupts = <2 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-0 = <&st1232_pins>;
		pinctrl-names = "default";
		gpios = <&pfc 166 GPIO_ACTIVE_LOW>;
	};
};

&i2c2 {
	status = "okay";
	rtc@30 {
		compatible = "sii,s35390a";
		reg = <0x30>;
	};
};

&pfc {
	pinctrl-0 = <&lcd0_pins>;
	pinctrl-names = "default";

	ether_pins: ether {
		groups = "gether_mii", "gether_int";
		function = "gether";
	};

	scifa1_pins: scifa1 {
		groups = "scifa1_data";
		function = "scifa1";
	};

	st1232_pins: touchscreen {
		groups = "intc_irq10";
		function = "intc";
	};

	backlight_pins: backlight {
		groups = "tpu0_to2_1";
		function = "tpu0";
	};

	mmc0_pins: mmc0 {
		groups = "mmc0_data8_1", "mmc0_ctrl_1";
		function = "mmc0";
	};

	sdhi0_pins: sd0 {
		groups = "sdhi0_data4", "sdhi0_ctrl", "sdhi0_wp";
		function = "sdhi0";
	};

	fsia_pins: sounda {
		groups = "fsia_sclk_in", "fsia_mclk_out",
			 "fsia_data_in_1", "fsia_data_out_0";
		function = "fsia";
	};

	lcd0_pins: lcd0 {
		groups = "lcd0_data24_0", "lcd0_lclk_1", "lcd0_sync";
		function = "lcd0";
	};

	lcd0-mux-hog {
		/* DBGMD/LCDC0/FSIA MUX */
		gpio-hog;
		gpios = <176 0>;
		output-high;
	};
};

&tpu {
	status = "okay";
};

&mmcif0 {
	pinctrl-0 = <&mmc0_pins>;
	pinctrl-names = "default";

	vmmc-supply = <&reg_3p3v>;
	bus-width = <8>;
	non-removable;
	status = "okay";
};

&scifa1 {
	pinctrl-0 = <&scifa1_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&sdhi0 {
	pinctrl-0 = <&sdhi0_pins>;
	pinctrl-names = "default";

	vmmc-supply = <&vcc_sdhi0>;
	vqmmc-supply = <&vccq_sdhi0>;
	bus-width = <4>;
	cd-gpios = <&pfc 167 GPIO_ACTIVE_LOW>;
	status = "okay";
};

&sh_fsi2 {
	pinctrl-0 = <&fsia_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&tmu0 {
	status = "okay";
};
