// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the r8a73a4 SoC
 *
 * Copyright (C) 2013 Renesas Solutions Corp.
 * Copyright (C) 2013 <PERSON> Damm
 */

#include <dt-bindings/clock/r8a73a4-clock.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	compatible = "renesas,r8a73a4";
	interrupt-parent = <&gic>;
	#address-cells = <2>;
	#size-cells = <2>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <0>;
			clocks = <&cpg_clocks R8A73A4_CLK_Z>;
			clock-frequency = <**********>;
			power-domains = <&pd_a2sl>;
			next-level-cache = <&L2_CA15>;
		};

		L2_CA15: cache-controller-0 {
			compatible = "cache";
			clocks = <&cpg_clocks R8A73A4_CLK_Z>;
			power-domains = <&pd_a3sm>;
			cache-unified;
			cache-level = <2>;
		};

		L2_CA7: cache-controller-1 {
			compatible = "cache";
			clocks = <&cpg_clocks R8A73A4_CLK_Z2>;
			power-domains = <&pd_a3km>;
			cache-unified;
			cache-level = <2>;
		};
	};

	ptm {
		compatible = "arm,coresight-etm3x";
		power-domains = <&pd_d4>;
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>;
	};

	dbsc1: memory-controller@e6790000 {
		compatible = "renesas,dbsc-r8a73a4";
		reg = <0 0xe6790000 0 0x10000>;
		power-domains = <&pd_a3bc>;
	};

	dbsc2: memory-controller@e67a0000 {
		compatible = "renesas,dbsc-r8a73a4";
		reg = <0 0xe67a0000 0 0x10000>;
		power-domains = <&pd_a3bc>;
	};

	i2c5: i2c@e60b0000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,iic-r8a73a4", "renesas,rmobile-iic";
		reg = <0 0xe60b0000 0 0x428>;
		interrupts = <GIC_SPI 179 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp4_clks R8A73A4_CLK_IIC5>;
		power-domains = <&pd_a3sp>;

		status = "disabled";
	};

	cmt1: timer@e6130000 {
		compatible = "renesas,r8a73a4-cmt1", "renesas,rcar-gen2-cmt1";
		reg = <0 0xe6130000 0 0x1004>;
		interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A73A4_CLK_CMT1>;
		clock-names = "fck";
		power-domains = <&pd_c5>;
		status = "disabled";
	};

	irqc0: interrupt-controller@e61c0000 {
		compatible = "renesas,irqc-r8a73a4", "renesas,irqc";
		#interrupt-cells = <2>;
		interrupt-controller;
		reg = <0 0xe61c0000 0 0x200>;
		interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp4_clks R8A73A4_CLK_IRQC>;
		power-domains = <&pd_c4>;
	};

	irqc1: interrupt-controller@e61c0200 {
		compatible = "renesas,irqc-r8a73a4", "renesas,irqc";
		#interrupt-cells = <2>;
		interrupt-controller;
		reg = <0 0xe61c0200 0 0x200>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 47 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 48 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 49 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 51 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 53 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 56 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 57 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp4_clks R8A73A4_CLK_IRQC>;
		power-domains = <&pd_c4>;
	};

	pfc: pinctrl@e6050000 {
		compatible = "renesas,pfc-r8a73a4";
		reg = <0 0xe6050000 0 0x9000>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges =
			<&pfc 0 0 31>, <&pfc 32 32 9>,
			<&pfc 64 64 22>, <&pfc 96 96 31>,
			<&pfc 128 128 7>, <&pfc 160 160 19>,
			<&pfc 192 192 31>, <&pfc 224 224 27>,
			<&pfc 256 256 28>, <&pfc 288 288 21>,
			<&pfc 320 320 10>;
		interrupts-extended =
			<&irqc0  0 0>, <&irqc0  1 0>, <&irqc0  2 0>, <&irqc0  3 0>,
			<&irqc0  4 0>, <&irqc0  5 0>, <&irqc0  6 0>, <&irqc0  7 0>,
			<&irqc0  8 0>, <&irqc0  9 0>, <&irqc0 10 0>, <&irqc0 11 0>,
			<&irqc0 12 0>, <&irqc0 13 0>, <&irqc0 14 0>, <&irqc0 15 0>,
			<&irqc0 16 0>, <&irqc0 17 0>, <&irqc0 18 0>, <&irqc0 19 0>,
			<&irqc0 20 0>, <&irqc0 21 0>, <&irqc0 22 0>, <&irqc0 23 0>,
			<&irqc0 24 0>, <&irqc0 25 0>, <&irqc0 26 0>, <&irqc0 27 0>,
			<&irqc0 28 0>, <&irqc0 29 0>, <&irqc0 30 0>, <&irqc0 31 0>,
			<&irqc1  0 0>, <&irqc1  1 0>, <&irqc1  2 0>, <&irqc1  3 0>,
			<&irqc1  4 0>, <&irqc1  5 0>, <&irqc1  6 0>, <&irqc1  7 0>,
			<&irqc1  8 0>, <&irqc1  9 0>, <&irqc1 10 0>, <&irqc1 11 0>,
			<&irqc1 12 0>, <&irqc1 13 0>, <&irqc1 14 0>, <&irqc1 15 0>,
			<&irqc1 16 0>, <&irqc1 17 0>, <&irqc1 18 0>, <&irqc1 19 0>,
			<&irqc1 20 0>, <&irqc1 21 0>, <&irqc1 22 0>, <&irqc1 23 0>,
			<&irqc1 24 0>, <&irqc1 25 0>;
		power-domains = <&pd_c5>;
	};

	thermal@e61f0000 {
		compatible = "renesas,thermal-r8a73a4", "renesas,rcar-thermal";
		reg = <0 0xe61f0000 0 0x14>, <0 0xe61f0100 0 0x38>,
			 <0 0xe61f0200 0 0x38>, <0 0xe61f0300 0 0x38>;
		interrupts = <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp5_clks R8A73A4_CLK_THERMAL>;
		power-domains = <&pd_c5>;
	};

	i2c0: i2c@e6500000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,iic-r8a73a4", "renesas,rmobile-iic";
		reg = <0 0xe6500000 0 0x428>;
		interrupts = <GIC_SPI 174 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A73A4_CLK_IIC0>;
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	i2c1: i2c@e6510000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,iic-r8a73a4", "renesas,rmobile-iic";
		reg = <0 0xe6510000 0 0x428>;
		interrupts = <GIC_SPI 175 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A73A4_CLK_IIC1>;
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	i2c2: i2c@e6520000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,iic-r8a73a4", "renesas,rmobile-iic";
		reg = <0 0xe6520000 0 0x428>;
		interrupts = <GIC_SPI 176 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A73A4_CLK_IIC2>;
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	i2c3: i2c@e6530000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,iic-r8a73a4", "renesas,rmobile-iic";
		reg = <0 0xe6530000 0 0x428>;
		interrupts = <GIC_SPI 177 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp4_clks R8A73A4_CLK_IIC3>;
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	i2c4: i2c@e6540000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,iic-r8a73a4", "renesas,rmobile-iic";
		reg = <0 0xe6540000 0 0x428>;
		interrupts = <GIC_SPI 178 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp4_clks R8A73A4_CLK_IIC4>;
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	i2c6: i2c@e6550000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,iic-r8a73a4", "renesas,rmobile-iic";
		reg = <0 0xe6550000 0 0x428>;
		interrupts = <GIC_SPI 184 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A73A4_CLK_IIC6>;
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	i2c7: i2c@e6560000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,iic-r8a73a4", "renesas,rmobile-iic";
		reg = <0 0xe6560000 0 0x428>;
		interrupts = <GIC_SPI 185 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A73A4_CLK_IIC7>;
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	i2c8: i2c@e6570000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,iic-r8a73a4", "renesas,rmobile-iic";
		reg = <0 0xe6570000 0 0x428>;
		interrupts = <GIC_SPI 173 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp5_clks R8A73A4_CLK_IIC8>;
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	scifb0: serial@e6c20000 {
		compatible = "renesas,scifb-r8a73a4", "renesas,scifb";
		reg = <0 0xe6c20000 0 0x100>;
		interrupts = <GIC_SPI 148 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp2_clks R8A73A4_CLK_SCIFB0>;
		clock-names = "fck";
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	scifb1: serial@e6c30000 {
		compatible = "renesas,scifb-r8a73a4", "renesas,scifb";
		reg = <0 0xe6c30000 0 0x100>;
		interrupts = <GIC_SPI 149 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp2_clks R8A73A4_CLK_SCIFB1>;
		clock-names = "fck";
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	scifa0: serial@e6c40000 {
		compatible = "renesas,scifa-r8a73a4", "renesas,scifa";
		reg = <0 0xe6c40000 0 0x100>;
		interrupts = <GIC_SPI 144 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp2_clks R8A73A4_CLK_SCIFA0>;
		clock-names = "fck";
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	scifa1: serial@e6c50000 {
		compatible = "renesas,scifa-r8a73a4", "renesas,scifa";
		reg = <0 0xe6c50000 0 0x100>;
		interrupts = <GIC_SPI 145 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp2_clks R8A73A4_CLK_SCIFA1>;
		clock-names = "fck";
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	scifb2: serial@e6ce0000 {
		compatible = "renesas,scifb-r8a73a4", "renesas,scifb";
		reg = <0 0xe6ce0000 0 0x100>;
		interrupts = <GIC_SPI 150 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp2_clks R8A73A4_CLK_SCIFB2>;
		clock-names = "fck";
		power-domains = <&pd_a3sp>;
		status = "disabled";
	};

	scifb3: serial@e6cf0000 {
		compatible = "renesas,scifb-r8a73a4", "renesas,scifb";
		reg = <0 0xe6cf0000 0 0x100>;
		interrupts = <GIC_SPI 151 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp2_clks R8A73A4_CLK_SCIFB3>;
		clock-names = "fck";
		power-domains = <&pd_c4>;
		status = "disabled";
	};

	sdhi0: mmc@ee100000 {
		compatible = "renesas,sdhi-r8a73a4";
		reg = <0 0xee100000 0 0x100>;
		interrupts = <GIC_SPI 165 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A73A4_CLK_SDHI0>;
		power-domains = <&pd_a3sp>;
		cap-sd-highspeed;
		status = "disabled";
	};

	sdhi1: mmc@ee120000 {
		compatible = "renesas,sdhi-r8a73a4";
		reg = <0 0xee120000 0 0x100>;
		interrupts = <GIC_SPI 166 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A73A4_CLK_SDHI1>;
		power-domains = <&pd_a3sp>;
		cap-sd-highspeed;
		status = "disabled";
	};

	sdhi2: mmc@ee140000 {
		compatible = "renesas,sdhi-r8a73a4";
		reg = <0 0xee140000 0 0x100>;
		interrupts = <GIC_SPI 167 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A73A4_CLK_SDHI2>;
		power-domains = <&pd_a3sp>;
		cap-sd-highspeed;
		status = "disabled";
	};

	mmcif0: mmc@ee200000 {
		compatible = "renesas,mmcif-r8a73a4", "renesas,sh-mmcif";
		reg = <0 0xee200000 0 0x80>;
		interrupts = <GIC_SPI 169 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A73A4_CLK_MMCIF0>;
		power-domains = <&pd_a3sp>;
		reg-io-width = <4>;
		status = "disabled";
	};

	mmcif1: mmc@ee220000 {
		compatible = "renesas,mmcif-r8a73a4", "renesas,sh-mmcif";
		reg = <0 0xee220000 0 0x80>;
		interrupts = <GIC_SPI 170 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A73A4_CLK_MMCIF1>;
		power-domains = <&pd_a3sp>;
		reg-io-width = <4>;
		status = "disabled";
	};

	gic: interrupt-controller@f1001000 {
		compatible = "arm,gic-400";
		#interrupt-cells = <3>;
		#address-cells = <0>;
		interrupt-controller;
		reg = <0 0xf1001000 0 0x1000>,
			<0 0xf1002000 0 0x2000>,
			<0 0xf1004000 0 0x2000>,
			<0 0xf1006000 0 0x2000>;
		interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_HIGH)>;
		clocks = <&mstp4_clks R8A73A4_CLK_INTC_SYS>;
		clock-names = "clk";
		power-domains = <&pd_c4>;
	};

	bsc: bus@fec10000 {
		compatible = "renesas,bsc-r8a73a4", "renesas,bsc",
			     "simple-pm-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0 0 0x20000000>;
		reg = <0 0xfec10000 0 0x400>;
		clocks = <&zb_clk>;
		power-domains = <&pd_c4>;
	};

	clocks {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		/* External root clocks */
		extalr_clk: extalr {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			/* This value must be overridden by the board. */
			clock-frequency = <0>;
		};
		extal1_clk: extal1 {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			/* This value must be overridden by the board. */
			clock-frequency = <0>;
		};
		extal2_clk: extal2 {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			/* This value must be overridden by the board. */
			clock-frequency = <0>;
		};
		fsiack_clk: fsiack {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			/* This value must be overridden by the board. */
			clock-frequency = <0>;
		};
		fsibck_clk: fsibck {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			/* This value must be overridden by the board. */
			clock-frequency = <0>;
		};

		/* Special CPG clocks */
		cpg_clocks: cpg_clocks@e6150000 {
			compatible = "renesas,r8a73a4-cpg-clocks";
			reg = <0 0xe6150000 0 0x10000>;
			clocks = <&extal1_clk>, <&extal2_clk>;
			#clock-cells = <1>;
			clock-output-names = "main", "pll0", "pll1", "pll2",
					     "pll2s", "pll2h", "z", "z2",
					     "i", "m3", "b", "m1", "m2",
					     "zx", "zs", "hp";
		};

		/* Variable factor clocks (DIV6) */
		zb_clk: zb_clk@e6150010 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150010 0 4>;
			clocks = <&pll1_div2_clk>, <0>,
				 <&cpg_clocks R8A73A4_CLK_PLL2S>, <0>;
			#clock-cells = <0>;
			clock-output-names = "zb";
		};
		sdhi0_clk: sdhi0ck@e6150074 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150074 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <0>, <&extal2_clk>;
			#clock-cells = <0>;
		};
		sdhi1_clk: sdhi1ck@e6150078 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150078 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <0>, <&extal2_clk>;
			#clock-cells = <0>;
		};
		sdhi2_clk: sdhi2ck@e615007c {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe615007c 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <0>, <&extal2_clk>;
			#clock-cells = <0>;
		};
		mmc0_clk: mmc0@e6150240 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150240 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <0>, <&extal2_clk>;
			#clock-cells = <0>;
		};
		mmc1_clk: mmc1@e6150244 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150244 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <0>, <&extal2_clk>;
			#clock-cells = <0>;
		};
		vclk1_clk: vclk1@e6150008 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150008 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <0>, <&extal2_clk>, <&main_div2_clk>,
				 <&extalr_clk>, <0>, <0>;
			#clock-cells = <0>;
		};
		vclk2_clk: vclk2@e615000c {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe615000c 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <0>, <&extal2_clk>, <&main_div2_clk>,
				 <&extalr_clk>, <0>, <0>;
			#clock-cells = <0>;
		};
		vclk3_clk: vclk3@e615001c {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe615001c 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <0>, <&extal2_clk>, <&main_div2_clk>,
				 <&extalr_clk>, <0>, <0>;
			#clock-cells = <0>;
		};
		vclk4_clk: vclk4@e6150014 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150014 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <0>, <&extal2_clk>, <&main_div2_clk>,
				 <&extalr_clk>, <0>, <0>;
			#clock-cells = <0>;
		};
		vclk5_clk: vclk5@e6150034 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150034 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <0>, <&extal2_clk>, <&main_div2_clk>,
				 <&extalr_clk>, <0>, <0>;
			#clock-cells = <0>;
		};
		fsia_clk: fsia@e6150018 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150018 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <&fsiack_clk>, <0>;
			#clock-cells = <0>;
		};
		fsib_clk: fsib@e6150090 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150090 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <&fsibck_clk>, <0>;
			#clock-cells = <0>;
		};
		mp_clk: mp@e6150080 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150080 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <&extal2_clk>, <&extal2_clk>;
			#clock-cells = <0>;
		};
		m4_clk: m4@e6150098 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150098 0 4>;
			clocks = <&cpg_clocks R8A73A4_CLK_PLL2S>;
			#clock-cells = <0>;
		};
		hsi_clk: hsi@e615026c {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe615026c 0 4>;
			clocks = <&cpg_clocks R8A73A4_CLK_PLL2H>, <&pll1_div2_clk>,
				 <&cpg_clocks R8A73A4_CLK_PLL2S>, <0>;
			#clock-cells = <0>;
		};
		spuv_clk: spuv@e6150094 {
			compatible = "renesas,r8a73a4-div6-clock", "renesas,cpg-div6-clock";
			reg = <0 0xe6150094 0 4>;
			clocks = <&pll1_div2_clk>, <&cpg_clocks R8A73A4_CLK_PLL2S>,
				 <&extal2_clk>, <&extal2_clk>;
			#clock-cells = <0>;
		};

		/* Fixed factor clocks */
		main_div2_clk: main_div2 {
			compatible = "fixed-factor-clock";
			clocks = <&cpg_clocks R8A73A4_CLK_MAIN>;
			#clock-cells = <0>;
			clock-div = <2>;
			clock-mult = <1>;
		};
		pll0_div2_clk: pll0_div2 {
			compatible = "fixed-factor-clock";
			clocks = <&cpg_clocks R8A73A4_CLK_PLL0>;
			#clock-cells = <0>;
			clock-div = <2>;
			clock-mult = <1>;
		};
		pll1_div2_clk: pll1_div2 {
			compatible = "fixed-factor-clock";
			clocks = <&cpg_clocks R8A73A4_CLK_PLL1>;
			#clock-cells = <0>;
			clock-div = <2>;
			clock-mult = <1>;
		};
		extal1_div2_clk: extal1_div2 {
			compatible = "fixed-factor-clock";
			clocks = <&extal1_clk>;
			#clock-cells = <0>;
			clock-div = <2>;
			clock-mult = <1>;
		};

		/* Gate clocks */
		mstp2_clks: mstp2_clks@e6150138 {
			compatible = "renesas,r8a73a4-mstp-clocks", "renesas,cpg-mstp-clocks";
			reg = <0 0xe6150138 0 4>, <0 0xe6150040 0 4>;
			clocks = <&mp_clk>, <&mp_clk>, <&mp_clk>, <&mp_clk>,
				 <&mp_clk>, <&mp_clk>, <&cpg_clocks R8A73A4_CLK_HP>;
			#clock-cells = <1>;
			clock-indices = <
				R8A73A4_CLK_SCIFA0 R8A73A4_CLK_SCIFA1
				R8A73A4_CLK_SCIFB0 R8A73A4_CLK_SCIFB1
				R8A73A4_CLK_SCIFB2 R8A73A4_CLK_SCIFB3
				R8A73A4_CLK_DMAC
			>;
			clock-output-names =
				"scifa0", "scifa1", "scifb0", "scifb1",
				"scifb2", "scifb3", "dmac";
		};
		mstp3_clks: mstp3_clks@e615013c {
			compatible = "renesas,r8a73a4-mstp-clocks", "renesas,cpg-mstp-clocks";
			reg = <0 0xe615013c 0 4>, <0 0xe6150048 0 4>;
			clocks = <&cpg_clocks R8A73A4_CLK_HP>, <&mmc1_clk>,
				 <&sdhi2_clk>, <&sdhi1_clk>, <&sdhi0_clk>,
				 <&mmc0_clk>, <&cpg_clocks R8A73A4_CLK_HP>,
				 <&cpg_clocks R8A73A4_CLK_HP>, <&cpg_clocks
				 R8A73A4_CLK_HP>, <&cpg_clocks
				 R8A73A4_CLK_HP>, <&extalr_clk>;
			#clock-cells = <1>;
			clock-indices = <
				R8A73A4_CLK_IIC2 R8A73A4_CLK_MMCIF1
				R8A73A4_CLK_SDHI2 R8A73A4_CLK_SDHI1
				R8A73A4_CLK_SDHI0 R8A73A4_CLK_MMCIF0
				R8A73A4_CLK_IIC6 R8A73A4_CLK_IIC7
				R8A73A4_CLK_IIC0 R8A73A4_CLK_IIC1
				R8A73A4_CLK_CMT1
			>;
			clock-output-names =
				"iic2", "mmcif1", "sdhi2", "sdhi1", "sdhi0",
				"mmcif0", "iic6", "iic7", "iic0", "iic1",
				"cmt1";
		};
		mstp4_clks: mstp4_clks@e6150140 {
			compatible = "renesas,r8a73a4-mstp-clocks", "renesas,cpg-mstp-clocks";
			reg = <0 0xe6150140 0 4>, <0 0xe615004c 0 4>;
			clocks = <&main_div2_clk>, <&cpg_clocks R8A73A4_CLK_ZS>,
				 <&main_div2_clk>,
				 <&cpg_clocks R8A73A4_CLK_HP>,
				 <&cpg_clocks R8A73A4_CLK_HP>;
			#clock-cells = <1>;
			clock-indices = <
				R8A73A4_CLK_IRQC R8A73A4_CLK_INTC_SYS
				R8A73A4_CLK_IIC5 R8A73A4_CLK_IIC4
				R8A73A4_CLK_IIC3
			>;
			clock-output-names =
				"irqc", "intc-sys", "iic5", "iic4", "iic3";
		};
		mstp5_clks: mstp5_clks@e6150144 {
			compatible = "renesas,r8a73a4-mstp-clocks", "renesas,cpg-mstp-clocks";
			reg = <0 0xe6150144 0 4>, <0 0xe615003c 0 4>;
			clocks = <&extal2_clk>, <&cpg_clocks R8A73A4_CLK_HP>;
			#clock-cells = <1>;
			clock-indices = <
				R8A73A4_CLK_THERMAL R8A73A4_CLK_IIC8
			>;
			clock-output-names =
				"thermal", "iic8";
		};
	};

	prr: chipid@ff000044 {
		compatible = "renesas,prr";
		reg = <0 0xff000044 0 4>;
	};

	sysc: system-controller@e6180000 {
		compatible = "renesas,sysc-r8a73a4", "renesas,sysc-rmobile";
		reg = <0 0xe6180000 0 0x8000>, <0 0xe6188000 0 0x8000>;

		pm-domains {
			pd_c5: c5 {
				#address-cells = <1>;
				#size-cells = <0>;
				#power-domain-cells = <0>;

				pd_c4: c4@0 {
					reg = <0>;
					#address-cells = <1>;
					#size-cells = <0>;
					#power-domain-cells = <0>;

					pd_a3sg: a3sg@16 {
						reg = <16>;
						#power-domain-cells = <0>;
					};

					pd_a3ex: a3ex@17 {
						reg = <17>;
						#power-domain-cells = <0>;
					};

					pd_a3sp: a3sp@18 {
						reg = <18>;
						#address-cells = <1>;
						#size-cells = <0>;
						#power-domain-cells = <0>;

						pd_a2us: a2us@19 {
							reg = <19>;
							#power-domain-cells = <0>;
						};
					};

					pd_a3sm: a3sm@20 {
						reg = <20>;
						#address-cells = <1>;
						#size-cells = <0>;
						#power-domain-cells = <0>;

						pd_a2sl: a2sl@21 {
							reg = <21>;
							#power-domain-cells = <0>;
						};
					};

					pd_a3km: a3km@22 {
						reg = <22>;
						#address-cells = <1>;
						#size-cells = <0>;
						#power-domain-cells = <0>;

						pd_a2kl: a2kl@23 {
							reg = <23>;
							#power-domain-cells = <0>;
						};
					};
				};

				pd_c4ma: c4ma@1 {
					reg = <1>;
					#power-domain-cells = <0>;
				};

				pd_c4cl: c4cl@2 {
					reg = <2>;
					#power-domain-cells = <0>;
				};

				pd_d4: d4@3 {
					reg = <3>;
					#power-domain-cells = <0>;
				};

				pd_a4bc: a4bc@4 {
					reg = <4>;
					#address-cells = <1>;
					#size-cells = <0>;
					#power-domain-cells = <0>;

					pd_a3bc: a3bc@5 {
						reg = <5>;
						#power-domain-cells = <0>;
					};
				};

				pd_a4l: a4l@6 {
					reg = <6>;
					#power-domain-cells = <0>;
				};

				pd_a4lc: a4lc@7 {
					reg = <7>;
					#power-domain-cells = <0>;
				};

				pd_a4mp: a4mp@8 {
					reg = <8>;
					#address-cells = <1>;
					#size-cells = <0>;
					#power-domain-cells = <0>;

					pd_a3mp: a3mp@9 {
						reg = <9>;
						#power-domain-cells = <0>;
					};

					pd_a3vc: a3vc@10 {
						reg = <10>;
						#power-domain-cells = <0>;
					};
				};

				pd_a4sf: a4sf@11 {
					reg = <11>;
					#power-domain-cells = <0>;
				};

				pd_a3r: a3r@12 {
					reg = <12>;
					#address-cells = <1>;
					#size-cells = <0>;
					#power-domain-cells = <0>;

					pd_a2rv: a2rv@13 {
						reg = <13>;
						#power-domain-cells = <0>;
					};

					pd_a2is: a2is@14 {
						reg = <14>;
						#power-domain-cells = <0>;
					};
				};
			};
		};
	};
};
