// SPDX-License-Identifier: GPL-2.0
/*
 * This include file ties a VIN interface with a single ov7725 sensor on
 * the iWave-RZ/G1H Qseven board development platform connected with the
 * camera daughter board.
 *
 * Copyright (C) 2020 Renesas Electronics Corp.
 */

#include <dt-bindings/media/video-interfaces.h>

#define CAM_ENABLED	1

&CAM_PARENT_I2C {
	status = "okay";

	ov7725@21 {
		compatible = "ovti,ov7725";
		reg = <0x21>;
		clocks = <&MCLK_CAM>;
		status = "okay";

		port {
			CAM_EP: endpoint {
				bus-width = <8>;
				bus-type = <MEDIA_BUS_TYPE_BT656>;
				remote-endpoint = <&VIN_EP>;
			};
		};
	};
};
