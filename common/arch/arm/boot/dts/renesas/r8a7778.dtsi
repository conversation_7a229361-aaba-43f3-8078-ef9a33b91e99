// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the R-Car M1A (R8A77781) SoC
 *
 * Copyright (C) 2013  Renesas Solutions Corp.
 * Copyright (C) 2013  Kuninori Morimoto <<EMAIL>>
 *
 * based on r8a7779
 *
 * Copyright (C) 2013 Renesas Solutions Corp.
 * Copyright (C) 2013 <PERSON>
 */

#include <dt-bindings/clock/r8a7778-clock.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	compatible = "renesas,r8a7778";
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0>;
			clock-frequency = <800000000>;
			clocks = <&z_clk>;
		};
	};

	aliases {
		spi0 = &hspi0;
		spi1 = &hspi1;
		spi2 = &hspi2;
	};

	bsc: bus@1c000000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0 0x1c000000>;
	};

	ether: ethernet@fde00000 {
		compatible = "renesas,ether-r8a7778",
			     "renesas,rcar-gen1-ether";
		reg = <0xfde00000 0x400>;
		interrupts = <GIC_SPI 105 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp1_clks R8A7778_CLK_ETHER>;
		power-domains = <&cpg_clocks>;
		phy-mode = "rmii";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	gic: interrupt-controller@fe438000 {
		compatible = "arm,pl390";
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0xfe438000 0x1000>,
		      <0xfe430000 0x100>;
	};

	/* irqpin: IRQ0 - IRQ3 */
	irqpin: interrupt-controller@fe78001c {
		compatible = "renesas,intc-irqpin-r8a7778", "renesas,intc-irqpin";
		#interrupt-cells = <2>;
		interrupt-controller;
		status = "disabled"; /* default off */
		reg =	<0xfe78001c 4>,
			<0xfe780010 4>,
			<0xfe780024 4>,
			<0xfe780044 4>,
			<0xfe780064 4>,
			<0xfe780000 4>;
		interrupts = <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>;
		sense-bitfield-width = <2>;
	};

	gpio0: gpio@ffc40000 {
		compatible = "renesas,gpio-r8a7778", "renesas,rcar-gen1-gpio";
		reg = <0xffc40000 0x2c>;
		interrupts = <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>;
		#gpio-cells = <2>;
		gpio-controller;
		gpio-ranges = <&pfc 0 0 32>;
		#interrupt-cells = <2>;
		interrupt-controller;
	};

	gpio1: gpio@ffc41000 {
		compatible = "renesas,gpio-r8a7778", "renesas,rcar-gen1-gpio";
		reg = <0xffc41000 0x2c>;
		interrupts = <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>;
		#gpio-cells = <2>;
		gpio-controller;
		gpio-ranges = <&pfc 0 32 32>;
		#interrupt-cells = <2>;
		interrupt-controller;
	};

	gpio2: gpio@ffc42000 {
		compatible = "renesas,gpio-r8a7778", "renesas,rcar-gen1-gpio";
		reg = <0xffc42000 0x2c>;
		interrupts = <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>;
		#gpio-cells = <2>;
		gpio-controller;
		gpio-ranges = <&pfc 0 64 32>;
		#interrupt-cells = <2>;
		interrupt-controller;
	};

	gpio3: gpio@ffc43000 {
		compatible = "renesas,gpio-r8a7778", "renesas,rcar-gen1-gpio";
		reg = <0xffc43000 0x2c>;
		interrupts = <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>;
		#gpio-cells = <2>;
		gpio-controller;
		gpio-ranges = <&pfc 0 96 32>;
		#interrupt-cells = <2>;
		interrupt-controller;
	};

	gpio4: gpio@ffc44000 {
		compatible = "renesas,gpio-r8a7778", "renesas,rcar-gen1-gpio";
		reg = <0xffc44000 0x2c>;
		interrupts = <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>;
		#gpio-cells = <2>;
		gpio-controller;
		gpio-ranges = <&pfc 0 128 27>;
		#interrupt-cells = <2>;
		interrupt-controller;
	};

	pfc: pinctrl@fffc0000 {
		compatible = "renesas,pfc-r8a7778";
		reg = <0xfffc0000 0x118>;
	};

	i2c0: i2c@ffc70000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,i2c-r8a7778", "renesas,rcar-gen1-i2c";
		reg = <0xffc70000 0x1000>;
		interrupts = <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_I2C0>;
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	i2c1: i2c@ffc71000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,i2c-r8a7778", "renesas,rcar-gen1-i2c";
		reg = <0xffc71000 0x1000>;
		interrupts = <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_I2C1>;
		power-domains = <&cpg_clocks>;
		i2c-scl-internal-delay-ns = <5>;
		status = "disabled";
	};

	i2c2: i2c@ffc72000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,i2c-r8a7778", "renesas,rcar-gen1-i2c";
		reg = <0xffc72000 0x1000>;
		interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_I2C2>;
		power-domains = <&cpg_clocks>;
		i2c-scl-internal-delay-ns = <5>;
		status = "disabled";
	};

	i2c3: i2c@ffc73000 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "renesas,i2c-r8a7778", "renesas,rcar-gen1-i2c";
		reg = <0xffc73000 0x1000>;
		interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_I2C3>;
		power-domains = <&cpg_clocks>;
		i2c-scl-internal-delay-ns = <5>;
		status = "disabled";
	};

	tmu0: timer@ffd80000 {
		compatible = "renesas,tmu-r8a7778", "renesas,tmu";
		reg = <0xffd80000 0x30>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_TMU0>;
		clock-names = "fck";
		power-domains = <&cpg_clocks>;

		#renesas,channels = <3>;

		status = "disabled";
	};

	tmu1: timer@ffd81000 {
		compatible = "renesas,tmu-r8a7778", "renesas,tmu";
		reg = <0xffd81000 0x30>;
		interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_TMU1>;
		clock-names = "fck";
		power-domains = <&cpg_clocks>;

		#renesas,channels = <3>;

		status = "disabled";
	};

	tmu2: timer@ffd82000 {
		compatible = "renesas,tmu-r8a7778", "renesas,tmu";
		reg = <0xffd82000 0x30>;
		interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_TMU2>;
		clock-names = "fck";
		power-domains = <&cpg_clocks>;

		#renesas,channels = <3>;

		status = "disabled";
	};

	rcar_sound: sound@ffd90000 {
		/*
		 * #sound-dai-cells is required if simple-card
		 *
		 * Single DAI : #sound-dai-cells = <0>;         <&rcar_sound>;
		 * Multi  DAI : #sound-dai-cells = <1>;         <&rcar_sound N>;
		 */
		compatible = "renesas,rcar_sound-r8a7778", "renesas,rcar_sound-gen1";
		reg =	<0xffd90000 0x1000>,	/* SRU */
			<0xffd91000 0x240>,	/* SSI */
			<0xfffe0000 0x24>;	/* ADG */
		clocks = <&mstp3_clks R8A7778_CLK_SSI8>,
			<&mstp3_clks R8A7778_CLK_SSI7>,
			<&mstp3_clks R8A7778_CLK_SSI6>,
			<&mstp3_clks R8A7778_CLK_SSI5>,
			<&mstp3_clks R8A7778_CLK_SSI4>,
			<&mstp0_clks R8A7778_CLK_SSI3>,
			<&mstp0_clks R8A7778_CLK_SSI2>,
			<&mstp0_clks R8A7778_CLK_SSI1>,
			<&mstp0_clks R8A7778_CLK_SSI0>,
			<&mstp5_clks R8A7778_CLK_SRU_SRC8>,
			<&mstp5_clks R8A7778_CLK_SRU_SRC7>,
			<&mstp5_clks R8A7778_CLK_SRU_SRC6>,
			<&mstp5_clks R8A7778_CLK_SRU_SRC5>,
			<&mstp5_clks R8A7778_CLK_SRU_SRC4>,
			<&mstp5_clks R8A7778_CLK_SRU_SRC3>,
			<&mstp5_clks R8A7778_CLK_SRU_SRC2>,
			<&mstp5_clks R8A7778_CLK_SRU_SRC1>,
			<&mstp5_clks R8A7778_CLK_SRU_SRC0>,
			<&audio_clk_a>, <&audio_clk_b>, <&audio_clk_c>,
			<&cpg_clocks R8A7778_CLK_S1>;
		clock-names = "ssi.8", "ssi.7", "ssi.6", "ssi.5", "ssi.4",
			"ssi.3", "ssi.2", "ssi.1", "ssi.0",
			"src.8", "src.7", "src.6", "src.5", "src.4",
			"src.3", "src.2", "src.1", "src.0",
			"clk_a", "clk_b", "clk_c", "clk_i";

		status = "disabled";

		rcar_sound,src {
			src3: src-3 { };
			src4: src-4 { };
			src5: src-5 { };
			src6: src-6 { };
			src7: src-7 { };
			src8: src-8 { };
			src9: src-9 { };
		};

		rcar_sound,ssi {
			ssi3: ssi-3 { interrupts = <GIC_SPI 0x85 IRQ_TYPE_LEVEL_HIGH>; };
			ssi4: ssi-4 { interrupts = <GIC_SPI 0x85 IRQ_TYPE_LEVEL_HIGH>; };
			ssi5: ssi-5 { interrupts = <GIC_SPI 0x86 IRQ_TYPE_LEVEL_HIGH>; };
			ssi6: ssi-6 { interrupts = <GIC_SPI 0x86 IRQ_TYPE_LEVEL_HIGH>; };
			ssi7: ssi-7 { interrupts = <GIC_SPI 0x86 IRQ_TYPE_LEVEL_HIGH>; };
			ssi8: ssi-8 { interrupts = <GIC_SPI 0x86 IRQ_TYPE_LEVEL_HIGH>; };
			ssi9: ssi-9 { interrupts = <GIC_SPI 0x86 IRQ_TYPE_LEVEL_HIGH>; };
		};
	};

	scif0: serial@ffe40000 {
		compatible = "renesas,scif-r8a7778", "renesas,rcar-gen1-scif",
			     "renesas,scif";
		reg = <0xffe40000 0x100>;
		interrupts = <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_SCIF0>,
			 <&cpg_clocks R8A7778_CLK_S1>, <&scif_clk>;
		clock-names = "fck", "brg_int", "scif_clk";
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	scif1: serial@ffe41000 {
		compatible = "renesas,scif-r8a7778", "renesas,rcar-gen1-scif",
			     "renesas,scif";
		reg = <0xffe41000 0x100>;
		interrupts = <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_SCIF1>,
			 <&cpg_clocks R8A7778_CLK_S1>, <&scif_clk>;
		clock-names = "fck", "brg_int", "scif_clk";
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	scif2: serial@ffe42000 {
		compatible = "renesas,scif-r8a7778", "renesas,rcar-gen1-scif",
			     "renesas,scif";
		reg = <0xffe42000 0x100>;
		interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_SCIF2>,
			 <&cpg_clocks R8A7778_CLK_S1>, <&scif_clk>;
		clock-names = "fck", "brg_int", "scif_clk";
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	scif3: serial@ffe43000 {
		compatible = "renesas,scif-r8a7778", "renesas,rcar-gen1-scif",
			     "renesas,scif";
		reg = <0xffe43000 0x100>;
		interrupts = <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_SCIF3>,
			 <&cpg_clocks R8A7778_CLK_S1>, <&scif_clk>;
		clock-names = "fck", "brg_int", "scif_clk";
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	scif4: serial@ffe44000 {
		compatible = "renesas,scif-r8a7778", "renesas,rcar-gen1-scif",
			     "renesas,scif";
		reg = <0xffe44000 0x100>;
		interrupts = <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_SCIF4>,
			 <&cpg_clocks R8A7778_CLK_S1>, <&scif_clk>;
		clock-names = "fck", "brg_int", "scif_clk";
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	scif5: serial@ffe45000 {
		compatible = "renesas,scif-r8a7778", "renesas,rcar-gen1-scif",
			     "renesas,scif";
		reg = <0xffe45000 0x100>;
		interrupts = <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_SCIF5>,
			 <&cpg_clocks R8A7778_CLK_S1>, <&scif_clk>;
		clock-names = "fck", "brg_int", "scif_clk";
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	hscif0: serial@ffe48000 {
		compatible = "renesas,hscif-r8a7778",
			     "renesas,rcar-gen1-hscif", "renesas,hscif";
		reg = <0xffe48000 96>;
		interrupts = <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_HSCIF0>,
			 <&cpg_clocks R8A7778_CLK_S>, <&scif_clk>;
		clock-names = "fck", "brg_int", "scif_clk";
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	hscif1: serial@ffe49000 {
		compatible = "renesas,hscif-r8a7778",
			     "renesas,rcar-gen1-hscif", "renesas,hscif";
		reg = <0xffe49000 96>;
		interrupts = <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_HSCIF1>,
			 <&cpg_clocks R8A7778_CLK_S>, <&scif_clk>;
		clock-names = "fck", "brg_int", "scif_clk";
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	mmcif: mmc@ffe4e000 {
		compatible = "renesas,mmcif-r8a7778", "renesas,sh-mmcif";
		reg = <0xffe4e000 0x100>;
		interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A7778_CLK_MMC>;
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	sdhi0: mmc@ffe4c000 {
		compatible = "renesas,sdhi-r8a7778",
			     "renesas,rcar-gen1-sdhi";
		reg = <0xffe4c000 0x100>;
		interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A7778_CLK_SDHI0>;
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	sdhi1: mmc@ffe4d000 {
		compatible = "renesas,sdhi-r8a7778",
			     "renesas,rcar-gen1-sdhi";
		reg = <0xffe4d000 0x100>;
		interrupts = <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A7778_CLK_SDHI1>;
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	sdhi2: mmc@ffe4f000 {
		compatible = "renesas,sdhi-r8a7778",
			     "renesas,rcar-gen1-sdhi";
		reg = <0xffe4f000 0x100>;
		interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp3_clks R8A7778_CLK_SDHI2>;
		power-domains = <&cpg_clocks>;
		status = "disabled";
	};

	hspi0: spi@fffc7000 {
		compatible = "renesas,hspi-r8a7778", "renesas,hspi";
		reg = <0xfffc7000 0x18>;
		interrupts = <GIC_SPI 63 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_HSPI>;
		power-domains = <&cpg_clocks>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	hspi1: spi@fffc8000 {
		compatible = "renesas,hspi-r8a7778", "renesas,hspi";
		reg = <0xfffc8000 0x18>;
		interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_HSPI>;
		power-domains = <&cpg_clocks>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	hspi2: spi@fffc6000 {
		compatible = "renesas,hspi-r8a7778", "renesas,hspi";
		reg = <0xfffc6000 0x18>;
		interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&mstp0_clks R8A7778_CLK_HSPI>;
		power-domains = <&cpg_clocks>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		/* External input clock */
		extal_clk: extal {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <0>;
		};

		/* External SCIF clock */
		scif_clk: scif {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			/* This value must be overridden by the board. */
			clock-frequency = <0>;
		};

		/* Special CPG clocks */
		cpg_clocks: cpg_clocks@ffc80000 {
			compatible = "renesas,r8a7778-cpg-clocks";
			reg = <0xffc80000 0x80>;
			#clock-cells = <1>;
			clocks = <&extal_clk>;
			clock-output-names = "plla", "pllb", "b",
					     "out", "p", "s", "s1";
			#power-domain-cells = <0>;
		};

		/* Audio clocks; frequencies are set by boards if applicable. */
		audio_clk_a: audio_clk_a {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <0>;
		};
		audio_clk_b: audio_clk_b {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <0>;
		};
		audio_clk_c: audio_clk_c {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <0>;
		};

		/* Fixed ratio clocks */
		g_clk: g {
			compatible = "fixed-factor-clock";
			clocks = <&cpg_clocks R8A7778_CLK_PLLA>;
			#clock-cells = <0>;
			clock-div = <12>;
			clock-mult = <1>;
		};
		i_clk: i {
			compatible = "fixed-factor-clock";
			clocks = <&cpg_clocks R8A7778_CLK_PLLA>;
			#clock-cells = <0>;
			clock-div = <1>;
			clock-mult = <1>;
		};
		s3_clk: s3 {
			compatible = "fixed-factor-clock";
			clocks = <&cpg_clocks R8A7778_CLK_PLLA>;
			#clock-cells = <0>;
			clock-div = <4>;
			clock-mult = <1>;
		};
		s4_clk: s4 {
			compatible = "fixed-factor-clock";
			clocks = <&cpg_clocks R8A7778_CLK_PLLA>;
			#clock-cells = <0>;
			clock-div = <8>;
			clock-mult = <1>;
		};
		z_clk: z {
			compatible = "fixed-factor-clock";
			clocks = <&cpg_clocks R8A7778_CLK_PLLB>;
			#clock-cells = <0>;
			clock-div = <1>;
			clock-mult = <1>;
		};

		/* Gate clocks */
		mstp0_clks: mstp0_clks@ffc80030 {
			compatible = "renesas,r8a7778-mstp-clocks", "renesas,cpg-mstp-clocks";
			reg = <0xffc80030 4>;
			clocks = <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_S>,
				 <&cpg_clocks R8A7778_CLK_S>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_S>;
			#clock-cells = <1>;
			clock-indices = <
				R8A7778_CLK_I2C0 R8A7778_CLK_I2C1
				R8A7778_CLK_I2C2 R8A7778_CLK_I2C3
				R8A7778_CLK_SCIF0 R8A7778_CLK_SCIF1
				R8A7778_CLK_SCIF2 R8A7778_CLK_SCIF3
				R8A7778_CLK_SCIF4 R8A7778_CLK_SCIF5
				R8A7778_CLK_HSCIF0 R8A7778_CLK_HSCIF1
				R8A7778_CLK_TMU0 R8A7778_CLK_TMU1
				R8A7778_CLK_TMU2 R8A7778_CLK_SSI0
				R8A7778_CLK_SSI1 R8A7778_CLK_SSI2
				R8A7778_CLK_SSI3 R8A7778_CLK_SRU
				R8A7778_CLK_HSPI
			>;
			clock-output-names =
				"i2c0", "i2c1", "i2c2", "i2c3", "scif0",
				"scif1", "scif2", "scif3", "scif4", "scif5",
				"hscif0", "hscif1",
				"tmu0", "tmu1", "tmu2", "ssi0", "ssi1",
				"ssi2", "ssi3", "sru", "hspi";
		};
		mstp1_clks: mstp1_clks@ffc80034 {
			compatible = "renesas,r8a7778-mstp-clocks", "renesas,cpg-mstp-clocks";
			reg = <0xffc80034 4>, <0xffc80044 4>;
			clocks = <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_S>,
				 <&cpg_clocks R8A7778_CLK_S>,
				 <&cpg_clocks R8A7778_CLK_P>;
			#clock-cells = <1>;
			clock-indices = <
				R8A7778_CLK_ETHER R8A7778_CLK_VIN0
				R8A7778_CLK_VIN1 R8A7778_CLK_USB
			>;
			clock-output-names =
				"ether", "vin0", "vin1", "usb";
		};
		mstp3_clks: mstp3_clks@ffc8003c {
			compatible = "renesas,r8a7778-mstp-clocks", "renesas,cpg-mstp-clocks";
			reg = <0xffc8003c 4>;
			clocks = <&s4_clk>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>;
			#clock-cells = <1>;
			clock-indices = <
				R8A7778_CLK_MMC R8A7778_CLK_SDHI0
				R8A7778_CLK_SDHI1 R8A7778_CLK_SDHI2
				R8A7778_CLK_SSI4 R8A7778_CLK_SSI5
				R8A7778_CLK_SSI6 R8A7778_CLK_SSI7
				R8A7778_CLK_SSI8
			>;
			clock-output-names =
				"mmc", "sdhi0", "sdhi1", "sdhi2", "ssi4",
				"ssi5", "ssi6", "ssi7", "ssi8";
		};
		mstp5_clks: mstp5_clks@ffc80054 {
			compatible = "renesas,r8a7778-mstp-clocks", "renesas,cpg-mstp-clocks";
			reg = <0xffc80054 4>;
			clocks = <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>,
				 <&cpg_clocks R8A7778_CLK_P>;
			#clock-cells = <1>;
			clock-indices = <
				R8A7778_CLK_SRU_SRC0 R8A7778_CLK_SRU_SRC1
				R8A7778_CLK_SRU_SRC2 R8A7778_CLK_SRU_SRC3
				R8A7778_CLK_SRU_SRC4 R8A7778_CLK_SRU_SRC5
				R8A7778_CLK_SRU_SRC6 R8A7778_CLK_SRU_SRC7
				R8A7778_CLK_SRU_SRC8
			>;
			clock-output-names =
				"sru-src0", "sru-src1", "sru-src2",
				"sru-src3", "sru-src4", "sru-src5",
				"sru-src6", "sru-src7", "sru-src8";
		};
	};

	rst: reset-controller@ffcc0000 {
		compatible = "renesas,r8a7778-reset-wdt";
		reg = <0xffcc0000 0x40>;
	};
};
