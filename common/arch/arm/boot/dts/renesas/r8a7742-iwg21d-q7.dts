// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the iWave-RZ/G1H Qseven board
 *
 * Copyright (C) 2020 Renesas Electronics Corp.
 */

/*
 * SSI-SGTL5000
 *
 * This command is required when Playback/Capture
 *
 *      amixer set "DVC Out" 100%
 *      amixer set "DVC In" 100%
 *
 * You can use Mute
 *
 *      amixer set "DVC Out Mute" on
 *      amixer set "DVC In Mute" on
 *
 * You can use Volume Ramp
 *
 *      amixer set "DVC Out Ramp Up Rate"   "0.125 dB/64 steps"
 *      amixer set "DVC Out Ramp Down Rate" "0.125 dB/512 steps"
 *      amixer set "DVC Out Ramp" on
 *      aplay xxx.wav &
 *      amixer set "DVC Out"  80%  // Volume Down
 *      amixer set "DVC Out" 100%  // Volume Up
 */

/dts-v1/;
#include "r8a7742-iwg21m.dtsi"
#include <dt-bindings/pwm/pwm.h>

/ {
	model = "iWave Systems RainboW-G21D-Qseven board based on RZ/G1H";
	compatible = "iwave,g21d", "iwave,g21m", "renesas,r8a7742";

	aliases {
		serial2 = &scifa2;
		serial4 = &scifb2;
		ethernet0 = &avb;
	};

	chosen {
		bootargs = "ignore_loglevel root=/dev/mmcblk0p1 rw rootwait";
		stdout-path = "serial2:115200n8";
	};

	audio_clock: audio_clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <26000000>;
	};

	lcd_backlight: backlight {
		compatible = "pwm-backlight";
		pwms = <&tpu 2 5000000 0>;
		brightness-levels = <0 4 8 16 32 64 128 255>;
		pinctrl-0 = <&backlight_pins>;
		pinctrl-names = "default";
		default-brightness-level = <7>;
		enable-gpios = <&gpio3 11 GPIO_ACTIVE_HIGH>;
	};

	leds {
		compatible = "gpio-leds";

		sdhi2_led {
			label = "sdio-led";
			gpios = <&gpio5 22 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "mmc1";
		};
	};

	lvds-receiver {
		compatible = "ti,ds90cf384a", "lvds-decoder";
		power-supply = <&vcc_3v3_tft1>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				lvds_receiver_in: endpoint {
					remote-endpoint = <&lvds0_out>;
				};
			};
			port@1 {
				reg = <1>;
				lvds_receiver_out: endpoint {
					remote-endpoint = <&panel_in>;
				};
			};
		};
	};

	panel {
		compatible = "edt,etm0700g0dh6";
		backlight = <&lcd_backlight>;
		power-supply = <&vcc_3v3_tft1>;

		port {
			panel_in: endpoint {
				remote-endpoint = <&lvds_receiver_out>;
			};
		};
	};

	reg_1p5v: 1p5v {
		compatible = "regulator-fixed";
		regulator-name = "1P5V";
		regulator-min-microvolt = <1500000>;
		regulator-max-microvolt = <1500000>;
		regulator-always-on;
	};

	rsnd_sgtl5000: sound {
		compatible = "simple-audio-card";
		simple-audio-card,format = "i2s";
		simple-audio-card,bitclock-master = <&sndcodec>;
		simple-audio-card,frame-master = <&sndcodec>;

		sndcpu: simple-audio-card,cpu {
			sound-dai = <&rcar_sound>;
		};

		sndcodec: simple-audio-card,codec {
			sound-dai = <&sgtl5000>;
		};
	};

	vcc_3v3_tft1: regulator-panel {
		compatible = "regulator-fixed";

		regulator-name = "vcc-3v3-tft1";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		enable-active-high;
		startup-delay-us = <500>;
		gpio = <&gpio5 28 GPIO_ACTIVE_HIGH>;
	};

	vcc_sdhi2: regulator-vcc-sdhi2 {
		compatible = "regulator-fixed";

		regulator-name = "SDHI2 Vcc";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;

		gpio = <&gpio1 27 GPIO_ACTIVE_LOW>;
	};

	vccq_sdhi2: regulator-vccq-sdhi2 {
		compatible = "regulator-gpio";

		regulator-name = "SDHI2 VccQ";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <3300000>;

		gpios = <&gpio1 8 GPIO_ACTIVE_HIGH>;
		gpios-states = <1>;
		states = <3300000 1>, <1800000 0>;
	};
};

&avb {
	pinctrl-0 = <&avb_pins>;
	pinctrl-names = "default";

	phy-handle = <&phy3>;
	phy-mode = "gmii";
	renesas,no-ether-link;
	status = "okay";

	phy3: ethernet-phy@3 {
		compatible = "ethernet-phy-id0022.1622",
			     "ethernet-phy-ieee802.3-c22";
		reg = <3>;
		micrel,led-mode = <1>;
	};
};

&i2c2 {
	pinctrl-0 = <&i2c2_pins>;
	pinctrl-names = "default";

	status = "okay";
	clock-frequency = <400000>;

	sgtl5000: codec@a {
		compatible = "fsl,sgtl5000";
		#sound-dai-cells = <0>;
		reg = <0x0a>;
		clocks = <&audio_clock>;
		VDDA-supply = <&reg_3p3v>;
		VDDIO-supply = <&reg_3p3v>;
		VDDD-supply = <&reg_1p5v>;
	};

	touch: touchpanel@38 {
		compatible = "edt,edt-ft5406";
		reg = <0x38>;
		interrupt-parent = <&gpio0>;
		interrupts = <24 IRQ_TYPE_EDGE_FALLING>;
		/* GP1_29 is also shared with audio codec reset pin */
		reset-gpios = <&gpio1 29 GPIO_ACTIVE_LOW>;
		vcc-supply = <&vcc_3v3_tft1>;
	};
};

&can1 {
	pinctrl-0 = <&can1_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&cmt0 {
	status = "okay";
};

&du {
	status = "okay";
};

&gpio0 {
	touch-interrupt-hog {
		gpio-hog;
		gpios = <24 GPIO_ACTIVE_LOW>;
		input;
	};
};

&gpio1 {
	can-trx-en-hog {
		gpio-hog;
		gpios = <28 GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "can-trx-en-gpio";
	};
};

&hsusb {
	pinctrl-0 = <&usb0_pins>;
	pinctrl-names = "default";
	status = "okay";
};

&lvds0 {
	status = "okay";
	ports {
		port@1 {
			lvds0_out: endpoint {
				remote-endpoint = <&lvds_receiver_in>;
			};
		};
	};
};

&msiof0 {
	pinctrl-0 = <&msiof0_pins>;
	pinctrl-names = "default";
	cs-gpios = <&gpio5 13 GPIO_ACTIVE_LOW>;

	status = "okay";

	flash1: flash@0 {
		compatible = "sst,sst25vf016b", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <50000000>;
		m25p,fast-read;

		partitions {
			compatible = "fixed-partitions";
			#address-cells = <1>;
			#size-cells = <1>;

			partition@0 {
				label = "user";
				reg = <0x00000000 0x00200000>;
			};
		};
	};
};

&pci0 {
	pinctrl-0 = <&usb0_pins>;
	pinctrl-names = "default";
	/* Disable hsusb to enable USB2.0 host mode support on J2 */
	/* status = "okay"; */
};

&pci1 {
	pinctrl-0 = <&usb1_pins>;
	pinctrl-names = "default";
	status = "okay";
};

&pci2 {
	/* Disable xhci to enable USB2.0 host mode support on J23 bottom port */
	/* status = "okay"; */
};

&pcie_bus_clk {
	clock-frequency = <100000000>;
};

&pciec {
	/* SW2[6] determines which connector is activated
	 * ON = PCIe X4 (connector-J7)
	 * OFF = mini-PCIe (connector-J26)
	 */
	status = "okay";
};

&pfc {
	avb_pins: avb {
		groups = "avb_mdio", "avb_gmii";
		function = "avb";
	};

	backlight_pins: backlight {
		groups = "tpu0_to2";
		function = "tpu0";
	};

	can1_pins: can1 {
		groups = "can1_data_b";
		function = "can1";
	};

	i2c2_pins: i2c2 {
		groups = "i2c2_b";
		function = "i2c2";
	};

	msiof0_pins: msiof0 {
		groups = "msiof0_clk", "msiof0_sync", "msiof0_tx", "msiof0_rx";
		function = "msiof0";
	};

	scifa2_pins: scifa2 {
		groups = "scifa2_data_c";
		function = "scifa2";
	};

	scifb2_pins: scifb2 {
		groups = "scifb2_data", "scifb2_ctrl";
		function = "scifb2";
	};

	sdhi2_pins: sd2 {
		groups = "sdhi2_data4", "sdhi2_ctrl";
		function = "sdhi2";
		power-source = <3300>;
	};

	sdhi2_pins_uhs: sd2_uhs {
		groups = "sdhi2_data4", "sdhi2_ctrl";
		function = "sdhi2";
		power-source = <1800>;
	};

	sound_pins: sound {
		groups = "ssi34_ctrl", "ssi3_data", "ssi4_data";
		function = "ssi";
	};

	usb0_pins: usb0 {
		groups = "usb0";
		function = "usb0";
	};

	usb1_pins: usb1 {
		groups = "usb1_pwen";
		function = "usb1";
	};
};

&rcar_sound {
	pinctrl-0 = <&sound_pins>;
	pinctrl-names = "default";
	status = "okay";

	/* Single DAI */
	#sound-dai-cells = <0>;

	rcar_sound,dai {
		dai0 {
			playback = <&ssi4>, <&src4>, <&dvc1>;
			capture = <&ssi3>, <&src3>, <&dvc0>;
		};
	};
};

&rwdt {
	timeout-sec = <60>;
	status = "okay";
};

&scifa2 {
	pinctrl-0 = <&scifa2_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&scifb2 {
	pinctrl-0 = <&scifb2_pins>;
	pinctrl-names = "default";

	uart-has-rtscts;
	status = "okay";
};

&sdhi2 {
	pinctrl-0 = <&sdhi2_pins>;
	pinctrl-1 = <&sdhi2_pins_uhs>;
	pinctrl-names = "default", "state_uhs";

	vmmc-supply = <&vcc_sdhi2>;
	vqmmc-supply = <&vccq_sdhi2>;
	cd-gpios = <&gpio3 22 GPIO_ACTIVE_LOW>;
	wp-gpios = <&gpio3 23 GPIO_ACTIVE_HIGH>;
	sd-uhs-sdr50;
	status = "okay";
};

&ssi4 {
	shared-pin;
};

&tpu {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&xhci {
	status = "okay";
};
