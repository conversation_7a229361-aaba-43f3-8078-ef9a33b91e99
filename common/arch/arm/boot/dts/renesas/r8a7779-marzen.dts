// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the R-Car H1 (R8A77790) Marzen board
 *
 * Copyright (C) 2013 Renesas Solutions Corp.
 * Copyright (C) 2013 <PERSON>
 */

/dts-v1/;
#include "r8a7779.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "marzen";
	compatible = "renesas,marzen", "renesas,r8a7779";

	aliases {
		serial0 = &scif2;
		serial1 = &scif4;
	};

	chosen {
		bootargs = "ignore_loglevel rw root=/dev/nfs ip=on";
		stdout-path = "serial0:115200n8";
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x40000000>;
	};

	fixedregulator3v3: regulator-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "fixed-3.3V";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
		regulator-always-on;
	};

	vccq_sdhi0: regulator-vccq-sdhi0 {
		compatible = "regulator-gpio";

		regulator-name = "SDHI0 VccQ";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <3300000>;

		gpios = <&gpio3 20 GPIO_ACTIVE_HIGH>;
		gpios-states = <1>;
		states = <3300000 1>, <1800000 0>;
	};

	ethernet@18000000 {
		compatible = "smsc,lan89218", "smsc,lan9115";
		reg = <0x18000000 0x100>;
		pinctrl-0 = <&ethernet_pins>;
		pinctrl-names = "default";

		phy-mode = "mii";
		interrupt-parent = <&irqpin0>;
		interrupts = <1 IRQ_TYPE_EDGE_FALLING>;
		smsc,irq-push-pull;
		reg-io-width = <4>;
		vddvario-supply = <&fixedregulator3v3>;
		vdd33a-supply = <&fixedregulator3v3>;
	};

	keyboard-irq {
		compatible = "gpio-keys";

		pinctrl-0 = <&keyboard_irq_pins>;
		pinctrl-names = "default";

		interrupt-parent = <&gpio0>;

		key-1 {
			interrupts = <17 IRQ_TYPE_EDGE_FALLING>;
			linux,code = <KEY_1>;
			label = "SW1-1";
			wakeup-source;
			debounce-interval = <20>;
		};
		key-2 {
			interrupts = <18 IRQ_TYPE_EDGE_FALLING>;
			linux,code = <KEY_2>;
			label = "SW1-2";
			wakeup-source;
			debounce-interval = <20>;
		};
	};

	keyboard-gpio {
		compatible = "gpio-keys-polled";
		poll-interval = <50>;

		pinctrl-0 = <&keyboard_gpio_pins>;
		pinctrl-names = "default";

		key-3 {
			gpios = <&gpio0 19 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_3>;
			label = "SW1-3";
			debounce-interval = <20>;
		};
		key-4 {
			gpios = <&gpio0 20 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_4>;
			label = "SW1-4";
			debounce-interval = <20>;
		};
	};

	leds {
		compatible = "gpio-leds";
		led2 {
			gpios = <&gpio4 29 GPIO_ACTIVE_HIGH>;
		};
		led3 {
			gpios = <&gpio4 30 GPIO_ACTIVE_HIGH>;
		};
		led4 {
			gpios = <&gpio4 31 GPIO_ACTIVE_HIGH>;
		};
	};

	vga-encoder {
		compatible = "adi,adv7123";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				vga_enc_in: endpoint {
					remote-endpoint = <&du_out_rgb0>;
				};
			};
			port@1 {
				reg = <1>;
				vga_enc_out: endpoint {
					remote-endpoint = <&vga_in>;
				};
			};
		};
	};

	vga {
		compatible = "vga-connector";

		port {
			vga_in: endpoint {
				remote-endpoint = <&vga_enc_out>;
			};
		};
	};

	lvds-encoder {
		compatible = "thine,thc63lvdm83d";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				lvds_enc_in: endpoint {
					remote-endpoint = <&du_out_rgb1>;
				};
			};
			port@1 {
				reg = <1>;
				lvds_connector: endpoint {
				};
			};
		};
	};

	x3_clk: x3-clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <65000000>;
	};
};

&du {
	pinctrl-0 = <&du_pins>;
	pinctrl-names = "default";
	status = "okay";

	clocks = <&mstp1_clks R8A7779_CLK_DU>, <&x3_clk>;
	clock-names = "du.0", "dclkin.0";

	ports {
		port@0 {
			endpoint {
				remote-endpoint = <&vga_enc_in>;
			};
		};
		port@1 {
			endpoint {
				remote-endpoint = <&lvds_enc_in>;
			};
		};
	};
};

&gpio0 {
	keyboard-irq-hog {
		gpio-hog;
		gpios = <17 GPIO_ACTIVE_LOW>, <18 GPIO_ACTIVE_LOW>;
		input;
	};
};

&i2c0 {
	status = "okay";

	clock-frequency = <100000>;
};

&irqpin0 {
	status = "okay";
};

&extal_clk {
	clock-frequency = <31250000>;
};

&tmu0 {
	status = "okay";
};

&pfc {
	pinctrl-0 = <&scif_clk_pins>;
	pinctrl-names = "default";

	du_pins: du {
		du0 {
			groups = "du0_rgb888", "du0_sync_1", "du0_clk_out_0", "du0_clk_in";
			function = "du0";
		};
		du1 {
			groups = "du1_rgb666", "du1_sync_1", "du1_clk_out";
			function = "du1";
		};
	};

	scif_clk_pins: scif_clk {
		groups = "scif_clk_b";
		function = "scif_clk";
	};

	ethernet_pins: ethernet {
		intc {
			groups = "intc_irq1_b";
			function = "intc";
		};
		lbsc {
			groups = "lbsc_ex_cs0";
			function = "lbsc";
		};
	};

	scif2_pins: scif2 {
		groups = "scif2_data_c";
		function = "scif2";
	};

	scif4_pins: scif4 {
		groups = "scif4_data";
		function = "scif4";
	};

	sdhi0_pins: sd0 {
		groups = "sdhi0_data4", "sdhi0_ctrl", "sdhi0_cd";
		function = "sdhi0";
	};

	hspi0_pins: hspi0 {
		groups = "hspi0";
		function = "hspi0";
	};

	keyboard_irq_pins: keyboard-irq {
		pins = "GP_0_17", "GP_0_18";
		bias-pull-up;
	};
	keyboard_gpio_pins: keyboard-gpio {
		pins = "GP_0_19", "GP_0_20";
		bias-pull-up;
	};
};

&sata {
	status = "okay";
};

&scif2 {
	pinctrl-0 = <&scif2_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&scif4 {
	pinctrl-0 = <&scif4_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&scif_clk {
	clock-frequency = <14745600>;
};

&sdhi0 {
	pinctrl-0 = <&sdhi0_pins>;
	pinctrl-names = "default";

	vmmc-supply = <&fixedregulator3v3>;
	vqmmc-supply = <&vccq_sdhi0>;
	bus-width = <4>;
	status = "okay";
};

&hspi0 {
	pinctrl-0 = <&hspi0_pins>;
	pinctrl-names = "default";
	status = "okay";
};
