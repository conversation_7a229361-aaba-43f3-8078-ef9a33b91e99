// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the iWave-RZG1M-20M Qseven SOM
 *
 * Copyright (C) 2017 Renesas Electronics Corp.
 */

#include "r8a7743.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	compatible = "iwave,g20m", "renesas,r8a7743";

	memory@40000000 {
		device_type = "memory";
		reg = <0 0x40000000 0 0x20000000>;
	};

	memory@200000000 {
		device_type = "memory";
		reg = <2 0x00000000 0 0x20000000>;
	};

	reg_3p3v: 3p3v {
		compatible = "regulator-fixed";
		regulator-name = "3P3V";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
	};
};

&extal_clk {
	clock-frequency = <20000000>;
};

&pfc {
	mmcif0_pins: mmc {
		groups = "mmc_data8_b", "mmc_ctrl";
		function = "mmc";
	};

	qspi_pins: qspi {
		groups = "qspi_ctrl", "qspi_data2";
		function = "qspi";
	};

	sdhi0_pins: sd0 {
		groups = "sdhi0_data4", "sdhi0_ctrl";
		function = "sdhi0";
		power-source = <3300>;
	};
};

&mmcif0 {
	pinctrl-0 = <&mmcif0_pins>;
	pinctrl-names = "default";

	vmmc-supply = <&reg_3p3v>;
	bus-width = <8>;
	non-removable;
	status = "okay";
};

&qspi {
	pinctrl-0 = <&qspi_pins>;
	pinctrl-names = "default";

	status = "okay";

	/* WARNING - This device contains the bootloader. Handle with care. */
	flash: flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "sst,sst25vf016b", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <50000000>;
		spi-tx-bus-width = <1>;
		spi-rx-bus-width = <1>;
		m25p,fast-read;
		spi-cpol;
		spi-cpha;
	};
};

&sdhi0 {
	pinctrl-0 = <&sdhi0_pins>;
	pinctrl-names = "default";

	vmmc-supply = <&reg_3p3v>;
	vqmmc-supply = <&reg_3p3v>;
	cd-gpios = <&gpio7 11 GPIO_ACTIVE_LOW>;
	status = "okay";
};
