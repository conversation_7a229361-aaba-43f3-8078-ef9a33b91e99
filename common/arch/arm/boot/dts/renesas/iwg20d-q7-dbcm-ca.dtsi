// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the iWave-RZ-G1M/N Daughter Board Camera Module
 *
 * Copyright (C) 2017 Renesas Electronics Corp.
 */

/ {
	aliases {
		serial1 = &scif1;
		serial4 = &hscif1;
	};

	cec_clock: cec-clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <12000000>;
	};

	hdmi-out {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_out: endpoint {
				remote-endpoint = <&adv7511_out>;
			};
		};
	};
};

&can1 {
	pinctrl-0 = <&can1_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&du {
	pinctrl-0 = <&du_pins>;
	pinctrl-names = "default";

	ports {
		port@0 {
			endpoint {
				remote-endpoint = <&adv7511_in>;
			};
		};
	};
};

&hscif1 {
	pinctrl-0 = <&hscif1_pins>;
	pinctrl-names = "default";

	uart-has-rtscts;
	status = "okay";
};

&i2c5 {
	status = "okay";
	clock-frequency = <400000>;

	hdmi@39 {
		compatible = "adi,adv7511w";
		reg = <0x39>;
		interrupt-parent = <&gpio0>;
		interrupts = <13 IRQ_TYPE_LEVEL_LOW>;
		clocks = <&cec_clock>;
		clock-names = "cec";

		adi,input-depth = <8>;
		adi,input-colorspace = "rgb";
		adi,input-clock = "1x";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				adv7511_in: endpoint {
					remote-endpoint = <&du_out_rgb>;
				};
			};

			port@1 {
				reg = <1>;
				adv7511_out: endpoint {
					remote-endpoint = <&hdmi_con_out>;
				};
			};
		};
	};
};

&pfc {
	can1_pins: can1 {
		groups = "can1_data_d";
		function = "can1";
	};

	du_pins: du {
		groups = "du_rgb888", "du_sync", "du_oddf", "du_clk_out_0";
		function = "du";
	};

	hscif1_pins: hscif1 {
		groups = "hscif1_data_c", "hscif1_ctrl_c";
		function = "hscif1";
	};

	scif1_pins: scif1 {
		groups = "scif1_data_d";
		function = "scif1";
	};
};

&scif1 {
	pinctrl-0 = <&scif1_pins>;
	pinctrl-names = "default";

	status = "okay";
};
