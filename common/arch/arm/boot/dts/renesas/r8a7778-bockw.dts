// SPDX-License-Identifier: GPL-2.0
/*
 * Reference Device Tree Source for the R-Car M1A (R8A77781) Bock-W board
 *
 * Copyright (C) 2013  Renesas Solutions Corp.
 * Copyright (C) 2013  Kuninori Mo<PERSON>oto <<EMAIL>>
 *
 * based on r8a7779
 *
 * Copyright (C) 2013 Renesas Solutions Corp.
 * Copyright (C) 2013 <PERSON>
 */

/dts-v1/;
#include "r8a7778.dtsi"
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "bockw";
	compatible = "renesas,bockw", "renesas,r8a7778";

	aliases {
		serial0 = &scif0;
	};

	chosen {
		bootargs = "ignore_loglevel rw root=/dev/nfs ip=on";
		stdout-path = "serial0:115200n8";
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x10000000>;
	};

	fixedregulator3v3: regulator-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "fixed-3.3V";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
		regulator-always-on;
	};

	sound {
		compatible = "simple-audio-card";

		simple-audio-card,format = "left_j";
		simple-audio-card,bitclock-master = <&sndcodec>;
		simple-audio-card,frame-master = <&sndcodec>;

		sndcpu: simple-audio-card,cpu {
			sound-dai = <&rcar_sound>;
		};

		sndcodec: simple-audio-card,codec {
			sound-dai = <&ak4643>;
			system-clock-frequency = <11289600>;
		};
	};
};

&bsc {
	ethernet@18300000 {
		compatible = "smsc,lan89218", "smsc,lan9115";
		reg = <0x18300000 0x1000>;

		phy-mode = "mii";
		interrupt-parent = <&irqpin>;
		interrupts = <0 IRQ_TYPE_EDGE_FALLING>;
		reg-io-width = <4>;
		vddvario-supply = <&fixedregulator3v3>;
		vdd33a-supply = <&fixedregulator3v3>;
	};
};

&extal_clk {
	clock-frequency = <33333333>;
};

&i2c0 {
	status = "okay";

	ak4643: codec@12 {
		compatible = "asahi-kasei,ak4643";
		#sound-dai-cells = <0>;
		reg = <0x12>;
	};

	camera@41 {
		compatible = "oki,ml86v7667";
		reg = <0x41>;
	};

	camera@43 {
		compatible = "oki,ml86v7667";
		reg = <0x43>;
	};

	rx8581: rtc@51 {
		compatible = "epson,rx8581";
		reg = <0x51>;
	};
};

&mmcif {
	pinctrl-0 = <&mmc_pins>;
	pinctrl-names = "default";

	vmmc-supply = <&fixedregulator3v3>;
	bus-width = <8>;
	broken-cd;
	status = "okay";
};

&irqpin {
	status = "okay";
};

&tmu0 {
	status = "okay";
};

&pfc {
	pinctrl-0 = <&scif_clk_pins>;
	pinctrl-names = "default";

	scif0_pins: scif0 {
		groups = "scif0_data_a", "scif0_ctrl";
		function = "scif0";
	};

	scif_clk_pins: scif_clk {
		groups = "scif_clk";
		function = "scif_clk";
	};

	mmc_pins: mmc {
		groups = "mmc_data8", "mmc_ctrl";
		function = "mmc";
	};

	sdhi0_pins: sd0 {
		groups = "sdhi0_data4", "sdhi0_ctrl";
		function = "sdhi0";
	};
	sdhi0_pup_pins: sd0_pup {
		groups = "sdhi0_cd", "sdhi0_wp";
		function = "sdhi0";
		bias-pull-up;
	};

	hspi0_pins: hspi0 {
		groups = "hspi0_a";
		function = "hspi0";
	};

	usb0_pins: usb0 {
		groups = "usb0";
		function = "usb0";
	};

	usb1_pins: usb1 {
		groups = "usb1";
		function = "usb1";
	};

	vin0_pins: vin0 {
		groups = "vin0_data8", "vin0_clk";
		function = "vin0";
	};

	vin1_pins: vin1 {
		groups = "vin1_data8", "vin1_clk";
		function = "vin1";
	};
};

&rcar_sound {
	/* Single DAI */
	#sound-dai-cells = <0>;
};

&sdhi0 {
	pinctrl-0 = <&sdhi0_pins>, <&sdhi0_pup_pins>;
	pinctrl-names = "default";

	vmmc-supply = <&fixedregulator3v3>;
	bus-width = <4>;
	status = "okay";
	wp-gpios = <&gpio3 18 GPIO_ACTIVE_HIGH>;
};

&hspi0 {
	pinctrl-0 = <&hspi0_pins>;
	pinctrl-names = "default";
	status = "okay";

	flash: flash@0 {
		compatible = "spansion,s25fl008k", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <104000000>;
		m25p,fast-read;

		partitions {
			compatible = "fixed-partitions";
			#address-cells = <1>;
			#size-cells = <1>;

			partition@0 {
				label = "data(spi)";
				reg = <0x00000000 0x00100000>;
			};
		};
	};
};

&scif0 {
	pinctrl-0 = <&scif0_pins>;
	pinctrl-names = "default";

	uart-has-rtscts;
	status = "okay";
};

&scif_clk {
	clock-frequency = <14745600>;
};
