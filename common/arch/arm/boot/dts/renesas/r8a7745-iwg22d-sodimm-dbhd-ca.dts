// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the iWave-RZG1E SODIMM carrier board + HDMI daughter
 * board
 *
 * Copyright (C) 2017 Renesas Electronics Corp.
 */

#include "r8a7745-iwg22d-sodimm.dts"

/ {
	model = "iWave RainboW-G22D-SODIMM RZ/G1E based board with HDMI add-on";
	compatible = "iwave,g22d", "iwave,g22m", "renesas,r8a7745";

	aliases {
		serial0 = &scif1;
		serial4 = &scif5;
		serial6 = &hscif2;
	};

	cec_clock: cec-clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <12000000>;
	};

	hdmi-out {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con: endpoint {
				remote-endpoint = <&adv7511_out>;
			};
		};
	};
};

&du {
	pinctrl-0 = <&du0_pins>;
	pinctrl-names = "default";

	status = "okay";

	ports {
		port@0 {
			endpoint {
				remote-endpoint = <&adv7511_in>;
			};
		};
	};
};

&can1 {
	pinctrl-0 = <&can1_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&hscif2 {
	pinctrl-0 = <&hscif2_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&i2c1 {
	pinctrl-0 = <&i2c1_pins>;
	pinctrl-names = "default";

	status = "okay";
	clock-frequency = <400000>;

	hdmi@39 {
		compatible = "adi,adv7511w";
		reg = <0x39>;
		interrupt-parent = <&gpio1>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
		clocks = <&cec_clock>;
		clock-names = "cec";
		pd-gpios = <&gpio2 24 GPIO_ACTIVE_HIGH>;

		adi,input-depth = <8>;
		adi,input-colorspace = "rgb";
		adi,input-clock = "1x";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				adv7511_in: endpoint {
					remote-endpoint = <&du_out_rgb0>;
				};
			};

			port@1 {
				reg = <1>;
				adv7511_out: endpoint {
					remote-endpoint = <&hdmi_con>;
				};
			};
		};
	};
};

&lcd_panel {
	status = "disabled";

	/delete-node/ port;
};

&pfc {
	can1_pins: can1 {
		groups = "can1_data_b";
		function = "can1";
	};

	du0_pins: du0 {
		groups = "du0_rgb888", "du0_sync", "du0_disp", "du0_clk0_out";
		function = "du0";
	};

	hscif2_pins: hscif2 {
		groups = "hscif2_data";
		function = "hscif2";
	};

	i2c1_pins: i2c1 {
		groups = "i2c1_d";
		function = "i2c1";
	};

	scif1_pins: scif1 {
		groups = "scif1_data";
		function = "scif1";
	};

	scif5_pins: scif5 {
		groups = "scif5_data_d";
		function = "scif5";
	};
};

&scif1 {
	pinctrl-0 = <&scif1_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&scif5 {
	pinctrl-0 = <&scif5_pins>;
	pinctrl-names = "default";

	status = "okay";
};
