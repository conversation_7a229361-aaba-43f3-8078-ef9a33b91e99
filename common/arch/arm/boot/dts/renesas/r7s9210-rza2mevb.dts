// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the RZA2MEVB board
 *
 * Copyright (C) 2018 Renesas Electronics
 *
 * As upstream Linux does not support XIP, it cannot run in 8 MiB of HyperRAM.
 * Hence the 64 MiB of SDRAM on the sub-board needs to be enabled, which has
 * the following ramifications:
 *   - SCIF4 connected to the on-board USB-serial can no longer be used as the
 *     serial console,
 *   - Instead, SCIF2 is used as the serial console, by connecting a 3.3V TTL
 *     USB-to-Serial adapter to the CMOS camera connector:
 *       - RXD = CN17-9,
 *       - TXD = CN17-10,
 *       - GND = CN17-2 or CN17-17,
 *   - The first Ethernet channel can no longer be used,
 *   - USB Channel 1 loses the overcurrent input signal.
 *
 * Please make sure your sub-board matches the following switch settings:
 *
 *           SW6                SW6-1 set to SDRAM
 *  ON                          SW6-2 set to Audio
 * +---------------------+      SW6-3 set to DRP
 * | =   =   = = =       |      SW6-4 set to CEU
 * |   =   =             |      SW6-5 set to Ether2
 * | 1 2 3 4 5 6 7 8 9 0 |      SW6-6 set to VDC6
 * +---------------------+      SW6-7 set to VDC6
 */

/dts-v1/;
#include "r7s9210.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/pinctrl/r7s9210-pinctrl.h>

/ {
	model = "RZA2MEVB";
	compatible = "renesas,rza2mevb", "renesas,r7s9210";

	aliases {
		serial0 = &scif2;
		ethernet0 = &ether1;
	};

	chosen {
		bootargs = "ignore_loglevel";
		stdout-path = "serial0:115200n8";
	};

	keyboard {
		compatible = "gpio-keys";

		pinctrl-names = "default";
		pinctrl-0 = <&keyboard_pins>;

		key-3 {
			interrupt-parent = <&irqc>;
			interrupts = <0 IRQ_TYPE_EDGE_BOTH>;
			linux,code = <KEY_3>;
			label = "SW3";
			wakeup-source;
		};
	};

	lbsc {
		#address-cells = <1>;
		#size-cells = <1>;
	};

	leds {
		compatible = "gpio-leds";

		led-red {
			gpios = <&pinctrl RZA2_PIN(PORT6, 0) GPIO_ACTIVE_HIGH>;
		};
		led-green {
			gpios = <&pinctrl RZA2_PIN(PORTC, 1) GPIO_ACTIVE_HIGH>;
		};
	};

	memory@c000000 {
		device_type = "memory";
		reg = <0x0c000000 0x04000000>;	/* SDRAM */
	};
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&ether1 {
	pinctrl-names = "default";
	pinctrl-0 = <&eth1_pins>;
	status = "okay";
	renesas,no-ether-link;
	phy-handle = <&phy1>;
	phy1: ethernet-phy@1 {
		compatible = "ethernet-phy-id001c.c816",
			     "ethernet-phy-ieee802.3-c22";
		reg = <0>;
	};
};

/* EXTAL */
&extal_clk {
	clock-frequency = <24000000>;	/* 24MHz */
};

&i2c3 {
	status = "okay";
	clock-frequency = <400000>;

	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;

	eeprom@50 {
		compatible = "renesas,r1ex24128", "atmel,24c128";
		reg = <0x50>;
		pagesize = <64>;
	};
};

/* High resolution System tick timers */
&ostm0 {
	status = "okay";
};

&ostm1 {
	status = "okay";
};

&pinctrl {
	eth0_pins: eth0 {
		pinmux = <RZA2_PINMUX(PORTE, 0, 7)>, /* REF50CK0 */
			 <RZA2_PINMUX(PORT6, 1, 7)>, /* RMMI0_TXDEN */
			 <RZA2_PINMUX(PORT6, 2, 7)>, /* RMII0_TXD0 */
			 <RZA2_PINMUX(PORT6, 3, 7)>, /* RMII0_TXD1 */
			 <RZA2_PINMUX(PORTE, 4, 7)>, /* RMII0_CRSDV */
			 <RZA2_PINMUX(PORTE, 1, 7)>, /* RMII0_RXD0 */
			 <RZA2_PINMUX(PORTE, 2, 7)>, /* RMII0_RXD1 */
			 <RZA2_PINMUX(PORTE, 3, 7)>, /* RMII0_RXER */
			 <RZA2_PINMUX(PORTE, 5, 1)>, /* ET0_MDC */
			 <RZA2_PINMUX(PORTE, 6, 1)>, /* ET0_MDIO */
			 <RZA2_PINMUX(PORTL, 0, 5)>; /* IRQ4 */
	};

	eth1_pins: eth1 {
		pinmux = <RZA2_PINMUX(PORTK, 3, 7)>, /* REF50CK1 */
			 <RZA2_PINMUX(PORTK, 0, 7)>, /* RMMI1_TXDEN */
			 <RZA2_PINMUX(PORTK, 1, 7)>, /* RMII1_TXD0 */
			 <RZA2_PINMUX(PORTK, 2, 7)>, /* RMII1_TXD1 */
			 <RZA2_PINMUX(PORT3, 2, 7)>, /* RMII1_CRSDV */
			 <RZA2_PINMUX(PORTK, 4, 7)>, /* RMII1_RXD0 */
			 <RZA2_PINMUX(PORT3, 5, 7)>, /* RMII1_RXD1 */
			 <RZA2_PINMUX(PORT3, 1, 7)>, /* RMII1_RXER */
			 <RZA2_PINMUX(PORT3, 3, 1)>, /* ET1_MDC */
			 <RZA2_PINMUX(PORT3, 4, 1)>, /* ET1_MDIO */
			 <RZA2_PINMUX(PORTL, 1, 5)>; /* IRQ5 */
	};

	i2c3_pins: i2c3 {
		pinmux = <RZA2_PINMUX(PORTD, 6, 1)>,	/* RIIC3SCL */
			 <RZA2_PINMUX(PORTD, 7, 1)>;	/* RIIC3SDA */
	};

	keyboard_pins: keyboard {
		pinmux = <RZA2_PINMUX(PORTJ, 1, 6)>;	/* IRQ0 */
	};

	/* Serial Console */
	scif2_pins: serial2 {
		pinmux = <RZA2_PINMUX(PORTE, 2, 3)>,	/* TxD2 */
			 <RZA2_PINMUX(PORTE, 1, 3)>;	/* RxD2 */
	};

	sdhi0_pins: sdhi0 {
		pinmux = <RZA2_PINMUX(PORT5, 0, 3)>,	/* SD0_CD */
			 <RZA2_PINMUX(PORT5, 1, 3)>;	/* SD0_WP */
	};

	sdhi1_pins: sdhi1 {
		pinmux = <RZA2_PINMUX(PORT5, 4, 3)>,	/* SD1_CD */
			 <RZA2_PINMUX(PORT5, 5, 3)>;	/* SD1_WP */
	};

	usb0_pins: usb0 {
		pinmux = <RZA2_PINMUX(PORT5, 2, 3)>,	/* VBUSIN0 */
			 <RZA2_PINMUX(PORTC, 6, 1)>,	/* VBUSEN0 */
			 <RZA2_PINMUX(PORTC, 7, 1)>;	/* OVRCUR0 */
	};

	usb1_pins: usb1 {
		pinmux = <RZA2_PINMUX(PORTC, 0, 1)>,	/* VBUSIN1 */
			 <RZA2_PINMUX(PORTC, 5, 1)>;	/* VBUSEN1 */
	};
};

/* RTC_X1 */
&rtc_x1_clk {
	clock-frequency = <32768>;
};

/* Serial Console */
&scif2 {
	pinctrl-names = "default";
	pinctrl-0 = <&scif2_pins>;

	status = "okay";
};

&sdhi0 {
	pinctrl-names = "default";
	pinctrl-0 = <&sdhi0_pins>;
	bus-width = <4>;
	status = "okay";
};

&sdhi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&sdhi1_pins>;
	bus-width = <4>;
	status = "okay";
};

/* USB-0 as Host */
&usb2_phy0 {
	pinctrl-names = "default";
	pinctrl-0 = <&usb0_pins>;
	dr_mode = "host";	/* Requires JP3 to be fitted */
	status = "okay";
};

/* USB-1 as Host */
&usb2_phy1 {
	pinctrl-names = "default";
	pinctrl-0 = <&usb1_pins>;
	dr_mode = "host";
	status = "okay";
};

/* USB_X1 */
&usb_x1_clk {
	clock-frequency = <48000000>;
};
