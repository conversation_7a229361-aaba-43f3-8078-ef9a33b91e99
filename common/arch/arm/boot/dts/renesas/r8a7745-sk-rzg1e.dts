// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the SK-RZG1E board
 *
 * Copyright (C) 2016-2017 Cogent Embedded, Inc.
 */

/dts-v1/;
#include "r8a7745.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "SK-RZG1E";
	compatible = "renesas,sk-rzg1e", "renesas,r8a7745";

	aliases {
		serial0 = &scif2;
	};

	chosen {
		bootargs = "ignore_loglevel rw root=/dev/nfs ip=on";
		stdout-path = "serial0:115200n8";
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0 0x40000000 0 0x40000000>;
	};
};

&extal_clk {
	clock-frequency = <20000000>;
};

&pfc {
	scif2_pins: scif2 {
		groups = "scif2_data";
		function = "scif2";
	};

	ether_pins: ether {
		groups = "eth_link", "eth_mdio", "eth_rmii";
		function = "eth";
	};

	phy1_pins: phy1 {
		groups = "intc_irq8";
		function = "intc";
	};
};

&scif2 {
	pinctrl-0 = <&scif2_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&ether {
	pinctrl-0 = <&ether_pins>, <&phy1_pins>;
	pinctrl-names = "default";

	phy-handle = <&phy1>;
	renesas,ether-link-active-low;
	status = "okay";

	phy1: ethernet-phy@1 {
		compatible = "ethernet-phy-id0022.1537",
			     "ethernet-phy-ieee802.3-c22";
		reg = <1>;
		interrupt-parent = <&irqc>;
		interrupts = <8 IRQ_TYPE_LEVEL_LOW>;
		micrel,led-mode = <1>;
		reset-gpios = <&gpio1 24 GPIO_ACTIVE_LOW>;
	};
};
