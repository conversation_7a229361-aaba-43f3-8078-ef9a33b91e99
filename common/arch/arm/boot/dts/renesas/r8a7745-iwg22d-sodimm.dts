// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the iWave-RZG1E SODIMM carrier board
 *
 * Copyright (C) 2017 Renesas Electronics Corp.
 */

/*
 * SSI-SGTL5000
 *
 * This command is required when Playback/Capture
 *
 *      amixer set "DVC Out" 100%
 *      amixer set "DVC In" 100%
 *
 * You can use Mute
 *
 *      amixer set "DVC Out Mute" on
 *      amixer set "DVC In Mute" on
 *
 * You can use Volume Ramp
 *
 *      amixer set "DVC Out Ramp Up Rate"   "0.125 dB/64 steps"
 *      amixer set "DVC Out Ramp Down Rate" "0.125 dB/512 steps"
 *      amixer set "DVC Out Ramp" on
 *      aplay xxx.wav &
 *      amixer set "DVC Out"  80%  // Volume Down
 *      amixer set "DVC Out" 100%  // Volume Up
 */

/dts-v1/;
#include "r8a7745-iwg22m.dtsi"
#include <dt-bindings/pwm/pwm.h>

/ {
	model = "iWave Systems RainboW-G22D-SODIMM board based on RZ/G1E";
	compatible = "iwave,g22d", "iwave,g22m", "renesas,r8a7745";

	aliases {
		ethernet0 = &avb;
		serial3 = &scif4;
		serial5 = &hscif1;
	};

	chosen {
		bootargs = "ignore_loglevel rw root=/dev/nfs ip=on";
		stdout-path = "serial3:115200n8";
	};

	audio_clock: audio_clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <26000000>;
	};

	backlight_lcd: backlight {
		compatible = "pwm-backlight";
		pwms = <&tpu 3 5000000 PWM_POLARITY_INVERTED>;
		brightness-levels = <0 4 8 16 32 64 128 255>;
		default-brightness-level = <7>;
	};

	lcd_panel: lcd {
		compatible = "edt,etm043080dh6gp";
		power-supply = <&vccq_panel>;
		backlight = <&backlight_lcd>;

		port {
			lcd_in: endpoint {
				remote-endpoint = <&du_out_rgb0>;
			};
		};
	};

	vccq_panel: regulator-vccq-panel {
		compatible = "regulator-fixed";
		regulator-name = "Panel VccQ";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio1 13 GPIO_ACTIVE_LOW>;
		enable-active-high;
	};

	vccq_sdhi0: regulator-vccq-sdhi0 {
		compatible = "regulator-gpio";

		regulator-name = "SDHI0 VccQ";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <3300000>;

		gpios = <&gpio0 20 GPIO_ACTIVE_LOW>;
		gpios-states = <1>;
		states = <3300000 1>, <1800000 0>;
	};

	rsnd_sgtl5000: sound {
		compatible = "simple-audio-card";
		simple-audio-card,format = "i2s";
		simple-audio-card,bitclock-master = <&sndcodec>;
		simple-audio-card,frame-master = <&sndcodec>;

		sndcpu: simple-audio-card,cpu {
			sound-dai = <&rcar_sound>;
		};

		sndcodec: simple-audio-card,codec {
			sound-dai = <&sgtl5000>;
		};
	};
};

&avb {
	pinctrl-0 = <&avb_pins>;
	pinctrl-names = "default";

	phy-handle = <&phy3>;
	phy-mode = "gmii";
	renesas,no-ether-link;
	status = "okay";

	phy3: ethernet-phy@3 {
	/*
	 * On some older versions of the platform (before R4.0) the phy address
	 * may be 1 or 3. The address is fixed to 3 for R4.0 onwards.
	 */
		compatible = "ethernet-phy-id0022.1622",
			     "ethernet-phy-ieee802.3-c22";
		reg = <3>;
		micrel,led-mode = <1>;
	};
};

&can0 {
	pinctrl-0 = <&can0_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&du {
	pinctrl-0 = <&du0_pins>;
	pinctrl-names = "default";

	status = "okay";

	ports {
		port@0 {
			endpoint {
				remote-endpoint = <&lcd_in>;
			};
		};
	};
};

&hscif1 {
	pinctrl-0 = <&hscif1_pins>;
	pinctrl-names = "default";

	uart-has-rtscts;
	status = "okay";
};

&hsusb {
	status = "okay";
	pinctrl-0 = <&usb0_pins>;
	pinctrl-names = "default";
};

&i2c5 {
	pinctrl-0 = <&i2c5_pins>;
	pinctrl-names = "default";

	status = "okay";
	clock-frequency = <400000>;

	sgtl5000: codec@a {
		compatible = "fsl,sgtl5000";
		#sound-dai-cells = <0>;
		reg = <0x0a>;
		clocks = <&audio_clock>;
		VDDA-supply = <&reg_3p3v>;
		VDDIO-supply = <&reg_3p3v>;
	};

	stmpe811@44 {
		compatible = "st,stmpe811";
		reg = <0x44>;
		interrupt-parent = <&gpio4>;
		interrupts = <4 IRQ_TYPE_LEVEL_LOW>;

		/* 3.25 MHz ADC clock speed */
		st,adc-freq = <1>;
		/* ADC conversion time: 80 clocks */
		st,sample-time = <4>;
		/* 12-bit ADC */
		st,mod-12b = <1>;
		/* internal ADC reference */
		st,ref-sel = <0>;

		stmpe_touchscreen {
			compatible = "st,stmpe-ts";
			/* 8 sample average control */
			st,ave-ctrl = <3>;
			/* 7 length fractional part in z */
			st,fraction-z = <7>;
			/*
			 * 50 mA typical 80 mA max touchscreen drivers
			 * current limit value
			 */
			st,i-drive = <1>;
			/* 1 ms panel driver settling time */
			st,settling = <3>;
			/* 5 ms touch detect interrupt delay */
			st,touch-det-delay = <5>;
		};
	};
};

&pci1 {
	status = "okay";
	pinctrl-0 = <&usb1_pins>;
	pinctrl-names = "default";
};

&pfc {
	avb_pins: avb {
		groups = "avb_mdio", "avb_gmii";
		function = "avb";
	};

	backlight_pins: backlight {
		groups = "tpu_to3_c";
		function = "tpu";
	};

	can0_pins: can0 {
		groups = "can0_data";
		function = "can0";
	};

	du0_pins: du0 {
		groups = "du0_rgb666", "du0_sync", "du0_disp", "du0_clk0_out";
		function = "du0";
	};

	hscif1_pins: hscif1 {
		groups = "hscif1_data", "hscif1_ctrl";
		function = "hscif1";
	};

	i2c5_pins: i2c5 {
		groups = "i2c5_b";
		function = "i2c5";
	};

	scif4_pins: scif4 {
		groups = "scif4_data_b";
		function = "scif4";
	};

	sdhi0_pins: sd0 {
		groups = "sdhi0_data4", "sdhi0_ctrl";
		function = "sdhi0";
		power-source = <3300>;
	};

	sound_pins: sound {
		groups = "ssi34_ctrl", "ssi3_data", "ssi4_data";
		function = "ssi";
	};

	usb0_pins: usb0 {
		groups = "usb0";
		function = "usb0";
	};

	usb1_pins: usb1 {
		groups = "usb1";
		function = "usb1";
	};
};

&rcar_sound {
	pinctrl-0 = <&sound_pins>;
	pinctrl-names = "default";
	status = "okay";

	/* Single DAI */

	#sound-dai-cells = <0>;

	rcar_sound,dai {
		dai0 {
			playback = <&ssi3>, <&src3>, <&dvc0>;
			capture = <&ssi4>, <&src4>, <&dvc1>;
		};
	};
};

&scif4 {
	pinctrl-0 = <&scif4_pins>;
	pinctrl-names = "default";

	status = "okay";
};

&sdhi0 {
	pinctrl-0 = <&sdhi0_pins>;
	pinctrl-names = "default";

	vmmc-supply = <&reg_3p3v>;
	vqmmc-supply = <&vccq_sdhi0>;
	cd-gpios = <&gpio6 6 GPIO_ACTIVE_LOW>;
	status = "okay";
};

&ssi4 {
	shared-pin;
};

&tpu {
	pinctrl-0 = <&backlight_pins>;
	pinctrl-names = "default";
	status = "okay";
};

&usbphy {
	status = "okay";
};
