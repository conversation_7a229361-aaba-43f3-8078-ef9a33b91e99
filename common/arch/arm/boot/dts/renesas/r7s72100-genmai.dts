// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the Genmai board
 *
 * Copyright (C) 2013-14 Renesas Solutions Corp.
 * Copyright (C) 2014 <PERSON><PERSON>, Sang Engineering <<EMAIL>>
 */

/dts-v1/;
#include "r7s72100.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pinctrl/r7s72100-pinctrl.h>

/ {
	model = "Genmai";
	compatible = "renesas,genmai", "renesas,r7s72100";

	aliases {
		serial0 = &scif2;
	};

	chosen {
		bootargs = "ignore_loglevel rw root=/dev/nfs ip=on";
		stdout-path = "serial0:115200n8";
	};

	memory@8000000 {
		device_type = "memory";
		reg = <0x08000000 0x08000000>;
	};

	lbsc {
		#address-cells = <1>;
		#size-cells = <1>;
	};

	leds {
		status = "okay";
		compatible = "gpio-leds";

		led1 {
			gpios = <&port4 10 GPIO_ACTIVE_LOW>;
		};

		led2 {
			gpios = <&port4 11 GPIO_ACTIVE_LOW>;
		};
	};
};

&pinctrl {

	scif2_pins: serial2 {
		/* P3_0 as TxD2; P3_2 as RxD2 */
		pinmux = <RZA1_PINMUX(3, 0, 6)>, <RZA1_PINMUX(3, 2, 4)>;
	};

	i2c2_pins: i2c2 {
		/* RIIC2: P1_4 as SCL, P1_5 as SDA */
		pinmux = <RZA1_PINMUX(1, 4, 1)>, <RZA1_PINMUX(1, 5, 1)>;
	};

	ether_pins: ether {
		/* Ethernet on Ports 1,2,3,5 */
		pinmux = <RZA1_PINMUX(1, 14, 4)>,/* P1_14 = ET_COL  */
			 <RZA1_PINMUX(5, 9, 2)>, /* P5_9 = ET_MDC   */
			 <RZA1_PINMUX(3, 3, 2)>, /* P3_3 = ET_MDIO */
			 <RZA1_PINMUX(3, 4, 2)>, /* P3_4 = ET_RXCLK */
			 <RZA1_PINMUX(3, 5, 2)>, /* P3_5 = ET_RXER  */
			 <RZA1_PINMUX(3, 6, 2)>, /* P3_6 = ET_RXDV  */
			 <RZA1_PINMUX(2, 0, 2)>, /* P2_0 = ET_TXCLK */
			 <RZA1_PINMUX(2, 1, 2)>, /* P2_1 = ET_TXER  */
			 <RZA1_PINMUX(2, 2, 2)>, /* P2_2 = ET_TXEN  */
			 <RZA1_PINMUX(2, 3, 2)>, /* P2_3 = ET_CRS   */
			 <RZA1_PINMUX(2, 4, 2)>, /* P2_4 = ET_TXD0  */
			 <RZA1_PINMUX(2, 5, 2)>, /* P2_5 = ET_TXD1  */
			 <RZA1_PINMUX(2, 6, 2)>, /* P2_6 = ET_TXD2  */
			 <RZA1_PINMUX(2, 7, 2)>, /* P2_7 = ET_TXD3  */
			 <RZA1_PINMUX(2, 8, 2)>, /* P2_8 = ET_RXD0  */
			 <RZA1_PINMUX(2, 9, 2)>, /* P2_9 = ET_RXD1  */
			 <RZA1_PINMUX(2, 10, 2)>,/* P2_10 = ET_RXD2 */
			 <RZA1_PINMUX(2, 11, 2)>;/* P2_11 = ET_RXD3 */
	};
};

&extal_clk {
	clock-frequency = <13330000>;
};

&usb_x1_clk {
	clock-frequency = <48000000>;
};

&rtc_x1_clk {
	clock-frequency = <32768>;
};

&mtu2 {
	status = "okay";
};

&ether {
	pinctrl-names = "default";
	pinctrl-0 = <&ether_pins>;

	status = "okay";

	renesas,no-ether-link;
	phy-handle = <&phy0>;
	phy0: ethernet-phy@0 {
		compatible = "ethernet-phy-idb824.2814",
			     "ethernet-phy-ieee802.3-c22";
		reg = <0>;
	};
};

&i2c2 {
	status = "okay";
	clock-frequency = <400000>;

	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_pins>;

	eeprom@50 {
		compatible = "renesas,r1ex24128", "atmel,24c128";
		reg = <0x50>;
		pagesize = <64>;
	};
};

&rtc {
	status = "okay";
};

&scif2 {
	pinctrl-names = "default";
	pinctrl-0 = <&scif2_pins>;

	status = "okay";
};

&spi4 {
	status = "okay";

	codec: codec@0 {
		compatible = "wlf,wm8978";
		reg = <0>;
		spi-max-frequency = <5000000>;
	};
};
