// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the r8a77470 SoC
 *
 * Copyright (C) 2018 Renesas Electronics Corp.
 */

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/clock/r8a77470-cpg-mssr.h>
#include <dt-bindings/power/r8a77470-sysc.h>
/ {
	compatible = "renesas,r8a77470";
	#address-cells = <2>;
	#size-cells = <2>;

	aliases {
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		i2c3 = &i2c3;
		i2c4 = &i2c4;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0>;
			clock-frequency = <**********>;
			clocks = <&cpg CPG_CORE R8A77470_CLK_Z2>;
			power-domains = <&sysc R8A77470_PD_CA7_CPU0>;
			enable-method = "renesas,apmu";
			next-level-cache = <&L2_CA7>;
		};

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <1>;
			clock-frequency = <**********>;
			clocks = <&cpg CPG_CORE R8A77470_CLK_Z2>;
			power-domains = <&sysc R8A77470_PD_CA7_CPU1>;
			enable-method = "renesas,apmu";
			next-level-cache = <&L2_CA7>;
		};

		L2_CA7: cache-controller-0 {
			compatible = "cache";
			cache-unified;
			cache-level = <2>;
			power-domains = <&sysc R8A77470_PD_CA7_SCU>;
		};
	};

	/* External root clock */
	extal_clk: extal {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		/* This value must be overridden by the board. */
		clock-frequency = <0>;
	};

	pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts-extended = <&gic GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>,
				      <&gic GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>;
	};

	/* External SCIF clock */
	scif_clk: scif {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		/* This value must be overridden by the board. */
		clock-frequency = <0>;
	};

	soc {
		compatible = "simple-bus";
		interrupt-parent = <&gic>;

		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		rwdt: watchdog@e6020000 {
			compatible = "renesas,r8a77470-wdt",
				     "renesas,rcar-gen2-wdt";
			reg = <0 0xe6020000 0 0x0c>;
			interrupts = <GIC_SPI 140 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 402>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 402>;
			status = "disabled";
		};

		gpio0: gpio@e6050000 {
			compatible = "renesas,gpio-r8a77470",
				     "renesas,rcar-gen2-gpio";
			reg = <0 0xe6050000 0 0x50>;
			interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
			#gpio-cells = <2>;
			gpio-controller;
			gpio-ranges = <&pfc 0 0 23>;
			#interrupt-cells = <2>;
			interrupt-controller;
			clocks = <&cpg CPG_MOD 912>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 912>;
		};

		gpio1: gpio@e6051000 {
			compatible = "renesas,gpio-r8a77470",
				     "renesas,rcar-gen2-gpio";
			reg = <0 0xe6051000 0 0x50>;
			interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
			#gpio-cells = <2>;
			gpio-controller;
			gpio-ranges = <&pfc 0 32 23>;
			#interrupt-cells = <2>;
			interrupt-controller;
			clocks = <&cpg CPG_MOD 911>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 911>;
		};

		gpio2: gpio@e6052000 {
			compatible = "renesas,gpio-r8a77470",
				     "renesas,rcar-gen2-gpio";
			reg = <0 0xe6052000 0 0x50>;
			interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
			#gpio-cells = <2>;
			gpio-controller;
			gpio-ranges = <&pfc 0 64 32>;
			#interrupt-cells = <2>;
			interrupt-controller;
			clocks = <&cpg CPG_MOD 910>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 910>;
		};

		gpio3: gpio@e6053000 {
			compatible = "renesas,gpio-r8a77470",
				     "renesas,rcar-gen2-gpio";
			reg = <0 0xe6053000 0 0x50>;
			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
			#gpio-cells = <2>;
			gpio-controller;
			gpio-ranges = <&pfc 0 96 30>;
			gpio-reserved-ranges = <17 10>;
			#interrupt-cells = <2>;
			interrupt-controller;
			clocks = <&cpg CPG_MOD 909>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 909>;
		};

		gpio4: gpio@e6054000 {
			compatible = "renesas,gpio-r8a77470",
				     "renesas,rcar-gen2-gpio";
			reg = <0 0xe6054000 0 0x50>;
			interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
			#gpio-cells = <2>;
			gpio-controller;
			gpio-ranges = <&pfc 0 128 26>;
			#interrupt-cells = <2>;
			interrupt-controller;
			clocks = <&cpg CPG_MOD 908>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 908>;
		};

		gpio5: gpio@e6055000 {
			compatible = "renesas,gpio-r8a77470",
				     "renesas,rcar-gen2-gpio";
			reg = <0 0xe6055000 0 0x50>;
			interrupts = <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>;
			#gpio-cells = <2>;
			gpio-controller;
			gpio-ranges = <&pfc 0 160 32>;
			#interrupt-cells = <2>;
			interrupt-controller;
			clocks = <&cpg CPG_MOD 907>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 907>;
		};

		pfc: pinctrl@e6060000 {
			compatible = "renesas,pfc-r8a77470";
			reg = <0 0xe6060000 0 0x118>;
		};

		cpg: clock-controller@e6150000 {
			compatible = "renesas,r8a77470-cpg-mssr";
			reg = <0 0xe6150000 0 0x1000>;
			clocks = <&extal_clk>, <&usb_extal_clk>;
			clock-names = "extal", "usb_extal";
			#clock-cells = <2>;
			#power-domain-cells = <0>;
			#reset-cells = <1>;
		};

		apmu@e6151000 {
			compatible = "renesas,r8a77470-apmu", "renesas,apmu";
			reg = <0 0xe6151000 0 0x188>;
			cpus = <&cpu0>, <&cpu1>;
		};

		rst: reset-controller@e6160000 {
			compatible = "renesas,r8a77470-rst";
			reg = <0 0xe6160000 0 0x100>;
		};

		sysc: system-controller@e6180000 {
			compatible = "renesas,r8a77470-sysc";
			reg = <0 0xe6180000 0 0x200>;
			#power-domain-cells = <1>;
		};

		irqc: interrupt-controller@e61c0000 {
			compatible = "renesas,irqc-r8a77470", "renesas,irqc";
			#interrupt-cells = <2>;
			interrupt-controller;
			reg = <0 0xe61c0000 0 0x200>;
			interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 407>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 407>;
		};

		icram0:	sram@e63a0000 {
			compatible = "mmio-sram";
			reg = <0 0xe63a0000 0 0x12000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0 0xe63a0000 0x12000>;
		};

		icram1:	sram@e63c0000 {
			compatible = "mmio-sram";
			reg = <0 0xe63c0000 0 0x1000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0 0xe63c0000 0x1000>;

			smp-sram@0 {
				compatible = "renesas,smp-sram";
				reg = <0 0x100>;
			};
		};

		icram2:	sram@e6300000 {
			compatible = "mmio-sram";
			reg = <0 0xe6300000 0 0x20000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0 0xe6300000 0x20000>;
		};

		i2c0: i2c@e6508000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "renesas,i2c-r8a77470",
				     "renesas,rcar-gen2-i2c";
			reg = <0 0xe6508000 0 0x40>;
			interrupts = <GIC_SPI 287 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 931>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 931>;
			i2c-scl-internal-delay-ns = <6>;
			status = "disabled";
		};

		i2c1: i2c@e6518000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "renesas,i2c-r8a77470",
				     "renesas,rcar-gen2-i2c";
			reg = <0 0xe6518000 0 0x40>;
			interrupts = <GIC_SPI 288 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 930>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 930>;
			i2c-scl-internal-delay-ns = <6>;
			status = "disabled";
		};

		i2c2: i2c@e6530000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "renesas,i2c-r8a77470",
				     "renesas,rcar-gen2-i2c";
			reg = <0 0xe6530000 0 0x40>;
			interrupts = <GIC_SPI 286 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 929>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 929>;
			i2c-scl-internal-delay-ns = <6>;
			status = "disabled";
		};

		i2c3: i2c@e6540000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "renesas,i2c-r8a77470",
				     "renesas,rcar-gen2-i2c";
			reg = <0 0xe6540000 0 0x40>;
			interrupts = <GIC_SPI 290 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 928>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 928>;
			i2c-scl-internal-delay-ns = <6>;
			status = "disabled";
		};

		i2c4: i2c@e6520000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "renesas,i2c-r8a77470",
				     "renesas,rcar-gen2-i2c";
			reg = <0 0xe6520000 0 0x40>;
			interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 927>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 927>;
			i2c-scl-internal-delay-ns = <6>;
			status = "disabled";
		};

		hsusb0: hsusb@e6590000 {
			compatible = "renesas,usbhs-r8a77470",
				     "renesas,rcar-gen2-usbhs";
			reg = <0 0xe6590000 0 0x100>;
			interrupts = <GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 704>;
			dmas = <&usb_dmac00 0>, <&usb_dmac00 1>,
			       <&usb_dmac10 0>, <&usb_dmac10 1>;
			dma-names = "ch0", "ch1", "ch2", "ch3";
			renesas,buswait = <4>;
			phys = <&usb0 1>;
			phy-names = "usb";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 704>;
			status = "disabled";
		};

		usbphy0: usb-phy-controller@e6590100 {
			compatible = "renesas,usb-phy-r8a77470",
				     "renesas,rcar-gen2-usb-phy";
			reg = <0 0xe6590100 0 0x100>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cpg CPG_MOD 704>;
			clock-names = "usbhs";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 704>;
			status = "disabled";

			usb0: usb-phy@0 {
				reg = <0>;
				#phy-cells = <1>;
			};
		};

		hsusb1: hsusb@e6598000 {
			compatible = "renesas,usbhs-r8a77470",
				     "renesas,rcar-gen2-usbhs";
			reg = <0 0xe6598000 0 0x100>;
			interrupts = <GIC_SPI 291 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 706>;
			dmas = <&usb_dmac01 0>, <&usb_dmac01 1>,
			       <&usb_dmac11 0>, <&usb_dmac11 1>;
			dma-names = "ch0", "ch1", "ch2", "ch3";
			renesas,buswait = <4>;
			/* We need to turn on usbphy0 to make usbphy1 to work */
			phys = <&usb1 1>;
			phy-names = "usb";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 706>;
			status = "disabled";
		};

		usbphy1: usb-phy-controller@e6598100 {
			compatible = "renesas,usb-phy-r8a77470",
				     "renesas,rcar-gen2-usb-phy";
			reg = <0 0xe6598100 0 0x100>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cpg CPG_MOD 706>;
			clock-names = "usbhs";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 706>;
			status = "disabled";

			usb1: usb-phy@0 {
				reg = <0>;
				#phy-cells = <1>;
			};
		};

		usb_dmac00: dma-controller@e65a0000 {
			compatible = "renesas,r8a77470-usb-dmac",
				     "renesas,usb-dmac";
			reg = <0 0xe65a0000 0 0x100>;
			interrupts = <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "ch0", "ch1";
			clocks = <&cpg CPG_MOD 330>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 330>;
			#dma-cells = <1>;
			dma-channels = <2>;
		};

		usb_dmac10: dma-controller@e65b0000 {
			compatible = "renesas,r8a77470-usb-dmac",
				     "renesas,usb-dmac";
			reg = <0 0xe65b0000 0 0x100>;
			interrupts = <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "ch0", "ch1";
			clocks = <&cpg CPG_MOD 331>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 331>;
			#dma-cells = <1>;
			dma-channels = <2>;
		};

		usb_dmac01: dma-controller@e65a8000 {
			compatible = "renesas,r8a77470-usb-dmac",
				     "renesas,usb-dmac";
			reg = <0 0xe65a8000 0 0x100>;
			interrupts = <GIC_SPI 293 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 293 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "ch0", "ch1";
			clocks = <&cpg CPG_MOD 326>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 326>;
			#dma-cells = <1>;
			dma-channels = <2>;
		};

		usb_dmac11: dma-controller@e65b8000 {
			compatible = "renesas,r8a77470-usb-dmac",
				     "renesas,usb-dmac";
			reg = <0 0xe65b8000 0 0x100>;
			interrupts = <GIC_SPI 292 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 292 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "ch0", "ch1";
			clocks = <&cpg CPG_MOD 327>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 327>;
			#dma-cells = <1>;
			dma-channels = <2>;
		};

		dmac0: dma-controller@e6700000 {
			compatible = "renesas,dmac-r8a77470",
				     "renesas,rcar-dmac";
			reg = <0 0xe6700000 0 0x20000>;
			interrupts = <GIC_SPI 197 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 200 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 201 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 202 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 203 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 204 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 205 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 206 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 207 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 209 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 210 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 211 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 212 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 213 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 214 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "error",
					  "ch0", "ch1", "ch2", "ch3",
					  "ch4", "ch5", "ch6", "ch7",
					  "ch8", "ch9", "ch10", "ch11",
					  "ch12", "ch13", "ch14";
			clocks = <&cpg CPG_MOD 219>;
			clock-names = "fck";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 219>;
			#dma-cells = <1>;
			dma-channels = <15>;
		};

		dmac1: dma-controller@e6720000 {
			compatible = "renesas,dmac-r8a77470",
				     "renesas,rcar-dmac";
			reg = <0 0xe6720000 0 0x20000>;
			interrupts = <GIC_SPI 220 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 216 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 217 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 218 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 219 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 308 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 309 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 310 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 311 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 312 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 313 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 314 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 315 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 316 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 317 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 318 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "error",
					  "ch0", "ch1", "ch2", "ch3",
					  "ch4", "ch5", "ch6", "ch7",
					  "ch8", "ch9", "ch10", "ch11",
					  "ch12", "ch13", "ch14";
			clocks = <&cpg CPG_MOD 218>;
			clock-names = "fck";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 218>;
			#dma-cells = <1>;
			dma-channels = <15>;
		};

		avb: ethernet@e6800000 {
			compatible = "renesas,etheravb-r8a77470",
				     "renesas,etheravb-rcar-gen2";
			reg = <0 0xe6800000 0 0x800>, <0 0xee0e8000 0 0x4000>;
			interrupts = <GIC_SPI 163 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 812>;
			clock-names = "fck";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 812>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		qspi0: spi@e6b10000 {
			compatible = "renesas,qspi-r8a77470", "renesas,qspi";
			reg = <0 0xe6b10000 0 0x2c>;
			interrupts = <GIC_SPI 184 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 918>;
			dmas = <&dmac0 0x17>, <&dmac0 0x18>,
			       <&dmac1 0x17>, <&dmac1 0x18>;
			dma-names = "tx", "rx", "tx", "rx";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			num-cs = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			resets = <&cpg 918>;
			status = "disabled";
		};

		qspi1: spi@ee200000 {
			compatible = "renesas,qspi-r8a77470", "renesas,qspi";
			reg = <0 0xee200000 0 0x2c>;
			interrupts = <GIC_SPI 239 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 917>;
			dmas = <&dmac0 0xd1>, <&dmac0 0xd2>,
			       <&dmac1 0xd1>, <&dmac1 0xd2>;
			dma-names = "tx", "rx", "tx", "rx";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			num-cs = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			resets = <&cpg 917>;
			status = "disabled";
		};

		scif0: serial@e6e60000 {
			compatible = "renesas,scif-r8a77470",
				     "renesas,rcar-gen2-scif", "renesas,scif";
			reg = <0 0xe6e60000 0 0x40>;
			interrupts = <GIC_SPI 152 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 721>,
				 <&cpg CPG_CORE R8A77470_CLK_ZS>, <&scif_clk>;
			clock-names = "fck", "brg_int", "scif_clk";
			dmas = <&dmac0 0x29>, <&dmac0 0x2a>,
			       <&dmac1 0x29>, <&dmac1 0x2a>;
			dma-names = "tx", "rx", "tx", "rx";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 721>;
			status = "disabled";
		};

		scif1: serial@e6e68000 {
			compatible = "renesas,scif-r8a77470",
				     "renesas,rcar-gen2-scif", "renesas,scif";
			reg = <0 0xe6e68000 0 0x40>;
			interrupts = <GIC_SPI 153 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 720>,
				 <&cpg CPG_CORE R8A77470_CLK_ZS>, <&scif_clk>;
			clock-names = "fck", "brg_int", "scif_clk";
			dmas = <&dmac0 0x2d>, <&dmac0 0x2e>,
			       <&dmac1 0x2d>, <&dmac1 0x2e>;
			dma-names = "tx", "rx", "tx", "rx";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 720>;
			status = "disabled";
		};

		scif2: serial@e6e58000 {
			compatible = "renesas,scif-r8a77470",
				     "renesas,rcar-gen2-scif", "renesas,scif";
			reg = <0 0xe6e58000 0 0x40>;
			interrupts = <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 719>,
				 <&cpg CPG_CORE R8A77470_CLK_ZS>, <&scif_clk>;
			clock-names = "fck", "brg_int", "scif_clk";
			dmas = <&dmac0 0x2b>, <&dmac0 0x2c>,
			       <&dmac1 0x2b>, <&dmac1 0x2c>;
			dma-names = "tx", "rx", "tx", "rx";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 719>;
			status = "disabled";
		};

		scif3: serial@e6ea8000 {
			compatible = "renesas,scif-r8a77470",
				     "renesas,rcar-gen2-scif", "renesas,scif";
			reg = <0 0xe6ea8000 0 0x40>;
			interrupts = <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 718>,
				 <&cpg CPG_CORE R8A77470_CLK_ZS>, <&scif_clk>;
			clock-names = "fck", "brg_int", "scif_clk";
			dmas = <&dmac0 0x2f>, <&dmac0 0x30>,
			       <&dmac1 0x2f>, <&dmac1 0x30>;
			dma-names = "tx", "rx", "tx", "rx";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 718>;
			status = "disabled";
		};

		scif4: serial@e6ee0000 {
			compatible = "renesas,scif-r8a77470",
				     "renesas,rcar-gen2-scif", "renesas,scif";
			reg = <0 0xe6ee0000 0 0x40>;
			interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 715>,
				 <&cpg CPG_CORE R8A77470_CLK_ZS>, <&scif_clk>;
			clock-names = "fck", "brg_int", "scif_clk";
			dmas = <&dmac0 0xfb>, <&dmac0 0xfc>,
			       <&dmac1 0xfb>, <&dmac1 0xfc>;
			dma-names = "tx", "rx", "tx", "rx";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 715>;
			status = "disabled";
		};

		scif5: serial@e6ee8000 {
			compatible = "renesas,scif-r8a77470",
				     "renesas,rcar-gen2-scif", "renesas,scif";
			reg = <0 0xe6ee8000 0 0x40>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 714>,
				 <&cpg CPG_CORE R8A77470_CLK_ZS>, <&scif_clk>;
			clock-names = "fck", "brg_int", "scif_clk";
			dmas = <&dmac0 0xfd>, <&dmac0 0xfe>,
			       <&dmac1 0xfd>, <&dmac1 0xfe>;
			dma-names = "tx", "rx", "tx", "rx";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 714>;
			status = "disabled";
		};

		hscif0: serial@e62c0000 {
			compatible = "renesas,hscif-r8a77470",
				     "renesas,rcar-gen2-hscif", "renesas,hscif";
			reg = <0 0xe62c0000 0 0x60>;
			interrupts = <GIC_SPI 154 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 717>,
				 <&cpg CPG_CORE R8A77470_CLK_ZS>, <&scif_clk>;
			clock-names = "fck", "brg_int", "scif_clk";
			dmas = <&dmac0 0x39>, <&dmac0 0x3a>,
			       <&dmac1 0x39>, <&dmac1 0x3a>;
			dma-names = "tx", "rx", "tx", "rx";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 717>;
			status = "disabled";
		};

		hscif1: serial@e62c8000 {
			compatible = "renesas,hscif-r8a77470",
				     "renesas,rcar-gen2-hscif", "renesas,hscif";
			reg = <0 0xe62c8000 0 0x60>;
			interrupts = <GIC_SPI 155 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 716>,
				 <&cpg CPG_CORE R8A77470_CLK_ZS>, <&scif_clk>;
			clock-names = "fck", "brg_int", "scif_clk";
			dmas = <&dmac0 0x4d>, <&dmac0 0x4e>,
			       <&dmac1 0x4d>, <&dmac1 0x4e>;
			dma-names = "tx", "rx", "tx", "rx";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 716>;
			status = "disabled";
		};

		hscif2: serial@e62d0000 {
			compatible = "renesas,hscif-r8a77470",
				     "renesas,rcar-gen2-hscif", "renesas,hscif";
			reg = <0 0xe62d0000 0 0x60>;
			interrupts = <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 713>,
				 <&cpg CPG_CORE R8A77470_CLK_ZS>, <&scif_clk>;
			clock-names = "fck", "brg_int", "scif_clk";
			dmas = <&dmac0 0x3b>, <&dmac0 0x3c>,
			       <&dmac1 0x3b>, <&dmac1 0x3c>;
			dma-names = "tx", "rx", "tx", "rx";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 713>;
			status = "disabled";
		};

		pwm0: pwm@e6e30000 {
			compatible = "renesas,pwm-r8a77470", "renesas,pwm-rcar";
			reg = <0 0xe6e30000 0 0x8>;
			clocks = <&cpg CPG_MOD 523>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 523>;
			#pwm-cells = <2>;
			status = "disabled";
		};

		pwm1: pwm@e6e31000 {
			compatible = "renesas,pwm-r8a77470", "renesas,pwm-rcar";
			reg = <0 0xe6e31000 0 0x8>;
			clocks = <&cpg CPG_MOD 523>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 523>;
			#pwm-cells = <2>;
			status = "disabled";
		};

		pwm2: pwm@e6e32000 {
			compatible = "renesas,pwm-r8a77470", "renesas,pwm-rcar";
			reg = <0 0xe6e32000 0 0x8>;
			clocks = <&cpg CPG_MOD 523>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 523>;
			#pwm-cells = <2>;
			status = "disabled";
		};

		pwm3: pwm@e6e33000 {
			compatible = "renesas,pwm-r8a77470", "renesas,pwm-rcar";
			reg = <0 0xe6e33000 0 0x8>;
			clocks = <&cpg CPG_MOD 523>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 523>;
			#pwm-cells = <2>;
			status = "disabled";
		};

		pwm4: pwm@e6e34000 {
			compatible = "renesas,pwm-r8a77470", "renesas,pwm-rcar";
			reg = <0 0xe6e34000 0 0x8>;
			clocks = <&cpg CPG_MOD 523>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 523>;
			#pwm-cells = <2>;
			status = "disabled";
		};

		pwm5: pwm@e6e35000 {
			compatible = "renesas,pwm-r8a77470", "renesas,pwm-rcar";
			reg = <0 0xe6e35000 0 0x8>;
			clocks = <&cpg CPG_MOD 523>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 523>;
			#pwm-cells = <2>;
			status = "disabled";
		};

		pwm6: pwm@e6e36000 {
			compatible = "renesas,pwm-r8a77470", "renesas,pwm-rcar";
			reg = <0 0xe6e36000 0 0x8>;
			clocks = <&cpg CPG_MOD 523>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 523>;
			#pwm-cells = <2>;
			status = "disabled";
		};

		vin0: video@e6ef0000 {
			compatible = "renesas,vin-r8a77470",
				     "renesas,rcar-gen2-vin";
			reg = <0 0xe6ef0000 0 0x1000>;
			interrupts = <GIC_SPI 188 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 811>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 811>;
			status = "disabled";
		};

		vin1: video@e6ef1000 {
			compatible = "renesas,vin-r8a77470",
				     "renesas,rcar-gen2-vin";
			reg = <0 0xe6ef1000 0 0x1000>;
			interrupts = <GIC_SPI 189 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 810>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 810>;
			status = "disabled";
		};

		ohci0: usb@ee080000 {
			compatible = "generic-ohci";
			reg = <0 0xee080000 0 0x100>;
			interrupts = <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 703>;
			phys = <&usb0 0>, <&usb2_phy0>;
			phy-names = "usb";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 703>;
			status = "disabled";
		};

		ehci0: usb@ee080100 {
			compatible = "generic-ehci";
			reg = <0 0xee080100 0 0x100>;
			interrupts = <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 703>;
			phys = <&usb0 0>, <&usb2_phy0>;
			phy-names = "usb";
			companion = <&ohci0>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 703>;
			status = "disabled";
		};

		usb2_phy0: usb-phy@ee080200 {
			compatible = "renesas,usb2-phy-r8a77470";
			reg = <0 0xee080200 0 0x700>;
			clocks = <&cpg CPG_MOD 703>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 703>;
			#phy-cells = <0>;
			status = "disabled";
		};

		ohci1: usb@ee0c0000 {
			compatible = "generic-ohci";
			reg = <0 0xee0c0000 0 0x100>;
			interrupts = <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 705>;
			phys = <&usb0 1>, <&usb2_phy1>, <&usb1 0>;
			phy-names = "usb";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 705>;
			status = "disabled";
		};

		ehci1: usb@ee0c0100 {
			compatible = "generic-ehci";
			reg = <0 0xee0c0100 0 0x100>;
			interrupts = <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 705>;
			phys = <&usb0 1>, <&usb2_phy1>, <&usb1 0>;
			phy-names = "usb";
			companion = <&ohci1>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 705>;
			status = "disabled";
		};

		usb2_phy1: usb-phy@ee0c0200 {
			compatible = "renesas,usb2-phy-r8a77470";
			reg = <0 0xee0c0200 0 0x700>;
			clocks = <&cpg CPG_MOD 705>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 705>;
			#phy-cells = <0>;
			status = "disabled";
		};

		sdhi0: mmc@ee100000 {
			compatible = "renesas,sdhi-r8a77470",
				     "renesas,rcar-gen2-sdhi";
			reg = <0 0xee100000 0 0x328>;
			interrupts = <GIC_SPI 165 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 314>;
			dmas = <&dmac0 0xcd>, <&dmac0 0xce>,
			       <&dmac1 0xcd>, <&dmac1 0xce>;
			dma-names = "tx", "rx", "tx", "rx";
			max-frequency = <156000000>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 314>;
			status = "disabled";
		};

		sdhi1: mmc@ee300000 {
			compatible = "renesas,sdhi-mmc-r8a77470";
			reg = <0 0xee300000 0 0x2000>;
			interrupts = <GIC_SPI 166 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 313>;
			max-frequency = <156000000>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 313>;
			status = "disabled";
		};

		sdhi2: mmc@ee160000 {
			compatible = "renesas,sdhi-r8a77470",
				     "renesas,rcar-gen2-sdhi";
			reg = <0 0xee160000 0 0x328>;
			interrupts = <GIC_SPI 167 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 312>;
			dmas = <&dmac0 0xd3>, <&dmac0 0xd4>,
			       <&dmac1 0xd3>, <&dmac1 0xd4>;
			dma-names = "tx", "rx", "tx", "rx";
			max-frequency = <78000000>;
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 312>;
			status = "disabled";
		};

		gic: interrupt-controller@f1001000 {
			compatible = "arm,gic-400";
			#interrupt-cells = <3>;
			#address-cells = <0>;
			interrupt-controller;
			reg = <0 0xf1001000 0 0x1000>, <0 0xf1002000 0 0x2000>,
			      <0 0xf1004000 0 0x2000>, <0 0xf1006000 0 0x2000>;
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_HIGH)>;
			clocks = <&cpg CPG_MOD 408>;
			clock-names = "clk";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 408>;
		};

		du: display@feb00000 {
			compatible = "renesas,du-r8a77470";
			reg = <0 0xfeb00000 0 0x40000>;
			interrupts = <GIC_SPI 256 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 268 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 724>, <&cpg CPG_MOD 723>;
			clock-names = "du.0", "du.1";
			resets = <&cpg 724>;
			reset-names = "du.0";
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;
					du_out_rgb0: endpoint {
					};
				};
				port@1 {
					reg = <1>;
					du_out_rgb1: endpoint {
					};
				};
				port@2 {
					reg = <2>;
					du_out_lvds0: endpoint {
					};
				};
			};
		};

		prr: chipid@ff000044 {
			compatible = "renesas,prr";
			reg = <0 0xff000044 0 4>;
		};

		cmt0: timer@ffca0000 {
			compatible = "renesas,r8a77470-cmt0",
				     "renesas,rcar-gen2-cmt0";
			reg = <0 0xffca0000 0 0x1004>;
			interrupts = <GIC_SPI 142 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 143 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 124>;
			clock-names = "fck";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 124>;
			status = "disabled";
		};

		cmt1: timer@e6130000 {
			compatible = "renesas,r8a77470-cmt1",
				     "renesas,rcar-gen2-cmt1";
			reg = <0 0xe6130000 0 0x1004>;
			interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 329>;
			clock-names = "fck";
			power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
			resets = <&cpg 329>;
			status = "disabled";
		};
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts-extended = <&gic GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
				      <&gic GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
				      <&gic GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>,
				      <&gic GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_LEVEL_LOW)>;
	};

	/* External USB clock - can be overridden by the board */
	usb_extal_clk: usb_extal {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <48000000>;
	};
};
