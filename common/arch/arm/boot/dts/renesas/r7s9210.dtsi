// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the R7S9210 SoC
 *
 * Copyright (C) 2018 Renesas Electronics Corporation
 *
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/clock/r7s9210-cpg-mssr.h>

/ {
	compatible = "renesas,r7s9210";
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	/* External clocks */
	extal_clk: extal {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		/* Value must be set by board */
		clock-frequency = <0>;
	};

	rtc_x1_clk: rtc_x1 {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		/* If clk present, value (32678) must be set by board */
		clock-frequency = <0>;
	};

	usb_x1_clk: usb_x1 {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		/* If clk present, value (48000000) must be set by board */
		clock-frequency = <0>;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0>;
			clock-frequency = <528000000>;
			next-level-cache = <&L2>;
		};
	};

	soc {
		compatible = "simple-bus";
		interrupt-parent = <&gic>;

		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		L2: cache-controller@1f003000 {
			compatible = "arm,pl310-cache";
			reg = <0x1f003000 0x1000>;
			interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;
			arm,early-bresp-disable;
			arm,full-line-zero-disable;
			cache-unified;
			cache-level = <2>;
		};

		scif0: serial@e8007000 {
			compatible = "renesas,scif-r7s9210";
			reg = <0xe8007000 0x18>;
			interrupts = <GIC_SPI 265 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 266 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 267 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 265 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 268 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 268 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "eri", "rxi", "txi",
					  "bri", "dri", "tei";
			clocks = <&cpg CPG_MOD 47>;
			clock-names = "fck";
			power-domains = <&cpg>;
			status = "disabled";
		};

		scif1: serial@e8007800 {
			compatible = "renesas,scif-r7s9210";
			reg = <0xe8007800 0x18>;
			interrupts = <GIC_SPI 271 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 272 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 273 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 271 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 274 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 274 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "eri", "rxi", "txi",
					  "bri", "dri", "tei";
			clocks = <&cpg CPG_MOD 46>;
			clock-names = "fck";
			power-domains = <&cpg>;
			status = "disabled";
		};

		scif2: serial@e8008000 {
			compatible = "renesas,scif-r7s9210";
			reg = <0xe8008000 0x18>;
			interrupts = <GIC_SPI 277 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 278 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 279 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 277 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 280 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 280 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "eri", "rxi", "txi",
					  "bri", "dri", "tei";
			clocks = <&cpg CPG_MOD 45>;
			clock-names = "fck";
			power-domains = <&cpg>;
			status = "disabled";
		};

		scif3: serial@e8008800 {
			compatible = "renesas,scif-r7s9210";
			reg = <0xe8008800 0x18>;
			interrupts = <GIC_SPI 283 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 284 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 285 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 283 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 286 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 286 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "eri", "rxi", "txi",
					  "bri", "dri", "tei";
			clocks = <&cpg CPG_MOD 44>;
			clock-names = "fck";
			power-domains = <&cpg>;
			status = "disabled";
		};

		scif4: serial@e8009000 {
			compatible = "renesas,scif-r7s9210";
			reg = <0xe8009000 0x18>;
			interrupts = <GIC_SPI 289 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 290 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 291 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 289 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 292 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 292 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "eri", "rxi", "txi",
					  "bri", "dri", "tei";
			clocks = <&cpg CPG_MOD 43>;
			clock-names = "fck";
			power-domains = <&cpg>;
			status = "disabled";
		};

		spi0: spi@e800c800 {
			compatible = "renesas,rspi-r7s9210", "renesas,rspi-rz";
			reg = <0xe800c800 0x24>;
			interrupts = <GIC_SPI 312 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 313 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 314 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "error", "rx", "tx";
			clocks = <&cpg CPG_MOD 97>;
			power-domains = <&cpg>;
			num-cs = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		spi1: spi@e800d000 {
			compatible = "renesas,rspi-r7s9210", "renesas,rspi-rz";
			reg = <0xe800d000 0x24>;
			interrupts = <GIC_SPI 315 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 316 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 317 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "error", "rx", "tx";
			clocks = <&cpg CPG_MOD 96>;
			power-domains = <&cpg>;
			num-cs = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		spi2: spi@e800d800 {
			compatible = "renesas,rspi-r7s9210", "renesas,rspi-rz";
			reg = <0xe800d800 0x24>;
			interrupts = <GIC_SPI 318 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 319 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 320 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "error", "rx", "tx";
			clocks = <&cpg CPG_MOD 95>;
			power-domains = <&cpg>;
			num-cs = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		ether0: ethernet@e8204000 {
			compatible = "renesas,ether-r7s9210";
			reg = <0xe8204000 0x200>;
			interrupts = <GIC_SPI 341 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 65>;
			power-domains = <&cpg>;

			phy-mode = "rmii";
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		ether1: ethernet@e8204200 {
			compatible = "renesas,ether-r7s9210";
			reg = <0xe8204200 0x200>;
			interrupts = <GIC_SPI 342 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 64>;
			power-domains = <&cpg>;
			phy-mode = "rmii";
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		i2c0: i2c@e803a000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "renesas,riic-r7s9210", "renesas,riic-rz";
			reg = <0xe803a000 0x44>;
			interrupts = <GIC_SPI 232 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 233 IRQ_TYPE_EDGE_RISING>,
				     <GIC_SPI 234 IRQ_TYPE_EDGE_RISING>,
				     <GIC_SPI 235 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 236 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 237 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 238 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 239 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "tei", "ri", "ti", "spi", "sti",
					  "naki", "ali", "tmoi";
			clocks = <&cpg CPG_MOD 87>;
			power-domains = <&cpg>;
			clock-frequency = <100000>;
			status = "disabled";
		};

		i2c1: i2c@e803a400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "renesas,riic-r7s9210", "renesas,riic-rz";
			reg = <0xe803a400 0x44>;
			interrupts = <GIC_SPI 240 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 241 IRQ_TYPE_EDGE_RISING>,
				     <GIC_SPI 242 IRQ_TYPE_EDGE_RISING>,
				     <GIC_SPI 243 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 244 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 245 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 246 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 247 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "tei", "ri", "ti", "spi", "sti",
					  "naki", "ali", "tmoi";
			clocks = <&cpg CPG_MOD 86>;
			power-domains = <&cpg>;
			clock-frequency = <100000>;
			status = "disabled";
		};

		i2c2: i2c@e803a800 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "renesas,riic-r7s9210", "renesas,riic-rz";
			reg = <0xe803a800 0x44>;
			interrupts = <GIC_SPI 248 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 249 IRQ_TYPE_EDGE_RISING>,
				     <GIC_SPI 250 IRQ_TYPE_EDGE_RISING>,
				     <GIC_SPI 251 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 252 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 253 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 254 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 255 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "tei", "ri", "ti", "spi", "sti",
					  "naki", "ali", "tmoi";
			clocks = <&cpg CPG_MOD 85>;
			power-domains = <&cpg>;
			clock-frequency = <100000>;
			status = "disabled";
		};

		i2c3: i2c@e803ac00 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "renesas,riic-r7s9210", "renesas,riic-rz";
			reg = <0xe803ac00 0x44>;
			interrupts = <GIC_SPI 256 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 257 IRQ_TYPE_EDGE_RISING>,
				     <GIC_SPI 258 IRQ_TYPE_EDGE_RISING>,
				     <GIC_SPI 259 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 260 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 261 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 262 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 263 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "tei", "ri", "ti", "spi", "sti",
					  "naki", "ali", "tmoi";
			clocks = <&cpg CPG_MOD 84>;
			power-domains = <&cpg>;
			clock-frequency = <100000>;
			status = "disabled";
		};

		ostm0: timer@e803b000 {
			compatible = "renesas,r7s9210-ostm", "renesas,ostm";
			reg = <0xe803b000 0x30>;
			interrupts = <GIC_SPI 56 IRQ_TYPE_EDGE_RISING>;
			clocks = <&cpg CPG_MOD 36>;
			power-domains = <&cpg>;
			status = "disabled";
		};

		ostm1: timer@e803c000 {
			compatible = "renesas,r7s9210-ostm", "renesas,ostm";
			reg = <0xe803c000 0x30>;
			interrupts = <GIC_SPI 57 IRQ_TYPE_EDGE_RISING>;
			clocks = <&cpg CPG_MOD 35>;
			power-domains = <&cpg>;
			status = "disabled";
		};

		ostm2: timer@e803d000 {
			compatible = "renesas,r7s9210-ostm", "renesas,ostm";
			reg = <0xe803d000 0x30>;
			interrupts = <GIC_SPI 58 IRQ_TYPE_EDGE_RISING>;
			clocks = <&cpg CPG_MOD 34>;
			power-domains = <&cpg>;
			status = "disabled";
		};

		ohci0: usb@e8218000 {
			compatible = "generic-ohci";
			reg = <0xe8218000 0x100>;
			interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 61>;
			phys = <&usb2_phy0>;
			phy-names = "usb";
			power-domains = <&cpg>;
			status = "disabled";
		};

		ehci0: usb@e8218100 {
			compatible = "generic-ehci";
			reg = <0xe8218100 0x100>;
			interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 61>;
			phys = <&usb2_phy0>;
			phy-names = "usb";
			power-domains = <&cpg>;
			status = "disabled";
		};

		usb2_phy0: usb-phy@e8218200 {
			compatible = "renesas,usb2-phy-r7s9210", "renesas,rcar-gen3-usb2-phy";
			reg = <0xe8218200 0x700>;
			interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 61>, <&usb_x1_clk>;
			clock-names = "fck", "usb_x1";
			power-domains = <&cpg>;
			#phy-cells = <0>;
			status = "disabled";
		};

		usbhs0: usb@e8219000 {
			compatible = "renesas,usbhs-r7s9210", "renesas,rza2-usbhs";
			reg = <0xe8219000 0x724>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 61>;
			renesas,buswait = <7>;
			phys = <&usb2_phy0>;
			phy-names = "usb";
			power-domains = <&cpg>;
			status = "disabled";
		};

		ohci1: usb@e821a000 {
			compatible = "generic-ohci";
			reg = <0xe821a000 0x100>;
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 60>;
			phys = <&usb2_phy1>;
			phy-names = "usb";
			power-domains = <&cpg>;
			status = "disabled";
		};

		ehci1: usb@e821a100 {
			compatible = "generic-ehci";
			reg = <0xe821a100 0x100>;
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 60>;
			phys = <&usb2_phy1>;
			phy-names = "usb";
			power-domains = <&cpg>;
			status = "disabled";
		};

		usb2_phy1: usb-phy@e821a200 {
			compatible = "renesas,usb2-phy-r7s9210", "renesas,rcar-gen3-usb2-phy";
			reg = <0xe821a200 0x700>;
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 60>, <&usb_x1_clk>;
			clock-names = "fck", "usb_x1";
			power-domains = <&cpg>;
			#phy-cells = <0>;
			status = "disabled";
		};

		usbhs1: usb@e821b000 {
			compatible = "renesas,usbhs-r7s9210", "renesas,rza2-usbhs";
			reg = <0xe821b000 0x724>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 60>;
			renesas,buswait = <7>;
			phys = <&usb2_phy1>;
			phy-names = "usb";
			power-domains = <&cpg>;
			status = "disabled";
		};

		sdhi0: mmc@e8228000 {
			compatible = "renesas,sdhi-r7s9210";
			reg = <0xe8228000 0x8c0>;
			interrupts = <GIC_SPI 322 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 103>, <&cpg CPG_MOD 102>;
			clock-names = "core", "cd";
			power-domains = <&cpg>;
			cap-sd-highspeed;
			cap-sdio-irq;
			status = "disabled";
		};

		sdhi1: mmc@e822a000 {
			compatible = "renesas,sdhi-r7s9210";
			reg = <0xe822a000 0x8c0>;
			interrupts = <GIC_SPI 324 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_MOD 101>, <&cpg CPG_MOD 100>;
			clock-names = "core", "cd";
			power-domains = <&cpg>;
			cap-sd-highspeed;
			cap-sdio-irq;
			status = "disabled";
		};

		gic: interrupt-controller@e8221000 {
			compatible = "arm,gic-400";
			#interrupt-cells = <3>;
			#address-cells = <0>;
			interrupt-controller;
			reg = <0xe8221000 0x1000>,
			      <0xe8222000 0x1000>;
		};

		cpg: clock-controller@fcfe0010 {
			compatible = "renesas,r7s9210-cpg-mssr";
			reg = <0xfcfe0010 0x455>;
			clocks = <&extal_clk>;
			clock-names = "extal";
			#clock-cells = <2>;
			#power-domain-cells = <0>;
		};

		wdt: watchdog@fcfe7000 {
			compatible = "renesas,r7s9210-wdt", "renesas,rza-wdt";
			reg = <0xfcfe7000 0x26>;
			interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cpg CPG_CORE R7S9210_CLK_P0>;
		};

		bsid: chipid@fcfe8004 {
			compatible = "renesas,bsid";
			reg = <0xfcfe8004 4>;
		};

		irqc: interrupt-controller@fcfef800 {
			compatible = "renesas,r7s9210-irqc",
				     "renesas,rza1-irqc";
			#interrupt-cells = <2>;
			#address-cells = <0>;
			interrupt-controller;
			reg = <0xfcfef800 0x6>;
			interrupt-map =
				<0 0 &gic GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>,
				<1 0 &gic GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>,
				<2 0 &gic GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
				<3 0 &gic GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
				<4 0 &gic GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
				<5 0 &gic GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>,
				<6 0 &gic GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>,
				<7 0 &gic GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-map-mask = <7 0>;
		};

		pinctrl: pinctrl@fcffe000 {
			compatible = "renesas,r7s9210-pinctrl";
			reg = <0xfcffe000 0x1000>;

			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl 0 0 176>;
		};
	};
};
