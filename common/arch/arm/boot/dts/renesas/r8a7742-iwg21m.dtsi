// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for the iWave RZ/G1H Qseven SOM
 *
 * Copyright (C) 2020 Renesas Electronics Corp.
 */

#include "r8a7742.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	compatible = "iwave,g21m", "renesas,r8a7742";

	memory@40000000 {
		device_type = "memory";
		reg = <0 0x40000000 0 0x40000000>;
	};

	memory@200000000 {
		device_type = "memory";
		reg = <2 0x00000000 0 0x40000000>;
	};

	reg_3p3v: 3p3v {
		compatible = "regulator-fixed";
		regulator-name = "3P3V";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
	};
};

&extal_clk {
	clock-frequency = <20000000>;
};

&gpio0 {
	/* GP0_18 set low to select QSPI. Doing so will disable VIN2 */
	qspi-en-hog {
		gpio-hog;
		gpios = <18 GPIO_ACTIVE_HIGH>;
		output-low;
		line-name = "QSPI_EN";
	};
};

&i2c0 {
	pinctrl-0 = <&i2c0_pins>;
	pinctrl-names = "default";

	status = "okay";
	clock-frequency = <400000>;

	rtc@68 {
		compatible = "ti,bq32000";
		reg = <0x68>;
		interrupt-parent = <&gpio1>;
		interrupts = <1 IRQ_TYPE_EDGE_FALLING>;
	};
};

&mmcif1 {
	pinctrl-0 = <&mmc1_pins>;
	pinctrl-names = "default";

	vmmc-supply = <&reg_3p3v>;
	bus-width = <4>;
	non-removable;
	status = "okay";
};

&pfc {
	i2c0_pins: i2c0 {
		groups = "i2c0";
		function = "i2c0";
	};

	mmc1_pins: mmc1 {
		groups = "mmc1_data4", "mmc1_ctrl";
		function = "mmc1";
	};

	qspi_pins: qspi {
		groups = "qspi_ctrl", "qspi_data2";
		function = "qspi";
	};
};

&qspi {
	pinctrl-0 = <&qspi_pins>;
	pinctrl-names = "default";

	status = "okay";

	flash: flash@0 {
		compatible = "sst,sst25vf016b", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <50000000>;
		m25p,fast-read;
		spi-cpol;
		spi-cpha;

		partitions {
			compatible = "fixed-partitions";
			#address-cells = <1>;
			#size-cells = <1>;

			partition@0 {
				label = "bootloader";
				reg = <0x00000000 0x000c0000>;
				read-only;
			};
			partition@c0000 {
				label = "env";
				reg = <0x000c0000 0x00002000>;
			};
			partition@c2000 {
				label = "user";
				reg = <0x000c2000 0x0013e000>;
			};
		};
	};
};
