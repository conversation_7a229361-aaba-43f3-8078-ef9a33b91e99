// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Copyright (c) 2021 thingy.jp.
 * Author: <PERSON> <<EMAIL>>
 * Author: <PERSON><PERSON> <<EMAIL>>
 */

/ {
	reg_vcc_dram: regulator-vcc-dram {
		compatible = "regulator-fixed";
		regulator-name = "vcc_dram";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-boot-on;
	};
};

&pm_uart {
	status = "okay";
};
