// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright (c) 2020 thingy.jp.
 * Author: <PERSON> <<EMAIL>>
 */

#include "mstar-v7.dtsi"

#include <dt-bindings/gpio/msc313-gpio.h>

/ {
	cpu0_opp_table: opp_table0 {
		compatible = "operating-points-v2";
		opp-shared;

		opp-240000000 {
			opp-hz = /bits/ 64 <240000000>;
			opp-microvolt = <1000000>;
			clock-latency-ns = <300000>;
		};

		opp-400000000 {
			opp-hz = /bits/ 64 <400000000>;
			opp-microvolt = <1000000>;
			clock-latency-ns = <300000>;
		};
		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <1000000>;
			clock-latency-ns = <300000>;
		};

		opp-800000000 {
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt = <1000000>;
			clock-latency-ns = <300000>;
		};
	};
};

&cpu0 {
	operating-points-v2 = <&cpu0_opp_table>;
};

&imi {
	reg = <0xa0000000 0x16000>;
};

&gpio {
	compatible = "mstar,msc313-gpio";
	status = "okay";
};
