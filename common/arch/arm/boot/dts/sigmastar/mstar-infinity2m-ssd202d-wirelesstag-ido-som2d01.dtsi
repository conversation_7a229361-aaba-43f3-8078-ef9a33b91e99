// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Copyright (c) 2021 thingy.jp.
 * Author: <PERSON> <<EMAIL>>
 * Author: <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "mstar-infinity2m-ssd202d.dtsi"
#include "mstar-infinity2m-ssd201-som2d01.dtsi"

/ {
	model = "Wireless Tag IDO-SOM2D01 (SSD202D)";
	compatible = "wirelesstag,ido-som2d01", "mstar,infinity2m";

	aliases {
		serial0 = &pm_uart;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};
};

&reg_vcc_dram {
	regulator-min-microvolt = <1500000>;
	regulator-max-microvolt = <1500000>;
};
