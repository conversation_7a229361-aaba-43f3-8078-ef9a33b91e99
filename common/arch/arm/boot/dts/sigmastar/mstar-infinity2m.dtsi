// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright (c) 2020 thingy.jp.
 * Author: <PERSON> <<EMAIL>>
 */

#include "mstar-infinity.dtsi"

&cpu0_opp_table {
		opp-********** {
			opp-hz = /bits/ 64 <**********>;
			opp-microvolt = <1000000>;
			clock-latency-ns = <300000>;
		};

		opp-********** {
			opp-hz = /bits/ 64 <**********>;
			opp-microvolt = <1000000>;
			clock-latency-ns = <300000>;
		};
};

&cpus {
	cpu1: cpu@1 {
		device_type = "cpu";
		compatible = "arm,cortex-a7";
		operating-points-v2 = <&cpu0_opp_table>;
		reg = <0x1>;
		clocks = <&cpupll>;
		clock-names = "cpuclk";
	};
};

&riu {
	smpctrl: smpctrl@204000 {
		reg = <0x204000 0x200>;
		status = "disabled";
	};
};
