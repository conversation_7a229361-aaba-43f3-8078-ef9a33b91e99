// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright (c) 2020 thingy.jp.
 * Author: <PERSON> <<EMAIL>>
 */

#include "mstar-infinity.dtsi"

&cpu0_opp_table {
	opp-********** {
		opp-hz = /bits/ 64 <**********>;
		opp-microvolt = <1000000>;
		clock-latency-ns = <300000>;
	};

	// overclock frequencies below, shown to work fine up to 1.3 GHz
	opp-108000000 {
		opp-hz = /bits/ 64 <**********>;
		opp-microvolt = <1000000>;
		clock-latency-ns = <300000>;
		turbo-mode;
	};

	opp-********** {
		opp-hz = /bits/ 64 <**********>;
		opp-microvolt = <1000000>;
		clock-latency-ns = <300000>;
		turbo-mode;
	};

	opp-********** {
		opp-hz = /bits/ 64 <**********>;
		opp-microvolt = <1000000>;
		clock-latency-ns = <300000>;
		turbo-mode;
	};

	opp-********** {
		opp-hz = /bits/ 64 <**********>;
		opp-microvolt = <1000000>;
		clock-latency-ns = <300000>;
		turbo-mode;
	};

	opp-********** {
		opp-hz = /bits/ 64 <**********>;
		opp-microvolt = <1000000>;
		clock-latency-ns = <300000>;
		turbo-mode;
	};

	opp-1458000000 {
		opp-hz = /bits/ 64 <1458000000>;
		opp-microvolt = <1000000>;
		clock-latency-ns = <300000>;
		turbo-mode;
	};

	opp-1512000000 {
		opp-hz = /bits/ 64 <1512000000>;
		opp-microvolt = <1000000>;
		clock-latency-ns = <300000>;
		turbo-mode;
	};
};

&imi {
	reg = <0xa0000000 0x20000>;
};
