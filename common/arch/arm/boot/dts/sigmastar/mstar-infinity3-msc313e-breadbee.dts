// SPDX-License-Identifier: GPL-2.0
/*
 * Copyright (c) 2019 thingy.jp.
 * Author: <PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "mstar-infinity3-msc313e.dtsi"
#include "mstar-infinity-breadbee-common.dtsi"

/ {
	model = "BreadBee";
	compatible = "thingyjp,breadbee", "mstar,infinity3";

	aliases {
		serial0 = &pm_uart;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};
};

&pm_uart {
	status = "okay";
};
