// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Copyright (c) 2021 thingy.jp.
 * Author: <PERSON> <<EMAIL>>
 * Author: <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "mstar-infinity2m-ssd202d-wirelesstag-ido-som2d01.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Wireless Tag IDO-SBC2D06-1VB-22W";
	compatible = "wirelesstag,ido-sbc2d06-v1b-22w", "mstar,infinity2m";

	leds {
		compatible = "gpio-leds";
		sys_led {
			gpios = <&gpio SSD20XD_GPIO_GPIO85 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "heartbeat";
		};
	};
};
