// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright (c) 2020 thingy.jp.
 * Author: <PERSON> <<EMAIL>>
 */

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/clock/mstar-msc313-mpll.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&gic>;

	cpus: cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x0>;
			clocks = <&cpupll>;
			clock-names = "cpuclk";
		};
	};

	arch_timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(2)
				| IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(2)
				| IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(2)
				| IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(2)
				| IRQ_TYPE_LEVEL_LOW)>;
		/*
		 * we shouldn't need this but the vendor
		 * u-boot is broken
		 */
		clock-frequency = <6000000>;
		arm,cpu-registers-not-fw-configured;
	};

	pmu: pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>;
	};

	clocks: clocks {
		xtal: xtal {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
		};

		rtc_xtal: rtc_xtal {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
			status = "disabled";
		};

		xtal_div2: xtal_div2 {
			#clock-cells = <0>;
			compatible = "fixed-factor-clock";
			clocks = <&xtal>;
			clock-div = <2>;
			clock-mult = <1>;
		};
	};

	soc: soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x16001000 0x16001000 0x00007000>,
			 <0x1f000000 0x1f000000 0x00400000>,
			 <0xa0000000 0xa0000000 0x20000>;

		gic: interrupt-controller@16001000 {
			compatible = "arm,cortex-a7-gic";
			reg = <0x16001000 0x1000>,
			      <0x16002000 0x2000>,
			      <0x16004000 0x2000>,
			      <0x16006000 0x2000>;
			#interrupt-cells = <3>;
			interrupt-controller;
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(2)
					| IRQ_TYPE_LEVEL_LOW)>;
		};

		riu: bus@1f000000 {
			compatible = "simple-bus";
			reg = <0x1f000000 0x00400000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x1f000000 0x00400000>;

			pmsleep: syscon@1c00 {
				compatible = "mstar,msc313-pmsleep", "syscon";
				reg = <0x1c00 0x100>;
			};

			reboot {
				compatible = "syscon-reboot";
				regmap = <&pmsleep>;
				offset = <0xb8>;
				mask = <0x79>;
			};

			rtc@2400 {
				compatible = "mstar,msc313-rtc";
				reg = <0x2400 0x40>;
				clocks = <&xtal_div2>;
				interrupts-extended = <&intc_irq GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
			};

			watchdog@6000 {
				compatible = "mstar,msc313e-wdt";
				reg = <0x6000 0x1f>;
				clocks = <&xtal_div2>;
			};


			intc_fiq: interrupt-controller@201310 {
				compatible = "mstar,mst-intc";
				reg = <0x201310 0x40>;
				#interrupt-cells = <3>;
				interrupt-controller;
				interrupt-parent = <&gic>;
				mstar,irqs-map-range = <96 127>;
			};

			intc_irq: interrupt-controller@201350 {
				compatible = "mstar,mst-intc";
				reg = <0x201350 0x40>;
				#interrupt-cells = <3>;
				interrupt-controller;
				interrupt-parent = <&gic>;
				mstar,irqs-map-range = <32 95>;
				mstar,intc-no-eoi;
			};

			l3bridge: l3bridge@204400 {
				compatible = "mstar,l3bridge";
				reg = <0x204400 0x200>;
			};

			mpll: mpll@206000 {
				compatible = "mstar,msc313-mpll";
				#clock-cells = <1>;
				reg = <0x206000 0x200>;
				clocks = <&xtal>;
			};

			cpupll: cpupll@206400 {
				compatible = "mstar,msc313-cpupll";
				reg = <0x206400 0x200>;
				#clock-cells = <0>;
				clocks = <&mpll MSTAR_MSC313_MPLL_DIV2>;
			};

			gpio: gpio@207800 {
				#gpio-cells = <2>;
				reg = <0x207800 0x200>;
				gpio-controller;
				#interrupt-cells = <2>;
				interrupt-controller;
				interrupt-parent = <&intc_fiq>;
				status = "disabled";
			};

			pm_uart: serial@221000 {
				compatible = "ns16550a";
				reg = <0x221000 0x100>;
				reg-shift = <3>;
				interrupts-extended = <&intc_irq GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
				clock-frequency = <172000000>;
				status = "disabled";
			};
		};

		imi: sram@a0000000 {
			compatible = "mmio-sram";
			reg = <0xa0000000 0x10000>;
		};
	};
};
