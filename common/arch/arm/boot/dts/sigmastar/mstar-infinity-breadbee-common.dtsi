// SPDX-License-Identifier: GPL-2.0
/*
 * Copyright (c) 2020 thingy.jp.
 * Author: <PERSON> <<EMAIL>>
 */

#include <dt-bindings/gpio/gpio.h>

/ {
	vcc_core: fixedregulator@0 {
		compatible = "regulator-fixed";
		regulator-name = "vcc_core";
		regulator-min-microvolt = <1000000>;
		regulator-max-microvolt = <1000000>;
		regulator-boot-on;
	};

	vcc_dram: fixedregulator@1 {
		compatible = "regulator-fixed";
		regulator-name = "vcc_dram";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-boot-on;
	};

	vcc_io: fixedregulator@2 {
		compatible = "regulator-fixed";
		regulator-name = "vcc_io";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
	};

	leds {
		compatible = "gpio-leds";
		red {
			gpios = <&gpio MSC313_GPIO_SR_IO16 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "activity";
		};
		yellow {
			gpios = <&gpio MSC313_GPIO_SR_IO17 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
		};
	};
};

&cpu0 {
	cpu-supply = <&vcc_core>;
};
