// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's S5PV210 SoC device tree source - pin control-related
 * definitions
 *
 * Copyright (c) 2013-2014 Samsung Electronics, Co. Ltd.
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 * <PERSON><PERSON> <<EMAIL>>
 *
 * Samsung's S5PV210 SoC pin banks, pin-mux and pin-config options are
 * listed as device tree nodes in this file.
 */

#include "s5pv210-pinctrl.h"

#define PIN_SLP(_pin, _mode, _pull)					\
	pin- ## _pin {							\
		samsung,pins = #_pin;					\
		samsung,pin-con-pdn = <S5PV210_PIN_PDN_ ##_mode>;	\
		samsung,pin-pud-pdn = <S5PV210_PIN_PULL_ ##_pull>;	\
	}

&pinctrl0 {
	gpa0: gpa0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpa1: gpa1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb: gpb-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc0: gpc0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc1: gpc1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpd0: gpd0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpd1: gpd1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpe0: gpe0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpe1: gpe1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf0: gpf0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf1: gpf1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf2: gpf2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf3: gpf3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpg0: gpg0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpg1: gpg1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpg2: gpg2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpg3: gpg3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpj0: gpj0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpj1: gpj1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpj2: gpj2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpj3: gpj3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpj4: gpj4-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpi: gpi-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	mp01: mp01-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	mp02: mp02-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	mp03: mp03-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	mp04: mp04-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	mp05: mp05-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	mp06: mp06-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	mp07: mp07-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gph0: gph0-gpio-bank {
		gpio-controller;
		interrupt-controller;
		interrupt-parent = <&vic0>;
		interrupts = <0>, <1>, <2>, <3>,
				<4>, <5>, <6>, <7>;
		#gpio-cells = <2>;
		#interrupt-cells = <2>;
	};

	gph1: gph1-gpio-bank {
		gpio-controller;
		interrupt-controller;
		interrupt-parent = <&vic0>;
		interrupts = <8>, <9>, <10>, <11>,
				<12>, <13>, <14>, <15>;
		#gpio-cells = <2>;
		#interrupt-cells = <2>;
	};

	gph2: gph2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gph3: gph3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	uart0_data: uart0-data-pins {
		samsung,pins = "gpa0-0", "gpa0-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	uart0_fctl: uart0-fctl-pins {
		samsung,pins = "gpa0-2", "gpa0-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	uart1_data: uart1-data-pins {
		samsung,pins = "gpa0-4", "gpa0-5";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	uart1_fctl: uart1-fctl-pins {
		samsung,pins = "gpa0-6", "gpa0-7";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	uart2_data: uart2-data-pins {
		samsung,pins = "gpa1-0", "gpa1-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	uart2_fctl: uart2-fctl-pins {
		samsung,pins = "gpa1-2", "gpa1-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	uart3_data: uart3-data-pins {
		samsung,pins = "gpa1-2", "gpa1-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	uart_audio: uart-audio-pins {
		samsung,pins = "gpa1-2", "gpa1-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_4>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	spi0_bus: spi0-bus-pins {
		samsung,pins = "gpb-0", "gpb-2", "gpb-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	spi1_bus: spi1-bus-pins {
		samsung,pins = "gpb-4", "gpb-6", "gpb-7";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	i2s0_bus: i2s0-bus-pins {
		samsung,pins = "gpi-0", "gpi-1", "gpi-2", "gpi-3",
				"gpi-4", "gpi-5", "gpi-6";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	i2s1_bus: i2s1-bus-pins {
		samsung,pins = "gpc0-0", "gpc0-1", "gpc0-2", "gpc0-3",
				"gpc0-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	i2s2_bus: i2s2-bus-pins {
		samsung,pins = "gpc1-0", "gpc1-1", "gpc1-2", "gpc1-3",
				"gpc1-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_4>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	pcm1_bus: pcm1-bus-pins {
		samsung,pins = "gpc0-0", "gpc0-1", "gpc0-2", "gpc0-3",
				"gpc0-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	ac97_bus: ac97-bus-pins {
		samsung,pins = "gpc0-0", "gpc0-1", "gpc0-2", "gpc0-3",
				"gpc0-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_4>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	i2s2_bus: i2s2-bus-pins {
		samsung,pins = "gpc1-0", "gpc1-1", "gpc1-2", "gpc1-3",
				"gpc1-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	pcm2_bus: pcm2-bus-pins {
		samsung,pins = "gpc1-0", "gpc1-1", "gpc1-2", "gpc1-3",
				"gpc1-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	spdif_bus: spdif-bus-pins {
		samsung,pins = "gpc1-0", "gpc1-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_4>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	spi2_bus: spi2-bus-pins {
		samsung,pins = "gpc1-1", "gpc1-2", "gpc1-3", "gpc1-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_5>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	i2c0_bus: i2c0-bus-pins {
		samsung,pins = "gpd1-0", "gpd1-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	i2c1_bus: i2c1-bus-pins {
		samsung,pins = "gpd1-2", "gpd1-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	i2c2_bus: i2c2-bus-pins {
		samsung,pins = "gpd1-4", "gpd1-5";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	pwm0_out: pwm0-out-pins {
		samsung,pins = "gpd0-0";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	pwm1_out: pwm1-out-pins {
		samsung,pins = "gpd0-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	pwm2_out: pwm2-out-pins {
		samsung,pins = "gpd0-2";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	pwm3_out: pwm3-out-pins {
		samsung,pins = "gpd0-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_row0: keypad-row-0-pins {
		samsung,pins = "gph3-0";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_row1: keypad-row-1-pins {
		samsung,pins = "gph3-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_row2: keypad-row-2-pins {
		samsung,pins = "gph3-2";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_row3: keypad-row-3-pins {
		samsung,pins = "gph3-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_row4: keypad-row-4-pins {
		samsung,pins = "gph3-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_row5: keypad-row-5-pins {
		samsung,pins = "gph3-5";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_row6: keypad-row-6-pins {
		samsung,pins = "gph3-6";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_row7: keypad-row-7-pins {
		samsung,pins = "gph3-7";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_col0: keypad-col-0-pins {
		samsung,pins = "gph2-0";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_col1: keypad-col-1-pins {
		samsung,pins = "gph2-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_col2: keypad-col-2-pins {
		samsung,pins = "gph2-2";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_col3: keypad-col-3-pins {
		samsung,pins = "gph2-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_col4: keypad-col-4-pins {
		samsung,pins = "gph2-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_col5: keypad-col-5-pins {
		samsung,pins = "gph2-5";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_col6: keypad-col-6-pins {
		samsung,pins = "gph2-6";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	keypad_col7: keypad-col-7-pins {
		samsung,pins = "gph2-7";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	sd0_clk: sd0-clk-pins {
		samsung,pins = "gpg0-0";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd0_cmd: sd0-cmd-pins {
		samsung,pins = "gpg0-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd0_cd: sd0-cd-pins {
		samsung,pins = "gpg0-2";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd0_bus1: sd0-bus-width1-pins {
		samsung,pins = "gpg0-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd0_bus4: sd0-bus-width4-pins {
		samsung,pins = "gpg0-3", "gpg0-4", "gpg0-5", "gpg0-6";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd0_bus8: sd0-bus-width8-pins {
		samsung,pins = "gpg1-3", "gpg1-4", "gpg1-5", "gpg1-6";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd1_clk: sd1-clk-pins {
		samsung,pins = "gpg1-0";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd1_cmd: sd1-cmd-pins {
		samsung,pins = "gpg1-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd1_cd: sd1-cd-pins {
		samsung,pins = "gpg1-2";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd1_bus1: sd1-bus-width1-pins {
		samsung,pins = "gpg1-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd1_bus4: sd1-bus-width4-pins {
		samsung,pins = "gpg1-3", "gpg1-4", "gpg1-5", "gpg1-6";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd2_clk: sd2-clk-pins {
		samsung,pins = "gpg2-0";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd2_cmd: sd2-cmd-pins {
		samsung,pins = "gpg2-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd2_cd: sd2-cd-pins {
		samsung,pins = "gpg2-2";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd2_bus1: sd2-bus-width1-pins {
		samsung,pins = "gpg2-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd2_bus4: sd2-bus-width4-pins {
		samsung,pins = "gpg2-3", "gpg2-4", "gpg2-5", "gpg2-6";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd2_bus8: sd2-bus-width8-pins {
		samsung,pins = "gpg3-3", "gpg3-4", "gpg3-5", "gpg3-6";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd3_clk: sd3-clk-pins {
		samsung,pins = "gpg3-0";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd3_cmd: sd3-cmd-pins {
		samsung,pins = "gpg3-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd3_cd: sd3-cd-pins {
		samsung,pins = "gpg3-2";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd3_bus1: sd3-bus-width1-pins {
		samsung,pins = "gpg3-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	sd3_bus4: sd3-bus-width4-pins {
		samsung,pins = "gpg3-3", "gpg3-4", "gpg3-5", "gpg3-6";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	eint0: ext-int0-pins {
		samsung,pins = "gph0-0";
		samsung,pin-function = <S5PV210_PIN_FUNC_F>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	eint8: ext-int8-pins {
		samsung,pins = "gph1-0";
		samsung,pin-function = <S5PV210_PIN_FUNC_F>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	eint15: ext-int15-pins {
		samsung,pins = "gph1-7";
		samsung,pin-function = <S5PV210_PIN_FUNC_F>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	eint16: ext-int16-pins {
		samsung,pins = "gph2-0";
		samsung,pin-function = <S5PV210_PIN_FUNC_F>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	eint31: ext-int31-pins {
		samsung,pins = "gph3-7";
		samsung,pin-function = <S5PV210_PIN_FUNC_F>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	cam_port_a_io: cam-port-a-io-pins {
		samsung,pins = "gpe0-0", "gpe0-1", "gpe0-2", "gpe0-3",
				"gpe0-4", "gpe0-5", "gpe0-6", "gpe0-7",
				"gpe1-0", "gpe1-1", "gpe1-2", "gpe1-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	cam_port_a_clk_active: cam-port-a-clk-active-pins {
		samsung,pins = "gpe1-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	cam_port_a_clk_idle: cam-port-a-clk-idle-pins {
		samsung,pins = "gpe1-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_INPUT>;
		samsung,pin-pud = <S5PV210_PIN_PULL_DOWN>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	cam_port_b_io: cam-port-b-io-pins {
		samsung,pins = "gpj0-0", "gpj0-1", "gpj0-2", "gpj0-3",
				"gpj0-4", "gpj0-5", "gpj0-6", "gpj0-7",
				"gpj1-0", "gpj1-1", "gpj1-2", "gpj1-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	cam_port_b_clk_active: cam-port-b-clk-active-pins {
		samsung,pins = "gpj1-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV4>;
	};

	cam_port_b_clk_idle: cam-port-b-clk-idle-pins {
		samsung,pins = "gpj1-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_INPUT>;
		samsung,pin-pud = <S5PV210_PIN_PULL_DOWN>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	lcd_ctrl: lcd-ctrl-pins {
		samsung,pins = "gpd0-0", "gpd0-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_3>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	lcd_sync: lcd-sync-pins {
		samsung,pins = "gpf0-0", "gpf0-1";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	lcd_clk: lcd-clk-pins {
		samsung,pins = "gpf0-0", "gpf0-1", "gpf0-2", "gpf0-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	lcd_data24: lcd-data-width24-pins {
		samsung,pins = "gpf0-4", "gpf0-5", "gpf0-6", "gpf0-7",
			       "gpf1-0", "gpf1-1", "gpf1-2", "gpf1-3",
			       "gpf1-4", "gpf1-5", "gpf1-6", "gpf1-7",
			       "gpf2-0", "gpf2-1", "gpf2-2", "gpf2-3",
			       "gpf2-4", "gpf2-5", "gpf2-6", "gpf2-7",
			       "gpf3-0", "gpf3-1", "gpf3-2", "gpf3-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_2>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};
};
