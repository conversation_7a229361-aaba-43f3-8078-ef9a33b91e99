// SPDX-License-Identifier: GPL-2.0
/*
 * Hardkernel Odroid XU3-Lite board device tree source
 *
 * Copyright (c) 2015 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Copyright (c) 2014 Collabora Ltd.
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

/dts-v1/;
#include "exynos5422-odroidxu3-common.dtsi"
#include "exynos5422-odroidxu3-audio.dtsi"
#include "exynos54xx-odroidxu-leds.dtsi"

/ {
	model = "Hardkernel Odroid XU3 Lite";
	compatible = "hardkernel,odroid-xu3-lite", "samsung,exynos5800", "samsung,exynos5";

	aliases {
		ethernet = &ethernet;
	};
};

&arm_a7_pmu {
	status = "disabled";
};

&arm_a15_pmu {
	status = "disabled";
};

&chipid {
	samsung,asv-bin = <2>;
};

/*
 * Odroid XU3-Lite board uses SoC revision with lower maximum frequencies
 * than Odroid XU3/XU4 boards: 1.8 GHz for A15 cores & 1.3 GHz for A7 cores.
 * Therefore we need to update OPPs tables and thermal maps accordingly.
 */
&cluster_a15_opp_table {
	/delete-node/opp-**********;
	/delete-node/opp-1900000000;
};

&cluster_a7_opp_table {
	/delete-node/opp-1400000000;
};

&cpu0_cooling_map4 {
	cooling-device = <&cpu0 3 7>,
			 <&cpu1 3 7>,
			 <&cpu2 3 7>,
			 <&cpu3 3 7>,
			 <&cpu4 3 12>,
			 <&cpu5 3 12>,
			 <&cpu6 3 12>,
			 <&cpu7 3 12>;
};

&cpu1_cooling_map4 {
	cooling-device = <&cpu0 3 7>,
			 <&cpu1 3 7>,
			 <&cpu2 3 7>,
			 <&cpu3 3 7>,
			 <&cpu4 3 12>,
			 <&cpu5 3 12>,
			 <&cpu6 3 12>,
			 <&cpu7 3 12>;
};

&cpu2_cooling_map4 {
	cooling-device = <&cpu0 3 7>,
			 <&cpu1 3 7>,
			 <&cpu2 3 7>,
			 <&cpu3 3 7>,
			 <&cpu4 3 12>,
			 <&cpu5 3 12>,
			 <&cpu6 3 12>,
			 <&cpu7 3 12>;
};

&cpu3_cooling_map4 {
	cooling-device = <&cpu0 3 7>,
			 <&cpu1 3 7>,
			 <&cpu2 3 7>,
			 <&cpu3 3 7>,
			 <&cpu4 3 12>,
			 <&cpu5 3 12>,
			 <&cpu6 3 12>,
			 <&cpu7 3 12>;
};

&pwm {
	/*
	 * PWM 0 -- fan
	 * PWM 1 -- Green LED
	 * PWM 2 -- Blue LED
	 * PWM 3 -- on MIPI connector for backlight
	 */
	pinctrl-0 = <&pwm0_out &pwm1_out &pwm2_out &pwm3_out>;
	pinctrl-names = "default";
	status = "okay";
};

&usbdrd_dwc3_1 {
	dr_mode = "peripheral";
};

&usbhost2 {
	#address-cells = <1>;
	#size-cells = <0>;

	hub@1 {
		compatible = "usb424,9514";
		reg = <1>;
		#address-cells = <1>;
		#size-cells = <0>;

		ethernet: ethernet@1 {
			compatible = "usb424,ec00";
			reg = <1>;
			local-mac-address = [00 00 00 00 00 00]; /* Filled in by a bootloader */
		};
	};
};
