// SPDX-License-Identifier: GPL-2.0
/*
 * TOPEET's Exynos4412 based itop board device tree source
 *
 * Copyright (c) 2016 SUMOMO Computer Association
 *			https://www.sumomo.mobi
 *			<PERSON> <<EMAIL>>
 *
 * Device tree source file for TOPEET iTop Exynos 4412 core board
 * which is based on Samsung's Exynos4412 SoC.
 */

/dts-v1/;
#include <dt-bindings/leds/common.h>
#include <dt-bindings/pwm/pwm.h>
#include <dt-bindings/sound/samsung-i2s.h>
#include "exynos4412-itop-scp-core.dtsi"

/ {
	model = "TOPEET iTop 4412 Elite board based on Exynos4412";
	compatible = "topeet,itop4412-elite", "samsung,exynos4412", "samsung,exynos4";

	aliases {
		mmc1 = &sdhci_2;
	};

	chosen {
		bootargs = "root=/dev/mmcblk0p2 rw rootfstype=ext4 rootdelay=1 rootwait";
		stdout-path = "serial2:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led2 {
			function = LED_FUNCTION_HEARTBEAT;
			color = <LED_COLOR_ID_RED>;
			gpios = <&gpx1 0 GPIO_ACTIVE_HIGH>;
			default-state = "off";
			linux,default-trigger = "heartbeat";
		};

		led3 {
			label = "red:user";
			color = <LED_COLOR_ID_RED>;
			gpios = <&gpk1 1 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-home {
			label = "GPIO Key Home";
			linux,code = <KEY_HOME>;
			gpios = <&gpx1 1 GPIO_ACTIVE_LOW>;
		};

		key-back {
			label = "GPIO Key Back";
			linux,code = <KEY_BACK>;
			gpios = <&gpx1 2 GPIO_ACTIVE_LOW>;
		};

		key-sleep {
			label = "GPIO Key Sleep";
			linux,code = <KEY_POWER>;
			gpios = <&gpx3 3 GPIO_ACTIVE_LOW>;
		};

		key-vol-up {
			label = "GPIO Key Vol+";
			linux,code = <KEY_UP>;
			gpios = <&gpx2 1 GPIO_ACTIVE_LOW>;
		};

		key-vol-down {
			label = "GPIO Key Vol-";
			linux,code = <KEY_DOWN>;
			gpios = <&gpx2 0 GPIO_ACTIVE_LOW>;
		};
	};

	sound {
		compatible = "simple-audio-card";
		simple-audio-card,name = "wm-sound";

		simple-audio-card,format = "i2s";
		simple-audio-card,bitclock-master = <&link0_codec>;
		simple-audio-card,frame-master = <&link0_codec>;

		simple-audio-card,widgets =
			"Microphone", "Mic Jack",
			"Line", "Line In",
			"Line", "Line Out",
			"Speaker", "Speaker",
			"Headphone", "Headphone Jack";
		simple-audio-card,routing =
			"Headphone Jack", "HP_L",
			"Headphone Jack", "HP_R",
			"Speaker", "SPK_LP",
			"Speaker", "SPK_LN",
			"Speaker", "SPK_RP",
			"Speaker", "SPK_RN",
			"LINPUT1", "Mic Jack",
			"LINPUT3", "Mic Jack",
			"RINPUT1", "Mic Jack",
			"RINPUT2", "Mic Jack";

		simple-audio-card,cpu {
			sound-dai = <&i2s0 0>;
		};

		link0_codec: simple-audio-card,codec {
			sound-dai = <&codec>;
			clocks = <&i2s0 CLK_I2S_CDCLK>;
			system-clock-frequency = <********>;
		};
	};

	beep {
		compatible = "pwm-beeper";
		pwms = <&pwm 0 4000000 PWM_POLARITY_INVERTED>;
	};
};

&adc {
	vdd-supply = <&ldo3_reg>;
	status = "okay";
};

&camera {
	pinctrl-0 = <&cam_port_a_clk_active>;
	pinctrl-names = "default";
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_CAM0>;
	assigned-clock-parents = <&clock CLK_XUSBXTI>;
};

&clock_audss {
	assigned-clocks = <&clock_audss EXYNOS_MOUT_AUDSS>,
			<&clock_audss EXYNOS_MOUT_I2S>,
			<&clock_audss EXYNOS_DOUT_SRP>,
			<&clock_audss EXYNOS_DOUT_AUD_BUS>;
	assigned-clock-parents = <&clock CLK_FOUT_EPLL>,
			<&clock_audss EXYNOS_MOUT_AUDSS>;
	assigned-clock-rates = <0>, <0>, <********0>, <********>;
};

&ehci {
	status = "okay";
	/* In order to reset USB ethernet */
	samsung,vbus-gpio = <&gpc0 1 GPIO_ACTIVE_HIGH>;

	phys = <&exynos_usbphy 1>, <&exynos_usbphy 3>;
	phy-names = "host", "hsic1";
};

&exynos_usbphy {
	status = "okay";
};

&fimc_0 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC0>,
			<&clock CLK_SCLK_FIMC0>;
	assigned-clock-parents = <&clock CLK_MOUT_MPLL_USER_T>;
	assigned-clock-rates = <0>, <176000000>;
};

&hsotg {
	dr_mode = "peripheral";
	status = "okay";
};

&i2c_4 {
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-slave-addr = <0x10>;
	samsung,i2c-max-bus-freq = <100000>;
	pinctrl-0 = <&i2c4_bus>;
	pinctrl-names = "default";
	status = "okay";

	codec: audio-codec@1a {
		compatible = "wlf,wm8960";
		reg = <0x1a>;
		clocks = <&pmu_system_controller 0>;
		clock-names = "mclk";
		wlf,shared-lrclk;
		#sound-dai-cells = <0>;
	};
};

&i2s0 {
	pinctrl-0 = <&i2s0_bus>;
	pinctrl-names = "default";
	status = "okay";
};

&pinctrl_1 {
	ether-reset-pins {
		samsung,pins = "gpc0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};
};

&pwm {
	status = "okay";
	pinctrl-0 = <&pwm0_out>;
	pinctrl-names = "default";
	samsung,pwm-outputs = <0>;
};

&sdhci_2 {
	bus-width = <4>;
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_bus4>;
	pinctrl-names = "default";
	cd-gpios = <&gpx0 7 GPIO_ACTIVE_LOW>;
	cap-sd-highspeed;
	vmmc-supply = <&ldo23_reg>;
	vqmmc-supply = <&ldo17_reg>;
	status = "okay";
};

&serial_1 {
	status = "okay";
};

&serial_2 {
	status = "okay";
};
