// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos4412 SoC device tree source
 *
 * Copyright (c) 2012 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Samsung's Exynos4x12 SoC series device nodes are listed in this file.
 * Particular SoCs from Exynos4x12 series can include this file and provide
 * values for SoCs specific bindings.
 *
 * Note: This file does not include device nodes for all the controllers in
 * Exynos4x12 SoCs. As device tree coverage for Exynos4x12 increases, additional
 * nodes can be added to this file.
 */

#include "exynos4.dtsi"

#include "exynos4-cpu-thermal.dtsi"

/ {
	aliases {
		pinctrl0 = &pinctrl_0;
		pinctrl1 = &pinctrl_1;
		pinctrl2 = &pinctrl_2;
		pinctrl3 = &pinctrl_3;
		fimc-lite0 = &fimc_lite_0;
		fimc-lite1 = &fimc_lite_1;
	};

	bus_acp: bus-acp {
		compatible = "samsung,exynos-bus";
		clocks = <&clock CLK_DIV_ACP>;
		clock-names = "bus";
		operating-points-v2 = <&bus_acp_opp_table>;
		status = "disabled";

		bus_acp_opp_table: opp-table {
			compatible = "operating-points-v2";

			opp-100000000 {
				opp-hz = /bits/ 64 <100000000>;
			};
			opp-134000000 {
				opp-hz = /bits/ 64 <134000000>;
			};
			opp-160000000 {
				opp-hz = /bits/ 64 <160000000>;
			};
			opp-267000000 {
				opp-hz = /bits/ 64 <267000000>;
			};
		};
	};

	bus_c2c: bus-c2c {
		compatible = "samsung,exynos-bus";
		clocks = <&clock CLK_DIV_C2C>;
		clock-names = "bus";
		operating-points-v2 = <&bus_dmc_opp_table>;
		status = "disabled";
	};

	bus_dmc: bus-dmc {
		compatible = "samsung,exynos-bus";
		clocks = <&clock CLK_DIV_DMC>;
		clock-names = "bus";
		operating-points-v2 = <&bus_dmc_opp_table>;
		samsung,data-clock-ratio = <4>;
		#interconnect-cells = <0>;
		status = "disabled";
	};

	bus_display: bus-display {
		compatible = "samsung,exynos-bus";
		clocks = <&clock CLK_ACLK160>;
		clock-names = "bus";
		operating-points-v2 = <&bus_display_opp_table>;
		interconnects = <&bus_leftbus &bus_dmc>;
		#interconnect-cells = <0>;
		status = "disabled";

		bus_display_opp_table: opp-table {
			compatible = "operating-points-v2";

			opp-160000000 {
				opp-hz = /bits/ 64 <160000000>;
			};
			opp-200000000 {
				opp-hz = /bits/ 64 <200000000>;
			};
		};
	};

	bus_fsys: bus-fsys {
		compatible = "samsung,exynos-bus";
		clocks = <&clock CLK_ACLK133>;
		clock-names = "bus";
		operating-points-v2 = <&bus_fsys_opp_table>;
		status = "disabled";

		bus_fsys_opp_table: opp-table {
			compatible = "operating-points-v2";

			opp-100000000 {
				opp-hz = /bits/ 64 <100000000>;
			};
			opp-134000000 {
				opp-hz = /bits/ 64 <134000000>;
			};
		};
	};

	bus_leftbus: bus-leftbus {
		compatible = "samsung,exynos-bus";
		clocks = <&clock CLK_DIV_GDL>;
		clock-names = "bus";
		operating-points-v2 = <&bus_leftbus_opp_table>;
		interconnects = <&bus_dmc>;
		#interconnect-cells = <0>;
		status = "disabled";
	};

	bus_mfc: bus-mfc {
		compatible = "samsung,exynos-bus";
		clocks = <&clock CLK_SCLK_MFC>;
		clock-names = "bus";
		operating-points-v2 = <&bus_leftbus_opp_table>;
		status = "disabled";
	};

	bus_peri: bus-peri {
		compatible = "samsung,exynos-bus";
		clocks = <&clock CLK_ACLK100>;
		clock-names = "bus";
		operating-points-v2 = <&bus_peri_opp_table>;
		status = "disabled";

		bus_peri_opp_table: opp-table {
			compatible = "operating-points-v2";

			opp-50000000 {
				opp-hz = /bits/ 64 <50000000>;
			};
			opp-100000000 {
				opp-hz = /bits/ 64 <100000000>;
			};
		};
	};

	bus_rightbus: bus-rightbus {
		compatible = "samsung,exynos-bus";
		clocks = <&clock CLK_DIV_GDR>;
		clock-names = "bus";
		operating-points-v2 = <&bus_leftbus_opp_table>;
		status = "disabled";
	};

	bus_dmc_opp_table: opp-table-1 {
		compatible = "operating-points-v2";

		opp-100000000 {
			opp-hz = /bits/ 64 <100000000>;
			opp-microvolt = <900000>;
		};
		opp-134000000 {
			opp-hz = /bits/ 64 <134000000>;
			opp-microvolt = <900000>;
		};
		opp-160000000 {
			opp-hz = /bits/ 64 <160000000>;
			opp-microvolt = <900000>;
		};
		opp-267000000 {
			opp-hz = /bits/ 64 <267000000>;
			opp-microvolt = <950000>;
		};
		opp-400000000 {
			opp-hz = /bits/ 64 <400000000>;
			opp-microvolt = <1050000>;
			opp-suspend;
		};
	};

	bus_leftbus_opp_table: opp-table-2 {
		compatible = "operating-points-v2";

		opp-100000000 {
			opp-hz = /bits/ 64 <100000000>;
			opp-microvolt = <900000>;
		};
		opp-134000000 {
			opp-hz = /bits/ 64 <134000000>;
			opp-microvolt = <925000>;
		};
		opp-160000000 {
			opp-hz = /bits/ 64 <160000000>;
			opp-microvolt = <950000>;
		};
		opp-200000000 {
			opp-hz = /bits/ 64 <200000000>;
			opp-microvolt = <1000000>;
			opp-suspend;
		};
	};

	soc: soc {

		pinctrl_0: pinctrl@11400000 {
			compatible = "samsung,exynos4x12-pinctrl";
			reg = <0x11400000 0x1000>;
			interrupts = <GIC_SPI 47 IRQ_TYPE_LEVEL_HIGH>;
		};

		pinctrl_1: pinctrl@11000000 {
			compatible = "samsung,exynos4x12-pinctrl";
			reg = <0x11000000 0x1000>;
			interrupts = <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>;

			wakup_eint: wakeup-interrupt-controller {
				compatible = "samsung,exynos4210-wakeup-eint";
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		pinctrl_2: pinctrl@3860000 {
			compatible = "samsung,exynos4x12-pinctrl";
			reg = <0x03860000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <10 0>;
		};

		pinctrl_3: pinctrl@106e0000 {
			compatible = "samsung,exynos4x12-pinctrl";
			reg = <0x106e0000 0x1000>;
			interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
		};

		sram@2020000 {
			compatible = "mmio-sram";
			reg = <0x02020000 0x40000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x02020000 0x40000>;

			smp-sram@0 {
				compatible = "samsung,exynos4210-sysram";
				reg = <0x0 0x1000>;
			};

			smp-sram@2f000 {
				compatible = "samsung,exynos4210-sysram-ns";
				reg = <0x2f000 0x1000>;
			};
		};

		pd_isp: power-domain@10023ca0 {
			compatible = "samsung,exynos4210-pd";
			reg = <0x10023ca0 0x20>;
			#power-domain-cells = <0>;
			label = "ISP";
		};

		l2c: cache-controller@10502000 {
			compatible = "arm,pl310-cache";
			reg = <0x10502000 0x1000>;
			cache-unified;
			cache-level = <2>;
			prefetch-data = <1>;
			prefetch-instr = <1>;
			arm,tag-latency = <2 2 1>;
			arm,data-latency = <3 2 1>;
			arm,double-linefill = <1>;
			arm,double-linefill-incr = <0>;
			arm,double-linefill-wrap = <1>;
			arm,prefetch-drop = <1>;
			arm,prefetch-offset = <7>;
		};

		clock: clock-controller@10030000 {
			reg = <0x10030000 0x18000>;
			#clock-cells = <1>;
		};

		isp_clock: clock-controller@10048000 {
			compatible = "samsung,exynos4412-isp-clock";
			reg = <0x10048000 0x1000>;
			#clock-cells = <1>;
			power-domains = <&pd_isp>;
			clocks = <&clock CLK_ACLK200>,
				 <&clock CLK_ACLK400_MCUISP>;
			clock-names = "aclk200", "aclk400_mcuisp";
		};

		timer@10050000 {
			compatible = "samsung,exynos4412-mct";
			reg = <0x10050000 0x800>;
			clocks = <&clock CLK_FIN_PLL>, <&clock CLK_MCT>;
			clock-names = "fin_pll", "mct";
			interrupts-extended = <&gic GIC_SPI 57 IRQ_TYPE_LEVEL_HIGH>,
					      <&combiner 12 5>,
					      <&combiner 12 6>,
					      <&combiner 12 7>,
					      <&gic GIC_PPI 12 IRQ_TYPE_LEVEL_HIGH>;
		};

		watchdog: watchdog@10060000 {
			compatible = "samsung,exynos5250-wdt";
			reg = <0x10060000 0x100>;
			interrupts = <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_WDT>;
			clock-names = "watchdog";
			samsung,syscon-phandle = <&pmu_system_controller>;
		};

		adc: adc@126c0000 {
			compatible = "samsung,exynos4212-adc";
			reg = <0x126c0000 0x100>;
			interrupt-parent = <&combiner>;
			interrupts = <10 3>;
			clocks = <&clock CLK_TSADC>;
			clock-names = "adc";
			#io-channel-cells = <1>;
			samsung,syscon-phandle = <&pmu_system_controller>;
			status = "disabled";
		};

		g2d: g2d@10800000 {
			compatible = "samsung,exynos4212-g2d";
			reg = <0x10800000 0x1000>;
			interrupts = <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_SCLK_FIMG2D>, <&clock CLK_G2D>;
			clock-names = "sclk_fimg2d", "fimg2d";
			iommus = <&sysmmu_g2d>;
		};

		mshc_0: mmc@12550000 {
			compatible = "samsung,exynos4412-dw-mshc";
			reg = <0x12550000 0x1000>;
			interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			fifo-depth = <0x80>;
			clocks = <&clock CLK_SDMMC4>, <&clock CLK_SCLK_MMC4>;
			clock-names = "biu", "ciu";
			status = "disabled";
		};

		sysmmu_g2d: sysmmu@10a40000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x10a40000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <4 7>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_G2D>, <&clock CLK_G2D>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_isp: sysmmu@12260000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x12260000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <16 2>;
			power-domains = <&pd_isp>;
			clock-names = "sysmmu";
			clocks = <&isp_clock CLK_ISP_SMMU_ISP>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_drc: sysmmu@12270000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x12270000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <16 3>;
			power-domains = <&pd_isp>;
			clock-names = "sysmmu";
			clocks = <&isp_clock CLK_ISP_SMMU_DRC>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_fd: sysmmu@122a0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x122a0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <16 4>;
			power-domains = <&pd_isp>;
			clock-names = "sysmmu";
			clocks = <&isp_clock CLK_ISP_SMMU_FD>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_mcuctl: sysmmu@122b0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x122b0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <16 5>;
			power-domains = <&pd_isp>;
			clock-names = "sysmmu";
			clocks = <&isp_clock CLK_ISP_SMMU_ISPCX>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_lite0: sysmmu@123b0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x123b0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <16 0>;
			power-domains = <&pd_isp>;
			clock-names = "sysmmu", "master";
			clocks = <&isp_clock CLK_ISP_SMMU_LITE0>,
				 <&isp_clock CLK_ISP_FIMC_LITE0>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_lite1: sysmmu@123c0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x123c0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <16 1>;
			power-domains = <&pd_isp>;
			clock-names = "sysmmu", "master";
			clocks = <&isp_clock CLK_ISP_SMMU_LITE1>,
				 <&isp_clock CLK_ISP_FIMC_LITE1>;
			#iommu-cells = <0>;
		};
	};
};

&combiner {
	interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 48 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
};

&camera {
	ranges = <0x0 0x11800000 0xba1000>;
	clocks = <&clock CLK_SCLK_CAM0>, <&clock CLK_SCLK_CAM1>,
		 <&clock CLK_PIXELASYNCM0>, <&clock CLK_PIXELASYNCM1>;
	clock-names = "sclk_cam0", "sclk_cam1", "pxl_async0", "pxl_async1";

	/* fimc_[0-3] are configured outside, under phandles */
	fimc_lite_0: fimc-lite@b90000 {
		compatible = "samsung,exynos4212-fimc-lite";
		reg = <0x00b90000 0x1000>;
		interrupts = <GIC_SPI 105 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&pd_isp>;
		clocks = <&isp_clock CLK_ISP_FIMC_LITE0>;
		clock-names = "flite";
		iommus = <&sysmmu_fimc_lite0>;
		status = "disabled";
	};

	fimc_lite_1: fimc-lite@ba0000 {
		compatible = "samsung,exynos4212-fimc-lite";
		reg = <0x00ba0000 0x1000>;
		interrupts = <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&pd_isp>;
		clocks = <&isp_clock CLK_ISP_FIMC_LITE1>;
		clock-names = "flite";
		iommus = <&sysmmu_fimc_lite1>;
		status = "disabled";
	};

	fimc_is: fimc-is@800000 {
		compatible = "samsung,exynos4212-fimc-is";
		reg = <0x00800000 0x260000>;
		interrupts = <GIC_SPI 90 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&pd_isp>;
		clocks = <&isp_clock CLK_ISP_FIMC_LITE0>,
			 <&isp_clock CLK_ISP_FIMC_LITE1>,
			 <&isp_clock CLK_ISP_PPMUISPX>,
			 <&isp_clock CLK_ISP_PPMUISPMX>,
			 <&isp_clock CLK_ISP_FIMC_ISP>,
			 <&isp_clock CLK_ISP_FIMC_DRC>,
			 <&isp_clock CLK_ISP_FIMC_FD>,
			 <&isp_clock CLK_ISP_MCUISP>,
			 <&isp_clock CLK_ISP_GICISP>,
			 <&isp_clock CLK_ISP_MCUCTL_ISP>,
			 <&isp_clock CLK_ISP_PWM_ISP>,
			 <&isp_clock CLK_ISP_DIV_ISP0>,
			 <&isp_clock CLK_ISP_DIV_ISP1>,
			 <&isp_clock CLK_ISP_DIV_MCUISP0>,
			 <&isp_clock CLK_ISP_DIV_MCUISP1>,
			 <&clock CLK_MOUT_MPLL_USER_T>,
			 <&clock CLK_ACLK200>,
			 <&clock CLK_ACLK400_MCUISP>,
			 <&clock CLK_DIV_ACLK200>,
			 <&clock CLK_DIV_ACLK400_MCUISP>,
			 <&clock CLK_UART_ISP_SCLK>;
		clock-names = "lite0", "lite1", "ppmuispx",
			      "ppmuispmx", "isp",
			      "drc", "fd", "mcuisp",
			      "gicisp", "mcuctl_isp", "pwm_isp",
			      "ispdiv0", "ispdiv1", "mcuispdiv0",
			      "mcuispdiv1", "mpll", "aclk200",
			      "aclk400mcuisp", "div_aclk200",
			      "div_aclk400mcuisp", "uart";
		iommus = <&sysmmu_fimc_isp>, <&sysmmu_fimc_drc>,
			 <&sysmmu_fimc_fd>, <&sysmmu_fimc_mcuctl>;
		iommu-names = "isp", "drc", "fd", "mcuctl";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		status = "disabled";

		pmu@10020000 {
			reg = <0x10020000 0x3000>;
		};

		i2c1_isp: i2c-isp@940000 {
			compatible = "samsung,exynos4212-i2c-isp";
			reg = <0x00940000 0x100>;
			clocks = <&isp_clock CLK_ISP_I2C1_ISP>;
			clock-names = "i2c_isp";
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};
};

&exynos_usbphy {
	compatible = "samsung,exynos4x12-usb2-phy";
	samsung,sysreg-phandle = <&sys_reg>;
};

&fimc_0 {
	compatible = "samsung,exynos4212-fimc";
	samsung,pix-limits = <4224 8192 1920 4224>;
	samsung,mainscaler-ext;
	samsung,isp-wb;
	samsung,cam-if;
};

&fimc_1 {
	compatible = "samsung,exynos4212-fimc";
	samsung,pix-limits = <4224 8192 1920 4224>;
	samsung,mainscaler-ext;
	samsung,isp-wb;
	samsung,cam-if;
};

&fimc_2 {
	compatible = "samsung,exynos4212-fimc";
	samsung,pix-limits = <4224 8192 1920 4224>;
	samsung,mainscaler-ext;
	samsung,isp-wb;
	samsung,lcd-wb;
	samsung,cam-if;
};

&fimc_3 {
	compatible = "samsung,exynos4212-fimc";
	samsung,pix-limits = <1920 8192 1366 1920>;
	samsung,rotators = <0>;
	samsung,mainscaler-ext;
	samsung,isp-wb;
	samsung,lcd-wb;
};

&gpu {
	interrupts = <GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>;
	interrupt-names = "gp",
			  "gpmmu",
			  "pp0",
			  "ppmmu0",
			  "pp1",
			  "ppmmu1",
			  "pp2",
			  "ppmmu2",
			  "pp3",
			  "ppmmu3",
			  "pmu";
	operating-points-v2 = <&gpu_opp_table>;

	gpu_opp_table: opp-table {
		compatible = "operating-points-v2";

		opp-160000000 {
			opp-hz = /bits/ 64 <160000000>;
			opp-microvolt = <875000>;
		};
		opp-267000000 {
			opp-hz = /bits/ 64 <267000000>;
			opp-microvolt = <900000>;
		};
		opp-350000000 {
			opp-hz = /bits/ 64 <350000000>;
			opp-microvolt = <950000>;
		};
		opp-440000000 {
			opp-hz = /bits/ 64 <440000000>;
			opp-microvolt = <1025000>;
		};
	};
};

&hdmi {
	compatible = "samsung,exynos4212-hdmi";
};

&jpeg_codec {
	compatible = "samsung,exynos4212-jpeg";
};

&rotator {
	compatible = "samsung,exynos4212-rotator";
};

&mixer {
	compatible = "samsung,exynos4212-mixer";
	clock-names = "mixer", "hdmi", "sclk_hdmi", "vp";
	clocks = <&clock CLK_MIXER>, <&clock CLK_HDMI>,
		 <&clock CLK_SCLK_HDMI>, <&clock CLK_VP>;
	interconnects = <&bus_display &bus_dmc>;
};

&pmu_system_controller {
	clock-names = "clkout0", "clkout1", "clkout2", "clkout3",
			"clkout4", "clkout8", "clkout9";
	clocks = <&clock CLK_OUT_DMC>, <&clock CLK_OUT_TOP>,
		<&clock CLK_OUT_LEFTBUS>, <&clock CLK_OUT_RIGHTBUS>,
		<&clock CLK_OUT_CPU>, <&clock CLK_XXTI>, <&clock CLK_XUSBXTI>;
	#clock-cells = <1>;
};

&tmu {
	compatible = "samsung,exynos4412-tmu";
	interrupt-parent = <&combiner>;
	interrupts = <2 4>;
	reg = <0x100c0000 0x100>;
	clocks = <&clock CLK_TMU_APBIF>;
	clock-names = "tmu_apbif";
	status = "disabled";
};

#include "exynos4x12-pinctrl.dtsi"
