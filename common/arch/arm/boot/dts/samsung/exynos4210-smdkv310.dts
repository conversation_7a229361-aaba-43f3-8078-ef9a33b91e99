// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos4210 based SMDKV310 board device tree source
 *
 * Copyright (c) 2010-2011 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 * Copyright (c) 2010-2011 Linaro Ltd.
 *		www.linaro.org
 *
 * Device tree source file for Samsung's SMDKV310 board which is based on
 * Samsung's Exynos4210 SoC.
 */

/dts-v1/;
#include "exynos4210.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include "exynos-mfc-reserved-memory.dtsi"

/ {
	model = "Samsung smdkv310 evaluation board based on Exynos4210";
	compatible = "samsung,smdkv310", "samsung,exynos4210", "samsung,exynos4";

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x80000000>;
	};

	aliases {
		mmc0 = &sdhci_2;
	};

	chosen {
		bootargs = "root=/dev/ram0 rw ramdisk=8192 initrd=0x41000000,8M init=/linuxrc";
		stdout-path = "serial1:115200n8";
	};

	fixed-rate-clocks {
		xxti {
			compatible = "samsung,clock-xxti";
			clock-frequency = <12000000>;
		};

		xusbxti {
			compatible = "samsung,clock-xusbxti";
			clock-frequency = <24000000>;
		};

		pmic_ap_clk: pmic-ap-clk {
			/* Workaround for missing clock on PMIC */
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <32768>;
		};
	};
};

&cpu_thermal {
	cooling-maps {
		map0 {
			/* Corresponds to 800MHz */
			cooling-device = <&cpu0 2 2>;
		};
		map1 {
			/* Corresponds to 200MHz */
			cooling-device = <&cpu0 4 4>;
		};
	};
};

&i2c_0 {
	#address-cells = <1>;
	#size-cells = <0>;
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <100000>;
	status = "okay";

	eeprom@50 {
		compatible = "samsung,24ad0xd1";
		reg = <0x50>;
	};

	eeprom@52 {
		compatible = "samsung,24ad0xd1";
		reg = <0x52>;
	};
};

&keypad {
	samsung,keypad-num-rows = <2>;
	samsung,keypad-num-columns = <8>;
	linux,input-no-autorepeat;
	wakeup-source;
	pinctrl-names = "default";
	pinctrl-0 = <&keypad_rows &keypad_cols>;
	status = "okay";

	key-1 {
		keypad,row = <0>;
		keypad,column = <3>;
		linux,code = <2>;
	};

	key-2 {
		keypad,row = <0>;
		keypad,column = <4>;
		linux,code = <3>;
	};

	key-3 {
		keypad,row = <0>;
		keypad,column = <5>;
		linux,code = <4>;
	};

	key-4 {
		keypad,row = <0>;
		keypad,column = <6>;
		linux,code = <5>;
	};

	key-5 {
		keypad,row = <0>;
		keypad,column = <7>;
		linux,code = <6>;
	};

	key-a {
		keypad,row = <1>;
		keypad,column = <3>;
		linux,code = <30>;
	};

	key-b {
		keypad,row = <1>;
		keypad,column = <4>;
		linux,code = <48>;
	};

	key-c {
		keypad,row = <1>;
		keypad,column = <5>;
		linux,code = <46>;
	};

	key-d {
		keypad,row = <1>;
		keypad,column = <6>;
		linux,code = <32>;
	};

	key-e {
		keypad,row = <1>;
		keypad,column = <7>;
		linux,code = <18>;
	};
};

&pinctrl_1 {
	keypad_rows: keypad-rows-pins {
		samsung,pins = "gpx2-0", "gpx2-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	keypad_cols: keypad-cols-pins {
		samsung,pins = "gpx1-0", "gpx1-1", "gpx1-2", "gpx1-3",
			       "gpx1-4", "gpx1-5", "gpx1-6", "gpx1-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};
};

&rtc {
	clocks = <&clock CLK_RTC>, <&pmic_ap_clk>;
	clock-names = "rtc", "rtc_src";
};

&sdhci_2 {
	bus-width = <4>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_cd &sd2_bus4>;
	status = "okay";
};

&serial_0 {
	status = "okay";
};

&serial_1 {
	status = "okay";
};

&serial_2 {
	status = "okay";
};

&serial_3 {
	status = "okay";
};

&spi_2 {
	cs-gpios = <&gpc1 2 GPIO_ACTIVE_HIGH>;
	status = "okay";

	flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "winbond,w25x80", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <1000000>;

		controller-data {
			samsung,spi-feedback-delay = <0>;
		};

		partition@0 {
			label = "U-Boot";
			reg = <0x0 0x40000>;
			read-only;
		};

		partition@40000 {
			label = "Kernel";
			reg = <0x40000 0xc0000>;
		};
	};
};
