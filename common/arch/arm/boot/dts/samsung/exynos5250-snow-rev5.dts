// SPDX-License-Identifier: GPL-2.0
/*
 * Google Snow Rev 5+ board device tree source
 *
 * Copyright (c) 2012 Google, Inc
 * Copyright (c) 2015 Samsung Electronics Co., Ltd.
 *             http://www.samsung.com
 */

/dts-v1/;
#include "exynos5250-snow-common.dtsi"

/ {
	model = "Google Snow Rev 5+";
	compatible = "google,snow-rev5", "samsung,exynos5250",
		"samsung,exynos5";
	chassis-type = "laptop";

	sound {
		compatible = "google,snow-audio-max98090";

		samsung,model = "Snow-I2S-MAX98090";
		samsung,audio-codec = <&max98090>;

		cpu {
			sound-dai = <&i2s0 0>;
		};

		codec {
			sound-dai = <&max98090>, <&hdmi>;
		};
	};
};

&i2c_7 {
	max98090: audio-codec@10 {
		compatible = "maxim,max98090";
		reg = <0x10>;
		interrupts = <4 IRQ_TYPE_NONE>;
		interrupt-parent = <&gpx0>;
		pinctrl-names = "default";
		pinctrl-0 = <&max98090_irq>;
		clocks = <&pmu_system_controller 0>;
		clock-names = "mclk";
		#sound-dai-cells = <0>;
	};
};

&pinctrl_0 {
	max98090_irq: max98090-irq-pins {
		samsung,pins = "gpx0-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};
};
