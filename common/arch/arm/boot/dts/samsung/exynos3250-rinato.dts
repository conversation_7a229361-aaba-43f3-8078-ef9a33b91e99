// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos3250 based Rinato board device tree source
 *
 * Copyright (c) 2014 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Device tree source file for Samsung's Rinato board which is based on
 * Samsung Exynos3250 SoC.
 */

/dts-v1/;
#include "exynos3250.dtsi"
#include "exynos4412-ppmu-common.dtsi"
#include <dt-bindings/input/input.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/clock/samsung,s2mps11.h>

/ {
	model = "Samsung Rinato board";
	compatible = "samsung,rinato", "samsung,exynos3250", "samsung,exynos3";
	chassis-type = "watch";

	aliases {
		i2c7 = &i2c_max77836;
		mmc0 = &mshc_0;
		mmc1 = &mshc_1;
	};

	chosen {
		stdout-path = &serial_1;
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x1ff00000>;
	};

	firmware@205f000 {
		compatible = "samsung,secure-firmware";
		reg = <0x0205f000 0x1000>;
	};

	gpio-keys {
		compatible = "gpio-keys";

		power-key {
			gpios = <&gpx2 7 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			label = "power key";
			debounce-interval = <10>;
			wakeup-source;
		};
	};

	wlan_pwrseq: mshc1-pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&gpe0 4 GPIO_ACTIVE_LOW>;
	};

	i2c_max77836: i2c-gpio-0 {
		compatible = "i2c-gpio";
		sda-gpios = <&gpd0 2 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpd0 3 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		#address-cells = <1>;
		#size-cells = <0>;

		max77836: pmic@25 {
			compatible = "maxim,max77836";
			interrupt-parent = <&gpx1>;
			interrupts = <5 IRQ_TYPE_NONE>;
			reg = <0x25>;
			wakeup-source;

			extcon {
				compatible = "maxim,max77836-muic";
			};

			regulators {
				compatible = "maxim,max77836-regulator";
				safeout_reg: SAFEOUT {
					regulator-name = "SAFEOUT";
				};

				charger_reg: CHARGER {
					regulator-name = "CHARGER";
					regulator-min-microamp = <45000>;
					regulator-max-microamp = <475000>;
					regulator-boot-on;
				};

				motor_reg: LDO1 {
					regulator-name = "MOT_2.7V";
					regulator-min-microvolt = <1100000>;
					regulator-max-microvolt = <2700000>;
				};

				LDO2 {
					regulator-name = "UNUSED_LDO2";
					regulator-min-microvolt = <800000>;
					regulator-max-microvolt = <3950000>;
				};
			};

			charger {
				compatible = "maxim,max77836-charger";

				maxim,constant-uvolt = <4350000>;
				maxim,fast-charge-uamp = <225000>;
				maxim,eoc-uamp = <7500>;
				maxim,ovp-uvolt = <6500000>;
			};
		};
	};

	haptics {
		compatible = "regulator-haptic";
		haptic-supply = <&motor_reg>;
		min-microvolt = <1100000>;
		max-microvolt = <2700000>;
	};

	thermal-zones {
		cpu_thermal: cpu-thermal {
			cooling-maps {
				map0 {
					/* Corresponds to 500MHz */
					cooling-device = <&cpu0 5 5>,
							 <&cpu1 5 5>;
				};
				map1 {
					/* Corresponds to 200MHz */
					cooling-device = <&cpu0 8 8>,
							 <&cpu1 8 8>;
				};
			};
		};
	};
};

&adc {
	vdd-supply = <&ldo3_reg>;
	status = "okay";
	assigned-clocks = <&cmu CLK_SCLK_TSADC>;
	assigned-clock-rates = <6000000>;

	thermistor-ap {
		compatible = "murata,ncp15wb473";
		pullup-uv = <1800000>;
		pullup-ohm = <100000>;
		pulldown-ohm = <100000>;
		io-channels = <&adc 0>;
	};

	thermistor-battery {
		compatible = "murata,ncp15wb473";
		pullup-uv = <1800000>;
		pullup-ohm = <100000>;
		pulldown-ohm = <100000>;
		io-channels = <&adc 1>;
	};
};

&bus_dmc {
	devfreq-events = <&ppmu_dmc0_3>, <&ppmu_dmc1_3>;
	vdd-supply = <&buck1_reg>;
	status = "okay";
};

&bus_leftbus {
	devfreq-events = <&ppmu_leftbus_3>, <&ppmu_rightbus_3>;
	vdd-supply = <&buck3_reg>;
	status = "okay";
};

&bus_rightbus {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_lcd0 {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_fsys {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_mcuisp {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_isp {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_peril {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_mfc {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&cmu {
	clocks = <&xusbxti>;
};

&cpu0 {
	cpu0-supply = <&buck2_reg>;
};

&exynos_usbphy {
	status = "okay";
	vbus-supply = <&safeout_reg>;
};

&hsotg {
	vusb_d-supply = <&ldo15_reg>;
	vusb_a-supply = <&ldo12_reg>;
	dr_mode = "peripheral";
	status = "okay";
};

&dsi_0 {
	vddcore-supply = <&ldo6_reg>;
	vddio-supply = <&ldo6_reg>;
	samsung,burst-clock-frequency = <250000000>;
	samsung,esc-clock-frequency = <20000000>;
	samsung,pll-clock-frequency = <24000000>;
	status = "okay";

	panel@0 {
		compatible = "samsung,s6e63j0x03";
		reg = <0>;
		vdd3-supply = <&ldo16_reg>;
		vci-supply = <&ldo20_reg>;
		reset-gpios = <&gpe0 1 GPIO_ACTIVE_LOW>;
		te-gpios = <&gpx0 6 GPIO_ACTIVE_HIGH>;
	};
};

&fimd {
	status = "okay";

	i80-if-timings {
		cs-setup = <0>;
		wr-setup = <0>;
		wr-active = <1>;
		wr-hold = <0>;
	};
};

&gpu {
	mali-supply = <&buck3_reg>;
	status = "okay";
};

&i2c_0 {
	#address-cells = <1>;
	#size-cells = <0>;
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-slave-addr = <0x10>;
	samsung,i2c-max-bus-freq = <100000>;
	status = "okay";

	pmic@66 {
		compatible = "samsung,s2mps14-pmic";
		interrupt-parent = <&gpx0>;
		interrupts = <7 IRQ_TYPE_LEVEL_LOW>;
		reg = <0x66>;
		wakeup-source;

		s2mps14_osc: clocks {
			compatible = "samsung,s2mps14-clk";
			#clock-cells = <1>;
			clock-output-names = "s2mps14_ap", "unused",
				"s2mps14_bt";
		};

		regulators {
			ldo1_reg: LDO1 {
				regulator-name = "VAP_ALIVE_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo2_reg: LDO2 {
				regulator-name = "VAP_M1_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo3_reg: LDO3 {
				regulator-name = "VCC_AP_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo4_reg: LDO4 {
				regulator-name = "VAP_AVDD_PLL1";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo5_reg: LDO5 {
				regulator-name = "VAP_PLL_ISO_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo6_reg: LDO6 {
				regulator-name = "VAP_VMIPI_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo7_reg: LDO7 {
				regulator-name = "VAP_AVDD_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo8_reg: LDO8 {
				regulator-name = "VAP_USB_3.0V";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo9_reg: LDO9 {
				regulator-name = "V_LPDDR_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo10_reg: LDO10 {
				regulator-name = "UNUSED_LDO10";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo11_reg: LDO11 {
				regulator-name = "V_EMMC_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				samsung,ext-control-gpios = <&gpk0 2 GPIO_ACTIVE_HIGH>;
			};

			ldo12_reg: LDO12 {
				regulator-name = "V_EMMC_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				samsung,ext-control-gpios = <&gpk0 2 GPIO_ACTIVE_HIGH>;
			};

			ldo13_reg: LDO13 {
				regulator-name = "CAM_AVDD_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo14_reg: LDO14 {
				regulator-name = "UNUSED_LDO14";
				regulator-min-microvolt = <2700000>;
				regulator-max-microvolt = <2700000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo15_reg: LDO15 {
				regulator-name = "TSP_AVDD_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo16_reg: LDO16 {
				regulator-name = "LCD_VDD_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo17_reg: LDO17 {
				regulator-name = "V_IRLED_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo18_reg: LDO18 {
				regulator-name = "CAM_AF_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo19_reg: LDO19 {
				regulator-name = "TSP_VDD_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo20_reg: LDO20 {
				regulator-name = "LCD_VDD_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo21_reg: LDO21 {
				regulator-name = "CAM_IO_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo22_reg: LDO22 {
				regulator-name = "CAM_DVDD_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo23_reg: LDO23 {
				regulator-name = "HRM_VCC_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo24_reg: LDO24 {
				regulator-name = "HRM_VCC_3.3V";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo25_reg: LDO25 {
				regulator-name = "UNUSED_LDO25";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck1_reg: BUCK1 {
				regulator-name = "VAP_MIF_1.0V";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <900000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck2_reg: BUCK2 {
				regulator-name = "VAP_ARM_1.0V";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1150000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck3_reg: BUCK3 {
				regulator-name = "VAP_INT3D_1.0V";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck4_reg: BUCK4 {
				regulator-name = "VCC_SUB_1.95V";
				regulator-min-microvolt = <1950000>;
				regulator-max-microvolt = <1950000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			buck5_reg: BUCK5 {
				regulator-name = "VCC_SUB_1.35V";
				regulator-min-microvolt = <1350000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};
		};
	};
};

&i2c_1 {
	#address-cells = <1>;
	#size-cells = <0>;
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-slave-addr = <0x10>;
	samsung,i2c-max-bus-freq = <400000>;
	status = "okay";

	fuelgauge@36 {
		compatible = "maxim,max77836-battery";
		interrupt-parent = <&gpx1>;
		interrupts = <2 IRQ_TYPE_LEVEL_LOW>;
		reg = <0x36>;
	};
};

&i2s2 {
	status = "okay";
};

&jpeg {
	status = "okay";
};

&mshc_0 {
	broken-cd;
	non-removable;
	cap-mmc-highspeed;
	mmc-hs200-1_8v;
	card-detect-delay = <200>;
	vmmc-supply = <&ldo12_reg>;
	clock-frequency = <100000000>;
	max-frequency = <100000000>;
	mmc-ddr-1_8v;
	samsung,dw-mshc-ciu-div = <1>;
	samsung,dw-mshc-sdr-timing = <0 1>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_cmd &sd0_bus1 &sd0_bus4 &sd0_bus8>;
	bus-width = <8>;
	status = "okay";
};

&mshc_1 {
	status = "okay";

	#address-cells = <1>;
	#size-cells = <0>;

	non-removable;
	cap-sd-highspeed;
	cap-sdio-irq;
	keep-power-in-suspend;
	samsung,dw-mshc-ciu-div = <1>;
	samsung,dw-mshc-sdr-timing = <0 1>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd1_clk &sd1_cmd &sd1_bus1 &sd1_bus4>;
	bus-width = <4>;

	mmc-pwrseq = <&wlan_pwrseq>;

	brcmf: wifi@1 {
		compatible = "brcm,bcm4334-fmac", "brcm,bcm4329-fmac";
		reg = <1>;

		interrupt-parent = <&gpx1>;
		interrupts = <1 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "host-wake";
	};
};

&serial_0 {
	assigned-clocks = <&cmu CLK_SCLK_UART0>;
	assigned-clock-rates = <100000000>;
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm4330-bt";
		max-speed = <3000000>;
		shutdown-gpios = <&gpe0 0 GPIO_ACTIVE_HIGH>;
		device-wakeup-gpios = <&gpx3 1 GPIO_ACTIVE_HIGH>;
		host-wakeup-gpios = <&gpx2 6 GPIO_ACTIVE_HIGH>;
		clocks = <&s2mps14_osc S2MPS11_CLK_BT>;
	};
};

&serial_1 {
	status = "okay";
};

&tmu {
	vtmu-supply = <&ldo7_reg>;
	status = "okay";
};

&rtc {
	clocks = <&cmu CLK_RTC>, <&s2mps14_osc S2MPS11_CLK_AP>;
	clock-names = "rtc", "rtc_src";
	status = "okay";
};

&xusbxti {
	clock-frequency = <24000000>;
};

&pinctrl_0 {
	pinctrl-names = "default";
	pinctrl-0 = <&initial0 &sleep0>;

	initial0: initial-state {
		PIN_IN(gpa1-4, DOWN, LV1);
		PIN_IN(gpa1-5, DOWN, LV1);

		PIN_IN(gpc0-0, DOWN, LV1);
		PIN_IN(gpc0-1, DOWN, LV1);
		PIN_IN(gpc0-2, DOWN, LV1);
		PIN_IN(gpc0-3, DOWN, LV1);
		PIN_IN(gpc0-4, DOWN, LV1);

		PIN_IN(gpd0-0, DOWN, LV1);
		PIN_IN(gpd0-1, DOWN, LV1);
	};

	sleep0: sleep-state {
		PIN_SLP(gpa0-0, INPUT, DOWN);
		PIN_SLP(gpa0-1, INPUT, DOWN);
		PIN_SLP(gpa0-2, INPUT, DOWN);
		PIN_SLP(gpa0-3, INPUT, DOWN);
		PIN_SLP(gpa0-4, INPUT, DOWN);
		PIN_SLP(gpa0-5, INPUT, DOWN);
		PIN_SLP(gpa0-6, INPUT, DOWN);
		PIN_SLP(gpa0-7, INPUT, DOWN);

		PIN_SLP(gpa1-0, INPUT, DOWN);
		PIN_SLP(gpa1-1, INPUT, DOWN);
		PIN_SLP(gpa1-2, INPUT, DOWN);
		PIN_SLP(gpa1-3, INPUT, DOWN);
		PIN_SLP(gpa1-4, INPUT, DOWN);
		PIN_SLP(gpa1-5, INPUT, DOWN);

		PIN_SLP(gpb-0, PREV, NONE);
		PIN_SLP(gpb-1, PREV, NONE);
		PIN_SLP(gpb-2, PREV, NONE);
		PIN_SLP(gpb-3, PREV, NONE);
		PIN_SLP(gpb-4, INPUT, DOWN);
		PIN_SLP(gpb-5, INPUT, DOWN);
		PIN_SLP(gpb-6, INPUT, DOWN);
		PIN_SLP(gpb-7, INPUT, DOWN);

		PIN_SLP(gpc0-0, INPUT, DOWN);
		PIN_SLP(gpc0-1, INPUT, DOWN);
		PIN_SLP(gpc0-2, INPUT, DOWN);
		PIN_SLP(gpc0-3, INPUT, DOWN);
		PIN_SLP(gpc0-4, INPUT, DOWN);

		PIN_SLP(gpc1-0, INPUT, DOWN);
		PIN_SLP(gpc1-1, INPUT, DOWN);
		PIN_SLP(gpc1-2, INPUT, DOWN);
		PIN_SLP(gpc1-3, INPUT, DOWN);
		PIN_SLP(gpc1-4, INPUT, DOWN);

		PIN_SLP(gpd0-0, INPUT, DOWN);
		PIN_SLP(gpd0-1, INPUT, DOWN);
		PIN_SLP(gpd0-2, INPUT, NONE);
		PIN_SLP(gpd0-3, INPUT, NONE);

		PIN_SLP(gpd1-0, INPUT, NONE);
		PIN_SLP(gpd1-1, INPUT, NONE);
		PIN_SLP(gpd1-2, INPUT, NONE);
		PIN_SLP(gpd1-3, INPUT, NONE);
	};
};

&pinctrl_1 {
	pinctrl-names = "default";
	pinctrl-0 = <&initial1 &sleep1>;

	initial1: initial-state {
		PIN_IN(gpe0-6, DOWN, LV1);
		PIN_IN(gpe0-7, DOWN, LV1);

		PIN_IN(gpe1-0, DOWN, LV1);
		PIN_IN(gpe1-3, DOWN, LV1);
		PIN_IN(gpe1-4, DOWN, LV1);
		PIN_IN(gpe1-5, DOWN, LV1);
		PIN_IN(gpe1-6, DOWN, LV1);

		PIN_IN(gpk2-0, DOWN, LV1);
		PIN_IN(gpk2-1, DOWN, LV1);
		PIN_IN(gpk2-2, DOWN, LV1);
		PIN_IN(gpk2-3, DOWN, LV1);
		PIN_IN(gpk2-4, DOWN, LV1);
		PIN_IN(gpk2-5, DOWN, LV1);
		PIN_IN(gpk2-6, DOWN, LV1);

		PIN_IN(gpm0-0, DOWN, LV1);
		PIN_IN(gpm0-1, DOWN, LV1);
		PIN_IN(gpm0-2, DOWN, LV1);
		PIN_IN(gpm0-3, DOWN, LV1);
		PIN_IN(gpm0-4, DOWN, LV1);
		PIN_IN(gpm0-5, DOWN, LV1);
		PIN_IN(gpm0-6, DOWN, LV1);
		PIN_IN(gpm0-7, DOWN, LV1);

		PIN_IN(gpm1-0, DOWN, LV1);
		PIN_IN(gpm1-1, DOWN, LV1);
		PIN_IN(gpm1-2, DOWN, LV1);
		PIN_IN(gpm1-3, DOWN, LV1);
		PIN_IN(gpm1-4, DOWN, LV1);
		PIN_IN(gpm1-5, DOWN, LV1);
		PIN_IN(gpm1-6, DOWN, LV1);

		PIN_IN(gpm2-0, DOWN, LV1);
		PIN_IN(gpm2-1, DOWN, LV1);

		PIN_IN(gpm3-0, DOWN, LV1);
		PIN_IN(gpm3-1, DOWN, LV1);
		PIN_IN(gpm3-2, DOWN, LV1);
		PIN_IN(gpm3-3, DOWN, LV1);
		PIN_IN(gpm3-4, DOWN, LV1);

		PIN_IN(gpm4-1, DOWN, LV1);
		PIN_IN(gpm4-2, DOWN, LV1);
		PIN_IN(gpm4-3, DOWN, LV1);
		PIN_IN(gpm4-4, DOWN, LV1);
		PIN_IN(gpm4-5, DOWN, LV1);
		PIN_IN(gpm4-6, DOWN, LV1);
		PIN_IN(gpm4-7, DOWN, LV1);
	};

	sleep1: sleep-state {
		PIN_SLP(gpe0-0, PREV, NONE);
		PIN_SLP(gpe0-1, PREV, NONE);
		PIN_SLP(gpe0-2, INPUT, DOWN);
		PIN_SLP(gpe0-3, INPUT, UP);
		PIN_SLP(gpe0-4, INPUT, DOWN);
		PIN_SLP(gpe0-5, INPUT, DOWN);
		PIN_SLP(gpe0-6, INPUT, DOWN);
		PIN_SLP(gpe0-7, INPUT, DOWN);

		PIN_SLP(gpe1-0, INPUT, DOWN);
		PIN_SLP(gpe1-1, PREV, NONE);
		PIN_SLP(gpe1-2, INPUT, DOWN);
		PIN_SLP(gpe1-3, INPUT, DOWN);
		PIN_SLP(gpe1-4, INPUT, DOWN);
		PIN_SLP(gpe1-5, INPUT, DOWN);
		PIN_SLP(gpe1-6, INPUT, DOWN);
		PIN_SLP(gpe1-7, INPUT, NONE);

		PIN_SLP(gpe2-0, INPUT, NONE);
		PIN_SLP(gpe2-1, INPUT, NONE);
		PIN_SLP(gpe2-2, INPUT, NONE);

		PIN_SLP(gpk0-0, INPUT, DOWN);
		PIN_SLP(gpk0-1, INPUT, DOWN);
		PIN_SLP(gpk0-2, OUT0, NONE);
		PIN_SLP(gpk0-3, INPUT, DOWN);
		PIN_SLP(gpk0-4, INPUT, DOWN);
		PIN_SLP(gpk0-5, INPUT, DOWN);
		PIN_SLP(gpk0-6, INPUT, DOWN);
		PIN_SLP(gpk0-7, INPUT, DOWN);

		PIN_SLP(gpk1-0, INPUT, DOWN);
		PIN_SLP(gpk1-1, INPUT, DOWN);
		PIN_SLP(gpk1-2, INPUT, DOWN);
		PIN_SLP(gpk1-3, INPUT, DOWN);
		PIN_SLP(gpk1-4, INPUT, DOWN);
		PIN_SLP(gpk1-5, INPUT, DOWN);
		PIN_SLP(gpk1-6, INPUT, DOWN);

		PIN_SLP(gpk2-0, INPUT, DOWN);
		PIN_SLP(gpk2-1, INPUT, DOWN);
		PIN_SLP(gpk2-2, INPUT, DOWN);
		PIN_SLP(gpk2-3, INPUT, DOWN);
		PIN_SLP(gpk2-4, INPUT, DOWN);
		PIN_SLP(gpk2-5, INPUT, DOWN);
		PIN_SLP(gpk2-6, INPUT, DOWN);

		PIN_SLP(gpl0-0, INPUT, DOWN);
		PIN_SLP(gpl0-1, INPUT, DOWN);
		PIN_SLP(gpl0-2, INPUT, DOWN);
		PIN_SLP(gpl0-3, INPUT, DOWN);

		PIN_SLP(gpm0-0, INPUT, DOWN);
		PIN_SLP(gpm0-1, INPUT, DOWN);
		PIN_SLP(gpm0-2, INPUT, DOWN);
		PIN_SLP(gpm0-3, INPUT, DOWN);
		PIN_SLP(gpm0-4, INPUT, DOWN);
		PIN_SLP(gpm0-5, INPUT, DOWN);
		PIN_SLP(gpm0-6, INPUT, DOWN);
		PIN_SLP(gpm0-7, INPUT, DOWN);

		PIN_SLP(gpm1-0, INPUT, DOWN);
		PIN_SLP(gpm1-1, INPUT, DOWN);
		PIN_SLP(gpm1-2, INPUT, DOWN);
		PIN_SLP(gpm1-3, INPUT, DOWN);
		PIN_SLP(gpm1-4, INPUT, DOWN);
		PIN_SLP(gpm1-5, INPUT, DOWN);
		PIN_SLP(gpm1-6, INPUT, DOWN);

		PIN_SLP(gpm2-0, INPUT, DOWN);
		PIN_SLP(gpm2-1, INPUT, DOWN);
		PIN_SLP(gpm2-2, INPUT, DOWN);
		PIN_SLP(gpm2-3, INPUT, DOWN);
		PIN_SLP(gpm2-4, INPUT, DOWN);

		PIN_SLP(gpm3-0, INPUT, DOWN);
		PIN_SLP(gpm3-1, INPUT, DOWN);
		PIN_SLP(gpm3-2, INPUT, DOWN);
		PIN_SLP(gpm3-3, INPUT, DOWN);
		PIN_SLP(gpm3-4, INPUT, DOWN);
		PIN_SLP(gpm3-5, INPUT, DOWN);
		PIN_SLP(gpm3-6, INPUT, DOWN);
		PIN_SLP(gpm3-7, INPUT, DOWN);

		PIN_SLP(gpm4-0, INPUT, DOWN);
		PIN_SLP(gpm4-1, INPUT, DOWN);
		PIN_SLP(gpm4-2, INPUT, DOWN);
		PIN_SLP(gpm4-3, INPUT, DOWN);
		PIN_SLP(gpm4-4, INPUT, DOWN);
		PIN_SLP(gpm4-5, INPUT, DOWN);
		PIN_SLP(gpm4-6, INPUT, DOWN);
		PIN_SLP(gpm4-7, INPUT, DOWN);
	};
};
