// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung Exynos5800 SoC device tree source
 *
 * Copyright (c) 2014 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Samsung Exynos5800 SoC device nodes are listed in this file.
 * Exynos5800 based board files can include this file and provide
 * values for board specific bindings.
 */

#include "exynos5420.dtsi"

/ {
	compatible = "samsung,exynos5800", "samsung,exynos5";
};

&clock {
	compatible = "samsung,exynos5800-clock", "syscon";
};

&cluster_a15_opp_table {
	opp-********** {
		opp-hz = /bits/ 64 <**********>;
		opp-microvolt = <1312500 1312500 1500000>;
		clock-latency-ns = <140000>;
	};
	opp-********** {
		opp-hz = /bits/ 64 <**********>;
		opp-microvolt = <1262500 1262500 1500000>;
		clock-latency-ns = <140000>;
	};
	opp-********** {
		opp-hz = /bits/ 64 <**********>;
		opp-microvolt = <1237500 1237500 1500000>;
		clock-latency-ns = <140000>;
	};
	opp-********** {
		opp-microvolt = <1250000 1250000 1500000>;
	};
	opp-********** {
		opp-microvolt = <1250000 1250000 1500000>;
	};
	opp-1500000000 {
		opp-microvolt = <1100000 1100000 1500000>;
	};
	opp-1400000000 {
		opp-microvolt = <1100000 1100000 1500000>;
	};
	opp-1300000000 {
		opp-microvolt = <1100000 1100000 1500000>;
	};
	opp-1200000000 {
		opp-microvolt = <1000000 1000000 1500000>;
	};
	opp-1100000000 {
		opp-microvolt = <1000000 1000000 1500000>;
	};
	opp-1000000000 {
		opp-microvolt = <1000000 1000000 1500000>;
	};
	opp-900000000 {
		opp-microvolt = <1000000 1000000 1500000>;
	};
	opp-800000000 {
		opp-microvolt = <900000 900000 1500000>;
	};
	opp-700000000 {
		opp-microvolt = <900000 900000 1500000>;
	};
	opp-600000000 {
		opp-hz = /bits/ 64 <600000000>;
		opp-microvolt = <900000 900000 1500000>;
		clock-latency-ns = <140000>;
	};
	opp-500000000 {
		opp-hz = /bits/ 64 <500000000>;
		opp-microvolt = <900000 900000 1500000>;
		clock-latency-ns = <140000>;
	};
	opp-400000000 {
		opp-hz = /bits/ 64 <400000000>;
		opp-microvolt = <900000 900000 1500000>;
		clock-latency-ns = <140000>;
	};
	opp-300000000 {
		opp-hz = /bits/ 64 <300000000>;
		opp-microvolt = <900000 900000 1500000>;
		clock-latency-ns = <140000>;
	};
	opp-200000000 {
		opp-hz = /bits/ 64 <200000000>;
		opp-microvolt = <900000 900000 1500000>;
		clock-latency-ns = <140000>;
	};
};

&cluster_a7_opp_table {
	opp-1400000000 {
		opp-hz = /bits/ 64 <1400000000>;
		opp-microvolt = <1275000>;
		clock-latency-ns = <140000>;
	};
	opp-1300000000 {
		opp-microvolt = <1250000>;
	};
	opp-1200000000 {
		opp-microvolt = <1250000>;
	};
	opp-1100000000 {
		opp-microvolt = <1250000>;
	};
	opp-1000000000 {
		opp-microvolt = <1100000>;
	};
	opp-900000000 {
		opp-microvolt = <1100000>;
	};
	opp-800000000 {
		opp-microvolt = <1100000>;
	};
	opp-700000000 {
		opp-microvolt = <1000000>;
	};
	opp-600000000 {
		opp-microvolt = <1000000>;
	};
	opp-500000000 {
		opp-hz = /bits/ 64 <500000000>;
		opp-microvolt = <1000000>;
		clock-latency-ns = <140000>;
	};
	opp-400000000 {
		opp-hz = /bits/ 64 <400000000>;
		opp-microvolt = <1000000>;
		clock-latency-ns = <140000>;
	};
	opp-300000000 {
		opp-hz = /bits/ 64 <300000000>;
		opp-microvolt = <900000>;
		clock-latency-ns = <140000>;
	};
	opp-200000000 {
		opp-hz = /bits/ 64 <200000000>;
		opp-microvolt = <900000>;
		clock-latency-ns = <140000>;
	};
};

&dsi {
	compatible = "samsung,exynos5422-mipi-dsi";
};

&mfc {
	compatible = "samsung,mfc-v8";
};

&soc {
	cam_pd: power-domain@10045100 {
		compatible = "samsung,exynos4210-pd";
		reg = <0x10045100 0x20>;
		#power-domain-cells = <0>;
		label = "CAM";
	};
};
