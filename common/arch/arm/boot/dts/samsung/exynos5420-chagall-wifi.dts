// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos5420 Chagall WiFi board device tree source
 *
 * Copyright (c) 2012-2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 * Copyright (c) 2022 <PERSON>
 */

/dts-v1/;
#include "exynos5420-galaxy-tab-common.dtsi"

/ {
	model = "Samsung Chagall WiFi based on Exynos5420";
	compatible = "samsung,chagall-wifi", "samsung,exynos5420", \
		     "samsung,exynos5";
};

&ldo15_reg {
	/* Unused */
	regulator-name = "VDD_LDO15";
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
};

&ldo17_reg {
	regulator-name = "VDD_IRLED_3V3";
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3350000>;
	regulator-always-on;
	regulator-boot-on;

	regulator-state-mem {
		regulator-off-in-suspend;
	};
};

&ldo28_reg {
	/* Unused */
	regulator-name = "VDD_LDO28";
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
};

&ldo29_reg {
	regulator-name = "VDD_TCON_1V8";
	regulator-min-microvolt = <1900000>;
	regulator-max-microvolt = <1900000>;

	regulator-state-mem {
		regulator-off-in-suspend;
	};
};

&ldo31_reg {
	regulator-name = "VDD_GRIP_1V8";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-always-on;
	regulator-boot-on;

	regulator-state-mem {
		regulator-off-in-suspend;
	};
};

&ldo32_reg {
	regulator-name = "VDD_TSP_1V8";
	regulator-min-microvolt = <1900000>;
	regulator-max-microvolt = <1900000>;

	regulator-state-mem {
		regulator-off-in-suspend;
	};
};
