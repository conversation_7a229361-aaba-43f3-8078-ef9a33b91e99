# SPDX-License-Identifier: GPL-2.0
dtb-$(CONFIG_ARCH_EXYNOS3) += \
	exynos3250-artik5-eval.dtb \
	exynos3250-monk.dtb \
	exynos3250-rinato.dtb
dtb-$(CONFIG_ARCH_EXYNOS4) += \
	exynos4210-i9100.dtb \
	exynos4210-origen.dtb \
	exynos4210-smdkv310.dtb \
	exynos4210-trats.dtb \
	exynos4210-universal_c210.dtb \
	exynos4212-tab3-3g8.dtb \
	exynos4212-tab3-lte8.dtb \
	exynos4212-tab3-wifi8.dtb \
	exynos4412-i9300.dtb \
	exynos4412-i9305.dtb \
	exynos4412-itop-elite.dtb \
	exynos4412-n710x.dtb \
	exynos4412-odroidu3.dtb \
	exynos4412-odroidx.dtb \
	exynos4412-odroidx2.dtb \
	exynos4412-origen.dtb \
	exynos4412-p4note-n8010.dtb \
	exynos4412-smdk4412.dtb \
	exynos4412-tiny4412.dtb \
	exynos4412-trats2.dtb
dtb-$(CONFIG_ARCH_EXYNOS5) += \
	exynos5250-arndale.dtb \
	exynos5250-smdk5250.dtb \
	exynos5250-snow.dtb \
	exynos5250-snow-rev5.dtb \
	exynos5250-spring.dtb \
	exynos5260-xyref5260.dtb \
	exynos5410-odroidxu.dtb \
	exynos5410-smdk5410.dtb \
	exynos5420-arndale-octa.dtb \
	exynos5420-peach-pit.dtb \
	exynos5420-smdk5420.dtb \
	exynos5420-chagall-wifi.dtb \
	exynos5420-klimt-wifi.dtb \
	exynos5422-odroidhc1.dtb \
	exynos5422-odroidxu3.dtb \
	exynos5422-odroidxu3-lite.dtb \
	exynos5422-odroidxu4.dtb \
	exynos5422-samsung-k3g.dtb \
	exynos5800-peach-pi.dtb
dtb-$(CONFIG_ARCH_S3C64XX) += \
	s3c6410-mini6410.dtb \
	s3c6410-smdk6410.dtb
dtb-$(CONFIG_ARCH_S5PV210) += \
	s5pv210-aquila.dtb \
	s5pv210-fascinate4g.dtb \
	s5pv210-galaxys.dtb \
	s5pv210-goni.dtb \
	s5pv210-smdkc110.dtb \
	s5pv210-smdkv210.dtb \
	s5pv210-torbreck.dtb
