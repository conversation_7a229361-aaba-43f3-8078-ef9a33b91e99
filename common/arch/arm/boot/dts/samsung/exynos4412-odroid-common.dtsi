// SPDX-License-Identifier: GPL-2.0
/*
 * Common definition for Hardkernel's Exynos4412 based ODROID-X/X2/U2/U3 boards
 * device tree source
 */

#include <dt-bindings/sound/samsung-i2s.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/clock/maxim,max77686.h>
#include "exynos4412.dtsi"
#include "exynos4412-ppmu-common.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include "exynos-mfc-reserved-memory.dtsi"

/ {
	aliases {
		mmc0 = &mshc_0;
		mmc2 = &sdhci_2;
	};

	chosen {
		stdout-path = &serial_1;
	};

	firmware@204f000 {
		compatible = "samsung,secure-firmware";
		reg = <0x0204f000 0x1000>;
	};

	gpio_keys: gpio-keys {
		compatible = "gpio-keys";
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_power_key>;

		power-key {
			gpios = <&gpx1 3 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			label = "power key";
			debounce-interval = <10>;
			wakeup-source;
		};
	};

	sound: sound {
		compatible = "hardkernel,odroid-xu4-audio";

		cpu {
			sound-dai = <&i2s0 0>;
		};

		codec {
			sound-dai = <&hdmi>, <&max98090>;
		};
	};

	emmc_pwrseq: pwrseq {
		pinctrl-0 = <&emmc_rstn>;
		pinctrl-names = "default";
		compatible = "mmc-pwrseq-emmc";
		reset-gpios = <&gpk1 2 GPIO_ACTIVE_LOW>;
	};

	fixed-rate-clocks {
		xxti {
			compatible = "samsung,clock-xxti";
			clock-frequency = <0>;
		};

		xusbxti {
			compatible = "samsung,clock-xusbxti";
			clock-frequency = <24000000>;
		};
	};
};

&bus_dmc {
	devfreq-events = <&ppmu_dmc0_3>, <&ppmu_dmc1_3>;
	vdd-supply = <&buck1_reg>;
	status = "okay";
};

&bus_acp {
	devfreq = <&bus_dmc>;
	status = "okay";
};

&bus_c2c {
	devfreq = <&bus_dmc>;
	status = "okay";
};

&bus_leftbus {
	devfreq-events = <&ppmu_leftbus_3>, <&ppmu_rightbus_3>;
	vdd-supply = <&buck3_reg>;
	status = "okay";
};

&bus_rightbus {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_display {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_fsys {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_peri {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_mfc {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&camera {
	status = "okay";
};

&clock {
	clocks = <&clock CLK_XUSBXTI>;
	assigned-clocks = <&clock CLK_FOUT_EPLL>;
	assigned-clock-rates = <45158401>;
};

&clock_audss {
	assigned-clocks = <&clock_audss EXYNOS_MOUT_AUDSS>,
			<&clock_audss EXYNOS_MOUT_I2S>,
			<&clock_audss EXYNOS_DOUT_SRP>,
			<&clock_audss EXYNOS_DOUT_AUD_BUS>,
			<&clock_audss EXYNOS_DOUT_I2S>;

	assigned-clock-parents = <&clock CLK_FOUT_EPLL>,
			  <&clock_audss EXYNOS_MOUT_AUDSS>;

	assigned-clock-rates = <0>, <0>,
			<196608001>,
			<(196608001 / 2)>,
			<(196608001 / 8)>;
};

&cpu0 {
	cpu0-supply = <&buck2_reg>;
};

&cpu0_opp_table {
	opp-1000000000 {
		opp-suspend;
	};
	opp-800000000 {
		/delete-property/opp-suspend;
	};
};

&cpu_thermal {
	cooling-maps {
		cooling_map0: map0 {
			/* Corresponds to 800MHz at freq_table */
			cooling-device = <&cpu0 7 7>, <&cpu1 7 7>,
					 <&cpu2 7 7>, <&cpu3 7 7>;
		};
		cooling_map1: map1 {
			/* Corresponds to 200MHz at freq_table */
			cooling-device = <&cpu0 13 13>, <&cpu1 13 13>,
					 <&cpu2 13 13>, <&cpu3 13 13>;
		};
	};
};

&pinctrl_1 {
	gpio_power_key: power-key-pins {
		samsung,pins = "gpx1-3";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	max77686_irq: max77686-irq-pins {
		samsung,pins = "gpx3-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	hdmi_hpd: hdmi-hpd-pins {
		samsung,pins = "gpx3-7";
		samsung,pin-pud = <EXYNOS_PIN_PULL_DOWN>;
	};

	emmc_rstn: emmc-rstn-pins {
		samsung,pins = "gpk1-2";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};
};

&ehci {
	status = "okay";
};

&exynos_usbphy {
	status = "okay";
};

&fimc_0 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC0>,
			<&clock CLK_SCLK_FIMC0>;
	assigned-clock-parents = <&clock CLK_MOUT_MPLL_USER_T>;
	assigned-clock-rates = <0>, <176000000>;
};

&fimc_1 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC1>,
			<&clock CLK_SCLK_FIMC1>;
	assigned-clock-parents = <&clock CLK_MOUT_MPLL_USER_T>;
	assigned-clock-rates = <0>, <176000000>;
};

&fimc_2 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC2>,
			<&clock CLK_SCLK_FIMC2>;
	assigned-clock-parents = <&clock CLK_MOUT_MPLL_USER_T>;
	assigned-clock-rates = <0>, <176000000>;
};

&fimc_3 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC3>,
			<&clock CLK_SCLK_FIMC3>;
	assigned-clock-parents = <&clock CLK_MOUT_MPLL_USER_T>;
	assigned-clock-rates = <0>, <176000000>;
};

&gpu {
	mali-supply = <&buck4_reg>;
	status = "okay";
};

&hdmi {
	hpd-gpios = <&gpx3 7 GPIO_ACTIVE_HIGH>;
	pinctrl-names = "default";
	pinctrl-0 = <&hdmi_hpd>;
	vdd-supply = <&ldo8_reg>;
	vdd_osc-supply = <&ldo10_reg>;
	vdd_pll-supply = <&ldo8_reg>;
	ddc = <&i2c_2>;
	status = "okay";
};

&hdmicec {
	status = "okay";
};

&hsotg {
	status = "okay";
	vusb_d-supply = <&ldo15_reg>;
	vusb_a-supply = <&ldo12_reg>;
};

&i2c_0 {
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <400000>;
	status = "okay";

	usb3503: usb-hub@8 {
		compatible = "smsc,usb3503";
		reg = <0x08>;

		intn-gpios = <&gpx3 0 GPIO_ACTIVE_HIGH>;
		connect-gpios = <&gpx3 4 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpx3 5 GPIO_ACTIVE_LOW>;
		initial-mode = <1>;
	};

	max77686: pmic@9 {
		compatible = "maxim,max77686";
		interrupt-parent = <&gpx3>;
		interrupts = <2 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&max77686_irq>;
		wakeup-source;
		reg = <0x09>;
		#clock-cells = <1>;

		voltage-regulators {
			ldo1_reg: LDO1 {
				regulator-name = "VDD_ALIVE_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo2_reg: LDO2 {
				regulator-name = "VDDQ_M1_2_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo3_reg: LDO3 {
				regulator-name = "VDDQ_EXT_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo4_reg: LDO4 {
				regulator-name = "VDDQ_MMC2_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-boot-on;
			};

			ldo5_reg: LDO5 {
				regulator-name = "VDDQ_MMC1_3_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
			};

			ldo6_reg: LDO6 {
				regulator-name = "VDD10_MPLL_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo7_reg: LDO7 {
				regulator-name = "VDD10_XPLL_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo8_reg: LDO8 {
				regulator-name = "VDD10_HDMI_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
			};

			ldo10_reg: LDO10 {
				regulator-name = "VDDQ_MIPIHSI_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo11_reg: LDO11 {
				regulator-name = "VDD18_ABB1_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo12_reg: LDO12 {
				regulator-name = "VDD33_USB_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-boot-on;
			};

			ldo13_reg: LDO13 {
				regulator-name = "VDDQ_C2C_W_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
			};

			ldo14_reg: LDO14 {
				regulator-name = "VDD18_ABB0_2_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
			};

			ldo15_reg: LDO15 {
				regulator-name = "VDD10_HSIC_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-boot-on;
			};

			ldo16_reg: LDO16 {
				regulator-name = "VDD18_HSIC_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
			};

			ldo20_reg: LDO20 {
				regulator-name = "LDO20_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo21_reg: LDO21 {
				regulator-name = "TFLASH_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-boot-on;
			};

			ldo22_reg: LDO22 {
				/*
				 * Only U3 uses it, so let it define the
				 * constraints
				 */
				regulator-name = "LDO22";
				regulator-boot-on;
			};

			ldo25_reg: LDO25 {
				regulator-name = "VDDQ_LCD_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck1_reg: BUCK1 {
				regulator-name = "VDD_MIF";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck2_reg: BUCK2 {
				regulator-name = "VDD_ARM";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck3_reg: BUCK3 {
				regulator-name = "VDD_INT";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <1050000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck4_reg: BUCK4 {
				regulator-name = "VDD_G3D";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <1100000>;
				regulator-microvolt-offset = <50000>;
			};

			buck5_reg: BUCK5 {
				regulator-name = "VDDQ_CKEM1_2_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck6_reg: BUCK6 {
				regulator-name = "BUCK6_1.35V";
				regulator-min-microvolt = <1350000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck7_reg: BUCK7 {
				regulator-name = "BUCK7_2.0V";
				regulator-min-microvolt = <2000000>;
				regulator-max-microvolt = <2000000>;
				regulator-always-on;
			};

			buck8_reg: BUCK8 {
				/*
				 * Constraints set by specific board: X,
				 * X2 and U3.
				 */
				regulator-name = "BUCK8_2.8V";
			};
		};
	};
};

&i2c_1 {
	status = "okay";
	max98090: audio-codec@10 {
		compatible = "maxim,max98090";
		reg = <0x10>;
		interrupt-parent = <&gpx0>;
		interrupts = <0 IRQ_TYPE_NONE>;
		clocks = <&i2s0 CLK_I2S_CDCLK>;
		clock-names = "mclk";
		#sound-dai-cells = <0>;
	};
};

&i2c_2 {
	status = "okay";
};

&i2c_8 {
	status = "okay";
};

&i2s0 {
	pinctrl-0 = <&i2s0_bus>;
	pinctrl-names = "default";
	status = "okay";
	assigned-clocks = <&i2s0 CLK_I2S_RCLK_SRC>;
	assigned-clock-parents = <&clock_audss EXYNOS_SCLK_I2S>;
};

&mixer {
	status = "okay";
};

&mshc_0 {
	pinctrl-0 = <&sd4_clk &sd4_cmd &sd4_bus4 &sd4_bus8>;
	pinctrl-names = "default";
	vmmc-supply = <&ldo20_reg>;
	mmc-pwrseq = <&emmc_pwrseq>;
	status = "okay";

	broken-cd;
	card-detect-delay = <200>;
	mmc-ddr-1_8v;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	bus-width = <8>;
	cap-mmc-highspeed;
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&max77686 MAX77686_CLK_AP>;
	clock-names = "rtc", "rtc_src";
};

&sdhci_2 {
	bus-width = <4>;
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_cd &sd2_bus4>;
	pinctrl-names = "default";
	vmmc-supply = <&ldo21_reg>;
	vqmmc-supply = <&ldo4_reg>;
	cd-gpios = <&gpk2 2 GPIO_ACTIVE_LOW>;
	status = "okay";
};

&serial_0 {
	status = "okay";
};

&serial_1 {
	status = "okay";
};

&tmu {
	vtmu-supply = <&ldo10_reg>;
	status = "okay";
};
