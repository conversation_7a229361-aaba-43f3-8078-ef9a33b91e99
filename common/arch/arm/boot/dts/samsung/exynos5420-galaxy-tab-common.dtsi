// SPDX-License-Identifier: GPL-2.0
/*
 * Base DT for Samsung's family of tablets based on Exynos5420.
 *
 * Copyright (c) 2012-2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 * Copyright (c) 2022 <PERSON>
 */

/dts-v1/;
#include "exynos5420.dtsi"
#include "exynos5420-cpus.dtsi"
#include <dt-bindings/input/input.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/clock/samsung,s2mps11.h>

/ {
	chassis-type = "tablet";

	/*
	 * To successfully boot the mainline kernel with the stock
	 * bootloader (SBOOT), the tlb needs to be flushed after the
	 * page table pointer has been updated in __common_mmu_cache_on.
	 * The same hack is also needed to boot exynos4412-i9300 with
	 * stock bootloader, and probably other Samsung devices of
	 * similar age.  See
	 * https://lore.kernel.org/all/<EMAIL>
	 * for more details.
	 */

	aliases {
		mmc0 = &mmc_0;
		mmc2 = &mmc_2;
	};

	chosen {
		stdout-path = "serial2:115200n8";
	};

	memory@20000000 {
		device_type = "memory";
		reg = <0x20000000 0xc0000000>;
	};

	firmware@2073000 {
		compatible = "samsung,secure-firmware";
		reg = <0x02073000 0x1000>;
	};

	fixed-rate-clocks {
		oscclk {
			compatible = "samsung,exynos5420-oscclk";
			clock-frequency = <24000000>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-power {
			debounce-interval = <10>;
			gpios = <&gpx2 2 GPIO_ACTIVE_LOW>;
			label = "Power";
			linux,code = <KEY_POWER>;
			wakeup-source;
		};

		key-home {
			debounce-interval = <10>;
			gpios = <&gpx0 5 GPIO_ACTIVE_LOW>;
			label = "Home";
			linux,code = <KEY_HOME>;
			wakeup-source;
		};

		key-volume-up {
			debounce-interval = <10>;
			gpios = <&gpx0 2 GPIO_ACTIVE_LOW>;
			label = "Volume Up";
			linux,code = <KEY_VOLUMEUP>;
		};

		key-volume-down {
			debounce-interval = <10>;
			gpios = <&gpx0 3 GPIO_ACTIVE_LOW>;
			label = "Volume Down";
			linux,code = <KEY_VOLUMEDOWN>;
		};
	};
};

&cci {
	/* CCI is disabled in hardware */
	status = "disabled";
};

&cpu0 {
	cpu-supply = <&buck2_reg>;
};

&cpu4 {
	cpu-supply = <&buck6_reg>;
};

&gpu {
	status = "okay";
	mali-supply = <&buck4_reg>;
};

&hsi2c_7 {
	status = "okay";

	pmic@66 {
		compatible = "samsung,s2mps11-pmic";
		reg = <0x66>;

		interrupt-parent = <&gpx3>;
		interrupts = <2 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&s2mps11_irq>;

		s2mps11_osc: clocks {
			compatible = "samsung,s2mps11-clk";
			#clock-cells = <1>;
			clock-output-names = "s2mps11_ap", "s2mps11_cp",
					     "s2mps11_bt";
		};

		regulators {
			buck1_reg: BUCK1 {
				regulator-name = "VDD_MIF_1V1";
				regulator-min-microvolt = <700000>;
				regulator-max-microvolt = <1300000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck2_reg: BUCK2 {
				regulator-name = "VDD_ARM_1V0";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck3_reg: BUCK3 {
				regulator-name = "VDD_INT_1V0";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1400000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck4_reg: BUCK4 {
				regulator-name = "VDD_G3D_1V0";
				regulator-min-microvolt = <700000>;
				regulator-max-microvolt = <1400000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck5_reg: BUCK5 {
				regulator-name = "VDD_MEM_1V2";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};

			};

			buck6_reg: BUCK6 {
				regulator-name = "VDD_KFC_1V0";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck7_reg: BUCK7 {
				regulator-name = "VIN_LLDO_1V4";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
			};

			buck8_reg: BUCK8 {
				regulator-name = "VIN_MLDO_2V0";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <2100000>;
				regulator-always-on;
			};

			buck9_reg: BUCK9 {
				regulator-name = "VIN_HLDO_3V5";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3500000>;
				regulator-always-on;
			};

			buck10_reg: BUCK10 {
				regulator-name = "VDD_CAM_ISP_1V0";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <3550000>;
			};

			ldo1_reg: LDO1 {
				regulator-name = "VDD_ALIVE_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo2_reg: LDO2 {
				regulator-name = "VDD_APIO_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo3_reg: LDO3 {
				regulator-name = "VDD_APIO_MMC01_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo4_reg: LDO4 {
				regulator-name = "VDD_ADC_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo5_reg: LDO5 {
				/* Unused */
				regulator-name = "VDD_LDO5";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo6_reg: LDO6 {
				regulator-name = "VDD_MIPI_1V0";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo7_reg: LDO7 {
				regulator-name = "VDD_MIPI_PLL_ABB1_18V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo8_reg: LDO8 {
				/* Unused */
				regulator-name = "VDD_LDO8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo9_reg: LDO9 {
				regulator-name = "VDD_UOTG_3V0";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo10_reg: LDO10 {
				regulator-name = "VDDQ_PRE_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo11_reg: LDO11 {
				regulator-name = "VDD_HSIC_1V0";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo12_reg: LDO12 {
				regulator-name = "VDD_HSIC_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo13_reg: LDO13 {
				regulator-name = "VDD_APIO_MMC2_2V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <2800000>;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo14_reg: LDO14 {
				regulator-name = "VDD_MOTOR_3V0";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo15_reg: LDO15 {
				regulator-name = "VDD_LDO15";
				/*
				 * LDO15 varies between devices and is
				 * specified in the device dts
				 */
			};

			ldo16_reg: LDO16 {
				regulator-name = "VDD_AP_2V8";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo17_reg: LDO17 {
				regulator-name = "VDD_LDO17";
				/*
				 * LDO17 varies between devices and is
				 * specified in the device dts
				 */
			};

			ldo18_reg: LDO18 {
				/* Unused */
				regulator-name = "VDD_LDO18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo19_reg: LDO19 {
				regulator-name = "VDD_VTF_2V8";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo20_reg: LDO20 {
				regulator-name = "VDD_CAM1_CAM_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo21_reg: LDO21 {
				regulator-name = "VDD_CAM_IO_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo22_reg: LDO22 {
				regulator-name = "VDD_CAM0_S_CORE_1V1";
				regulator-min-microvolt = <1050000>;
				regulator-max-microvolt = <1200000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo23_reg: LDO23 {
				regulator-name = "VDD_MIFS_1V1";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo24_reg: LDO24 {
				regulator-name = "VDD_TSP_3V3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo25_reg: LDO25 {
				/* Unused */
				regulator-name = "VDD_LDO25";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo26_reg: LDO26 {
				regulator-name = "VDD_CAM0_AF_2V8";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo27_reg: LDO27 {
				regulator-name = "VDD_G3DS_1V0";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo28_reg: LDO28 {
				regulator-name = "VDD_LDO28";
				/*
				 * LDO28 varies between devices and is
				 * specified in the device dts
				 */
			};

			ldo29_reg: LDO29 {
				regulator-name = "VDD_LDO29";
				/*
				 * LDO29 varies between devices and is
				 * specified in the device dts
				 */
			};

			ldo30_reg: LDO30 {
				regulator-name = "VDD_TOUCH_1V8";
				regulator-min-microvolt = <1900000>;
				regulator-max-microvolt = <1900000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo31_reg: LDO31 {
				regulator-name = "VDD_LDO31";
				/*
				 * LDO31 varies between devices and is
				 * specified in the device dts
				 */
			};

			ldo32_reg: LDO32 {
				regulator-name = "VDD_LDO32";
				/*
				 * LDO32 varies between devices and is
				 * specified in the device dts
				 */
			};

			ldo33_reg: LDO33 {
				regulator-name = "VDD_MHL_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo34_reg: LDO34 {
				regulator-name = "VDD_MHL_3V3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo35_reg: LDO35 {
				regulator-name = "VDD_SIL_1V2";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo36_reg: LDO36 {
				/* Unused */
				regulator-name = "VDD_LDO36";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo37_reg: LDO37 {
				/* Unused */
				regulator-name = "VDD_LDO37";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo38_reg: LDO38 {
				regulator-name = "VDD_KEY_LED_3V3";
				regulator-min-microvolt = <2500000>;
				regulator-max-microvolt = <3300000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};
		};
	};
};

&mixer {
	status = "okay";
};

/* Internal storage */
&mmc_0 {
	status = "okay";
	bus-width = <8>;
	cap-mmc-highspeed;
	card-detect-delay = <200>;
	mmc-ddr-1_8v;
	mmc-hs200-1_8v;
	non-removable;
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_bus1 &sd0_bus4 &sd0_bus8>;
	pinctrl-names = "default";
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-ddr-timing = <0 2>;
	samsung,dw-mshc-sdr-timing = <0 4>;
	vqmmc-supply = <&ldo3_reg>;
};

/* External sdcard */
&mmc_2 {
	status = "okay";
	bus-width = <4>;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	cd-gpios = <&gpx2 4 GPIO_ACTIVE_LOW>;
	pinctrl-0 = <&sd2_clk &sd2_cmd &mmc2_cd &sd2_bus1 &sd2_bus4>;
	pinctrl-names = "default";
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-ddr-timing = <0 2>;
	samsung,dw-mshc-sdr-timing = <0 4>;
	sd-uhs-sdr50;
	vmmc-supply = <&ldo19_reg>;
	vqmmc-supply = <&ldo13_reg>;
};

&pinctrl_0 {
	mmc2_cd: mmc2-cd-pins {
		samsung,pins = "gpx2-4";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	s2mps11_irq: s2mps11-irq-pins {
		samsung,pins = "gpx3-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_F>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&s2mps11_osc S2MPS11_CLK_AP>;
	clock-names = "rtc", "rtc_src";
};

&tmu_cpu0 {
	vtmu-supply = <&ldo10_reg>;
};

&tmu_cpu1 {
	vtmu-supply = <&ldo10_reg>;
};

&tmu_cpu2 {
	vtmu-supply = <&ldo10_reg>;
};

&tmu_cpu3 {
	vtmu-supply = <&ldo10_reg>;
};

&tmu_gpu {
	vtmu-supply = <&ldo10_reg>;
};

&usbdrd_dwc3_0 {
	dr_mode = "peripheral";
};

&usbdrd_dwc3_1 {
	dr_mode = "peripheral";
};

&usbdrd3_0 {
	vdd33-supply = <&ldo9_reg>;
	vdd10-supply = <&ldo11_reg>;
};

&usbdrd3_1 {
	vdd33-supply = <&ldo9_reg>;
	vdd10-supply = <&ldo11_reg>;
};
