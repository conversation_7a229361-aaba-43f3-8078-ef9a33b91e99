// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung Exynos5410 SoC device tree source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Samsung Exynos5410 SoC device nodes are listed in this file.
 * Exynos5410 based board files can include this file and provide
 * values for board specific bindings.
 */

#include "exynos54xx.dtsi"
#include <dt-bindings/clock/exynos5410.h>
#include <dt-bindings/clock/exynos-audss-clk.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	compatible = "samsung,exynos5410", "samsung,exynos5";
	interrupt-parent = <&gic>;

	aliases {
		pinctrl0 = &pinctrl_0;
		pinctrl1 = &pinctrl_1;
		pinctrl2 = &pinctrl_2;
		pinctrl3 = &pinctrl_3;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <0x0>;
			clock-frequency = <**********>;
		};

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <0x1>;
			clock-frequency = <**********>;
		};

		cpu2: cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <0x2>;
			clock-frequency = <**********>;
		};

		cpu3: cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <0x3>;
			clock-frequency = <**********>;
		};
	};

	soc: soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		pmu_system_controller: system-controller@10040000 {
			compatible = "samsung,exynos5410-pmu", "syscon";
			reg = <0x10040000 0x5000>;
			clock-names = "clkout16";
			clocks = <&fin_pll>;
			#clock-cells = <1>;
		};

		clock: clock-controller@10010000 {
			compatible = "samsung,exynos5410-clock";
			reg = <0x10010000 0x30000>;
			#clock-cells = <1>;
		};

		clock_audss: audss-clock-controller@3810000 {
			compatible = "samsung,exynos5410-audss-clock";
			reg = <0x03810000 0x0c>;
			#clock-cells = <1>;
			clocks = <&fin_pll>, <&clock CLK_FOUT_EPLL>;
			clock-names = "pll_ref", "pll_in";
		};

		tmu_cpu0: tmu@10060000 {
			compatible = "samsung,exynos5420-tmu";
			reg = <0x10060000 0x100>;
			interrupts = <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_TMU>;
			clock-names = "tmu_apbif";
			#thermal-sensor-cells = <0>;
		};

		tmu_cpu1: tmu@10064000 {
			compatible = "samsung,exynos5420-tmu";
			reg = <0x10064000 0x100>;
			interrupts = <GIC_SPI 183 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_TMU>;
			clock-names = "tmu_apbif";
			#thermal-sensor-cells = <0>;
		};

		tmu_cpu2: tmu@10068000 {
			compatible = "samsung,exynos5420-tmu";
			reg = <0x10068000 0x100>;
			interrupts = <GIC_SPI 184 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_TMU>;
			clock-names = "tmu_apbif";
			#thermal-sensor-cells = <0>;
		};

		tmu_cpu3: tmu@1006c000 {
			compatible = "samsung,exynos5420-tmu";
			reg = <0x1006c000 0x100>;
			interrupts = <GIC_SPI 185 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_TMU>;
			clock-names = "tmu_apbif";
			#thermal-sensor-cells = <0>;
		};

		mmc_0: mmc@12200000 {
			compatible = "samsung,exynos5250-dw-mshc";
			reg = <0x12200000 0x1000>;
			interrupts = <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_MMC0>, <&clock CLK_SCLK_MMC0>;
			clock-names = "biu", "ciu";
			fifo-depth = <0x80>;
			status = "disabled";
		};

		mmc_1: mmc@12210000 {
			compatible = "samsung,exynos5250-dw-mshc";
			reg = <0x12210000 0x1000>;
			interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_MMC1>, <&clock CLK_SCLK_MMC1>;
			clock-names = "biu", "ciu";
			fifo-depth = <0x80>;
			status = "disabled";
		};

		mmc_2: mmc@12220000 {
			compatible = "samsung,exynos5250-dw-mshc";
			reg = <0x12220000 0x1000>;
			interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_MMC2>, <&clock CLK_SCLK_MMC2>;
			clock-names = "biu", "ciu";
			fifo-depth = <0x80>;
			status = "disabled";
		};

		pinctrl_0: pinctrl@13400000 {
			compatible = "samsung,exynos5410-pinctrl";
			reg = <0x13400000 0x1000>;
			interrupts = <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>;

			wakeup-interrupt-controller {
				compatible = "samsung,exynos4210-wakeup-eint";
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		pinctrl_1: pinctrl@14000000 {
			compatible = "samsung,exynos5410-pinctrl";
			reg = <0x14000000 0x1000>;
			interrupts = <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>;
		};

		pinctrl_2: pinctrl@10d10000 {
			compatible = "samsung,exynos5410-pinctrl";
			reg = <0x10d10000 0x1000>;
			interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
		};

		pinctrl_3: pinctrl@3860000 {
			compatible = "samsung,exynos5410-pinctrl";
			reg = <0x03860000 0x1000>;
			interrupts = <GIC_SPI 47 IRQ_TYPE_LEVEL_HIGH>;
		};

		pdma0: dma-controller@121a0000 {
			compatible = "arm,pl330", "arm,primecell";
			reg = <0x121a0000 0x1000>;
			interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_PDMA0>;
			clock-names = "apb_pclk";
			#dma-cells = <1>;
		};

		pdma1: dma-controller@121b0000 {
			compatible = "arm,pl330", "arm,primecell";
			reg = <0x121b0000 0x1000>;
			interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_PDMA1>;
			clock-names = "apb_pclk";
			#dma-cells = <1>;
		};

		audi2s0: i2s@3830000 {
			compatible = "samsung,exynos5420-i2s";
			reg = <0x03830000 0x100>;
			dmas = <&pdma0 10>,
				<&pdma0 9>,
				<&pdma0 8>;
			dma-names = "tx", "rx", "tx-sec";
			clocks = <&clock_audss EXYNOS_I2S_BUS>,
				<&clock_audss EXYNOS_I2S_BUS>,
				<&clock_audss EXYNOS_SCLK_I2S>;
			clock-names = "iis", "i2s_opclk0", "i2s_opclk1";
			#clock-cells = <1>;
			clock-output-names = "i2s_cdclk0";
			#sound-dai-cells = <1>;
			samsung,idma-addr = <0x03000000>;
			pinctrl-names = "default";
			pinctrl-0 = <&audi2s0_bus>;
			status = "disabled";
		};
	};

	thermal-zones {
		cpu0_thermal: cpu0-thermal {
			thermal-sensors = <&tmu_cpu0>;
			#include "exynos5420-trip-points.dtsi"
		};
		cpu1_thermal: cpu1-thermal {
			thermal-sensors = <&tmu_cpu1>;
			#include "exynos5420-trip-points.dtsi"
		};
		cpu2_thermal: cpu2-thermal {
			thermal-sensors = <&tmu_cpu2>;
			#include "exynos5420-trip-points.dtsi"
		};
		cpu3_thermal: cpu3-thermal {
			thermal-sensors = <&tmu_cpu3>;
			#include "exynos5420-trip-points.dtsi"
		};
	};
};

&adc {
	clocks = <&clock CLK_TSADC>;
	clock-names = "adc";
	samsung,syscon-phandle = <&pmu_system_controller>;
};

&arm_a15_pmu {
	interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
	status = "okay";
};

&i2c_0 {
	clocks = <&clock CLK_I2C0>;
	clock-names = "i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c0_bus>;
};

&i2c_1 {
	clocks = <&clock CLK_I2C1>;
	clock-names = "i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_bus>;
};

&i2c_2 {
	clocks = <&clock CLK_I2C2>;
	clock-names = "i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_bus>;
};

&i2c_3 {
	clocks = <&clock CLK_I2C3>;
	clock-names = "i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_bus>;
};

&hsi2c_4 {
	clocks = <&clock CLK_USI0>;
	clock-names = "hsi2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4_hs_bus>;
};

&hsi2c_5 {
	clocks = <&clock CLK_USI1>;
	clock-names = "hsi2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c5_hs_bus>;
};

&hsi2c_6 {
	clocks = <&clock CLK_USI2>;
	clock-names = "hsi2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c6_hs_bus>;
};

&hsi2c_7 {
	clocks = <&clock CLK_USI3>;
	clock-names = "hsi2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c7_hs_bus>;
};

&mct {
	clocks = <&fin_pll>, <&clock CLK_MCT>;
	clock-names = "fin_pll", "mct";
};

&prng {
	clocks = <&clock CLK_SSS>;
	clock-names = "secss";
};

&pwm {
	clocks = <&clock CLK_PWM>;
	clock-names = "timers";
};

&rtc {
	clocks = <&clock CLK_RTC>;
	clock-names = "rtc";
	status = "disabled";
};

&serial_0 {
	clocks = <&clock CLK_UART0>, <&clock CLK_SCLK_UART0>;
	clock-names = "uart", "clk_uart_baud0";
	dmas = <&pdma0 13>, <&pdma0 14>;
	dma-names = "rx", "tx";
};

&serial_1 {
	clocks = <&clock CLK_UART1>, <&clock CLK_SCLK_UART1>;
	clock-names = "uart", "clk_uart_baud0";
	dmas = <&pdma1 15>, <&pdma1 16>;
	dma-names = "rx", "tx";
};

&serial_2 {
	clocks = <&clock CLK_UART2>, <&clock CLK_SCLK_UART2>;
	clock-names = "uart", "clk_uart_baud0";
	dmas = <&pdma0 15>, <&pdma0 16>;
	dma-names = "rx", "tx";
};

&serial_3 {
	clocks = <&clock CLK_UART3>, <&clock CLK_SCLK_UART3>;
	clock-names = "uart", "clk_uart_baud0";
	dmas = <&pdma1 17>, <&pdma1 18>;
	dma-names = "rx", "tx";
};

&sss {
	clocks = <&clock CLK_SSS>;
	clock-names = "secss";
};

&sromc {
	#address-cells = <2>;
	#size-cells = <1>;
	ranges = <0 0 0x04000000 0x20000
		  1 0 0x05000000 0x20000
		  2 0 0x06000000 0x20000
		  3 0 0x07000000 0x20000>;
};

&trng {
	clocks = <&clock CLK_SSS>;
	clock-names = "secss";
};

&usbdrd3_0 {
	clocks = <&clock CLK_USBD300>;
	clock-names = "usbdrd30";
	pinctrl-names = "default";
	pinctrl-0 = <&usb3_0_oc>, <&usb3_0_vbusctrl>;
};

&usbdrd_phy0 {
	clocks = <&clock CLK_USBD300>, <&clock CLK_SCLK_USBPHY300>;
	clock-names = "phy", "ref";
	samsung,pmu-syscon = <&pmu_system_controller>;
};

&usbdrd3_1 {
	clocks = <&clock CLK_USBD301>;
	clock-names = "usbdrd30";
	pinctrl-names = "default";
	pinctrl-0 = <&usb3_1_oc>, <&usb3_1_vbusctrl>;
};

&usbdrd_dwc3_1 {
	interrupts = <GIC_SPI 200 IRQ_TYPE_LEVEL_HIGH>;
};

&usbdrd_phy1 {
	clocks = <&clock CLK_USBD301>, <&clock CLK_SCLK_USBPHY301>;
	clock-names = "phy", "ref";
	samsung,pmu-syscon = <&pmu_system_controller>;
};

&usbhost1 {
	clocks = <&clock CLK_USBH20>;
	clock-names = "usbhost";
};

&usbhost2 {
	clocks = <&clock CLK_USBH20>;
	clock-names = "usbhost";
};

&usb2_phy {
	clocks = <&clock CLK_USBH20>, <&clock CLK_SCLK_USBPHY300>;
	clock-names = "phy", "ref";
	samsung,sysreg-phandle = <&sysreg_system_controller>;
	samsung,pmureg-phandle = <&pmu_system_controller>;
};

&watchdog {
	clocks = <&clock CLK_WDT>;
	clock-names = "watchdog";
	samsung,syscon-phandle = <&pmu_system_controller>;
};

#include "exynos5410-pinctrl.dtsi"
#include "exynos-syscon-restart.dtsi"
