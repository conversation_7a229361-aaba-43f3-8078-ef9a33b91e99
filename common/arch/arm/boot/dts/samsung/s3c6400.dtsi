// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's S3C6400 SoC device tree source
 *
 * Copyright (c) 2013 <PERSON><PERSON> <<EMAIL>>
 *
 * Samsung's S3C6400 SoC device nodes are listed in this file. S3C6400
 * based board files can include this file and provide values for board specific
 * bindings.
 *
 * Note: This file does not include device nodes for all the controllers in
 * S3C6400 SoC. As device tree coverage for S3C6400 increases, additional
 * nodes can be added to this file.
 */

#include "s3c64xx.dtsi"

/ {
	compatible = "samsung,s3c6400";
};

&vic0 {
	valid-mask = <0xfffffe1f>;
	valid-wakeup-mask = <0x00200004>;
};

&vic1 {
	valid-mask = <0xffffffff>;
	valid-wakeup-mask = <0x53020000>;
};

&soc {
	clocks: clock-controller@7e00f000 {
		compatible = "samsung,s3c6400-clock";
		reg = <0x7e00f000 0x1000>;
		#clock-cells = <1>;
	};
};
