// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's S5PV210 SoC device tree source
 *
 * Copyright (c) 2013-2014 Samsung Electronics, Co. Ltd.
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 * <PERSON><PERSON> <<EMAIL>>
 *
 * Board device tree source for YIC System SMDV210 board.
 *
 * NOTE: This file is completely based on original board file for mach-smdkv210
 * available in Linux 3.15 and intends to provide equivalent level of hardware
 * support. Due to lack of hardware, _no_ testing has been performed.
 */

/dts-v1/;
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/input/input.h>
#include "s5pv210.dtsi"

/ {
	model = "YIC System SMDKV210 based on S5PV210";
	compatible = "yic,smdkv210", "samsung,s5pv210";

	chosen {
		bootargs = "console=ttySAC0,115200n8 root=/dev/mmcblk0p1 rw rootwait ignore_loglevel earlyprintk";
	};

	memory@20000000 {
		device_type = "memory";
		reg = <0x20000000 0x40000000>;
	};

	pmic_ap_clk: clock-0 {
		/* Workaround for missing PMIC and its clock */
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <32768>;
	};

	ethernet@a8000000 {
		compatible = "davicom,dm9000";
		reg = <0xa8000000 0x2>, <0xa8000002 0x2>;
		interrupt-parent = <&gph1>;
		interrupts = <1 IRQ_TYPE_LEVEL_HIGH>;
		local-mac-address = [00 00 de ad be ef];
		davicom,no-eeprom;
	};

	backlight {
		compatible = "pwm-backlight";
		pwms = <&pwm 3 5000000 0>;
		brightness-levels = <0 4 8 16 32 64 128 255>;
		default-brightness-level = <6>;
		pinctrl-names = "default";
		pinctrl-0 = <&pwm3_out>;
		power-supply = <&dc5v_reg>;
	};

	dc5v_reg: regulator-0 {
		compatible = "regulator-fixed";
		regulator-name = "DC5V";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};
};

&xusbxti {
	clock-frequency = <24000000>;
};

&keypad {
	linux,input-no-autorepeat;
	wakeup-source;
	samsung,keypad-num-rows = <8>;
	samsung,keypad-num-columns = <8>;
	pinctrl-names = "default";
	pinctrl-0 = <&keypad_row0>, <&keypad_row1>, <&keypad_row2>,
			<&keypad_row3>, <&keypad_row4>, <&keypad_row5>,
			<&keypad_row6>, <&keypad_row7>,
			<&keypad_col0>, <&keypad_col1>, <&keypad_col2>,
			<&keypad_col3>, <&keypad_col4>, <&keypad_col5>,
			<&keypad_col6>, <&keypad_col7>;
	status = "okay";

	key-1 {
		keypad,row = <0>;
		keypad,column = <3>;
		linux,code = <KEY_1>;
	};

	key-2 {
		keypad,row = <0>;
		keypad,column = <4>;
		linux,code = <KEY_2>;
	};

	key-3 {
		keypad,row = <0>;
		keypad,column = <5>;
		linux,code = <KEY_3>;
	};

	key-4 {
		keypad,row = <0>;
		keypad,column = <6>;
		linux,code = <KEY_4>;
	};

	key-5 {
		keypad,row = <0
		>;
		keypad,column = <7>;
		linux,code = <KEY_5>;
	};

	key-6 {
		keypad,row = <1>;
		keypad,column = <3>;
		linux,code = <KEY_A>;
	};
	key-7 {
		keypad,row = <1>;
		keypad,column = <4>;
		linux,code = <KEY_B>;
	};

	key-8 {
		keypad,row = <1>;
		keypad,column = <5>;
		linux,code = <KEY_C>;
	};

	key-9 {
		keypad,row = <1>;
		keypad,column = <6>;
		linux,code = <KEY_D>;
	};

	key-10 {
		keypad,row = <1>;
		keypad,column = <7>;
		linux,code = <KEY_E>;
	};
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&rtc {
	status = "okay";
	clocks = <&clocks CLK_RTC>, <&pmic_ap_clk>;
	clock-names = "rtc", "rtc_src";
};

&sdhci0 {
	bus-width = <4>;
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_cd &sd0_bus1 &sd0_bus4>;
	pinctrl-names = "default";
	status = "okay";
};

&sdhci1 {
	bus-width = <4>;
	pinctrl-0 = <&sd1_clk &sd1_cmd &sd1_cd &sd1_bus1 &sd1_bus4>;
	pinctrl-names = "default";
	status = "okay";
};

&sdhci2 {
	bus-width = <4>;
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_cd &sd2_bus1 &sd2_bus4>;
	pinctrl-names = "default";
	status = "okay";
};

&sdhci3 {
	bus-width = <4>;
	pinctrl-0 = <&sd3_clk &sd3_cmd &sd3_cd &sd3_bus1 &sd3_bus4>;
	pinctrl-names = "default";
	status = "okay";
};

&hsotg {
	dr_mode = "peripheral";
	status = "okay";
};

&usbphy {
	status = "okay";
};

&fimd {
	pinctrl-0 = <&lcd_clk &lcd_data24>;
	pinctrl-names = "default";
	status = "okay";

	display-timings {
		native-mode = <&timing0>;

		timing0: timing {
			/* 800x480@60Hz */
			clock-frequency = <24373920>;
			hactive = <800>;
			vactive = <480>;
			hfront-porch = <8>;
			hback-porch = <13>;
			hsync-len = <3>;
			vback-porch = <7>;
			vfront-porch = <5>;
			vsync-len = <1>;
			hsync-active = <0>;
			vsync-active = <0>;
			de-active = <1>;
			pixelclk-active = <1>;
		};
	};
};

&pwm {
	samsung,pwm-outputs = <3>;
};

&i2c0 {
	status = "okay";

	audio-codec@1b {
		compatible = "wlf,wm8580";
		reg = <0x1b>;
	};

	eeprom@50 {
		compatible = "atmel,24c08";
		reg = <0x50>;
	};
};

&i2s0 {
	status = "okay";
};
