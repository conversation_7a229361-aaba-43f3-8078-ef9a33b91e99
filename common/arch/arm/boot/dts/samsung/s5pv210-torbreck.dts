// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's S5PV210 SoC device tree source
 *
 * Copyright (c) 2013-2014 Samsung Electronics, Co. Ltd.
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 * <PERSON><PERSON> <<EMAIL>>
 *
 * Board device tree source for Torbreck board.
 *
 * NOTE: This file is completely based on original board file for mach-torbreck
 * available in Linux 3.15 and intends to provide equivalent level of hardware
 * support. Due to lack of hardware, _no_ testing has been performed.
 */

/dts-v1/;
#include <dt-bindings/input/input.h>
#include "s5pv210.dtsi"

/ {
	model = "aESOP Torbreck based on S5PV210";
	compatible = "aesop,torbreck", "samsung,s5pv210";

	chosen {
		bootargs = "console=ttySAC0,115200n8 root=/dev/mmcblk0p1 rw rootwait ignore_loglevel earlyprintk";
	};

	memory@20000000 {
		device_type = "memory";
		reg = <0x20000000 0x20000000>;
	};

	pmic_ap_clk: clock-0 {
		/* Workaround for missing PMIC and its clock */
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <32768>;
	};
};

&xusbxti {
	clock-frequency = <24000000>;
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&rtc {
	status = "okay";
	clocks = <&clocks CLK_RTC>, <&pmic_ap_clk>;
	clock-names = "rtc", "rtc_src";
};

&sdhci0 {
	bus-width = <4>;
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_cd &sd0_bus1 &sd0_bus4>;
	pinctrl-names = "default";
	status = "okay";
};

&sdhci1 {
	bus-width = <4>;
	pinctrl-0 = <&sd1_clk &sd1_cmd &sd1_cd &sd1_bus1 &sd1_bus4>;
	pinctrl-names = "default";
	status = "okay";
};

&sdhci2 {
	bus-width = <4>;
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_bus1 &sd2_bus4>;
	pinctrl-names = "default";
	status = "okay";
};

&sdhci3 {
	bus-width = <4>;
	pinctrl-0 = <&sd3_clk &sd3_cmd &sd3_cd &sd3_bus1 &sd3_bus4>;
	pinctrl-names = "default";
	status = "okay";
};

&i2s0 {
	status = "okay";
};
