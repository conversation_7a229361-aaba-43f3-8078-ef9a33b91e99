// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos54xx SoC series common device tree source
 *
 * Copyright (c) 2012-2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 * Copyright (c) 2016 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 *
 * Device nodes common for Samsung Exynos5410/5420/5422/5800. Specific
 * Exynos 54xx SoCs should include this file and customize it further
 * (e.g. with clocks).
 */

#include "exynos5.dtsi"

/ {
	compatible = "samsung,exynos5";

	aliases {
		i2c4 = &hsi2c_4;
		i2c5 = &hsi2c_5;
		i2c6 = &hsi2c_6;
		i2c7 = &hsi2c_7;
		usbdrdphy0 = &usbdrd_phy0;
		usbdrdphy1 = &usbdrd_phy1;
	};

	arm_a7_pmu: arm-a7-pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupt-parent = <&gic>;
		interrupts = <GIC_SPI 160 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 161 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 162 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 163 IRQ_TYPE_LEVEL_HIGH>;
		status = "disabled";
	};

	arm_a15_pmu: arm-a15-pmu {
		compatible = "arm,cortex-a15-pmu";
		interrupt-parent = <&combiner>;
		interrupts = <1 2>,
			     <7 0>,
			     <16 6>,
			     <19 2>;
		status = "disabled";
	};

	timer: timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
		clock-frequency = <24000000>;
	};

	soc: soc {
		sram@2020000 {
			compatible = "mmio-sram";
			reg = <0x02020000 0x54000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x02020000 0x54000>;

			smp-sram@0 {
				compatible = "samsung,exynos4210-sysram";
				reg = <0x0 0x1000>;
			};

			smp-sram@53000 {
				compatible = "samsung,exynos4210-sysram-ns";
				reg = <0x53000 0x1000>;
			};
		};

		mct: timer@101c0000 {
			compatible = "samsung,exynos5420-mct",
				     "samsung,exynos4210-mct";
			reg = <0x101c0000 0xb00>;
			interrupts-extended = <&combiner 23 3>,
					      <&combiner 23 4>,
					      <&combiner 25 2>,
					      <&combiner 25 3>,
					      <&gic GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
					      <&gic GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>,
					      <&gic GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>,
					      <&gic GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>,
					      <&gic GIC_SPI 128 IRQ_TYPE_LEVEL_HIGH>,
					      <&gic GIC_SPI 129 IRQ_TYPE_LEVEL_HIGH>,
					      <&gic GIC_SPI 130 IRQ_TYPE_LEVEL_HIGH>,
					      <&gic GIC_SPI 131 IRQ_TYPE_LEVEL_HIGH>;
		};

		watchdog: watchdog@101d0000 {
			compatible = "samsung,exynos5420-wdt";
			reg = <0x101d0000 0x100>;
			interrupts = <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
		};

		adc: adc@12d10000 {
			compatible = "samsung,exynos-adc-v2";
			reg = <0x12d10000 0x100>;
			interrupts = <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>;
			#io-channel-cells = <1>;
			status = "disabled";
		};

		/* i2c_0-3 are defined in exynos5.dtsi */
		hsi2c_4: i2c@12ca0000 {
			compatible = "samsung,exynos5250-hsi2c";
			reg = <0x12ca0000 0x1000>;
			interrupts = <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		hsi2c_5: i2c@12cb0000 {
			compatible = "samsung,exynos5250-hsi2c";
			reg = <0x12cb0000 0x1000>;
			interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		hsi2c_6: i2c@12cc0000 {
			compatible = "samsung,exynos5250-hsi2c";
			reg = <0x12cc0000 0x1000>;
			interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		hsi2c_7: i2c@12cd0000 {
			compatible = "samsung,exynos5250-hsi2c";
			reg = <0x12cd0000 0x1000>;
			interrupts = <GIC_SPI 63 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		usbdrd3_0: usb@12000000 {
			compatible = "samsung,exynos5250-dwusb3";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x12000000 0x10000>;

			usbdrd_dwc3_0: usb@0 {
				compatible = "snps,dwc3";
				reg = <0x0 0x10000>;
				interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
				phys = <&usbdrd_phy0 0>, <&usbdrd_phy0 1>;
				phy-names = "usb2-phy", "usb3-phy";
				snps,dis_u3_susphy_quirk;
			};
		};

		usbdrd_phy0: phy@12100000 {
			compatible = "samsung,exynos5420-usbdrd-phy";
			reg = <0x12100000 0x100>;
			#phy-cells = <1>;
		};

		usbdrd3_1: usb@12400000 {
			compatible = "samsung,exynos5250-dwusb3";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x12400000 0x10000>;

			usbdrd_dwc3_1: usb@0 {
				compatible = "snps,dwc3";
				reg = <0x0 0x10000>;
				phys = <&usbdrd_phy1 0>, <&usbdrd_phy1 1>;
				phy-names = "usb2-phy", "usb3-phy";
				snps,dis_u3_susphy_quirk;
			};
		};

		usbdrd_phy1: phy@12500000 {
			compatible = "samsung,exynos5420-usbdrd-phy";
			reg = <0x12500000 0x100>;
			#phy-cells = <1>;
		};

		usbhost2: usb@12110000 {
			compatible = "samsung,exynos4210-ehci";
			reg = <0x12110000 0x100>;
			interrupts = <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
			phys = <&usb2_phy 0>;
			phy-names = "host";
		};

		usbhost1: usb@12120000 {
			compatible = "samsung,exynos4210-ohci";
			reg = <0x12120000 0x100>;
			interrupts = <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
			phys = <&usb2_phy 0>;
			phy-names = "host";
		};

		usb2_phy: phy@12130000 {
			compatible = "samsung,exynos5420-usb2-phy";
			reg = <0x12130000 0x100>;
			#phy-cells = <1>;
		};
	};
};
