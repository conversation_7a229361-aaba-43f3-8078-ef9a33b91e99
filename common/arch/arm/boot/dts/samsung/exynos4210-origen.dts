// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos4210 based Origen board device tree source
 *
 * Copyright (c) 2010-2011 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 * Copyright (c) 2010-2011 Linaro Ltd.
 *		www.linaro.org
 *
 * Device tree source file for Insignal's Origen board which is based on
 * Samsung's Exynos4210 SoC.
 */

/dts-v1/;
#include "exynos4210.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/leds/common.h>
#include "exynos-mfc-reserved-memory.dtsi"

/ {
	model = "Insignal Origen evaluation board based on Exynos4210";
	compatible = "insignal,origen", "samsung,exynos4210", "samsung,exynos4";

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x10000000
		       0x50000000 0x10000000
		       0x60000000 0x10000000
		       0x70000000 0x10000000>;
	};

	aliases {
		mmc0 = &sdhci_0;
		mmc1 = &sdhci_2;
	};

	chosen {
		bootargs = "root=/dev/ram0 rw ramdisk=8192 initrd=0x41000000,8M init=/linuxrc";
		stdout-path = "serial2:115200n8";
	};

	mmc_reg: voltage-regulator {
		compatible = "regulator-fixed";
		regulator-name = "VMEM_VDD_2.8V";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		gpio = <&gpx1 1 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-up {
			label = "Up";
			gpios = <&gpx2 0 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_UP>;
			wakeup-source;
		};

		key-down {
			label = "Down";
			gpios = <&gpx2 1 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_DOWN>;
			wakeup-source;
		};

		key-back {
			label = "Back";
			gpios = <&gpx1 7 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_BACK>;
			wakeup-source;
		};

		key-home {
			label = "Home";
			gpios = <&gpx1 6 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_HOME>;
			wakeup-source;
		};

		key-menu {
			label = "Menu";
			gpios = <&gpx1 5 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_MENU>;
			wakeup-source;
		};
	};

	leds {
		compatible = "gpio-leds";
		led-status {
			gpios = <&gpx1 3 GPIO_ACTIVE_LOW>;
			function = LED_FUNCTION_HEARTBEAT;
			linux,default-trigger = "heartbeat";
		};
	};

	fixed-rate-clocks {
		xxti {
			compatible = "samsung,clock-xxti";
			clock-frequency = <0>;
		};

		xusbxti {
			compatible = "samsung,clock-xusbxti";
			clock-frequency = <24000000>;
		};

		pmic_ap_clk: pmic-ap-clk {
			/* Workaround for missing clock on max8997 PMIC */
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <32768>;
		};
	};

	display-timings {
		native-mode = <&timing0>;
		timing0: timing {
			clock-frequency = <47500000>;
			hactive = <1024>;
			vactive = <600>;
			hfront-porch = <64>;
			hback-porch = <16>;
			hsync-len = <48>;
			vback-porch = <64>;
			vfront-porch = <16>;
			vsync-len = <3>;
		};
	};
};

&cpu0 {
	cpu0-supply = <&buck1_reg>;
};

&cpu_thermal {
	cooling-maps {
		map0 {
			/* Corresponds to 800MHz */
			cooling-device = <&cpu0 2 2>;
		};
		map1 {
			/* Corresponds to 200MHz */
			cooling-device = <&cpu0 4 4>;
		};
	};
};

&exynos_usbphy {
	status = "okay";
};

&fimd {
	pinctrl-0 = <&lcd_en &lcd_clk &lcd_data24 &pwm0_out>;
	pinctrl-names = "default";
	status = "okay";
};

&gpu {
	mali-supply = <&buck3_reg>;
	status = "okay";
};

&hsotg {
	vusb_d-supply = <&ldo3_reg>;
	vusb_a-supply = <&ldo8_reg>;
	dr_mode = "peripheral";
	status = "okay";
};

&i2c_0 {
	status = "okay";
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <20000>;
	pinctrl-0 = <&i2c0_bus>;
	pinctrl-names = "default";

	pmic@66 {
		compatible = "maxim,max8997-pmic";
		reg = <0x66>;
		interrupt-parent = <&gpx0>;
		interrupts = <4 IRQ_TYPE_NONE>, <3 IRQ_TYPE_NONE>;
		pinctrl-names = "default";
		pinctrl-0 = <&max8997_irq>;

		max8997,pmic-buck1-dvs-voltage = <1350000>;
		max8997,pmic-buck2-dvs-voltage = <1100000>;
		max8997,pmic-buck5-dvs-voltage = <1200000>;

		regulators {
			ldo1_reg: LDO1 {
				regulator-name = "VDD_ABB_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
			};

			ldo2_reg: LDO2 {
				regulator-name = "VDD_ALIVE_1.1V";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
			};

			ldo3_reg: LDO3 {
				regulator-name = "VMIPI_1.1V";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
			};

			ldo4_reg: LDO4 {
				regulator-name = "VDD_RTC_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo6_reg: LDO6 {
				regulator-name = "VMIPI_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo7_reg: LDO7 {
				regulator-name = "VDD_AUD_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo8_reg: LDO8 {
				regulator-name = "VADC_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
			};

			ldo9_reg: LDO9 {
				regulator-name = "DVDD_SWB_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
			};

			ldo10_reg: LDO10 {
				regulator-name = "VDD_PLL_1.1V";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
			};

			ldo11_reg: LDO11 {
				regulator-name = "VDD_AUD_3V";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
			};

			ldo14_reg: LDO14 {
				regulator-name = "AVDD18_SWB_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo17_reg: LDO17 {
				regulator-name = "VDD_SWB_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			ldo21_reg: LDO21 {
				regulator-name = "VDD_MIF_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			buck1_reg: BUCK1 {
				regulator-name = "VDD_ARM_1.2V";
				regulator-min-microvolt = <950000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck2_reg: BUCK2 {
				regulator-name = "VDD_INT_1.1V";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck3_reg: BUCK3 {
				regulator-name = "VDD_G3D_1.1V";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <1100000>;
			};

			buck5_reg: BUCK5 {
				regulator-name = "VDDQ_M1M2_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			buck7_reg: BUCK7 {
				regulator-name = "VDD_LCD_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-boot-on;
				regulator-always-on;
			};

			EN32KHZ_AP {
				regulator-name = "EN32KHZ_AP";
				regulator-always-on;
			};
		};
	};
};

&pinctrl_1 {
	max8997_irq: max8997-irq-pins {
		samsung,pins = "gpx0-3", "gpx0-4";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};
};

&sdhci_0 {
	bus-width = <4>;
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_bus4 &sd0_cd>;
	pinctrl-names = "default";
	vmmc-supply = <&mmc_reg>;
	status = "okay";
};

&sdhci_2 {
	bus-width = <4>;
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_bus4 &sd2_cd>;
	pinctrl-names = "default";
	vmmc-supply = <&mmc_reg>;
	status = "okay";
};

&serial_0 {
	status = "okay";
};

&serial_1 {
	status = "okay";
};

&serial_2 {
	status = "okay";
};

&serial_3 {
	status = "okay";
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&pmic_ap_clk>;
	clock-names = "rtc", "rtc_src";
};

&tmu {
	status = "okay";
};
