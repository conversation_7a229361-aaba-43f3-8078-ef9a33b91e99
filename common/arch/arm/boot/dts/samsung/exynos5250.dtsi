// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung Exynos5250 SoC device tree source
 *
 * Copyright (c) 2012 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Samsung Exynos5250 SoC device nodes are listed in this file.
 * Exynos5250 based board files can include this file and provide
 * values for board specific bindings.
 *
 * Note: This file does not include device nodes for all the controllers in
 * Exynos5250 SoC. As device tree coverage for Exynos5250 increases,
 * additional nodes can be added to this file.
 */

#include <dt-bindings/clock/exynos5250.h>
#include "exynos5.dtsi"
#include "exynos4-cpu-thermal.dtsi"
#include <dt-bindings/clock/exynos-audss-clk.h>

/ {
	compatible = "samsung,exynos5250", "samsung,exynos5";

	aliases {
		spi0 = &spi_0;
		spi1 = &spi_1;
		spi2 = &spi_2;
		gsc0 = &gsc_0;
		gsc1 = &gsc_1;
		gsc2 = &gsc_2;
		gsc3 = &gsc_3;
		i2c4 = &i2c_4;
		i2c5 = &i2c_5;
		i2c6 = &i2c_6;
		i2c7 = &i2c_7;
		i2c8 = &i2c_8;
		i2c9 = &i2c_9;
		pinctrl0 = &pinctrl_0;
		pinctrl1 = &pinctrl_1;
		pinctrl2 = &pinctrl_2;
		pinctrl3 = &pinctrl_3;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu-map {
			cluster0 {
				core0 {
					cpu = <&cpu0>;
				};
				core1 {
					cpu = <&cpu1>;
				};
			};
		};

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <0>;
			clocks = <&clock CLK_ARM_CLK>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
		};
		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <1>;
			clocks = <&clock CLK_ARM_CLK>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
		};
	};

	cpu0_opp_table: opp-table-0 {
		compatible = "operating-points-v2";
		opp-shared;

		opp-200000000 {
			opp-hz = /bits/ 64 <200000000>;
			opp-microvolt = <925000>;
			clock-latency-ns = <140000>;
		};
		opp-300000000 {
			opp-hz = /bits/ 64 <300000000>;
			opp-microvolt = <937500>;
			clock-latency-ns = <140000>;
		};
		opp-400000000 {
			opp-hz = /bits/ 64 <400000000>;
			opp-microvolt = <950000>;
			clock-latency-ns = <140000>;
		};
		opp-500000000 {
			opp-hz = /bits/ 64 <500000000>;
			opp-microvolt = <975000>;
			clock-latency-ns = <140000>;
		};
		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <1000000>;
			clock-latency-ns = <140000>;
		};
		opp-700000000 {
			opp-hz = /bits/ 64 <700000000>;
			opp-microvolt = <1012500>;
			clock-latency-ns = <140000>;
		};
		opp-800000000 {
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt = <1025000>;
			clock-latency-ns = <140000>;
		};
		opp-900000000 {
			opp-hz = /bits/ 64 <900000000>;
			opp-microvolt = <1050000>;
			clock-latency-ns = <140000>;
		};
		opp-1000000000 {
			opp-hz = /bits/ 64 <1000000000>;
			opp-microvolt = <1075000>;
			clock-latency-ns = <140000>;
			opp-suspend;
		};
		opp-1100000000 {
			opp-hz = /bits/ 64 <1100000000>;
			opp-microvolt = <1100000>;
			clock-latency-ns = <140000>;
		};
		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <1125000>;
			clock-latency-ns = <140000>;
		};
		opp-1300000000 {
			opp-hz = /bits/ 64 <1300000000>;
			opp-microvolt = <1150000>;
			clock-latency-ns = <140000>;
		};
		opp-1400000000 {
			opp-hz = /bits/ 64 <1400000000>;
			opp-microvolt = <1200000>;
			clock-latency-ns = <140000>;
		};
		opp-1500000000 {
			opp-hz = /bits/ 64 <1500000000>;
			opp-microvolt = <1225000>;
			clock-latency-ns = <140000>;
		};
		opp-1600000000 {
			opp-hz = /bits/ 64 <1600000000>;
			opp-microvolt = <1250000>;
			clock-latency-ns = <140000>;
		};
		opp-1700000000 {
			opp-hz = /bits/ 64 <1700000000>;
			opp-microvolt = <1300000>;
			clock-latency-ns = <140000>;
		};
	};

	pmu {
		compatible = "arm,cortex-a15-pmu";
		interrupt-parent = <&combiner>;
		interrupts = <1 2>, <22 4>;
	};

	soc: soc {
		sram@2020000 {
			compatible = "mmio-sram";
			reg = <0x02020000 0x30000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x02020000 0x30000>;

			smp-sram@0 {
				compatible = "samsung,exynos4210-sysram";
				reg = <0x0 0x1000>;
			};

			smp-sram@2f000 {
				compatible = "samsung,exynos4210-sysram-ns";
				reg = <0x2f000 0x1000>;
			};
		};

		pd_gsc: power-domain@10044000 {
			compatible = "samsung,exynos4210-pd";
			reg = <0x10044000 0x20>;
			#power-domain-cells = <0>;
			label = "GSC";
		};

		pd_mfc: power-domain@10044040 {
			compatible = "samsung,exynos4210-pd";
			reg = <0x10044040 0x20>;
			#power-domain-cells = <0>;
			label = "MFC";
		};

		pd_g3d: power-domain@10044060 {
			compatible = "samsung,exynos4210-pd";
			reg = <0x10044060 0x20>;
			#power-domain-cells = <0>;
			label = "G3D";
		};

		pd_disp1: power-domain@100440a0 {
			compatible = "samsung,exynos4210-pd";
			reg = <0x100440a0 0x20>;
			#power-domain-cells = <0>;
			label = "DISP1";
		};

		pd_mau: power-domain@100440c0 {
			compatible = "samsung,exynos4210-pd";
			reg = <0x100440c0 0x20>;
			#power-domain-cells = <0>;
			label = "MAU";
		};

		clock: clock-controller@10010000 {
			compatible = "samsung,exynos5250-clock";
			reg = <0x10010000 0x30000>;
			#clock-cells = <1>;
		};

		clock_audss: audss-clock-controller@3810000 {
			compatible = "samsung,exynos5250-audss-clock";
			reg = <0x03810000 0x0c>;
			#clock-cells = <1>;
			clocks = <&clock CLK_FIN_PLL>, <&clock CLK_FOUT_EPLL>,
				 <&clock CLK_SCLK_AUDIO0>, <&clock CLK_DIV_PCM0>;
			clock-names = "pll_ref", "pll_in", "sclk_audio", "sclk_pcm_in";
			power-domains = <&pd_mau>;
		};

		timer@101c0000 {
			compatible = "samsung,exynos5250-mct",
				     "samsung,exynos4210-mct";
			reg = <0x101c0000 0x800>;
			clocks = <&clock CLK_FIN_PLL>, <&clock CLK_MCT>;
			clock-names = "fin_pll", "mct";
			interrupts-extended = <&combiner 23 3>,
					      <&combiner 23 4>,
					      <&combiner 25 2>,
					      <&combiner 25 3>,
					      <&gic GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
					      <&gic GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>;
		};

		pinctrl_0: pinctrl@11400000 {
			compatible = "samsung,exynos5250-pinctrl";
			reg = <0x11400000 0x1000>;
			interrupts = <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>;

			wakup_eint: wakeup-interrupt-controller {
				compatible = "samsung,exynos4210-wakeup-eint";
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		pinctrl_1: pinctrl@13400000 {
			compatible = "samsung,exynos5250-pinctrl";
			reg = <0x13400000 0x1000>;
			interrupts = <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>;
		};

		pinctrl_2: pinctrl@10d10000 {
			compatible = "samsung,exynos5250-pinctrl";
			reg = <0x10d10000 0x1000>;
			interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
		};

		pinctrl_3: pinctrl@3860000 {
			compatible = "samsung,exynos5250-pinctrl";
			reg = <0x03860000 0x1000>;
			interrupts = <GIC_SPI 47 IRQ_TYPE_LEVEL_HIGH>;
			power-domains = <&pd_mau>;
		};

		pmu_system_controller: system-controller@10040000 {
			compatible = "samsung,exynos5250-pmu", "simple-mfd", "syscon";
			reg = <0x10040000 0x5000>;
			clock-names = "clkout16";
			clocks = <&clock CLK_FIN_PLL>;
			#clock-cells = <1>;
			interrupt-controller;
			#interrupt-cells = <3>;
			interrupt-parent = <&gic>;

			dp_phy: dp-phy {
				compatible = "samsung,exynos5250-dp-video-phy";
				#phy-cells = <0>;
			};

			mipi_phy: mipi-phy {
				compatible = "samsung,s5pv210-mipi-video-phy";
				#phy-cells = <1>;
			};
		};

		watchdog@101d0000 {
			compatible = "samsung,exynos5250-wdt";
			reg = <0x101d0000 0x100>;
			interrupts = <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_WDT>;
			clock-names = "watchdog";
			samsung,syscon-phandle = <&pmu_system_controller>;
		};

		mfc: codec@11000000 {
			compatible = "samsung,mfc-v6";
			reg = <0x11000000 0x10000>;
			interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
			power-domains = <&pd_mfc>;
			clocks = <&clock CLK_MFC>;
			clock-names = "mfc";
			iommus = <&sysmmu_mfc_l>, <&sysmmu_mfc_r>;
			iommu-names = "left", "right";
		};

		rotator: rotator@11c00000 {
			compatible = "samsung,exynos5250-rotator";
			reg = <0x11c00000 0x64>;
			interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_ROTATOR>;
			clock-names = "rotator";
			iommus = <&sysmmu_rotator>;
		};

		mali: gpu@11800000 {
			compatible = "samsung,exynos5250-mali", "arm,mali-t604";
			reg = <0x11800000 0x5000>;
			interrupts = <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "job", "mmu", "gpu";
			clocks = <&clock CLK_G3D>;
			clock-names = "core";
			operating-points-v2 = <&gpu_opp_table>;
			power-domains = <&pd_g3d>;
			status = "disabled";

			gpu_opp_table: opp-table {
				compatible = "operating-points-v2";

				opp-100000000 {
					opp-hz = /bits/ 64 <100000000>;
					opp-microvolt = <925000>;
				};
				opp-160000000 {
					opp-hz = /bits/ 64 <160000000>;
					opp-microvolt = <925000>;
				};
				opp-266000000 {
					opp-hz = /bits/ 64 <266000000>;
					opp-microvolt = <1025000>;
				};
				opp-350000000 {
					opp-hz = /bits/ 64 <350000000>;
					opp-microvolt = <1075000>;
				};
				opp-400000000 {
					opp-hz = /bits/ 64 <400000000>;
					opp-microvolt = <1125000>;
				};
				opp-450000000 {
					opp-hz = /bits/ 64 <450000000>;
					opp-microvolt = <1150000>;
				};
				opp-533000000 {
					opp-hz = /bits/ 64 <533000000>;
					opp-microvolt = <1250000>;
				};
			};
		};

		tmu: tmu@10060000 {
			compatible = "samsung,exynos5250-tmu";
			reg = <0x10060000 0x100>;
			interrupts = <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_TMU>;
			clock-names = "tmu_apbif";
			#thermal-sensor-cells = <0>;
		};

		sata: sata@122f0000 {
			compatible = "snps,dwc-ahci";
			reg = <0x122f0000 0x1ff>;
			interrupts = <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_SATA>, <&clock CLK_SCLK_SATA>;
			clock-names = "sata", "pclk";
			phys = <&sata_phy>;
			phy-names = "sata-phy";
			ports-implemented = <0x1>;
			status = "disabled";
		};

		sata_phy: sata-phy@12170000 {
			compatible = "samsung,exynos5250-sata-phy";
			reg = <0x12170000 0x1ff>;
			clocks = <&clock CLK_SATA_PHYCTRL>;
			clock-names = "sata_phyctrl";
			#phy-cells = <0>;
			samsung,syscon-phandle = <&pmu_system_controller>;
			status = "disabled";
		};

		/* i2c_0-3 are defined in exynos5.dtsi */
		i2c_4: i2c@12ca0000 {
			compatible = "samsung,s3c2440-i2c";
			reg = <0x12ca0000 0x100>;
			interrupts = <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_I2C4>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c4_bus>;
			status = "disabled";
		};

		i2c_5: i2c@12cb0000 {
			compatible = "samsung,s3c2440-i2c";
			reg = <0x12cb0000 0x100>;
			interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_I2C5>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c5_bus>;
			status = "disabled";
		};

		i2c_6: i2c@12cc0000 {
			compatible = "samsung,s3c2440-i2c";
			reg = <0x12cc0000 0x100>;
			interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_I2C6>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c6_bus>;
			status = "disabled";
		};

		i2c_7: i2c@12cd0000 {
			compatible = "samsung,s3c2440-i2c";
			reg = <0x12cd0000 0x100>;
			interrupts = <GIC_SPI 63 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_I2C7>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c7_bus>;
			status = "disabled";
		};

		i2c_8: i2c@12ce0000 {
			compatible = "samsung,s3c2440-hdmiphy-i2c";
			reg = <0x12ce0000 0x1000>;
			interrupts = <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_I2C_HDMI>;
			clock-names = "i2c";
			status = "disabled";

			hdmiphy: hdmi-phy@38 {
				compatible = "samsung,exynos4212-hdmiphy";
				reg = <0x38>;
			};
		};

		i2c_9: i2c@121d0000 {
			compatible = "samsung,exynos5-sata-phy-i2c";
			reg = <0x121d0000 0x100>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_SATA_PHYI2C>;
			clock-names = "i2c";
			status = "disabled";

			sata_phy_i2c: sata-phy-i2c@38 {
				compatible = "samsung,exynos-sataphy-i2c";
				reg = <0x38>;
				status = "disabled";
			};
		};

		spi_0: spi@12d20000 {
			compatible = "samsung,exynos4210-spi";
			status = "disabled";
			reg = <0x12d20000 0x100>;
			interrupts = <GIC_SPI 66 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&pdma0 5>, <&pdma0 4>;
			dma-names = "tx", "rx";
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_SPI0>, <&clock CLK_SCLK_SPI0>;
			clock-names = "spi", "spi_busclk0";
			pinctrl-names = "default";
			pinctrl-0 = <&spi0_bus>;
		};

		spi_1: spi@12d30000 {
			compatible = "samsung,exynos4210-spi";
			status = "disabled";
			reg = <0x12d30000 0x100>;
			interrupts = <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&pdma1 5>, <&pdma1 4>;
			dma-names = "tx", "rx";
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_SPI1>, <&clock CLK_SCLK_SPI1>;
			clock-names = "spi", "spi_busclk0";
			pinctrl-names = "default";
			pinctrl-0 = <&spi1_bus>;
		};

		spi_2: spi@12d40000 {
			compatible = "samsung,exynos4210-spi";
			status = "disabled";
			reg = <0x12d40000 0x100>;
			interrupts = <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&pdma0 7>, <&pdma0 6>;
			dma-names = "tx", "rx";
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_SPI2>, <&clock CLK_SCLK_SPI2>;
			clock-names = "spi", "spi_busclk0";
			pinctrl-names = "default";
			pinctrl-0 = <&spi2_bus>;
		};

		mmc_0: mmc@12200000 {
			compatible = "samsung,exynos5250-dw-mshc";
			interrupts = <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x12200000 0x1000>;
			clocks = <&clock CLK_SDMMC0>, <&clock CLK_SCLK_MMC0>;
			clock-names = "biu", "ciu";
			fifo-depth = <0x80>;
			status = "disabled";
		};

		mmc_1: mmc@12210000 {
			compatible = "samsung,exynos5250-dw-mshc";
			interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x12210000 0x1000>;
			clocks = <&clock CLK_SDMMC1>, <&clock CLK_SCLK_MMC1>;
			clock-names = "biu", "ciu";
			fifo-depth = <0x80>;
			status = "disabled";
		};

		mmc_2: mmc@12220000 {
			compatible = "samsung,exynos5250-dw-mshc";
			interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x12220000 0x1000>;
			clocks = <&clock CLK_SDMMC2>, <&clock CLK_SCLK_MMC2>;
			clock-names = "biu", "ciu";
			fifo-depth = <0x80>;
			status = "disabled";
		};

		mmc_3: mmc@12230000 {
			compatible = "samsung,exynos5250-dw-mshc";
			reg = <0x12230000 0x1000>;
			interrupts = <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock CLK_SDMMC3>, <&clock CLK_SCLK_MMC3>;
			clock-names = "biu", "ciu";
			fifo-depth = <0x80>;
			status = "disabled";
		};

		i2s0: i2s@3830000 {
			compatible = "samsung,s5pv210-i2s";
			status = "disabled";
			reg = <0x03830000 0x100>;
			dmas = <&pdma0 10>,
				<&pdma0 9>,
				<&pdma0 8>;
			dma-names = "tx", "rx", "tx-sec";
			clocks = <&clock_audss EXYNOS_I2S_BUS>,
				<&clock_audss EXYNOS_I2S_BUS>,
				<&clock_audss EXYNOS_SCLK_I2S>;
			clock-names = "iis", "i2s_opclk0", "i2s_opclk1";
			samsung,idma-addr = <0x03000000>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2s0_bus>;
			power-domains = <&pd_mau>;
			#clock-cells = <1>;
			#sound-dai-cells = <1>;
		};

		i2s1: i2s@12d60000 {
			compatible = "samsung,s3c6410-i2s";
			status = "disabled";
			reg = <0x12d60000 0x100>;
			dmas = <&pdma1 12>,
				<&pdma1 11>;
			dma-names = "tx", "rx";
			clocks = <&clock CLK_I2S1>, <&clock CLK_DIV_I2S1>;
			clock-names = "iis", "i2s_opclk0";
			pinctrl-names = "default";
			pinctrl-0 = <&i2s1_bus>;
			power-domains = <&pd_mau>;
			#sound-dai-cells = <1>;
		};

		i2s2: i2s@12d70000 {
			compatible = "samsung,s3c6410-i2s";
			status = "disabled";
			reg = <0x12d70000 0x100>;
			dmas = <&pdma0 12>,
				<&pdma0 11>;
			dma-names = "tx", "rx";
			clocks = <&clock CLK_I2S2>, <&clock CLK_DIV_I2S2>;
			clock-names = "iis", "i2s_opclk0";
			pinctrl-names = "default";
			pinctrl-0 = <&i2s2_bus>;
			power-domains = <&pd_mau>;
			#sound-dai-cells = <1>;
		};

		usbdrd: usb@12000000 {
			compatible = "samsung,exynos5250-dwusb3";
			clocks = <&clock CLK_USB3>;
			clock-names = "usbdrd30";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x12000000 0x10000>;

			usbdrd_dwc3: usb@0 {
				compatible = "snps,dwc3";
				reg = <0x0 0x10000>;
				interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
				phys = <&usbdrd_phy 0>, <&usbdrd_phy 1>;
				phy-names = "usb2-phy", "usb3-phy";
			};
		};

		usbdrd_phy: phy@12100000 {
			compatible = "samsung,exynos5250-usbdrd-phy";
			reg = <0x12100000 0x100>;
			clocks = <&clock CLK_USB3>, <&clock CLK_FIN_PLL>;
			clock-names = "phy", "ref";
			samsung,pmu-syscon = <&pmu_system_controller>;
			#phy-cells = <1>;
		};

		ehci: usb@12110000 {
			compatible = "samsung,exynos4210-ehci";
			reg = <0x12110000 0x100>;
			interrupts = <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;

			clocks = <&clock CLK_USB2>;
			clock-names = "usbhost";
			phys = <&usb2_phy_gen 1>;
			phy-names = "host";
		};

		ohci: usb@12120000 {
			compatible = "samsung,exynos4210-ohci";
			reg = <0x12120000 0x100>;
			interrupts = <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;

			clocks = <&clock CLK_USB2>;
			clock-names = "usbhost";
			phys = <&usb2_phy_gen 1>;
			phy-names = "host";
		};

		usb2_phy_gen: phy@12130000 {
			compatible = "samsung,exynos5250-usb2-phy";
			reg = <0x12130000 0x100>;
			clocks = <&clock CLK_USB2>, <&clock CLK_FIN_PLL>;
			clock-names = "phy", "ref";
			#phy-cells = <1>;
			samsung,sysreg-phandle = <&sysreg_system_controller>;
			samsung,pmureg-phandle = <&pmu_system_controller>;
		};

		pdma0: dma-controller@121a0000 {
			compatible = "arm,pl330", "arm,primecell";
			reg = <0x121a0000 0x1000>;
			interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_PDMA0>;
			clock-names = "apb_pclk";
			#dma-cells = <1>;
		};

		pdma1: dma-controller@121b0000 {
			compatible = "arm,pl330", "arm,primecell";
			reg = <0x121b0000 0x1000>;
			interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_PDMA1>;
			clock-names = "apb_pclk";
			#dma-cells = <1>;
		};

		mdma0: dma-controller@10800000 {
			compatible = "arm,pl330", "arm,primecell";
			reg = <0x10800000 0x1000>;
			interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_MDMA0>;
			clock-names = "apb_pclk";
			#dma-cells = <1>;
		};

		mdma1: dma-controller@11c10000 {
			compatible = "arm,pl330", "arm,primecell";
			reg = <0x11c10000 0x1000>;
			interrupts = <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_MDMA1>;
			clock-names = "apb_pclk";
			#dma-cells = <1>;
		};

		gsc_0: gsc@13e00000 {
			compatible = "samsung,exynos5250-gsc", "samsung,exynos5-gsc";
			reg = <0x13e00000 0x1000>;
			interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
			power-domains = <&pd_gsc>;
			clocks = <&clock CLK_GSCL0>;
			clock-names = "gscl";
			iommus = <&sysmmu_gsc0>;
		};

		gsc_1: gsc@13e10000 {
			compatible = "samsung,exynos5250-gsc", "samsung,exynos5-gsc";
			reg = <0x13e10000 0x1000>;
			interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
			power-domains = <&pd_gsc>;
			clocks = <&clock CLK_GSCL1>;
			clock-names = "gscl";
			iommus = <&sysmmu_gsc1>;
		};

		gsc_2: gsc@13e20000 {
			compatible = "samsung,exynos5250-gsc", "samsung,exynos5-gsc";
			reg = <0x13e20000 0x1000>;
			interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
			power-domains = <&pd_gsc>;
			clocks = <&clock CLK_GSCL2>;
			clock-names = "gscl";
			iommus = <&sysmmu_gsc2>;
		};

		gsc_3: gsc@13e30000 {
			compatible = "samsung,exynos5250-gsc", "samsung,exynos5-gsc";
			reg = <0x13e30000 0x1000>;
			interrupts = <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
			power-domains = <&pd_gsc>;
			clocks = <&clock CLK_GSCL3>;
			clock-names = "gscl";
			iommus = <&sysmmu_gsc3>;
		};

		hdmi: hdmi@14530000 {
			compatible = "samsung,exynos4212-hdmi";
			reg = <0x14530000 0x70000>;
			power-domains = <&pd_disp1>;
			interrupts = <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_HDMI>, <&clock CLK_SCLK_HDMI>,
				 <&clock CLK_SCLK_PIXEL>, <&clock CLK_SCLK_HDMIPHY>,
				 <&clock CLK_MOUT_HDMI>;
			clock-names = "hdmi", "sclk_hdmi", "sclk_pixel",
					"sclk_hdmiphy", "mout_hdmi";
			samsung,syscon-phandle = <&pmu_system_controller>;
			phy = <&hdmiphy>;
			#sound-dai-cells = <0>;
			status = "disabled";
		};

		hdmicec: cec@101b0000 {
			compatible = "samsung,s5p-cec";
			reg = <0x101b0000 0x200>;
			interrupts = <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_HDMI_CEC>;
			clock-names = "hdmicec";
			samsung,syscon-phandle = <&pmu_system_controller>;
			hdmi-phandle = <&hdmi>;
			pinctrl-names = "default";
			pinctrl-0 = <&hdmi_cec>;
			status = "disabled";
		};

		mixer: mixer@14450000 {
			compatible = "samsung,exynos5250-mixer";
			reg = <0x14450000 0x10000>;
			power-domains = <&pd_disp1>;
			interrupts = <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_MIXER>, <&clock CLK_HDMI>,
				 <&clock CLK_SCLK_HDMI>;
			clock-names = "mixer", "hdmi", "sclk_hdmi";
			iommus = <&sysmmu_tv>;
			status = "disabled";
		};

		dsi_0: dsi@14500000 {
			compatible = "samsung,exynos4210-mipi-dsi";
			reg = <0x14500000 0x10000>;
			interrupts = <GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>;
			samsung,power-domain = <&pd_disp1>;
			phys = <&mipi_phy 3>;
			phy-names = "dsim";
			clocks = <&clock CLK_DSIM0>, <&clock CLK_SCLK_MIPI1>;
			clock-names = "bus_clk", "sclk_mipi";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		adc: adc@12d10000 {
			compatible = "samsung,exynos-adc-v1";
			reg = <0x12d10000 0x100>;
			interrupts = <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock CLK_ADC>;
			clock-names = "adc";
			#io-channel-cells = <1>;
			samsung,syscon-phandle = <&pmu_system_controller>;
			status = "disabled";
		};

		sysmmu_g2d: sysmmu@10a60000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x10a60000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <24 5>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_2D>, <&clock CLK_G2D>;
			#iommu-cells = <0>;
		};

		sysmmu_mfc_r: sysmmu@11200000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x11200000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <6 2>;
			power-domains = <&pd_mfc>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_MFCR>, <&clock CLK_MFC>;
			#iommu-cells = <0>;
		};

		sysmmu_mfc_l: sysmmu@11210000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x11210000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <8 5>;
			power-domains = <&pd_mfc>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_MFCL>, <&clock CLK_MFC>;
			#iommu-cells = <0>;
		};

		sysmmu_rotator: sysmmu@11d40000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x11d40000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <4 0>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_ROTATOR>, <&clock CLK_ROTATOR>;
			#iommu-cells = <0>;
		};

		sysmmu_jpeg: sysmmu@11f20000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x11f20000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <4 2>;
			power-domains = <&pd_gsc>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_JPEG>, <&clock CLK_JPEG>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_isp: sysmmu@13260000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x13260000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <10 6>;
			clock-names = "sysmmu";
			clocks = <&clock CLK_SMMU_FIMC_ISP>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_drc: sysmmu@13270000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x13270000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <11 6>;
			clock-names = "sysmmu";
			clocks = <&clock CLK_SMMU_FIMC_DRC>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_fd: sysmmu@132a0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x132a0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <5 0>;
			clock-names = "sysmmu";
			clocks = <&clock CLK_SMMU_FIMC_FD>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_scc: sysmmu@13280000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x13280000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <5 2>;
			clock-names = "sysmmu";
			clocks = <&clock CLK_SMMU_FIMC_SCC>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_scp: sysmmu@13290000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x13290000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <3 6>;
			clock-names = "sysmmu";
			clocks = <&clock CLK_SMMU_FIMC_SCP>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_mcuctl: sysmmu@132b0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x132b0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <5 4>;
			clock-names = "sysmmu";
			clocks = <&clock CLK_SMMU_FIMC_MCU>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_odc: sysmmu@132c0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x132c0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <11 0>;
			clock-names = "sysmmu";
			clocks = <&clock CLK_SMMU_FIMC_ODC>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_dis0: sysmmu@132d0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x132d0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <10 4>;
			clock-names = "sysmmu";
			clocks = <&clock CLK_SMMU_FIMC_DIS0>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_dis1: sysmmu@132e0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x132e0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <9 4>;
			clock-names = "sysmmu";
			clocks = <&clock CLK_SMMU_FIMC_DIS1>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_3dnr: sysmmu@132f0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x132f0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <5 6>;
			clock-names = "sysmmu";
			clocks = <&clock CLK_SMMU_FIMC_3DNR>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_lite0: sysmmu@13c40000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x13c40000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <3 4>;
			power-domains = <&pd_gsc>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_FIMC_LITE0>, <&clock CLK_CAMIF_TOP>;
			#iommu-cells = <0>;
		};

		sysmmu_fimc_lite1: sysmmu@13c50000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x13c50000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <24 1>;
			power-domains = <&pd_gsc>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_FIMC_LITE1>, <&clock CLK_CAMIF_TOP>;
			#iommu-cells = <0>;
		};

		sysmmu_gsc0: sysmmu@13e80000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x13e80000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <2 0>;
			power-domains = <&pd_gsc>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_GSCL0>, <&clock CLK_GSCL0>;
			#iommu-cells = <0>;
		};

		sysmmu_gsc1: sysmmu@13e90000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x13e90000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <2 2>;
			power-domains = <&pd_gsc>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_GSCL1>, <&clock CLK_GSCL1>;
			#iommu-cells = <0>;
		};

		sysmmu_gsc2: sysmmu@13ea0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x13ea0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <2 4>;
			power-domains = <&pd_gsc>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_GSCL2>, <&clock CLK_GSCL2>;
			#iommu-cells = <0>;
		};

		sysmmu_gsc3: sysmmu@13eb0000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x13eb0000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <2 6>;
			power-domains = <&pd_gsc>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_GSCL3>, <&clock CLK_GSCL3>;
			#iommu-cells = <0>;
		};

		sysmmu_fimd1: sysmmu@14640000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x14640000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <3 2>;
			power-domains = <&pd_disp1>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_FIMD1>, <&clock CLK_FIMD1>;
			#iommu-cells = <0>;
		};

		sysmmu_tv: sysmmu@14650000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x14650000 0x1000>;
			interrupt-parent = <&combiner>;
			interrupts = <7 4>;
			power-domains = <&pd_disp1>;
			clock-names = "sysmmu", "master";
			clocks = <&clock CLK_SMMU_TV>, <&clock CLK_MIXER>;
			#iommu-cells = <0>;
		};
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
		/*
		 * Unfortunately we need this since some versions
		 * of U-Boot on Exynos don't set the CNTFRQ register,
		 * so we need the value from DT.
		 */
		clock-frequency = <24000000>;
	};
};

&cpu_thermal {
	polling-delay-passive = <0>;
	polling-delay = <0>;
	thermal-sensors = <&tmu>;

	cooling-maps {
		map0 {
			/* Corresponds to 800MHz at freq_table */
			cooling-device = <&cpu0 9 9>, <&cpu1 9 9>;
		};
		map1 {
			/* Corresponds to 200MHz at freq_table */
			cooling-device = <&cpu0 15 15>,
					 <&cpu1 15 15>;
		};
	};
};

&dp {
	power-domains = <&pd_disp1>;
	clocks = <&clock CLK_DP>;
	clock-names = "dp";
	phys = <&dp_phy>;
	phy-names = "dp";
};

&fimd {
	power-domains = <&pd_disp1>;
	clocks = <&clock CLK_SCLK_FIMD1>, <&clock CLK_FIMD1>;
	clock-names = "sclk_fimd", "fimd";
	iommus = <&sysmmu_fimd1>;
};

&g2d {
	iommus = <&sysmmu_g2d>;
	clocks = <&clock CLK_G2D>;
	clock-names = "fimg2d";
	status = "okay";
};

&i2c_0 {
	clocks = <&clock CLK_I2C0>;
	clock-names = "i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c0_bus>;
};

&i2c_1 {
	clocks = <&clock CLK_I2C1>;
	clock-names = "i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_bus>;
};

&i2c_2 {
	clocks = <&clock CLK_I2C2>;
	clock-names = "i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_bus>;
};

&i2c_3 {
	clocks = <&clock CLK_I2C3>;
	clock-names = "i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_bus>;
};

&prng {
	clocks = <&clock CLK_SSS>;
	clock-names = "secss";
};

&pwm {
	clocks = <&clock CLK_PWM>;
	clock-names = "timers";
};

&rtc {
	clocks = <&clock CLK_RTC>;
	clock-names = "rtc";
	interrupt-parent = <&pmu_system_controller>;
	status = "disabled";
};

&serial_0 {
	clocks = <&clock CLK_UART0>, <&clock CLK_SCLK_UART0>;
	clock-names = "uart", "clk_uart_baud0";
	dmas = <&pdma0 13>, <&pdma0 14>;
	dma-names = "rx", "tx";
};

&serial_1 {
	clocks = <&clock CLK_UART1>, <&clock CLK_SCLK_UART1>;
	clock-names = "uart", "clk_uart_baud0";
	dmas = <&pdma1 15>, <&pdma1 16>;
	dma-names = "rx", "tx";
};

&serial_2 {
	clocks = <&clock CLK_UART2>, <&clock CLK_SCLK_UART2>;
	clock-names = "uart", "clk_uart_baud0";
	dmas = <&pdma0 15>, <&pdma0 16>;
	dma-names = "rx", "tx";
};

&serial_3 {
	clocks = <&clock CLK_UART3>, <&clock CLK_SCLK_UART3>;
	clock-names = "uart", "clk_uart_baud0";
	dmas = <&pdma1 17>, <&pdma1 18>;
	dma-names = "rx", "tx";
};

&sss {
	clocks = <&clock CLK_SSS>;
	clock-names = "secss";
};

&trng {
	clocks = <&clock CLK_SSS>;
	clock-names = "secss";
};

#include "exynos5250-pinctrl.dtsi"
#include "exynos-syscon-restart.dtsi"
