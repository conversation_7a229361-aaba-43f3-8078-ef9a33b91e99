// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung SMDK5410 board device tree source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

/dts-v1/;
#include "exynos5410.dtsi"
#include <dt-bindings/interrupt-controller/irq.h>
/ {
	model = "Samsung SMDK5410 board based on Exynos5410";
	compatible = "samsung,smdk5410", "samsung,exynos5410", "samsung,exynos5";

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x80000000>;
	};

	aliases {
		mmc0 = &mmc_0;
		mmc1 = &mmc_2;
	};

	chosen {
		stdout-path = "serial2:115200n8";
	};

	fin_pll: xxti {
		compatible = "fixed-clock";
		clock-frequency = <24000000>;
		clock-output-names = "fin_pll";
		#clock-cells = <0>;
	};

	pmic_ap_clk: pmic-ap-clk {
		/* Workaround for missing PMIC and its clock */
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <32768>;
	};

	firmware@2037000 {
		compatible = "samsung,secure-firmware";
		reg = <0x02037000 0x1000>;
	};

	vdd10_usb3: voltage-regulator-0 {
		compatible = "regulator-fixed";
		regulator-name = "VDD10_USB3";
		regulator-min-microvolt = <1000000>;
		regulator-max-microvolt = <1000000>;
	};

	vdd33_usb3: voltage-regulator-0 {
		compatible = "regulator-fixed";
		regulator-name = "VDD33_USB3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};
};

&mmc_0 {
	status = "okay";
	cap-mmc-highspeed;
	broken-cd;
	card-detect-delay = <200>;
	mmc-ddr-1_8v;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	bus-width = <8>;
};

&mmc_2 {
	status = "okay";
	cap-sd-highspeed;
	card-detect-delay = <200>;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	bus-width = <4>;
	disable-wp;
};

&pinctrl_0 {
	srom_ctl: srom-ctl-pins {
		samsung,pins = "gpy0-3", "gpy0-4", "gpy0-5",
			       "gpy1-0", "gpy1-1", "gpy1-2", "gpy1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	srom_ebi: srom-ebi-pins {
		samsung,pins = "gpy3-0", "gpy3-1", "gpy3-2", "gpy3-3",
			       "gpy3-4", "gpy3-5", "gpy3-6", "gpy3-7",
			       "gpy5-0", "gpy5-1", "gpy5-2", "gpy5-3",
			       "gpy5-4", "gpy5-5", "gpy5-6", "gpy5-7",
			       "gpy6-0", "gpy6-1", "gpy6-2", "gpy6-3",
			       "gpy6-4", "gpy6-5", "gpy6-6", "gpy6-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};

&rtc {
	clocks = <&clock CLK_RTC>, <&pmic_ap_clk>;
	clock-names = "rtc", "rtc_src";
};

&sromc {
	pinctrl-names = "default";
	pinctrl-0 = <&srom_ctl>, <&srom_ebi>;

	ethernet@3,0 {
		compatible = "smsc,lan9115";
		reg = <3 0 0x10000>;
		phy-mode = "mii";
		interrupt-parent = <&gpx0>;
		interrupts = <5 IRQ_TYPE_LEVEL_LOW>;
		reg-io-width = <2>;
		smsc,irq-push-pull;
		smsc,force-internal-phy;

		samsung,srom-page-mode;
		samsung,srom-timing = <9 12 1 9 1 1>;
	};
};

&serial_0 {
	status = "okay";
};

&serial_1 {
	status = "okay";
};

&serial_2 {
	status = "okay";
};

&usbdrd3_0 {
	vdd10-supply = <&vdd10_usb3>;
	vdd33-supply = <&vdd33_usb3>;
};

&usbdrd3_1 {
	vdd10-supply = <&vdd10_usb3>;
	vdd33-supply = <&vdd33_usb3>;
};
