// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung SMDK5250 board device tree source
 *
 * Copyright (c) 2012 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

/dts-v1/;
#include <dt-bindings/clock/maxim,max77686.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include "exynos5250.dtsi"

/ {
	model = "Samsung SMDK5250 board based on Exynos5250";
	compatible = "samsung,smdk5250", "samsung,exynos5250", "samsung,exynos5";

	aliases {
		mmc0 = &mmc_0;
		mmc1 = &mmc_2;
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x80000000>;
	};

	chosen {
		bootargs = "root=/dev/ram0 rw ramdisk=8192 initrd=0x41000000,8M init=/linuxrc";
		stdout-path = "serial2:115200n8";
	};

	vdd: fixed-regulator-vdd {
		compatible = "regulator-fixed";
		regulator-name = "vdd-supply";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	dbvdd: fixed-regulator-dbvdd {
		compatible = "regulator-fixed";
		regulator-name = "dbvdd-supply";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	spkvdd: fixed-regulator-spkvdd {
		compatible = "regulator-fixed";
		regulator-name = "spkvdd-supply";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};

	sound {
		compatible = "samsung,smdk-wm8994";

		samsung,i2s-controller = <&i2s0>;
		samsung,audio-codec = <&wm8994>;
	};

	fixed-rate-clocks {
		xxti {
			compatible = "samsung,clock-xxti";
			clock-frequency = <24000000>;
		};

		codec_mclk: codec-mclk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <16934000>;
		};
	};
};

&cpu0 {
	cpu0-supply = <&buck2_reg>;
};

&dp {
	samsung,color-space = <0>;
	samsung,color-depth = <1>;
	samsung,link-rate = <0x0a>;
	samsung,lane-count = <4>;

	pinctrl-names = "default";
	pinctrl-0 = <&dp_hpd>;
	status = "okay";

	display-timings {
		native-mode = <&timing0>;

		timing0: timing {
			/* 1280x800 */
			clock-frequency = <50000>;
			hactive = <1280>;
			vactive = <800>;
			hfront-porch = <4>;
			hback-porch = <4>;
			hsync-len = <4>;
			vback-porch = <4>;
			vfront-porch = <4>;
			vsync-len = <4>;
		};
	};
};

&ehci {
	samsung,vbus-gpio = <&gpx2 6 GPIO_ACTIVE_HIGH>;
};

&fimd {
	status = "okay";
};

&hdmi {
	status = "okay";
	ddc = <&i2c_2>;
	hpd-gpios = <&gpx3 7 GPIO_ACTIVE_HIGH>;
	vdd-supply = <&ldo8_reg>;
	vdd_osc-supply = <&ldo10_reg>;
	vdd_pll-supply = <&ldo8_reg>;
};

&i2c_0 {
	status = "okay";
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <20000>;

	eeprom@50 {
		compatible = "samsung,s524ad0xd1", "atmel,24c128";
		reg = <0x50>;
	};

	max77686: pmic@9 {
		compatible = "maxim,max77686";
		reg = <0x09>;
		interrupt-parent = <&gpx3>;
		interrupts = <2 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&max77686_irq>;
		#clock-cells = <1>;
		wakeup-source;

		voltage-regulators {
			ldo1_reg: LDO1 {
				regulator-name = "P1.0V_LDO_OUT1";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo2_reg: LDO2 {
				regulator-name = "P1.2V_LDO_OUT2";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			ldo3_reg: LDO3 {
				regulator-name = "P1.8V_LDO_OUT3";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo4_reg: LDO4 {
				regulator-name = "P2.8V_LDO_OUT4";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
			};

			ldo5_reg: LDO5 {
				regulator-name = "P1.8V_LDO_OUT5";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo6_reg: LDO6 {
				regulator-name = "P1.1V_LDO_OUT6";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
			};

			ldo7_reg: LDO7 {
				regulator-name = "P1.1V_LDO_OUT7";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
			};

			ldo8_reg: LDO8 {
				regulator-name = "P1.0V_LDO_OUT8";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
			};

			ldo10_reg: LDO10 {
				regulator-name = "P1.8V_LDO_OUT10";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo11_reg: LDO11 {
				regulator-name = "P1.8V_LDO_OUT11";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo12_reg: LDO12 {
				regulator-name = "P3.0V_LDO_OUT12";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
			};

			ldo13_reg: LDO13 {
				regulator-name = "P1.8V_LDO_OUT13";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo14_reg: LDO14 {
				regulator-name = "P1.8V_LDO_OUT14";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo15_reg: LDO15 {
				regulator-name = "P1.0V_LDO_OUT15";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
			};

			ldo16_reg: LDO16 {
				regulator-name = "P1.8V_LDO_OUT16";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			buck1_reg: BUCK1 {
				regulator-name = "vdd_mif";
				regulator-min-microvolt = <950000>;
				regulator-max-microvolt = <1300000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck2_reg: BUCK2 {
				regulator-name = "vdd_arm";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck3_reg: BUCK3 {
				regulator-name = "vdd_int";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck4_reg: BUCK4 {
				regulator-name = "vdd_g3d";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1300000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck5_reg: BUCK5 {
				regulator-name = "P1.8V_BUCK_OUT5";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
			};
		};
	};
};

&i2c_1 {
	status = "okay";
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <20000>;

	eeprom@51 {
		compatible = "samsung,s524ad0xd1", "atmel,24c128";
		reg = <0x51>;
	};

	wm8994: audio-codec@1a {
		compatible = "wlf,wm8994";
		reg = <0x1a>;

		gpio-controller;
		#gpio-cells = <2>;

		clocks = <&codec_mclk>;
		clock-names = "MCLK1";

		AVDD2-supply = <&vdd>;
		CPVDD-supply = <&vdd>;
		DBVDD-supply = <&dbvdd>;
		SPKVDD1-supply = <&spkvdd>;
		SPKVDD2-supply = <&spkvdd>;
	};
};

&i2c_2 {
	status = "okay";
	/* used by HDMI DDC */
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <66000>;
};

&i2c_8 {
	status = "okay";
	/* used by HDMI PHY */
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <66000>;
};

&i2c_9 {
	status = "okay";
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <40000>;
};

&i2s0 {
	status = "okay";
};

&mixer {
	status = "okay";
};

&mmc_0 {
	status = "okay";
	broken-cd;
	card-detect-delay = <200>;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_bus4 &sd0_bus8>;
	bus-width = <8>;
	cap-mmc-highspeed;
	mmc-ddr-1_8v;
};

&mmc_2 {
	status = "okay";
	card-detect-delay = <200>;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_cd &sd2_bus4>;
	bus-width = <4>;
	disable-wp;
	cap-sd-highspeed;
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&max77686 MAX77686_CLK_AP>;
	clock-names = "rtc", "rtc_src";
};

&sata {
	status = "okay";
};

&sata_phy {
	status = "okay";
	samsung,exynos-sataphy-i2c-phandle = <&sata_phy_i2c>;
};

&sata_phy_i2c {
	status = "okay";
};

&spi_1 {
	status = "okay";
	cs-gpios = <&gpa2 5 GPIO_ACTIVE_HIGH>;

	flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "winbond,w25x80", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <1000000>;

		controller-data {
			samsung,spi-feedback-delay = <0>;
		};

		partition@0 {
			label = "U-Boot";
			reg = <0x0 0x40000>;
			read-only;
		};

		partition@40000 {
			label = "Kernel";
			reg = <0x40000 0xc0000>;
		};
	};
};

&pinctrl_0 {
	max77686_irq: max77686-irq-pins {
		samsung,pins = "gpx3-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_F>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};
};

&usbdrd {
	vdd10-supply = <&ldo15_reg>;
	vdd33-supply = <&ldo12_reg>;
};
