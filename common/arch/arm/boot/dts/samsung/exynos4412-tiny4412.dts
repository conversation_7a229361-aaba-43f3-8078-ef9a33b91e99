// SPDX-License-Identifier: GPL-2.0
/*
 * FriendlyARM's Exynos4412 based TINY4412 board device tree source
 *
 * Copyright (c) 2013 <PERSON> <<EMAIL>>
 *
 * Device tree source file for FriendlyARM's TINY4412 board which is based on
 * Samsung's Exynos4412 SoC.
 */

/dts-v1/;
#include "exynos4412.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/leds/common.h>

/ {
	model = "FriendlyARM TINY4412 board based on Exynos4412";
	compatible = "friendlyarm,tiny4412", "samsung,exynos4412", "samsung,exynos4";

	aliases {
		mmc0 = &sdhci_2;
	};

	chosen {
		stdout-path = &serial_0;
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x40000000>;
	};

	leds {
		compatible = "gpio-leds";

		led1 {
			label = "led1";
			function = LED_FUNCTION_HEARTBEAT;
			gpios = <&gpm4 0 GPIO_ACTIVE_LOW>;
			default-state = "off";
			linux,default-trigger = "heartbeat";
		};

		led2 {
			label = "led2";
			gpios = <&gpm4 1 GPIO_ACTIVE_LOW>;
			default-state = "off";
		};

		led3 {
			label = "led3";
			gpios = <&gpm4 2 GPIO_ACTIVE_LOW>;
			default-state = "off";
		};

		led4 {
			label = "led4";
			function = LED_FUNCTION_DISK_ACTIVITY;
			gpios = <&gpm4 3 GPIO_ACTIVE_LOW>;
			default-state = "off";
			linux,default-trigger = "mmc0";
		};
	};

	fixed-rate-clocks {
		xxti {
			compatible = "samsung,clock-xxti";
			clock-frequency = <0>;
		};

		xusbxti {
			compatible = "samsung,clock-xusbxti";
			clock-frequency = <24000000>;
		};

		pmic_ap_clk: pmic-ap-clk {
			/* Workaround for missing clock on PMIC */
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <32768>;
		};
	};

	panel {
		compatible = "innolux,at070tn92";
		power-supply = <&vddq_lcd>;

		port {
			panel_input: endpoint {
				remote-endpoint = <&lcdc_output>;
			};
		};
	};

	vddq_lcd: regulator-vddq-lcd {
		compatible = "regulator-fixed";
		regulator-name = "vddq-lcd";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
	};
};

&cpu_thermal {
	cooling-maps {
		cooling_map0: map0 {
			/* Corresponds to 800MHz at freq_table */
			cooling-device = <&cpu0 7 7>, <&cpu1 7 7>,
					 <&cpu2 7 7>, <&cpu3 7 7>;
		};
		cooling_map1: map1 {
			/* Corresponds to 200MHz at freq_table */
			cooling-device = <&cpu0 13 13>, <&cpu1 13 13>,
					 <&cpu2 13 13>, <&cpu3 13 13>;
		};
	};
};

&fimd {
	pinctrl-0 = <&lcd_clk>, <&lcd_data24>;
	pinctrl-names = "default";
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	port@3 {
		reg = <3>;
		lcdc_output: endpoint {
			remote-endpoint = <&panel_input>;
		};
	};
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&pmic_ap_clk>;
	clock-names = "rtc", "rtc_src";
};

&sdhci_2 {
	bus-width = <4>;
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_cd &sd2_bus4>;
	pinctrl-names = "default";
	status = "okay";
};

&serial_0 {
	status = "okay";
};

&serial_1 {
	status = "okay";
};

&serial_2 {
	status = "okay";
};

&serial_3 {
	status = "okay";
};
