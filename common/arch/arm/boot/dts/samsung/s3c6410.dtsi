// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's S3C6410 SoC device tree source
 *
 * Copyright (c) 2013 <PERSON><PERSON> <<EMAIL>>
 *
 * Samsung's S3C6410 SoC device nodes are listed in this file. S3C6410
 * based board files can include this file and provide values for board specific
 * bindings.
 *
 * Note: This file does not include device nodes for all the controllers in
 * S3C6410 SoC. As device tree coverage for S3C6410 increases, additional
 * nodes can be added to this file.
 */

#include "s3c64xx.dtsi"

/ {
	compatible = "samsung,s3c6410";

	aliases {
		i2c1 = &i2c1;
	};
};

&vic0 {
	valid-mask = <0xffffff7f>;
	valid-wakeup-mask = <0x00200004>;
};

&vic1 {
	valid-mask = <0xffffffff>;
	valid-wakeup-mask = <0x53020000>;
};

&soc {
	clocks: clock-controller@7e00f000 {
		compatible = "samsung,s3c6410-clock";
		reg = <0x7e00f000 0x1000>;
		#clock-cells = <1>;
	};

	i2c1: i2c@7f00f000 {
		compatible = "samsung,s3c2440-i2c";
		reg = <0x7f00f000 0x1000>;
		interrupt-parent = <&vic0>;
		interrupts = <5>;
		clock-names = "i2c";
		clocks = <&clocks PCLK_IIC1>;
		status = "disabled";
		#address-cells = <1>;
		#size-cells = <0>;
	};
};
