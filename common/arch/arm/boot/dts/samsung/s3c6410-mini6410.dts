// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's S3C6410 based Mini6410 board device tree source
 *
 * Copyright (c) 2013 <PERSON><PERSON>ga <<EMAIL>>
 *
 * Device tree source file for FriendlyARM Mini6410 board which is based on
 * Samsung's S3C6410 SoC.
 */

/dts-v1/;

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>

#include "s3c6410.dtsi"

/ {
	model = "FriendlyARM Mini6410 board based on S3C6410";
	compatible = "friendlyarm,mini6410", "samsung,s3c6410";

	memory@50000000 {
		device_type = "memory";
		reg = <0x50000000 0x10000000>;
	};

	chosen {
		bootargs = "console=ttySAC0,115200n8 earlyprintk rootwait root=/dev/mmcblk0p1";
	};

	fin_pll: oscillator-0 {
		compatible = "fixed-clock";
		clock-frequency = <12000000>;
		clock-output-names = "fin_pll";
		#clock-cells = <0>;
	};

	xusbxti: oscillator-1 {
		compatible = "fixed-clock";
		clock-output-names = "xusbxti";
		clock-frequency = <48000000>;
		#clock-cells = <0>;
	};

	srom-cs1-bus@18000000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		reg = <0x18000000 0x8000000>;
		ranges;

		ethernet@18000000 {
			compatible = "davicom,dm9000";
			reg = <0x18000000 0x2>, <0x18000004 0x2>;
			interrupt-parent = <&gpn>;
			interrupts = <7 IRQ_TYPE_LEVEL_HIGH>;
			davicom,no-eeprom;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_keys>;
		autorepeat;

		button-k1 {
			label = "K1";
			gpios = <&gpn 0 GPIO_ACTIVE_LOW>;
			linux,code = <2>;
			debounce-interval = <20>;
		};

		button-k2 {
			label = "K2";
			gpios = <&gpn 1 GPIO_ACTIVE_LOW>;
			linux,code = <3>;
			debounce-interval = <20>;
		};

		button-k3 {
			label = "K3";
			gpios = <&gpn 2 GPIO_ACTIVE_LOW>;
			linux,code = <4>;
			debounce-interval = <20>;
		};

		button-k4 {
			label = "K4";
			gpios = <&gpn 3 GPIO_ACTIVE_LOW>;
			linux,code = <5>;
			debounce-interval = <20>;
		};

		button-k5 {
			label = "K5";
			gpios = <&gpn 4 GPIO_ACTIVE_LOW>;
			linux,code = <6>;
			debounce-interval = <20>;
		};

		button-k6 {
			label = "K6";
			gpios = <&gpn 5 GPIO_ACTIVE_LOW>;
			linux,code = <7>;
			debounce-interval = <20>;
		};

		button-k7 {
			label = "K7";
			gpios = <&gpl 11 GPIO_ACTIVE_LOW>;
			linux,code = <8>;
			debounce-interval = <20>;
		};

		button-k8 {
			label = "K8";
			gpios = <&gpl 12 GPIO_ACTIVE_LOW>;
			linux,code = <9>;
			debounce-interval = <20>;
		};
	};

	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_leds>;

		led-1 {
			label = "LED1";
			gpios = <&gpk 4 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "heartbeat";
		};

		led-2 {
			label = "LED2";
			gpios = <&gpk 5 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "mmc0";
		};

		led-3 {
			label = "LED3";
			gpios = <&gpk 6 GPIO_ACTIVE_LOW>;
		};

		led-4 {
			label = "LED4";
			gpios = <&gpk 7 GPIO_ACTIVE_LOW>;
		};
	};

	buzzer {
		compatible = "pwm-beeper";
		pwms = <&pwm 0 1000000 0>;
		pinctrl-names = "default";
		pinctrl-0 = <&pwm0_out>;
	};
};

&clocks {
	clocks = <&fin_pll>;
};

&sdhci0 {
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk>, <&sd0_cmd>, <&sd0_cd>, <&sd0_bus4>;
	bus-width = <4>;
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_data>;
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_data>, <&uart1_fctl>;
	status = "okay";
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_data>;
	status = "okay";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_data>;
	status = "okay";
};

&pinctrl0 {
	gpio_leds: gpio-leds-pins {
		samsung,pins = "gpk-4", "gpk-5", "gpk-6", "gpk-7";
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	gpio_keys: gpio-keys-pins {
		samsung,pins = "gpn-0", "gpn-1", "gpn-2", "gpn-3",
				"gpn-4", "gpn-5", "gpl-11", "gpl-12";
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};
};

&i2c0 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c0_bus>;
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c08";
		reg = <0x50>;
		pagesize = <16>;
	};
};
