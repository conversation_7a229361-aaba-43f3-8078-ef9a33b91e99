// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung S3C6410 based SMDK6410 board device tree source.
 *
 * Copyright (c) 2013 <PERSON><PERSON> <<EMAIL>>
 *
 * Device tree source file for Samsung SMDK6410 board which is based on
 * Samsung's S3C6410 SoC.
 */

/dts-v1/;

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>

#include "s3c6410.dtsi"

/ {
	model = "Samsung SMDK6410 board based on S3C6410";
	compatible = "samsung,smdk6410", "samsung,s3c6410";

	memory@50000000 {
		device_type = "memory";
		reg = <0x50000000 0x8000000>;
	};

	chosen {
		bootargs = "console=ttySAC0,115200n8 earlyprintk rootwait root=/dev/mmcblk0p1";
	};

	fin_pll: oscillator-0 {
		compatible = "fixed-clock";
		clock-frequency = <12000000>;
		clock-output-names = "fin_pll";
		#clock-cells = <0>;
	};

	xusbxti: oscillator-1 {
		compatible = "fixed-clock";
		clock-output-names = "xusbxti";
		clock-frequency = <48000000>;
		#clock-cells = <0>;
	};

	srom-cs1-bus@18000000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		reg = <0x18000000 0x8000000>;
		ranges;

		ethernet@18000000 {
			compatible = "smsc,lan9115";
			reg = <0x18000000 0x10000>;
			interrupt-parent = <&gpn>;
			interrupts = <10 IRQ_TYPE_LEVEL_LOW>;
			phy-mode = "mii";
			reg-io-width = <4>;
			smsc,force-internal-phy;
		};
	};
};

&clocks {
	clocks = <&fin_pll>;
};

&sdhci0 {
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk>, <&sd0_cmd>, <&sd0_cd>, <&sd0_bus4>;
	bus-width = <4>;
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_data>, <&uart0_fctl>;
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_data>;
	status = "okay";
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_data>;
	status = "okay";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_data>;
	status = "okay";
};
