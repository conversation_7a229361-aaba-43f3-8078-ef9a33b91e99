// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos5260 SoC pin-mux and pin-config device tree source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Samsung's Exynos5260 SoC pin-mux and pin-config options are listed as device
 * tree nodes in this file.
 */

#include "exynos-pinctrl.h"

&pinctrl_0 {
	gpa0: gpa0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpa1: gpa1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpa2: gpa2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb0: gpb0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb1: gpb1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb2: gpb2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb3: gpb3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb4: gpb4-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb5: gpb5-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpd0: gpd0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpd1: gpd1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpd2: gpd2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpe0: gpe0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpe1: gpe1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf0: gpf0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf1: gpf1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpk0: gpk0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpx0: gpx0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
		#interrupt-cells = <2>;
	};

	gpx1: gpx1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 47 IRQ_TYPE_LEVEL_HIGH>;
		#interrupt-cells = <2>;
	};

	gpx2: gpx2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpx3: gpx3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	uart0_data: uart0-data-pins {
		samsung,pins = "gpa0-0", "gpa0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	uart0_fctl: uart0-fctl-pins {
		samsung,pins = "gpa0-2", "gpa0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	uart1_data: uart1-data-pins {
		samsung,pins = "gpa1-0", "gpa1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	uart1_fctl: uart1-fctl-pins {
		samsung,pins = "gpa1-2", "gpa1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	uart2_data: uart2-data-pins {
		samsung,pins = "gpa1-4", "gpa1-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	spi0_bus: spi0-bus-pins {
		samsung,pins = "gpa2-0", "gpa2-2", "gpa2-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	spi1_bus: spi1-bus-pins {
		samsung,pins = "gpa2-4", "gpa2-6", "gpa2-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	usb3_vbus0_en: usb3-vbus0-en-pins {
		samsung,pins = "gpa2-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_OUTPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2s1_bus: i2s1-bus-pins {
		samsung,pins = "gpb0-0", "gpb0-1", "gpb0-2", "gpb0-3",
				"gpb0-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	pcm1_bus: pcm1-bus-pins {
		samsung,pins = "gpb0-0", "gpb0-1", "gpb0-2", "gpb0-3",
				"gpb0-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	spdif1_bus: spdif1-bus-pins {
		samsung,pins = "gpb0-0", "gpb0-1", "gpb0-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_4>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	spi2_bus: spi2-bus-pins {
		samsung,pins = "gpb1-0", "gpb1-2", "gpb1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c0_hs_bus: i2c0-hs-bus-pins {
		samsung,pins = "gpb3-0", "gpb3-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c1_hs_bus: i2c1-hs-bus-pins {
		samsung,pins = "gpb3-2", "gpb3-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c2_hs_bus: i2c2-hs-bus-pins {
		samsung,pins = "gpb3-4", "gpb3-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c3_hs_bus: i2c3-hs-bus-pins {
		samsung,pins = "gpb3-6", "gpb3-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c4_bus: i2c4-bus-pins {
		samsung,pins = "gpb4-0", "gpb4-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c5_bus: i2c5-bus-pins {
		samsung,pins = "gpb4-2", "gpb4-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c6_bus: i2c6-bus-pins {
		samsung,pins = "gpb4-4", "gpb4-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c7_bus: i2c7-bus-pins {
		samsung,pins = "gpb4-6", "gpb4-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c8_bus: i2c8-bus-pins {
		samsung,pins = "gpb5-0", "gpb5-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c9_bus: i2c9-bus-pins {
		samsung,pins = "gpb5-2", "gpb5-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c10_bus: i2c10-bus-pins {
		samsung,pins = "gpb5-4", "gpb5-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	i2c11_bus: i2c11-bus-pins {
		samsung,pins = "gpb5-6", "gpb5-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	cam_gpio_a: cam-gpio-a-pins {
		samsung,pins = "gpe0-0", "gpe0-1", "gpe0-2", "gpe0-3",
			"gpe0-4", "gpe0-5", "gpe0-6", "gpe0-7",
			"gpe1-0", "gpe1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	cam_gpio_b: cam-gpio-b-pins {
		samsung,pins = "gpf0-0", "gpf0-1", "gpf0-2", "gpf0-3",
			"gpf1-0", "gpf1-1", "gpf1-2", "gpf1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	cam_i2c1_bus: cam-i2c1-bus-pins {
		samsung,pins = "gpf0-2", "gpf0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	cam_i2c0_bus: cam-i2c0-bus-pins {
		samsung,pins = "gpf0-0", "gpf0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	cam_spi0_bus: cam-spi0-bus-pins {
		samsung,pins = "gpf1-0", "gpf1-1", "gpf1-2", "gpf1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};

	cam_spi1_bus: cam-spi1-bus-pins {
		samsung,pins = "gpf1-4", "gpf1-5", "gpf1-6", "gpf1-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};
};

&pinctrl_1 {
	gpc0: gpc0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc1: gpc1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc2: gpc2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc3: gpc3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc4: gpc4-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	sd0_clk: sd0-clk-pins {
		samsung,pins = "gpc0-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd0_cmd: sd0-cmd-pins {
		samsung,pins = "gpc0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd0_bus1: sd0-bus-width1-pins {
		samsung,pins = "gpc0-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd0_bus4: sd0-bus-width4-pins {
		samsung,pins = "gpc0-3", "gpc0-4", "gpc0-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd0_bus8: sd0-bus-width8-pins {
		samsung,pins = "gpc3-0", "gpc3-1", "gpc3-2", "gpc3-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd0_rdqs: sd0-rdqs-pins {
		samsung,pins = "gpc0-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd1_clk: sd1-clk-pins {
		samsung,pins = "gpc1-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd1_cmd: sd1-cmd-pins {
		samsung,pins = "gpc1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd1_bus1: sd1-bus-width1-pins {
		samsung,pins = "gpc1-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd1_bus4: sd1-bus-width4-pins {
		samsung,pins = "gpc1-3", "gpc1-4", "gpc1-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd1_bus8: sd1-bus-width8-pins {
		samsung,pins = "gpc4-0", "gpc4-1", "gpc4-2", "gpc4-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd2_clk: sd2-clk-pins {
		samsung,pins = "gpc2-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd2_cmd: sd2-cmd-pins {
		samsung,pins = "gpc2-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd2_cd: sd2-cd-pins {
		samsung,pins = "gpc2-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd2_bus1: sd2-bus-width1-pins {
		samsung,pins = "gpc2-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};

	sd2_bus4: sd2-bus-width4-pins {
		samsung,pins = "gpc2-4", "gpc2-5", "gpc2-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV6>;
	};
};

&pinctrl_2 {
	gpz0: gpz0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpz1: gpz1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};
};
