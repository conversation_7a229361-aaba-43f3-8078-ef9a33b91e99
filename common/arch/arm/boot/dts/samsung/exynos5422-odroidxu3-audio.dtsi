// SPDX-License-Identifier: GPL-2.0
/*
 * Hardkernel Odroid XU3 audio subsystem device tree source
 *
 * Copyright (c) 2015 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Copyright (c) 2014 Collabora Ltd.
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

#include <dt-bindings/sound/samsung-i2s.h>

/ {
	sound: sound {
		compatible = "samsung,odroid-xu3-audio";
		model = "Odroid-XU3";

		samsung,audio-widgets =
			"Headphone", "Headphone Jack",
			"Speakers", "Speakers";
		samsung,audio-routing =
			"Headphone Jack", "HPL",
			"Headphone Jack", "HPR",
			"Headphone Jack", "MIC<PERSON>AS",
			"IN12", "Headphone Jack",
			"Speakers", "SPKL",
			"Speakers", "SPKR",
			"I2S Playback", "Mixer DAI TX",
			"HiFi Playback", "Mixer DAI TX",
			"Mixer DAI RX", "HiFi Capture";

		cpu {
			sound-dai = <&i2s0 0>, <&i2s0 1>;
		};
		codec {
			sound-dai = <&hdmi>, <&max98090>;
		};
	};
};

&hsi2c_5 {
	status = "okay";
	max98090: audio-codec@10 {
		compatible = "maxim,max98090";
		reg = <0x10>;
		interrupt-parent = <&gpx3>;
		interrupts = <2 IRQ_TYPE_NONE>;
		clocks = <&i2s0 CLK_I2S_CDCLK>;
		clock-names = "mclk";
		#sound-dai-cells = <0>;
	};
};

&i2s0 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_EPLL>,
			<&clock CLK_MOUT_MAU_EPLL>,
			<&clock CLK_MOUT_USER_MAU_EPLL>,
			<&clock_audss EXYNOS_MOUT_AUDSS>,
			<&clock_audss EXYNOS_MOUT_I2S>,
			<&i2s0 CLK_I2S_RCLK_SRC>,
			<&clock_audss EXYNOS_DOUT_SRP>,
			<&clock_audss EXYNOS_DOUT_AUD_BUS>,
			<&clock_audss EXYNOS_DOUT_I2S>;

	assigned-clock-parents = <&clock CLK_FOUT_EPLL>,
			<&clock CLK_MOUT_EPLL>,
			<&clock CLK_MOUT_MAU_EPLL>,
			<&clock CLK_MAU_EPLL>,
			<&clock_audss EXYNOS_MOUT_AUDSS>,
			<&clock_audss EXYNOS_SCLK_I2S>;

	assigned-clock-rates = <0>,
			<0>,
			<0>,
			<0>,
			<0>,
			<0>,
			<196608001>,
			<(196608002 / 2)>,
			<196608000>;

};
