// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos3250 SoCs pin-mux and pin-config device tree source
 *
 * Copyright (c) 2014 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Samsung's Exynos3250 SoCs pin-mux and pin-config options are listed as device
 * tree nodes in this file.
 */

#include "exynos-pinctrl.h"

#define PIN_IN(_pin, _pull, _drv)					\
	pin- ## _pin {							\
		samsung,pins = #_pin;					\
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;		\
		samsung,pin-pud = <EXYNOS_PIN_PULL_ ##_pull>;		\
		samsung,pin-drv = <EXYNOS4_PIN_DRV_ ##_drv>;		\
	}

#define PIN_SLP(_pin, _mode, _pull)					\
	pin- ## _pin {							\
		samsung,pins = #_pin;					\
		samsung,pin-con-pdn = <EXYNOS_PIN_PDN_ ##_mode>;	\
		samsung,pin-pud-pdn = <EXYNOS_PIN_PULL_ ##_pull>;	\
	}

&pinctrl_0 {
	gpa0: gpa0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpa1: gpa1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb: gpb-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc0: gpc0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc1: gpc1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpd0: gpd0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpd1: gpd1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	uart0_data: uart0-data-pins {
		samsung,pins = "gpa0-0", "gpa0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart0_fctl: uart0-fctl-pins {
		samsung,pins = "gpa0-2", "gpa0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart1_data: uart1-data-pins {
		samsung,pins = "gpa0-4", "gpa0-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart1_fctl: uart1-fctl-pins {
		samsung,pins = "gpa0-6", "gpa0-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c2_bus: i2c2-bus-pins {
		samsung,pins = "gpa0-6", "gpa0-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart2_data: uart2-data-pins {
		samsung,pins = "gpa1-0", "gpa1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c3_bus: i2c3-bus-pins {
		samsung,pins = "gpa1-2", "gpa1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	spi0_bus: spi0-bus-pins {
		samsung,pins = "gpb-0", "gpb-2", "gpb-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c4_bus: i2c4-bus-pins {
		samsung,pins = "gpb-0", "gpb-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	spi1_bus: spi1-bus-pins {
		samsung,pins = "gpb-4", "gpb-6", "gpb-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c5_bus: i2c5-bus-pins {
		samsung,pins = "gpb-2", "gpb-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2s2_bus: i2s2-bus-pins {
		samsung,pins = "gpc1-0", "gpc1-1", "gpc1-2", "gpc1-3",
				"gpc1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pcm2_bus: pcm2-bus-pins {
		samsung,pins = "gpc1-0", "gpc1-1", "gpc1-2", "gpc1-3",
				"gpc1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c6_bus: i2c6-bus-pins {
		samsung,pins = "gpc1-3", "gpc1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_4>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pwm0_out: pwm0-out-pins {
		samsung,pins = "gpd0-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pwm1_out: pwm1-out-pins {
		samsung,pins = "gpd0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c7_bus: i2c7-bus-pins {
		samsung,pins = "gpd0-2", "gpd0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pwm2_out: pwm2-out-pins {
		samsung,pins = "gpd0-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pwm3_out: pwm3-out-pins {
		samsung,pins = "gpd0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c0_bus: i2c0-bus-pins {
		samsung,pins = "gpd1-0", "gpd1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	mipi0_clk: mipi0-clk-pins {
		samsung,pins = "gpd1-0", "gpd1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c1_bus: i2c1-bus-pins {
		samsung,pins = "gpd1-2", "gpd1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};
};

&pinctrl_1 {
	gpe0: gpe0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpe1: gpe1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpe2: gpe2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpk0: gpk0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpk1: gpk1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpk2: gpk2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpl0: gpl0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpm0: gpm0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpm1: gpm1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpm2: gpm2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpm3: gpm3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpm4: gpm4-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpx0: gpx0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		interrupt-parent = <&gic>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
		#interrupt-cells = <2>;
	};

	gpx1: gpx1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		interrupt-parent = <&gic>;
		interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 47 IRQ_TYPE_LEVEL_HIGH>;
		#interrupt-cells = <2>;
	};

	gpx2: gpx2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpx3: gpx3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	sd0_clk: sd0-clk-pins {
		samsung,pins = "gpk0-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd0_cmd: sd0-cmd-pins {
		samsung,pins = "gpk0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd0_cd: sd0-cd-pins {
		samsung,pins = "gpk0-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd0_rdqs: sd0-rdqs-pins {
		samsung,pins = "gpk0-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd0_bus1: sd0-bus-width1-pins {
		samsung,pins = "gpk0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd0_bus4: sd0-bus-width4-pins {
		samsung,pins = "gpk0-4", "gpk0-5", "gpk0-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd0_bus8: sd0-bus-width8-pins {
		samsung,pins = "gpl0-0", "gpl0-1", "gpl0-2", "gpl0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd1_clk: sd1-clk-pins {
		samsung,pins = "gpk1-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd1_cmd: sd1-cmd-pins {
		samsung,pins = "gpk1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd1_cd: sd1-cd-pins {
		samsung,pins = "gpk1-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd1_bus1: sd1-bus-width1-pins {
		samsung,pins = "gpk1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd1_bus4: sd1-bus-width4-pins {
		samsung,pins = "gpk1-4", "gpk1-5", "gpk1-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd2_clk: sd2-clk-pins {
		samsung,pins = "gpk2-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd2_cmd: sd2-cmd-pins {
		samsung,pins = "gpk2-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd2_cd: sd2-cd-pins {
		samsung,pins = "gpk2-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd2_bus1: sd2-bus-width1-pins {
		samsung,pins = "gpk2-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd2_bus4: sd2-bus-width4-pins {
		samsung,pins = "gpk2-4", "gpk2-5", "gpk2-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	cam_port_b_io: cam-port-b-io-pins {
		samsung,pins = "gpm0-0", "gpm0-1", "gpm0-2", "gpm0-3",
				"gpm0-4", "gpm0-5", "gpm0-6", "gpm0-7",
				"gpm1-0", "gpm1-1", "gpm2-0", "gpm2-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	cam_port_b_clk_active: cam-port-b-clk-active-pins {
		samsung,pins = "gpm2-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	cam_port_b_clk_idle: cam-port-b-clk-idle-pins {
		samsung,pins = "gpm2-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	fimc_is_i2c0: fimc-is-i2c0-pins {
		samsung,pins = "gpm4-0", "gpm4-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	fimc_is_i2c1: fimc-is-i2c1-pins {
		samsung,pins = "gpm4-2", "gpm4-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	fimc_is_uart: fimc-is-uart-pins {
		samsung,pins = "gpm3-5", "gpm3-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};
};
