// SPDX-License-Identifier: GPL-2.0
/*
 * Hardkernel Odroid XU3 board device tree source
 *
 * Copyright (c) 2014 Collabora Ltd.
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

/dts-v1/;
#include "exynos5422-odroidxu3-common.dtsi"
#include "exynos5422-odroidxu3-audio.dtsi"
#include "exynos54xx-odroidxu-leds.dtsi"

/ {
	model = "Hardkernel Odroid XU3";
	compatible = "hardkernel,odroid-xu3", "samsung,exynos5800", "samsung,exynos5";

	aliases {
		ethernet = &ethernet;
	};
};

&i2c_0 {
	status = "okay";

	/* A15 cluster: VDD_ARM */
	power-sensor@40 {
		compatible = "ti,ina231";
		reg = <0x40>;
		shunt-resistor = <10000>;
	};

	/* memory: VDD_MEM */
	power-sensor@41 {
		compatible = "ti,ina231";
		reg = <0x41>;
		shunt-resistor = <10000>;
	};

	/* GPU: VDD_G3D */
	power-sensor@44 {
		compatible = "ti,ina231";
		reg = <0x44>;
		shunt-resistor = <10000>;
	};

	/* A7 cluster: VDD_KFC */
	power-sensor@45 {
		compatible = "ti,ina231";
		reg = <0x45>;
		shunt-resistor = <10000>;
	};
};

&ldo28_reg {
	regulator-name = "dp_p3v3";
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
};

&pwm {
	/*
	 * PWM 0 -- fan
	 * PWM 1 -- Green LED
	 * PWM 2 -- Blue LED
	 * PWM 3 -- on MIPI connector for backlight
	 */
	pinctrl-0 = <&pwm0_out &pwm1_out &pwm2_out &pwm3_out>;
	pinctrl-names = "default";
	status = "okay";
};

&usbdrd_dwc3_1 {
	dr_mode = "peripheral";
};

&usbhost2 {
	#address-cells = <1>;
	#size-cells = <0>;

	hub@1 {
		compatible = "usb424,9514";
		reg = <1>;
		#address-cells = <1>;
		#size-cells = <0>;

		ethernet: ethernet@1 {
			compatible = "usb424,ec00";
			reg = <1>;
			local-mac-address = [00 00 00 00 00 00]; /* Filled in by a bootloader */
		};
	};
};
