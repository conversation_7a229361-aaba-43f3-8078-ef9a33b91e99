// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos4212 based Galaxy Tab 3 8.0 LTE board device tree
 * source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

/dts-v1/;
#include "exynos4212-tab3.dtsi"

/ {
	model = "Samsung Galaxy Tab 3 8.0 LTE (SM-T315) based on Exynos4212";
	compatible = "samsung,t315", "samsung,tab3", "samsung,exynos4212", "samsung,exynos4";
	chassis-type = "tablet";
};

/* Pin control sleep state overrides */
&sleep0 {
	PIN_SLP(gpa0-4, INPUT, UP);
	PIN_SLP(gpa0-5, INPUT, UP);

	PIN_SLP(gpb-5, INPUT, UP);

	PIN_SLP(gpc0-0, PREV, NONE);
	PIN_SLP(gpc1-3, INPUT, NONE);

	PIN_SLP(gpf1-6, INPUT, NONE);
	PIN_SLP(gpf2-2, PREV, NONE);
};

&sleep1 {
	PIN_SLP(gpl0-0, PREV, NONE);

	PIN_SLP(gpl1-0, PREV, NONE);

	PIN_SLP(gpl2-1, INPUT, DOWN);
	PIN_SLP(gpl2-2, INPUT, DOWN);
	PIN_SLP(gpl2-4, OUT0, NONE);
	PIN_SLP(gpl2-5, PREV, NONE);

	PIN_SLP(gpm3-3, OUT1, NONE);
};
