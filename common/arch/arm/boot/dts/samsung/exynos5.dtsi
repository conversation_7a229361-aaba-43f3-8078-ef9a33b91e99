// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos5 SoC series common device tree source
 *
 * Copyright (c) 2012-2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Samsung's Exynos5 SoC series device nodes are listed in this file. Particular
 * SoCs from Exynos5 series can include this file and provide values for SoCs
 * specific bindings.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	aliases {
		i2c0 = &i2c_0;
		i2c1 = &i2c_1;
		i2c2 = &i2c_2;
		i2c3 = &i2c_3;
		serial0 = &serial_0;
		serial1 = &serial_1;
		serial2 = &serial_2;
		serial3 = &serial_3;
	};

	soc: soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		chipid: chipid@10000000 {
			compatible = "samsung,exynos4210-chipid";
			reg = <0x10000000 0x100>;
		};

		sromc: memory-controller@12250000 {
			compatible = "samsung,exynos4210-srom";
			reg = <0x12250000 0x14>;
		};

		combiner: interrupt-controller@10440000 {
			compatible = "samsung,exynos4210-combiner";
			#interrupt-cells = <2>;
			interrupt-controller;
			samsung,combiner-nr = <32>;
			reg = <0x10440000 0x1000>;
			interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
		};

		gic: interrupt-controller@10481000 {
			compatible = "arm,gic-400", "arm,cortex-a15-gic";
			#interrupt-cells = <3>;
			interrupt-controller;
			reg = <0x10481000 0x1000>,
				<0x10482000 0x2000>,
				<0x10484000 0x2000>,
				<0x10486000 0x2000>;
			interrupts = <GIC_PPI 9
					(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		};

		sysreg_system_controller: syscon@10050000 {
			compatible = "samsung,exynos5-sysreg", "syscon";
			reg = <0x10050000 0x5000>;
		};

		serial_0: serial@12c00000 {
			compatible = "samsung,exynos4210-uart";
			reg = <0x12c00000 0x100>;
			interrupts = <GIC_SPI 51 IRQ_TYPE_LEVEL_HIGH>;
		};

		serial_1: serial@12c10000 {
			compatible = "samsung,exynos4210-uart";
			reg = <0x12c10000 0x100>;
			interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
		};

		serial_2: serial@12c20000 {
			compatible = "samsung,exynos4210-uart";
			reg = <0x12c20000 0x100>;
			interrupts = <GIC_SPI 53 IRQ_TYPE_LEVEL_HIGH>;
		};

		serial_3: serial@12c30000 {
			compatible = "samsung,exynos4210-uart";
			reg = <0x12c30000 0x100>;
			interrupts = <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
		};

		i2c_0: i2c@12c60000 {
			compatible = "samsung,s3c2440-i2c";
			reg = <0x12c60000 0x100>;
			interrupts = <GIC_SPI 56 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			samsung,sysreg-phandle = <&sysreg_system_controller>;
			status = "disabled";
		};

		i2c_1: i2c@12c70000 {
			compatible = "samsung,s3c2440-i2c";
			reg = <0x12c70000 0x100>;
			interrupts = <GIC_SPI 57 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			samsung,sysreg-phandle = <&sysreg_system_controller>;
			status = "disabled";
		};

		i2c_2: i2c@12c80000 {
			compatible = "samsung,s3c2440-i2c";
			reg = <0x12c80000 0x100>;
			interrupts = <GIC_SPI 58 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			samsung,sysreg-phandle = <&sysreg_system_controller>;
			status = "disabled";
		};

		i2c_3: i2c@12c90000 {
			compatible = "samsung,s3c2440-i2c";
			reg = <0x12c90000 0x100>;
			interrupts = <GIC_SPI 59 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			samsung,sysreg-phandle = <&sysreg_system_controller>;
			status = "disabled";
		};

		pwm: pwm@12dd0000 {
			compatible = "samsung,exynos4210-pwm";
			reg = <0x12dd0000 0x100>;
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>;
			samsung,pwm-outputs = <0>, <1>, <2>, <3>;
			#pwm-cells = <3>;
		};

		rtc: rtc@101e0000 {
			compatible = "samsung,s3c6410-rtc";
			reg = <0x101e0000 0x100>;
			interrupts = <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		fimd: fimd@14400000 {
			compatible = "samsung,exynos5250-fimd";
			interrupt-parent = <&combiner>;
			reg = <0x14400000 0x40000>;
			interrupt-names = "fifo", "vsync", "lcd_sys";
			interrupts = <18 4>, <18 5>, <18 6>;
			samsung,sysreg = <&sysreg_system_controller>;
			status = "disabled";
		};

		dp: dp-controller@145b0000 {
			compatible = "samsung,exynos5-dp";
			reg = <0x145b0000 0x1000>;
			interrupts = <10 3>;
			interrupt-parent = <&combiner>;
			status = "disabled";
		};

		sss: sss@10830000 {
			compatible = "samsung,exynos4210-secss";
			reg = <0x10830000 0x300>;
			interrupts = <GIC_SPI 112 IRQ_TYPE_LEVEL_HIGH>;
		};

		prng: rng@10830400 {
			compatible = "samsung,exynos5250-prng";
			reg = <0x10830400 0x200>;
		};

		trng: rng@10830600 {
			compatible = "samsung,exynos5250-trng";
			reg = <0x10830600 0x100>;
		};

		g2d: g2d@10850000 {
			compatible = "samsung,exynos5250-g2d";
			reg = <0x10850000 0x1000>;
			interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};
	};
};
