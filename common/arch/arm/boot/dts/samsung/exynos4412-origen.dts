// SPDX-License-Identifier: GPL-2.0
/*
 * Insignal's Exynos4412 based Origen board device tree source
 *
 * Copyright (c) 2012-2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Device tree source file for Insignal's Origen board which is based on
 * Samsung's Exynos4412 SoC.
 */

/dts-v1/;
#include "exynos4412.dtsi"
#include <dt-bindings/clock/samsung,s2mps11.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include "exynos-mfc-reserved-memory.dtsi"

/ {
	model = "Insignal Origen evaluation board based on Exynos4412";
	compatible = "insignal,origen4412", "samsung,exynos4412", "samsung,exynos4";

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x40000000>;
	};

	aliases {
		mmc0 = &mshc_0;
		mmc1 = &sdhci_2;
	};

	chosen {
		stdout-path = "serial2:115200n8";
	};

	firmware@203f000 {
		compatible = "samsung,secure-firmware";
		reg = <0x0203f000 0x1000>;
	};

	mmc_reg: regulator-0 {
		compatible = "regulator-fixed";
		regulator-name = "VMEM_VDD_2.8V";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		gpio = <&gpx1 1 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	display-timings {
		native-mode = <&timing0>;
		timing0: timing {
			clock-frequency = <47500000>;
			hactive = <1024>;
			vactive = <600>;
			hfront-porch = <64>;
			hback-porch = <16>;
			hsync-len = <48>;
			vback-porch = <64>;
			vfront-porch = <16>;
			vsync-len = <3>;
		};
	};

	fixed-rate-clocks {
		xxti {
			compatible = "samsung,clock-xxti";
			clock-frequency = <0>;
		};

		xusbxti {
			compatible = "samsung,clock-xusbxti";
			clock-frequency = <24000000>;
		};
	};
};

&cpu0 {
	cpu0-supply = <&buck2_reg>;
};

&cpu_thermal {
	cooling-maps {
		cooling_map0: map0 {
			/* Corresponds to 800MHz at freq_table */
			cooling-device = <&cpu0 7 7>, <&cpu1 7 7>,
					 <&cpu2 7 7>, <&cpu3 7 7>;
		};
		cooling_map1: map1 {
			/* Corresponds to 200MHz at freq_table */
			cooling-device = <&cpu0 13 13>, <&cpu1 13 13>,
					 <&cpu2 13 13>, <&cpu3 13 13>;
		};
	};
};

&exynos_usbphy {
	status = "okay";
};

&ehci {
	samsung,vbus-gpio = <&gpx3 5 GPIO_ACTIVE_HIGH>;
	status = "okay";
	phys = <&exynos_usbphy 2>, <&exynos_usbphy 3>;
	phy-names = "hsic0", "hsic1";
};

&fimd {
	pinctrl-0 = <&lcd_clk &lcd_data24 &pwm1_out>;
	pinctrl-names = "default";
	status = "okay";
};

&i2c_0 {
	#address-cells = <1>;
	#size-cells = <0>;
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <20000>;
	pinctrl-0 = <&i2c0_bus>;
	pinctrl-names = "default";
	status = "okay";

	pmic@66 {
		compatible = "samsung,s5m8767-pmic";
		reg = <0x66>;

		s5m8767,pmic-buck-default-dvs-idx = <3>;

		s5m8767,pmic-buck-dvs-gpios = <&gpx2 3 GPIO_ACTIVE_HIGH>,
						 <&gpx2 4 GPIO_ACTIVE_HIGH>,
						 <&gpx2 5 GPIO_ACTIVE_HIGH>;

		s5m8767,pmic-buck-ds-gpios = <&gpm3 5 GPIO_ACTIVE_HIGH>,
						<&gpm3 6 GPIO_ACTIVE_HIGH>,
						<&gpm3 7 GPIO_ACTIVE_HIGH>;

		s5m8767,pmic-buck2-dvs-voltage = <1250000>, <1200000>,
						 <1200000>, <1200000>,
						 <1200000>, <1200000>,
						 <1200000>, <1200000>;

		s5m8767,pmic-buck3-dvs-voltage = <1100000>, <1100000>,
						 <1100000>, <1100000>,
						 <1100000>, <1100000>,
						 <1100000>, <1100000>;

		s5m8767,pmic-buck4-dvs-voltage = <1200000>, <1200000>,
						 <1200000>, <1200000>,
						 <1200000>, <1200000>,
						 <1200000>, <1200000>;
		wakeup-source;

		s5m8767_osc: clocks {
			compatible = "samsung,s5m8767-clk";
			#clock-cells = <1>;
			clock-output-names = "s5m8767_ap", "s5m8767_cp",
					     "s5m8767_bt";
		};

		regulators {
			ldo1_reg: LDO1 {
				regulator-name = "VDD_ALIVE";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo2_reg: LDO2 {
				regulator-name = "VDDQ_M12";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo3_reg: LDO3 {
				regulator-name = "VDDIOAP_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo4_reg: LDO4 {
				regulator-name = "VDDQ_PRE";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo5_reg: LDO5 {
				regulator-name = "VDD18_2M";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo6_reg: LDO6 {
				regulator-name = "VDD10_MPLL";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo7_reg: LDO7 {
				regulator-name = "VDD10_XPLL";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo8_reg: LDO8 {
				regulator-name = "VDD10_MIPI";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo9_reg: LDO9 {
				regulator-name = "VDD33_LCD";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo10_reg: LDO10 {
				regulator-name = "VDD18_MIPI";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo11_reg: LDO11 {
				regulator-name = "VDD18_ABB1";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo12_reg: LDO12 {
				regulator-name = "VDD33_UOTG";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo13_reg: LDO13 {
				regulator-name = "VDDIOPERI_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo14_reg: LDO14 {
				regulator-name = "VDD18_ABB02";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo15_reg: LDO15 {
				regulator-name = "VDD10_USH";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo16_reg: LDO16 {
				regulator-name = "VDD18_HSIC";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo17_reg: LDO17 {
				regulator-name = "VDDIOAP_MMC012_28";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo18_reg: LDO18 {
				regulator-name = "VDDIOPERI_28";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo19_reg: LDO19 {
				regulator-name = "DVDD25";
				regulator-min-microvolt = <2500000>;
				regulator-max-microvolt = <2500000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo20_reg: LDO20 {
				regulator-name = "VDD28_CAM";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo21_reg: LDO21 {
				regulator-name = "VDD28_AF";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo22_reg: LDO22 {
				regulator-name = "VDDA28_2M";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo23_reg: LDO23 {
				regulator-name = "VDD28_TF";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo24_reg: LDO24 {
				regulator-name = "VDD33_A31";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo25_reg: LDO25 {
				regulator-name = "VDD18_CAM";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo26_reg: LDO26 {
				regulator-name = "VDD18_A31";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo27_reg: LDO27 {
				regulator-name = "GPS_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo28_reg: LDO28 {
				regulator-name = "DVDD12";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck1_reg: BUCK1 {
				regulator-name = "VDD_MIF";
				regulator-min-microvolt = <950000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck2_reg: BUCK2 {
				regulator-name = "VDD_ARM";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck3_reg: BUCK3 {
				regulator-name = "VDD_INT";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck4_reg: BUCK4 {
				regulator-name = "VDD_G3D";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck5_reg: BUCK5 {
				regulator-name = "VDD_M12";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck6_reg: BUCK6 {
				regulator-name = "VDD12_5M";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck9_reg: BUCK9 {
				regulator-name = "VDDF28_EMMC";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <3000000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};
		};
	};
};

&keypad {
	samsung,keypad-num-rows = <3>;
	samsung,keypad-num-columns = <2>;
	linux,input-no-autorepeat;
	wakeup-source;
	pinctrl-0 = <&keypad_rows &keypad_cols>;
	pinctrl-names = "default";
	status = "okay";

	key-home {
		keypad,row = <0>;
		keypad,column = <0>;
		linux,code = <KEY_HOME>;
	};

	key-down {
		keypad,row = <0>;
		keypad,column = <1>;
		linux,code = <KEY_DOWN>;
	};

	key-up {
		keypad,row = <1>;
		keypad,column = <0>;
		linux,code = <KEY_UP>;
	};

	key-menu {
		keypad,row = <1>;
		keypad,column = <1>;
		linux,code = <KEY_MENU>;
	};

	key-back {
		keypad,row = <2>;
		keypad,column = <0>;
		linux,code = <KEY_BACK>;
	};

	key-enter {
		keypad,row = <2>;
		keypad,column = <1>;
		linux,code = <KEY_ENTER>;
	};
};

&mshc_0 {
	pinctrl-0 = <&sd4_clk &sd4_cmd &sd4_bus4 &sd4_bus8>;
	pinctrl-names = "default";
	status = "okay";

	broken-cd;
	card-detect-delay = <200>;
	mmc-ddr-1_8v;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	bus-width = <8>;
	cap-mmc-highspeed;
};

&pinctrl_1 {
	keypad_rows: keypad-rows-pins {
		samsung,pins = "gpx2-0", "gpx2-1", "gpx2-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	keypad_cols: keypad-cols-pins {
		samsung,pins = "gpx1-0", "gpx1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&s5m8767_osc S2MPS11_CLK_AP>;
	clock-names = "rtc", "rtc_src";
};

&sdhci_2 {
	bus-width = <4>;
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_bus4 &sd2_cd>;
	pinctrl-names = "default";
	vmmc-supply = <&mmc_reg>;
	status = "okay";
};

&serial_0 {
	status = "okay";
};

&serial_1 {
	status = "okay";
};

&serial_2 {
	status = "okay";
};

&serial_3 {
	status = "okay";
};
