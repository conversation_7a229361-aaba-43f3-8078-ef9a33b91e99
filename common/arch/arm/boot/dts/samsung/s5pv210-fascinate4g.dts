// SPDX-License-Identifier: GPL-2.0

/dts-v1/;
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include "s5pv210-aries.dtsi"

/ {
	model = "Samsung Galaxy S Fascinate 4G (SGH-T959P) based on S5PV210";
	compatible = "samsung,fascinate4g", "samsung,aries", "samsung,s5pv210";
	chassis-type = "handset";

	chosen {
		stdout-path = &uart2;
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-power {
			label = "power";
			gpios = <&gph2 6 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			wakeup-source;
		};

		key-vol-down {
			label = "volume_down";
			gpios = <&gph3 2 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_VOLUMEDOWN>;
		};

		key-vol-up {
			label = "volume_up";
			gpios = <&gph3 1 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_VOLUMEUP>;
		};
	};

	headset_micbias_reg: regulator-fixed-3 {
		compatible = "regulator-fixed";
		regulator-name = "Headset_Micbias";
		gpio = <&gpj2 5 GPIO_ACTIVE_HIGH>;
		enable-active-high;

		pinctrl-names = "default";
		pinctrl-0 = <&headset_micbias_ena>;
	};

	main_micbias_reg: regulator-fixed-4 {
		compatible = "regulator-fixed";
		regulator-name = "Main_Micbias";
		gpio = <&gpj4 2 GPIO_ACTIVE_HIGH>;
		enable-active-high;

		pinctrl-names = "default";
		pinctrl-0 = <&main_micbias_ena>;
	};

	sound {
		compatible = "samsung,fascinate4g-wm8994";

		model = "Fascinate4G";

		extcon = <&fsa9480>;

		main-micbias-supply = <&main_micbias_reg>;
		headset-micbias-supply = <&headset_micbias_reg>;

		earpath-sel-gpios = <&gpj2 6 GPIO_ACTIVE_HIGH>;

		io-channels = <&adc 3>;
		io-channel-names = "headset-detect";
		headset-detect-gpios = <&gph0 6 GPIO_ACTIVE_HIGH>;
		headset-key-gpios = <&gph3 6 GPIO_ACTIVE_HIGH>;

		samsung,audio-routing =
			"HP", "HPOUT1L",
			"HP", "HPOUT1R",

			"SPK", "SPKOUTLN",
			"SPK", "SPKOUTLP",

			"RCV", "HPOUT2N",
			"RCV", "HPOUT2P",

			"LINE", "LINEOUT2N",
			"LINE", "LINEOUT2P",

			"IN1LP", "Main Mic",
			"IN1LN", "Main Mic",

			"IN1RP", "Headset Mic",
			"IN1RN", "Headset Mic",

			"Modem Out", "Modem TX",
			"Modem RX", "Modem In",

			"Bluetooth SPK", "TX",
			"RX", "Bluetooth Mic";

		pinctrl-names = "default";
		pinctrl-0 = <&headset_det &earpath_sel>;

		cpu {
			sound-dai = <&i2s0>, <&bt_codec>;
		};

		codec {
			sound-dai = <&wm8994>;
		};
	};
};

&fg {
	compatible = "maxim,max77836-battery";

	interrupt-parent = <&gph3>;
	interrupts = <3 IRQ_TYPE_LEVEL_LOW>;

	pinctrl-names = "default";
	pinctrl-0 = <&fg_irq>;
};

&pinctrl0 {
	pinctrl-names = "default";
	pinctrl-0 = <&sleep_cfg>;

	headset_det: headset-det-pins {
		samsung,pins = "gph0-6", "gph3-6";
		samsung,pin-function = <S5PV210_PIN_FUNC_F>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
	};

	fg_irq: fg-irq-pins {
		samsung,pins = "gph3-3";
		samsung,pin-function = <S5PV210_PIN_FUNC_F>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	headset_micbias_ena: headset-micbias-ena-pins {
		samsung,pins = "gpj2-5";
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	earpath_sel: earpath-sel-pins {
		samsung,pins = "gpj2-6";
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	main_micbias_ena: main-micbias-ena-pins {
		samsung,pins = "gpj4-2";
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	/* Based on vendor kernel v2.6.35.7 */
	sleep_cfg: sleep-state {
		PIN_SLP(gpa0-0, PREV, NONE);
		PIN_SLP(gpa0-1, PREV, NONE);
		PIN_SLP(gpa0-2, PREV, NONE);
		PIN_SLP(gpa0-3, OUT1, NONE);
		PIN_SLP(gpa0-4, PREV, NONE);
		PIN_SLP(gpa0-5, PREV, NONE);
		PIN_SLP(gpa0-6, PREV, NONE);
		PIN_SLP(gpa0-7, PREV, NONE);

		PIN_SLP(gpa1-0, INPUT, DOWN);
		PIN_SLP(gpa1-1, OUT0, NONE);
		PIN_SLP(gpa1-2, INPUT, DOWN);
		PIN_SLP(gpa1-3, OUT0, NONE);

		PIN_SLP(gpb-0, OUT0, NONE);
		PIN_SLP(gpb-1, OUT1, NONE);
		PIN_SLP(gpb-2, OUT0, NONE);
		PIN_SLP(gpb-3, PREV, NONE);
		PIN_SLP(gpb-4, INPUT, NONE);
		PIN_SLP(gpb-5, PREV, NONE);
		PIN_SLP(gpb-6, INPUT, DOWN);
		PIN_SLP(gpb-7, OUT0, NONE);

		PIN_SLP(gpc0-0, OUT0, NONE);
		PIN_SLP(gpc0-1, INPUT, DOWN);
		PIN_SLP(gpc0-2, OUT0, NONE);
		PIN_SLP(gpc0-3, INPUT, DOWN);
		PIN_SLP(gpc0-4, OUT0, NONE);

		PIN_SLP(gpc1-0, INPUT, DOWN);
		PIN_SLP(gpc1-1, INPUT, DOWN);
		PIN_SLP(gpc1-2, INPUT, DOWN);
		PIN_SLP(gpc1-3, INPUT, DOWN);
		PIN_SLP(gpc1-4, INPUT, DOWN);

		PIN_SLP(gpd0-0, INPUT, DOWN);
		PIN_SLP(gpd0-1, OUT0, NONE);
		PIN_SLP(gpd0-2, INPUT, DOWN);
		PIN_SLP(gpd0-3, INPUT, DOWN);

		PIN_SLP(gpd1-0, INPUT, NONE);
		PIN_SLP(gpd1-1, INPUT, NONE);
		PIN_SLP(gpd1-2, INPUT, DOWN);
		PIN_SLP(gpd1-3, INPUT, DOWN);
		PIN_SLP(gpd1-4, INPUT, DOWN);
		PIN_SLP(gpd1-5, INPUT, DOWN);

		PIN_SLP(gpe0-0, INPUT, DOWN);
		PIN_SLP(gpe0-1, INPUT, DOWN);
		PIN_SLP(gpe0-2, INPUT, DOWN);
		PIN_SLP(gpe0-3, INPUT, DOWN);
		PIN_SLP(gpe0-4, INPUT, DOWN);
		PIN_SLP(gpe0-5, INPUT, DOWN);
		PIN_SLP(gpe0-6, INPUT, DOWN);
		PIN_SLP(gpe0-7, INPUT, DOWN);

		PIN_SLP(gpe1-0, INPUT, DOWN);
		PIN_SLP(gpe1-1, INPUT, DOWN);
		PIN_SLP(gpe1-2, INPUT, DOWN);
		PIN_SLP(gpe1-3, OUT0, NONE);
		PIN_SLP(gpe1-4, INPUT, DOWN);

		PIN_SLP(gpf0-0, OUT0, NONE);
		PIN_SLP(gpf0-1, OUT0, NONE);
		PIN_SLP(gpf0-2, OUT0, NONE);
		PIN_SLP(gpf0-3, OUT0, NONE);
		PIN_SLP(gpf0-4, OUT0, NONE);
		PIN_SLP(gpf0-5, OUT0, NONE);
		PIN_SLP(gpf0-6, OUT0, NONE);
		PIN_SLP(gpf0-7, OUT0, NONE);

		PIN_SLP(gpf1-0, OUT0, NONE);
		PIN_SLP(gpf1-1, OUT0, NONE);
		PIN_SLP(gpf1-2, OUT0, NONE);
		PIN_SLP(gpf1-3, OUT0, NONE);
		PIN_SLP(gpf1-4, OUT0, NONE);
		PIN_SLP(gpf1-5, OUT0, NONE);
		PIN_SLP(gpf1-6, OUT0, NONE);
		PIN_SLP(gpf1-7, OUT0, NONE);

		PIN_SLP(gpf2-0, OUT0, NONE);
		PIN_SLP(gpf2-1, OUT0, NONE);
		PIN_SLP(gpf2-2, OUT0, NONE);
		PIN_SLP(gpf2-3, OUT0, NONE);
		PIN_SLP(gpf2-4, OUT0, NONE);
		PIN_SLP(gpf2-5, OUT0, NONE);
		PIN_SLP(gpf2-6, OUT0, NONE);
		PIN_SLP(gpf2-7, OUT0, NONE);

		PIN_SLP(gpf3-0, OUT0, NONE);
		PIN_SLP(gpf3-1, OUT0, NONE);
		PIN_SLP(gpf3-2, OUT0, NONE);
		PIN_SLP(gpf3-3, OUT0, NONE);
		PIN_SLP(gpf3-4, PREV, NONE);
		PIN_SLP(gpf3-5, INPUT, DOWN);

		PIN_SLP(gpg0-0, INPUT, DOWN);
		PIN_SLP(gpg0-1, INPUT, DOWN);
		PIN_SLP(gpg0-2, INPUT, NONE);
		PIN_SLP(gpg0-3, INPUT, DOWN);
		PIN_SLP(gpg0-4, INPUT, DOWN);
		PIN_SLP(gpg0-5, INPUT, DOWN);
		PIN_SLP(gpg0-6, INPUT, DOWN);

		PIN_SLP(gpg1-0, OUT0, NONE);
		PIN_SLP(gpg1-1, OUT1, NONE);
		PIN_SLP(gpg1-2, PREV, NONE);
		PIN_SLP(gpg1-3, OUT1, NONE);
		PIN_SLP(gpg1-4, OUT1, NONE);
		PIN_SLP(gpg1-5, OUT1, NONE);
		PIN_SLP(gpg1-6, OUT1, NONE);

		PIN_SLP(gpg2-0, OUT0, NONE);
		PIN_SLP(gpg2-1, OUT0, NONE);
		PIN_SLP(gpg2-2, INPUT, NONE);
		PIN_SLP(gpg2-3, OUT0, NONE);
		PIN_SLP(gpg2-4, OUT0, NONE);
		PIN_SLP(gpg2-5, OUT0, NONE);
		PIN_SLP(gpg2-6, OUT0, NONE);

		PIN_SLP(gpg3-0, PREV, UP);
		PIN_SLP(gpg3-1, PREV, UP);
		PIN_SLP(gpg3-2, INPUT, NONE);
		PIN_SLP(gpg3-3, INPUT, DOWN);
		PIN_SLP(gpg3-4, OUT0, NONE);
		PIN_SLP(gpg3-5, OUT0, NONE);
		PIN_SLP(gpg3-6, INPUT, DOWN);

		PIN_SLP(gpi-0, PREV, NONE);
		PIN_SLP(gpi-1, INPUT, DOWN);
		PIN_SLP(gpi-2, PREV, NONE);
		PIN_SLP(gpi-3, PREV, NONE);
		PIN_SLP(gpi-4, PREV, NONE);
		PIN_SLP(gpi-5, INPUT, DOWN);
		PIN_SLP(gpi-6, INPUT, DOWN);

		PIN_SLP(gpj0-0, INPUT, NONE);
		PIN_SLP(gpj0-1, INPUT, NONE);
		PIN_SLP(gpj0-2, INPUT, NONE);
		PIN_SLP(gpj0-3, INPUT, NONE);
		PIN_SLP(gpj0-4, INPUT, NONE);
		PIN_SLP(gpj0-5, INPUT, DOWN);
		PIN_SLP(gpj0-6, OUT0, NONE);
		PIN_SLP(gpj0-7, INPUT, NONE);

		PIN_SLP(gpj1-0, OUT1, NONE);
		PIN_SLP(gpj1-1, OUT0, NONE);
		PIN_SLP(gpj1-2, INPUT, DOWN);
		PIN_SLP(gpj1-3, PREV, NONE);
		PIN_SLP(gpj1-4, PREV, NONE);
		PIN_SLP(gpj1-5, OUT0, NONE);

		PIN_SLP(gpj2-0, INPUT, DOWN);
		PIN_SLP(gpj2-1, INPUT, DOWN);
		PIN_SLP(gpj2-2, OUT0, NONE);
		PIN_SLP(gpj2-3, INPUT, DOWN);
		PIN_SLP(gpj2-4, INPUT, DOWN);
		PIN_SLP(gpj2-5, PREV, NONE);
		PIN_SLP(gpj2-6, PREV, NONE);
		PIN_SLP(gpj2-7, INPUT, DOWN);

		PIN_SLP(gpj3-0, INPUT, NONE);
		PIN_SLP(gpj3-1, INPUT, NONE);
		PIN_SLP(gpj3-2, OUT0, NONE);
		PIN_SLP(gpj3-3, INPUT, DOWN);
		PIN_SLP(gpj3-4, INPUT, NONE);
		PIN_SLP(gpj3-5, INPUT, NONE);
		PIN_SLP(gpj3-6, INPUT, NONE);
		PIN_SLP(gpj3-7, INPUT, NONE);

		PIN_SLP(gpj4-0, INPUT, NONE);
		PIN_SLP(gpj4-1, INPUT, DOWN);
		PIN_SLP(gpj4-2, PREV, NONE);
		PIN_SLP(gpj4-3, INPUT, NONE);
		PIN_SLP(gpj4-4, INPUT, DOWN);

		PIN_SLP(mp01-0, OUT1, NONE);
		PIN_SLP(mp01-1, OUT0, NONE);
		PIN_SLP(mp01-2, INPUT, DOWN);
		PIN_SLP(mp01-3, INPUT, DOWN);
		PIN_SLP(mp01-4, OUT1, NONE);
		PIN_SLP(mp01-5, INPUT, DOWN);
		PIN_SLP(mp01-6, INPUT, DOWN);
		PIN_SLP(mp01-7, INPUT, DOWN);

		PIN_SLP(mp02-0, INPUT, DOWN);
		PIN_SLP(mp02-1, INPUT, DOWN);
		PIN_SLP(mp02-2, INPUT, NONE);
		PIN_SLP(mp02-3, INPUT, DOWN);

		PIN_SLP(mp03-0, INPUT, DOWN);
		PIN_SLP(mp03-1, INPUT, DOWN);
		PIN_SLP(mp03-2, OUT1, NONE);
		PIN_SLP(mp03-3, OUT0, NONE);
		PIN_SLP(mp03-4, INPUT, NONE);
		PIN_SLP(mp03-5, OUT0, NONE);
		PIN_SLP(mp03-6, INPUT, DOWN);
		PIN_SLP(mp03-7, INPUT, DOWN);

		PIN_SLP(mp04-0, INPUT, DOWN);
		PIN_SLP(mp04-1, OUT0, NONE);
		PIN_SLP(mp04-2, INPUT, DOWN);
		PIN_SLP(mp04-3, OUT0, NONE);
		PIN_SLP(mp04-4, INPUT, DOWN);
		PIN_SLP(mp04-5, INPUT, DOWN);
		PIN_SLP(mp04-6, OUT0, NONE);
		PIN_SLP(mp04-7, INPUT, DOWN);

		PIN_SLP(mp05-0, INPUT, NONE);
		PIN_SLP(mp05-1, INPUT, NONE);
		PIN_SLP(mp05-2, INPUT, NONE);
		PIN_SLP(mp05-3, INPUT, NONE);
		PIN_SLP(mp05-4, INPUT, DOWN);
		PIN_SLP(mp05-5, OUT0, NONE);
		PIN_SLP(mp05-6, INPUT, DOWN);
		PIN_SLP(mp05-7, PREV, NONE);

		PIN_SLP(mp06-0, INPUT, DOWN);
		PIN_SLP(mp06-1, INPUT, DOWN);
		PIN_SLP(mp06-2, INPUT, DOWN);
		PIN_SLP(mp06-3, INPUT, DOWN);
		PIN_SLP(mp06-4, INPUT, DOWN);
		PIN_SLP(mp06-5, INPUT, DOWN);
		PIN_SLP(mp06-6, INPUT, DOWN);
		PIN_SLP(mp06-7, INPUT, DOWN);

		PIN_SLP(mp07-0, INPUT, DOWN);
		PIN_SLP(mp07-1, INPUT, DOWN);
		PIN_SLP(mp07-2, INPUT, DOWN);
		PIN_SLP(mp07-3, INPUT, DOWN);
		PIN_SLP(mp07-4, INPUT, DOWN);
		PIN_SLP(mp07-5, INPUT, DOWN);
		PIN_SLP(mp07-6, INPUT, DOWN);
		PIN_SLP(mp07-7, INPUT, DOWN);
	};
};

&wm8994 {
	/* GPIO3 (BCLK2) and GPIO4 (LRCLK2) as outputs */
	wlf,gpio-cfg = <0xa101 0x8100 0x8100 0x8100 0x8100 0xa101
			0x0100 0x8100 0x0100 0x0100 0x0100>;
};
