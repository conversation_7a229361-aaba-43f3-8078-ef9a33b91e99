// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos4210 SoC pin-mux and pin-config device tree source
 *
 * Copyright (c) 2011-2012 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 * Copyright (c) 2011-2012 Linaro Ltd.
 *		www.linaro.org
 *
 * Samsung's Exynos4210 SoC pin-mux and pin-config options are listed as device
 * tree nodes in this file.
 */

#include "exynos-pinctrl.h"

&pinctrl_0 {
	gpa0: gpa0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpa1: gpa1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb: gpb-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc0: gpc0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc1: gpc1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpd0: gpd0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpd1: gpd1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpe0: gpe0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpe1: gpe1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpe2: gpe2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpe3: gpe3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpe4: gpe4-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf0: gpf0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf1: gpf1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf2: gpf2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf3: gpf3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	uart0_data: uart0-data-pins {
		samsung,pins = "gpa0-0", "gpa0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart0_fctl: uart0-fctl-pins {
		samsung,pins = "gpa0-2", "gpa0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart1_data: uart1-data-pins {
		samsung,pins = "gpa0-4", "gpa0-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart1_fctl: uart1-fctl-pins {
		samsung,pins = "gpa0-6", "gpa0-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c2_bus: i2c2-bus-pins {
		samsung,pins = "gpa0-6", "gpa0-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart2_data: uart2-data-pins {
		samsung,pins = "gpa1-0", "gpa1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart2_fctl: uart2-fctl-pins {
		samsung,pins = "gpa1-2", "gpa1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart_audio_a: uart-audio-a-pins {
		samsung,pins = "gpa1-0", "gpa1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_4>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c3_bus: i2c3-bus-pins {
		samsung,pins = "gpa1-2", "gpa1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart3_data: uart3-data-pins {
		samsung,pins = "gpa1-4", "gpa1-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	uart_audio_b: uart-audio-b-pins {
		samsung,pins = "gpa1-4", "gpa1-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_4>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	spi0_bus: spi0-bus-pins {
		samsung,pins = "gpb-0", "gpb-2", "gpb-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c4_bus: i2c4-bus-pins {
		samsung,pins = "gpb-2", "gpb-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	spi1_bus: spi1-bus-pins {
		samsung,pins = "gpb-4", "gpb-6", "gpb-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c5_bus: i2c5-bus-pins {
		samsung,pins = "gpb-6", "gpb-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2s1_bus: i2s1-bus-pins {
		samsung,pins = "gpc0-0", "gpc0-1", "gpc0-2", "gpc0-3",
				"gpc0-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pcm1_bus: pcm1-bus-pins {
		samsung,pins = "gpc0-0", "gpc0-1", "gpc0-2", "gpc0-3",
				"gpc0-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	ac97_bus: ac97-bus-pins {
		samsung,pins = "gpc0-0", "gpc0-1", "gpc0-2", "gpc0-3",
				"gpc0-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_4>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2s2_bus: i2s2-bus-pins {
		samsung,pins = "gpc1-0", "gpc1-1", "gpc1-2", "gpc1-3",
				"gpc1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pcm2_bus: pcm2-bus-pins {
		samsung,pins = "gpc1-0", "gpc1-1", "gpc1-2", "gpc1-3",
				"gpc1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	spdif_bus: spdif-bus-pins {
		samsung,pins = "gpc1-0", "gpc1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_4>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c6_bus: i2c6-bus-pins {
		samsung,pins = "gpc1-3", "gpc1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_4>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	spi2_bus: spi2-bus-pins {
		samsung,pins = "gpc1-1", "gpc1-2", "gpc1-3", "gpc1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_5>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c7_bus: i2c7-bus-pins {
		samsung,pins = "gpd0-2", "gpd0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c0_bus: i2c0-bus-pins {
		samsung,pins = "gpd1-0", "gpd1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	i2c1_bus: i2c1-bus-pins {
		samsung,pins = "gpd1-2", "gpd1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pwm0_out: pwm0-out-pins {
		samsung,pins = "gpd0-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pwm1_out: pwm1-out-pins {
		samsung,pins = "gpd0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pwm2_out: pwm2-out-pins {
		samsung,pins = "gpd0-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pwm3_out: pwm3-out-pins {
		samsung,pins = "gpd0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	lcd_ctrl: lcd-ctrl-pins {
		samsung,pins = "gpd0-0", "gpd0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	lcd_sync: lcd-sync-pins {
		samsung,pins = "gpf0-0", "gpf0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	lcd_en: lcd-en-pins {
		samsung,pins = "gpe3-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	lcd_clk: lcd-clk-pins {
		samsung,pins = "gpf0-0", "gpf0-1", "gpf0-2", "gpf0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	lcd_data16: lcd-data-width16-pins {
		samsung,pins = "gpf0-7", "gpf1-0", "gpf1-1", "gpf1-2",
				"gpf1-3", "gpf1-6", "gpf1-7", "gpf2-0",
				"gpf2-1", "gpf2-2", "gpf2-3", "gpf2-7",
				"gpf3-0", "gpf3-1", "gpf3-2", "gpf3-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	lcd_data18: lcd-data-width18-pins {
		samsung,pins = "gpf0-6", "gpf0-7", "gpf1-0", "gpf1-1",
				"gpf1-2", "gpf1-3", "gpf1-6", "gpf1-7",
				"gpf2-0", "gpf2-1", "gpf2-2", "gpf2-3",
				"gpf2-6", "gpf2-7", "gpf3-0", "gpf3-1",
				"gpf3-2", "gpf3-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	lcd_data24: lcd-data-width24-pins {
		samsung,pins = "gpf0-4", "gpf0-5", "gpf0-6", "gpf0-7",
				"gpf1-0", "gpf1-1", "gpf1-2", "gpf1-3",
				"gpf1-4", "gpf1-5", "gpf1-6", "gpf1-7",
				"gpf2-0", "gpf2-1", "gpf2-2", "gpf2-3",
				"gpf2-4", "gpf2-5", "gpf2-6", "gpf2-7",
				"gpf3-0", "gpf3-1", "gpf3-2", "gpf3-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};
};

&pinctrl_1 {
	gpj0: gpj0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpj1: gpj1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpk0: gpk0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpk1: gpk1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpk2: gpk2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpk3: gpk3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpl0: gpl0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpl1: gpl1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpl2: gpl2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpy0: gpy0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy1: gpy1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy2: gpy2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy3: gpy3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy4: gpy4-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy5: gpy5-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy6: gpy6-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpx0: gpx0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		interrupt-parent = <&gic>;
		interrupts = <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
		#interrupt-cells = <2>;
	};

	gpx1: gpx1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		interrupt-parent = <&gic>;
		interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
		#interrupt-cells = <2>;
	};

	gpx2: gpx2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpx3: gpx3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	sd0_clk: sd0-clk-pins {
		samsung,pins = "gpk0-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd0_cmd: sd0-cmd-pins {
		samsung,pins = "gpk0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd0_cd: sd0-cd-pins {
		samsung,pins = "gpk0-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd0_bus1: sd0-bus-width1-pins {
		samsung,pins = "gpk0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd0_bus4: sd0-bus-width4-pins {
		samsung,pins = "gpk0-3", "gpk0-4", "gpk0-5", "gpk0-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd0_bus8: sd0-bus-width8-pins {
		samsung,pins = "gpk1-3", "gpk1-4", "gpk1-5", "gpk1-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd4_clk: sd4-clk-pins {
		samsung,pins = "gpk0-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd4_cmd: sd4-cmd-pins {
		samsung,pins = "gpk0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd4_cd: sd4-cd-pins {
		samsung,pins = "gpk0-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd4_bus1: sd4-bus-width1-pins {
		samsung,pins = "gpk0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd4_bus4: sd4-bus-width4-pins {
		samsung,pins = "gpk0-3", "gpk0-4", "gpk0-5", "gpk0-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd4_bus8: sd4-bus-width8-pins {
		samsung,pins = "gpk1-3", "gpk1-4", "gpk1-5", "gpk1-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd1_clk: sd1-clk-pins {
		samsung,pins = "gpk1-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd1_cmd: sd1-cmd-pins {
		samsung,pins = "gpk1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd1_cd: sd1-cd-pins {
		samsung,pins = "gpk1-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd1_bus1: sd1-bus-width1-pins {
		samsung,pins = "gpk1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd1_bus4: sd1-bus-width4-pins {
		samsung,pins = "gpk1-3", "gpk1-4", "gpk1-5", "gpk1-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd2_clk: sd2-clk-pins {
		samsung,pins = "gpk2-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd2_cmd: sd2-cmd-pins {
		samsung,pins = "gpk2-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd2_cd: sd2-cd-pins {
		samsung,pins = "gpk2-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd2_bus1: sd2-bus-width1-pins {
		samsung,pins = "gpk2-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd2_bus4: sd2-bus-width4-pins {
		samsung,pins = "gpk2-3", "gpk2-4", "gpk2-5", "gpk2-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd2_bus8: sd2-bus-width8-pins {
		samsung,pins = "gpk3-3", "gpk3-4", "gpk3-5", "gpk3-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd3_clk: sd3-clk-pins {
		samsung,pins = "gpk3-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd3_cmd: sd3-cmd-pins {
		samsung,pins = "gpk3-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd3_cd: sd3-cd-pins {
		samsung,pins = "gpk3-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd3_bus1: sd3-bus-width1-pins {
		samsung,pins = "gpk3-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	sd3_bus4: sd3-bus-width4-pins {
		samsung,pins = "gpk3-3", "gpk3-4", "gpk3-5", "gpk3-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	eint0: ext-int0-pins {
		samsung,pins = "gpx0-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_F>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	eint8: ext-int8-pins {
		samsung,pins = "gpx1-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_F>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	eint15: ext-int15-pins {
		samsung,pins = "gpx1-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_F>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	eint16: ext-int16-pins {
		samsung,pins = "gpx2-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_F>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	eint31: ext-int31-pins {
		samsung,pins = "gpx3-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_F>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	cam_port_a_io: cam-port-a-io-pins {
		samsung,pins = "gpj0-0", "gpj0-1", "gpj0-2", "gpj0-3",
				"gpj0-4", "gpj0-5", "gpj0-6", "gpj0-7",
				"gpj1-0", "gpj1-1", "gpj1-2", "gpj1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	cam_port_a_clk_active: cam-port-a-clk-active-pins {
		samsung,pins = "gpj1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};

	cam_port_a_clk_idle: cam-port-a-clk-idle-pins {
		samsung,pins = "gpj1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_DOWN>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	hdmi_cec: hdmi-cec-pins {
		samsung,pins = "gpx3-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};
};

&pinctrl_2 {
	gpz: gpz-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	i2s0_bus: i2s0-bus-pins {
		samsung,pins = "gpz-0", "gpz-1", "gpz-2", "gpz-3",
				"gpz-4", "gpz-5", "gpz-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	pcm0_bus: pcm0-bus-pins {
		samsung,pins = "gpz-0", "gpz-1", "gpz-2", "gpz-3",
				"gpz-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};
};
