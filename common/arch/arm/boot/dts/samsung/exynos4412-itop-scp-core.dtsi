// SPDX-License-Identifier: GPL-2.0
/*
 * TOPEET's Exynos4412 based itop board device tree source
 *
 * Copyright (c) 2016 SUMOMO Computer Association
 *			https://www.sumomo.mobi
 *			Randy <PERSON> <<EMAIL>>
 *
 * Device tree source file for TOPEET iTop Exynos 4412 SCP package core
 * board which is based on Samsung's Exynos4412 SoC.
 */

#include <dt-bindings/clock/samsung,s2mps11.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include "exynos4412.dtsi"
#include "exynos4412-ppmu-common.dtsi"
#include "exynos-mfc-reserved-memory.dtsi"

/ {
	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x40000000>;
	};

	aliases {
		mmc0 = &mshc_0;
	};

	firmware@203f000 {
		compatible = "samsung,secure-firmware";
		reg = <0x0203f000 0x1000>;
	};

	fixed-rate-clocks {
		xxti {
			compatible = "samsung,clock-xxti";
			clock-frequency = <0>;
		};

		xusbxti {
			compatible = "samsung,clock-xusbxti";
			clock-frequency = <24000000>;
		};
	};

	thermal-zones {
		cpu_thermal: cpu-thermal {
			cooling-maps {
				map0 {
				     /* Corresponds to 800MHz at freq_table */
				     cooling-device = <&cpu0 7 7>, <&cpu1 7 7>,
						      <&cpu2 7 7>, <&cpu3 7 7>;
				};
				map1 {
				     /* Corresponds to 200MHz at freq_table */
				     cooling-device = <&cpu0 13 13>,
						      <&cpu1 13 13>,
						      <&cpu2 13 13>,
						      <&cpu3 13 13>;
			       };
		       };
		};
	};

	usb-hub {
		compatible = "smsc,usb3503a";
		reset-gpios = <&gpm2 4 GPIO_ACTIVE_LOW>;
		connect-gpios = <&gpm3 3 GPIO_ACTIVE_HIGH>;
		intn-gpios = <&gpx2 3 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&hsic_reset>;
	};
};

&bus_dmc {
	devfreq-events = <&ppmu_dmc0_3>, <&ppmu_dmc1_3>;
	vdd-supply = <&buck1_reg>;
	status = "okay";
};

&bus_acp {
	devfreq = <&bus_dmc>;
	status = "okay";
};

&bus_c2c {
	devfreq = <&bus_dmc>;
	status = "okay";
};

&bus_leftbus {
	devfreq-events = <&ppmu_leftbus_3>, <&ppmu_rightbus_3>;
	vdd-supply = <&buck3_reg>;
	status = "okay";
};

&bus_rightbus {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_fsys {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_peri {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_mfc {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&cpu0 {
	cpu0-supply = <&buck2_reg>;
};

&gpu {
	mali-supply = <&buck4_reg>;
	status = "okay";
};

&hsotg {
	vusb_d-supply = <&ldo15_reg>;
	vusb_a-supply = <&ldo12_reg>;
};

&i2c_1 {
	#address-cells = <1>;
	#size-cells = <0>;
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <400000>;
	pinctrl-0 = <&i2c1_bus>;
	pinctrl-names = "default";
	status = "okay";

	s5m8767: pmic@66 {
		compatible = "samsung,s5m8767-pmic";
		reg = <0x66>;

		s5m8767,pmic-buck-default-dvs-idx = <3>;

		s5m8767,pmic-buck-dvs-gpios = <&gpb 5 GPIO_ACTIVE_HIGH>,
						 <&gpb 6 GPIO_ACTIVE_HIGH>,
						 <&gpb 7 GPIO_ACTIVE_HIGH>;

		s5m8767,pmic-buck-ds-gpios = <&gpm3 5 GPIO_ACTIVE_HIGH>,
						<&gpm3 6 GPIO_ACTIVE_HIGH>,
						<&gpm3 7 GPIO_ACTIVE_HIGH>;

		/* VDD_ARM */
		s5m8767,pmic-buck2-dvs-voltage = <1356250>, <1300000>,
						 <1243750>, <1118750>,
						 <1068750>, <1012500>,
						 <956250>, <900000>;
		/* VDD_INT */
		s5m8767,pmic-buck3-dvs-voltage = <1000000>, <1000000>,
						 <925000>, <925000>,
						 <887500>, <887500>,
						 <850000>, <850000>;
		/* VDD_G3D */
		s5m8767,pmic-buck4-dvs-voltage = <1081250>, <1081250>,
						 <1025000>, <950000>,
						 <918750>, <900000>,
						 <875000>, <831250>;
		wakeup-source;

		regulators {
			ldo1_reg: LDO1 {
				regulator-name = "VDD_ALIVE";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			/* SCP uses 1.5v, POP uses 1.2v */
			ldo2_reg: LDO2 {
				regulator-name = "VDDQ_M12";
				regulator-min-microvolt = <1500000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo3_reg: LDO3 {
				regulator-name = "VDDIOAP_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo4_reg: LDO4 {
				regulator-name = "VDDQ_PRE";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo5_reg: LDO5 {
				regulator-name = "VDD_LDO5";
				op_mode = <0>; /* Always off Mode */
			};

			ldo6_reg: LDO6 {
				regulator-name = "VDD10_MPLL";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo7_reg: LDO7 {
				regulator-name = "VDD10_XPLL";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo8_reg: LDO8 {
				regulator-name = "VDD10_MIPI";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				op_mode = <1>; /* Normal Mode */
			};

			ldo9_reg: LDO9 {
				regulator-name = "VDD33_LCD";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				op_mode = <1>; /* Normal Mode */
			};

			ldo10_reg: LDO10 {
				regulator-name = "VDD18_MIPI";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				op_mode = <1>; /* Normal Mode */
			};

			ldo11_reg: LDO11 {
				regulator-name = "VDD18_ABB1";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo12_reg: LDO12 {
				regulator-name = "VDD33_UOTG";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo13_reg: LDO13 {
				regulator-name = "VDDIOPERI_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo14_reg: LDO14 {
				regulator-name = "VDD18_ABB02";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo15_reg: LDO15 {
				regulator-name = "VDD10_USH";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo16_reg: LDO16 {
				regulator-name = "VDD18_HSIC";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo17_reg: LDO17 {
				regulator-name = "VDDIOAP_MMC012_28";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				op_mode = <1>; /* Normal Mode */
			};

			/* Used by HSIC */
			ldo18_reg: LDO18 {
				regulator-name = "VDDIOPERI_28";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			ldo19_reg: LDO19 {
				regulator-name = "VDD_LDO19";
				op_mode = <0>; /* Always off Mode */
			};

			ldo20_reg: LDO20 {
				regulator-name = "VDD28_CAM";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <2800000>;
				op_mode = <1>; /* Normal Mode */
			};

			ldo21_reg: LDO21 {
				regulator-name = "VDD28_AF";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <2800000>;
				op_mode = <1>; /* Normal Mode */
			};

			ldo22_reg: LDO22 {
				regulator-name = "VDDA28_2M";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				op_mode = <1>; /* Normal Mode */
			};

			ldo23_reg: LDO23 {
				regulator-name = "VDD28_TF";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				op_mode = <1>; /* Normal Mode */
			};

			ldo24_reg: LDO24 {
				regulator-name = "VDD33_A31";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				op_mode = <1>; /* Normal Mode */
			};

			ldo25_reg: LDO25 {
				regulator-name = "VDD18_CAM";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				op_mode = <1>; /* Normal Mode */
			};

			ldo26_reg: LDO26 {
				regulator-name = "VDD18_A31";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				op_mode = <1>; /* Normal Mode */
			};

			ldo27_reg: LDO27 {
				regulator-name = "GPS_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				op_mode = <1>; /* Normal Mode */
			};

			ldo28_reg: LDO28 {
				regulator-name = "DVDD12";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				op_mode = <1>; /* Normal Mode */
			};

			buck1_reg: BUCK1 {
				regulator-name = "vdd_mif";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck2_reg: BUCK2 {
				regulator-name = "vdd_arm";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1456250>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck3_reg: BUCK3 {
				regulator-name = "vdd_int";
				regulator-min-microvolt = <875000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck4_reg: BUCK4 {
				regulator-name = "vdd_g3d";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck5_reg: BUCK5 {
				regulator-name = "vdd_m12";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck6_reg: BUCK6 {
				regulator-name = "vdd12_5m";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck7_reg: BUCK7 {
				regulator-name = "pvdd_buck7";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <2000000>;
				regulator-boot-on;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck8_reg: BUCK8 {
				regulator-name = "pvdd_buck8";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1500000>;
				regulator-boot-on;
				regulator-always-on;
				op_mode = <1>; /* Normal Mode */
			};

			buck9_reg: BUCK9 {
				regulator-name = "vddf28_emmc";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <3000000>;
				op_mode = <1>; /* Normal Mode */
			};
		};

		s5m8767_osc: clocks {
			compatible = "samsung,s5m8767-clk";
			#clock-cells = <1>;
			clock-output-names = "s5m8767_ap",
					"s5m8767_cp", "s5m8767_bt";
		};

	};
};

&mfc {
	status = "okay";
};

&mshc_0 {
	pinctrl-0 = <&sd4_clk &sd4_cmd &sd4_bus4 &sd4_bus8>;
	pinctrl-names = "default";
	status = "okay";
	vmmc-supply = <&buck9_reg>;
	broken-cd;
	card-detect-delay = <200>;
	mmc-ddr-1_8v;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	bus-width = <8>;
	cap-mmc-highspeed;
};

&pinctrl_1 {
	hsic_reset: hsic-reset-pins {
		samsung,pins = "gpm2-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
	};
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&s5m8767_osc S2MPS11_CLK_AP>;
	clock-names = "rtc", "rtc_src";
};

&tmu {
	vtmu-supply = <&ldo16_reg>;
	status = "okay";
};
