// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos4210 based Universal C210 board device tree source
 *
 * Copyright (c) 2012-2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Device tree source file for Samsung's Universal C210 board which is based on
 * Samsung's Exynos4210 rev0 SoC.
 */

/dts-v1/;
#include "exynos4210.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Samsung Universal C210 based on Exynos4210 rev0";
	compatible = "samsung,universal_c210", "samsung,exynos4210", "samsung,exynos4";
	chassis-type = "handset";

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x10000000
		       0x50000000 0x10000000>;
	};

	aliases {
		mmc0 = &sdhci_0;
		mmc1 = &sdhci_2;
		mmc2 = &sdhci_3;
	};

	chosen {
		bootargs = "root=/dev/mmcblk0p5 rw rootwait earlyprintk panic=5 maxcpus=1";
		stdout-path = "serial2:115200n8";
	};


	fixed-rate-clocks {
		xxti {
			compatible = "samsung,clock-xxti";
			clock-frequency = <0>;
		};

		xusbxti {
			compatible = "samsung,clock-xusbxti";
			clock-frequency = <24000000>;
		};

		pmic_ap_clk: pmic-ap-clk {
			/* Workaround for missing clock on PMIC */
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <32768>;
		};
	};

	vemmc_reg: voltage-regulator {
		compatible = "regulator-fixed";
		regulator-name = "VMEM_VDD_2_8V";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		gpio = <&gpe1 3 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	wlan_pwrseq: sdhci3-pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&gpe3 1 GPIO_ACTIVE_LOW>;
	};

	gpio-keys {
		compatible = "gpio-keys";

		vol-up-key {
			gpios = <&gpx2 0 GPIO_ACTIVE_LOW>;
			linux,code = <115>;
			label = "volume up";
			debounce-interval = <1>;
		};

		vol-down-key {
			gpios = <&gpx2 1 GPIO_ACTIVE_LOW>;
			linux,code = <114>;
			label = "volume down";
			debounce-interval = <1>;
		};

		config-key {
			gpios = <&gpx2 2 GPIO_ACTIVE_LOW>;
			linux,code = <171>;
			label = "config";
			debounce-interval = <1>;
			wakeup-source;
		};

		camera-key {
			gpios = <&gpx2 3 GPIO_ACTIVE_LOW>;
			linux,code = <212>;
			label = "camera";
			debounce-interval = <1>;
		};

		power-key {
			gpios = <&gpx2 7 GPIO_ACTIVE_LOW>;
			linux,code = <116>;
			label = "power";
			debounce-interval = <1>;
			wakeup-source;
		};

		ok-key {
			gpios = <&gpx3 5 GPIO_ACTIVE_LOW>;
			linux,code = <352>;
			label = "ok";
			debounce-interval = <1>;
		};
	};

	tsp_reg: voltage-regulator {
		compatible = "regulator-fixed";
		regulator-name = "TSP_2_8V";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		gpio = <&gpe2 3 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	spi-3 {
		compatible = "spi-gpio";
		#address-cells = <1>;
		#size-cells = <0>;

		sck-gpios = <&gpy3 1 GPIO_ACTIVE_HIGH>;
		mosi-gpios = <&gpy3 3 GPIO_ACTIVE_HIGH>;
		num-chipselects = <1>;
		cs-gpios = <&gpy4 3 GPIO_ACTIVE_LOW>;

		lcd@0 {
			compatible = "samsung,ld9040";
			reg = <0>;
			vdd3-supply = <&ldo7_reg>;
			vci-supply = <&ldo17_reg>;
			reset-gpios = <&gpy4 5 GPIO_ACTIVE_HIGH>;
			spi-max-frequency = <1200000>;
			power-on-delay = <10>;
			reset-delay = <10>;
			panel-width-mm = <90>;
			panel-height-mm = <154>;
			display-timings {
				timing {
					clock-frequency = <23492370>;
					hactive = <480>;
					vactive = <800>;
					hback-porch = <16>;
					hfront-porch = <16>;
					vback-porch = <2>;
					vfront-porch = <28>;
					hsync-len = <2>;
					vsync-len = <1>;
					hsync-active = <0>;
					vsync-active = <0>;
					de-active = <0>;
					pixelclk-active = <0>;
				};
			};
			port {
				lcd_ep: endpoint {
					remote-endpoint = <&fimd_dpi_ep>;
				};
			};
		};
	};

	hdmi_en: voltage-regulator-hdmi-5v {
		compatible = "regulator-fixed";
		regulator-name = "HDMI_5V";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&gpe0 1 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	hdmi_ddc: i2c-ddc {
		compatible = "i2c-gpio";
		sda-gpios = <&gpe4 2 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpe4 3 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		i2c-gpio,delay-us = <100>;
		#address-cells = <1>;
		#size-cells = <0>;

		pinctrl-0 = <&i2c_ddc_bus>;
		pinctrl-names = "default";
		status = "okay";
	};
};

&camera {
	status = "okay";
};

&cpu0 {
	cpu0-supply = <&vdd_arm_reg>;
};

&cpu_thermal {
	cooling-maps {
		map0 {
			/* Corresponds to 800MHz */
			cooling-device = <&cpu0 2 2>;
		};
		map1 {
			/* Corresponds to 200MHz */
			cooling-device = <&cpu0 4 4>;
		};
	};
};

&ehci {
	status = "okay";
	phys = <&exynos_usbphy 1>;
	phy-names = "host";
};

&exynos_usbphy {
	status = "okay";
	vbus-supply = <&safeout1_reg>;
};

&fimc_0 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC0>,
			  <&clock CLK_SCLK_FIMC0>;
	assigned-clock-parents = <&clock CLK_SCLK_MPLL>;
	assigned-clock-rates = <0>, <160000000>;
};

&fimc_1 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC1>,
			  <&clock CLK_SCLK_FIMC1>;
	assigned-clock-parents = <&clock CLK_SCLK_MPLL>;
	assigned-clock-rates = <0>, <160000000>;
};

&fimc_2 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC2>,
			  <&clock CLK_SCLK_FIMC2>;
	assigned-clock-parents = <&clock CLK_SCLK_MPLL>;
	assigned-clock-rates = <0>, <160000000>;
};

&fimc_3 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC3>,
			  <&clock CLK_SCLK_FIMC3>;
	assigned-clock-parents = <&clock CLK_SCLK_MPLL>;
	assigned-clock-rates = <0>, <160000000>;
};

&fimd {
	pinctrl-0 = <&lcd_clk>, <&lcd_data24>;
	pinctrl-names = "default";
	status = "okay";
	samsung,invert-vden;
	samsung,invert-vclk;
	#address-cells = <1>;
	#size-cells = <0>;
	port@3 {
		reg = <3>;
		fimd_dpi_ep: endpoint {
			remote-endpoint = <&lcd_ep>;
		};
	};
};

&gpu {
	mali-supply = <&buck2_reg>;
	status = "okay";
};

&hdmi {
	hpd-gpios = <&gpx3 7 GPIO_ACTIVE_HIGH>;
	pinctrl-names = "default";
	pinctrl-0 = <&hdmi_hpd>;
	hdmi-en-supply = <&hdmi_en>;
	vdd-supply = <&ldo3_reg>;
	vdd_osc-supply = <&ldo4_reg>;
	vdd_pll-supply = <&ldo3_reg>;
	ddc = <&hdmi_ddc>;
	status = "okay";
};

&hsotg {
	vusb_d-supply = <&ldo3_reg>;
	vusb_a-supply = <&ldo8_reg>;
	dr_mode = "peripheral";
	status = "okay";
};

&i2c_3 {
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-slave-addr = <0x10>;
	samsung,i2c-max-bus-freq = <100000>;
	pinctrl-0 = <&i2c3_bus>;
	pinctrl-names = "default";
	status = "okay";

	tsp@4a {
		/* TBD: Atmel maXtouch touchscreen */
		reg = <0x4a>;
	};
};

&i2c_5 {
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-slave-addr = <0x10>;
	samsung,i2c-max-bus-freq = <100000>;
	pinctrl-0 = <&i2c5_bus>;
	pinctrl-names = "default";
	status = "okay";

	vdd_arm_reg: pmic@60 {
		compatible = "maxim,max8952";
		reg = <0x60>;

		max8952,vid-gpios = <&gpx0 3 GPIO_ACTIVE_HIGH>,
				    <&gpx0 4 GPIO_ACTIVE_HIGH>;
		max8952,default-mode = <0>;
		max8952,dvs-mode-microvolt = <1250000>, <1200000>,
						<1050000>, <950000>;
		max8952,sync-freq = <0>;
		max8952,ramp-speed = <0>;

		regulator-name = "VARM_1.2V_C210";
		regulator-min-microvolt = <770000>;
		regulator-max-microvolt = <1400000>;
		regulator-always-on;
		regulator-boot-on;
	};

	pmic@66 {
		compatible = "national,lp3974";
		interrupts-extended = <&gpx0 7 0>, <&gpx2 7 0>;
		pinctrl-names = "default";
		pinctrl-0 = <&lp3974_irq>;
		reg = <0x66>;

		max8998,pmic-buck1-default-dvs-idx = <0>;
		max8998,pmic-buck1-dvs-gpios = <&gpx0 5 GPIO_ACTIVE_HIGH>,
						<&gpx0 6 GPIO_ACTIVE_HIGH>;
		max8998,pmic-buck1-dvs-voltage = <1100000>, <1000000>,
						<1100000>, <1000000>;

		max8998,pmic-buck2-default-dvs-idx = <0>;
		max8998,pmic-buck2-dvs-gpio = <&gpe2 0 GPIO_ACTIVE_HIGH>;
		max8998,pmic-buck2-dvs-voltage = <1200000>, <1100000>;

		regulators {
			ldo2_reg: LDO2 {
				regulator-name = "VALIVE_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			ldo3_reg: LDO3 {
				regulator-name = "VUSB+MIPI_1.1V";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
			};

			ldo4_reg: LDO4 {
				regulator-name = "VADC_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
			};

			ldo5_reg: LDO5 {
				regulator-name = "VTF_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
			};

			ldo6_reg: LDO6 {
				regulator-name = "LDO6";
				regulator-min-microvolt = <2000000>;
				regulator-max-microvolt = <2000000>;
			};

			ldo7_reg: LDO7 {
				regulator-name = "VLCD+VMIPI_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo8_reg: LDO8 {
				regulator-name = "VUSB+VDAC_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			ldo9_reg: LDO9 {
				regulator-name = "VCC_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
			};

			ldo10_reg: LDO10 {
				regulator-name = "VPLL_1.1V";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo11_reg: LDO11 {
				regulator-name = "CAM_AF_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
			};

			ldo12_reg: LDO12 {
				regulator-name = "PS_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
			};

			ldo13_reg: LDO13 {
				regulator-name = "VHIC_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
			};

			ldo14_reg: LDO14 {
				regulator-name = "CAM_I_HOST_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo15_reg: LDO15 {
				regulator-name = "CAM_S_DIG+FM33_CORE_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
			};

			ldo16_reg: LDO16 {
				regulator-name = "CAM_S_ANA_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
			};

			ldo17_reg: LDO17 {
				regulator-name = "VCC_3.0V_LCD";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
			};

			buck1_reg: BUCK1 {
				regulator-name = "VINT_1.1V";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1500000>;
				regulator-boot-on;
				regulator-always-on;
			};

			buck2_reg: BUCK2 {
				regulator-name = "VG3D_1.1V";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1500000>;
				regulator-boot-on;
			};

			buck3_reg: BUCK3 {
				regulator-name = "VCC_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			buck4_reg: BUCK4 {
				regulator-name = "VMEM_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			ap32khz_reg: EN32KHz-AP {
				regulator-name = "32KHz AP";
				regulator-always-on;
			};

			cp32khz_reg: EN32KHz-CP {
				regulator-name = "32KHz CP";
			};

			vichg_reg: ENVICHG {
				regulator-name = "VICHG";
			};

			safeout1_reg: ESAFEOUT1 {
				regulator-name = "SAFEOUT1";
			};

			safeout2_reg: ESAFEOUT2 {
				regulator-name = "SAFEOUT2";
				regulator-boot-on;
			};
		};
	};
};

&i2c_8 {
	status = "okay";
};

&mct {
	status = "disabled";
};

&mdma1 {
	/* Use the secure mdma0 */
	status = "disabled";
};

&mixer {
	status = "okay";
};

&ohci {
	status = "okay";
};

&pinctrl_1 {
	bt_shutdown: bt-shutdown-pins {
		samsung,pins = "gpe1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_OUTPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	bt_host_wakeup: bt-host-wakeup-pins {
		samsung,pins = "gpx2-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	bt_device_wakeup: bt-device-wakeup-pins {
		samsung,pins = "gpx3-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_OUTPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	lp3974_irq: lp3974-irq-pins {
		samsung,pins = "gpx0-7", "gpx2-7";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	hdmi_hpd: hdmi-hpd-pins {
		samsung,pins = "gpx3-7";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};
};

&pinctrl_0 {
	i2c_ddc_bus: i2c-ddc-bus-pins {
		samsung,pins = "gpe4-2", "gpe4-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};
};

&pwm {
	compatible = "samsung,s5p6440-pwm";
	status = "okay";
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&pmic_ap_clk>;
	clock-names = "rtc", "rtc_src";
};

&sdhci_0 {
	bus-width = <8>;
	non-removable;
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_bus8>;
	pinctrl-names = "default";
	vmmc-supply = <&vemmc_reg>;
	status = "okay";
};

&sdhci_2 {
	bus-width = <4>;
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_bus4>;
	pinctrl-names = "default";
	vmmc-supply = <&ldo5_reg>;
	cd-gpios = <&gpx3 4 GPIO_ACTIVE_LOW>;
	status = "okay";
};

&sdhci_3 {
	status = "okay";

	#address-cells = <1>;
	#size-cells = <0>;

	non-removable;
	bus-width = <4>;
	mmc-pwrseq = <&wlan_pwrseq>;
	vmmc-supply = <&ldo5_reg>;

	pinctrl-names = "default";
	pinctrl-0 = <&sd3_clk>, <&sd3_cmd>, <&sd3_bus4>;

	brcmf: wifi@1 {
		compatible = "brcm,bcm4330-fmac", "brcm,bcm4329-fmac";
		reg = <1>;
		interrupt-parent = <&gpx2>;
		interrupts = <5 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "host-wake";
	};
};

&serial_0 {
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;
	pinctrl-0 = <&uart0_data &uart0_fctl>;
	pinctrl-names = "default";

	bluetooth {
		compatible = "brcm,bcm4330-bt";
		pinctrl-0 = <&bt_shutdown &bt_device_wakeup &bt_host_wakeup>;
		pinctrl-names = "default";
		shutdown-gpios = <&gpe1 4 GPIO_ACTIVE_HIGH>;
		device-wakeup-gpios = <&gpx3 1 GPIO_ACTIVE_HIGH>;
		host-wakeup-gpios = <&gpx2 6 GPIO_ACTIVE_HIGH>;
	};
};

&serial_1 {
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;
};

&serial_2 {
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;
};

&serial_3 {
	status = "okay";
	/delete-property/dmas;
	/delete-property/dma-names;
};

&soc {
	mdma0: dma-controller@12840000 {
		compatible = "arm,pl330", "arm,primecell";
		reg = <0x12840000 0x1000>;
		interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&clock CLK_MDMA>;
		clock-names = "apb_pclk";
		#dma-cells = <1>;
		power-domains = <&pd_lcd0>;
	};
};

&sysram {
	smp-sram@0 {
		status = "disabled";
	};

	smp-sram@5000 {
		compatible = "samsung,exynos4210-sysram";
		reg = <0x5000 0x1000>;
	};

	smp-sram@1f000 {
		status = "disabled";
	};
};
