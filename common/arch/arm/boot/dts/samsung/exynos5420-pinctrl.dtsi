// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos5420 SoC pin-mux and pin-config device tree source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Samsung's Exynos5420 SoC pin-mux and pin-config options are listed as device
 * tree nodes in this file.
 */

#include "exynos-pinctrl.h"

&pinctrl_0 {
	gpy7: gpy7-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpx0: gpx0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		interrupt-parent = <&combiner>;
		#interrupt-cells = <2>;
		interrupts = <23 0>, <24 0>, <25 0>, <25 1>,
			     <26 0>, <26 1>, <27 0>, <27 1>;
	};

	gpx1: gpx1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		interrupt-parent = <&combiner>;
		#interrupt-cells = <2>;
		interrupts = <28 0>, <28 1>, <29 0>, <29 1>,
			     <30 0>, <30 1>, <31 0>, <31 1>;
	};

	gpx2: gpx2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpx3: gpx3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	dp_hpd: dp-hpd-pins {
		samsung,pins = "gpx0-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	hdmi_cec: hdmi-cec-pins {
		samsung,pins = "gpx3-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};

&pinctrl_1 {
	gpc0: gpc0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc1: gpc1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc2: gpc2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc3: gpc3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc4: gpc4-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpd1: gpd1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpy0: gpy0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy1: gpy1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy2: gpy2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy3: gpy3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy4: gpy4-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy5: gpy5-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpy6: gpy6-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	sd0_clk: sd0-clk-pins {
		samsung,pins = "gpc0-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd0_cmd: sd0-cmd-pins {
		samsung,pins = "gpc0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd0_cd: sd0-cd-pins {
		samsung,pins = "gpc0-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd0_bus1: sd0-bus-width1-pins {
		samsung,pins = "gpc0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd0_bus4: sd0-bus-width4-pins {
		samsung,pins = "gpc0-4", "gpc0-5", "gpc0-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd0_bus8: sd0-bus-width8-pins {
		samsung,pins = "gpc3-0", "gpc3-1", "gpc3-2", "gpc3-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd0_rclk: sd0-rclk-pins {
		samsung,pins = "gpc0-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_DOWN>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd1_clk: sd1-clk-pins {
		samsung,pins = "gpc1-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd1_cmd: sd1-cmd-pins {
		samsung,pins = "gpc1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd1_cd: sd1-cd-pins {
		samsung,pins = "gpc1-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd1_int: sd1-int-pins {
		samsung,pins = "gpd1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	sd1_bus1: sd1-bus-width1-pins {
		samsung,pins = "gpc1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd1_bus4: sd1-bus-width4-pins {
		samsung,pins = "gpc1-4", "gpc1-5", "gpc1-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd1_bus8: sd1-bus-width8-pins {
		samsung,pins = "gpd1-4", "gpd1-5", "gpd1-6", "gpd1-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd2_clk: sd2-clk-pins {
		samsung,pins = "gpc2-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd2_cmd: sd2-cmd-pins {
		samsung,pins = "gpc2-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd2_cd: sd2-cd-pins {
		samsung,pins = "gpc2-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd2_bus1: sd2-bus-width1-pins {
		samsung,pins = "gpc2-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd2_bus4: sd2-bus-width4-pins {
		samsung,pins = "gpc2-4", "gpc2-5", "gpc2-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	sd2_wp: sd2-wp-pins {
		samsung,pins = "gpc4-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_DOWN>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};
};

&pinctrl_2 {
	gpe0: gpe0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpe1: gpe1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf0: gpf0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpf1: gpf1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpg0: gpg0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpg1: gpg1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpg2: gpg2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpj4: gpj4-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	cam_gpio_a: cam-gpio-a-pins {
		samsung,pins = "gpe0-0", "gpe0-1", "gpe0-2", "gpe0-3",
			       "gpe0-4", "gpe0-5", "gpe0-6", "gpe0-7",
			       "gpe1-0", "gpe1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	cam_gpio_b: cam-gpio-b-pins {
		samsung,pins = "gpf0-0", "gpf0-1", "gpf0-2", "gpf0-3",
			       "gpf1-0", "gpf1-1", "gpf1-2", "gpf1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	cam_i2c2_bus: cam-i2c2-bus-pins {
		samsung,pins = "gpf0-4", "gpf0-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	cam_spi1_bus: cam-spi1-bus-pins {
		samsung,pins = "gpe0-4", "gpe0-5", "gpf0-2", "gpf0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_4>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	cam_i2c1_bus: cam-i2c1-bus-pins {
		samsung,pins = "gpf0-2", "gpf0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	cam_i2c0_bus: cam-i2c0-bus-pins {
		samsung,pins = "gpf0-0", "gpf0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	cam_spi0_bus: cam-spi0-bus-pins {
		samsung,pins = "gpf1-0", "gpf1-1", "gpf1-2", "gpf1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	cam_bayrgb_bus: cam-bayrgb-bus-pins {
		samsung,pins = "gpg0-0", "gpg0-1", "gpg0-2", "gpg0-3",
			       "gpg0-4", "gpg0-5", "gpg0-6", "gpg0-7",
			       "gpg1-0", "gpg1-1", "gpg1-2", "gpg1-3",
			       "gpg1-4", "gpg1-5", "gpg1-6", "gpg1-7",
			       "gpg2-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};

&pinctrl_3 {
	gpa0: gpa0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpa1: gpa1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpa2: gpa2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb0: gpb0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb1: gpb1-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb2: gpb2-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb3: gpb3-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb4: gpb4-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gph0: gph0-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	uart0_data: uart0-data-pins {
		samsung,pins = "gpa0-0", "gpa0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	uart0_fctl: uart0-fctl-pins {
		samsung,pins = "gpa0-2", "gpa0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	uart1_data: uart1-data-pins {
		samsung,pins = "gpa0-4", "gpa0-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	uart1_fctl: uart1-fctl-pins {
		samsung,pins = "gpa0-6", "gpa0-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2c2_bus: i2c2-bus-pins {
		samsung,pins = "gpa0-6", "gpa0-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	uart2_data: uart2-data-pins {
		samsung,pins = "gpa1-0", "gpa1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	uart2_fctl: uart2-fctl-pins {
		samsung,pins = "gpa1-2", "gpa1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2c3_bus: i2c3-bus-pins {
		samsung,pins = "gpa1-2", "gpa1-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	uart3_data: uart3-data-pins {
		samsung,pins = "gpa1-4", "gpa1-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	spi0_bus: spi0-bus-pins {
		samsung,pins = "gpa2-0", "gpa2-1", "gpa2-2", "gpa2-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	spi1_bus: spi1-bus-pins {
		samsung,pins = "gpa2-4", "gpa2-6", "gpa2-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2c4_hs_bus: i2c4-hs-bus-pins {
		samsung,pins = "gpa2-0", "gpa2-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2c5_hs_bus: i2c5-hs-bus-pins {
		samsung,pins = "gpa2-2", "gpa2-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2s1_bus: i2s1-bus-pins {
		samsung,pins = "gpb0-0", "gpb0-1", "gpb0-2", "gpb0-3",
			       "gpb0-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	pcm1_bus: pcm1-bus-pins {
		samsung,pins = "gpb0-0", "gpb0-1", "gpb0-2", "gpb0-3",
			       "gpb0-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2s2_bus: i2s2-bus-pins {
		samsung,pins = "gpb1-0", "gpb1-1", "gpb1-2", "gpb1-3",
			       "gpb1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	pcm2_bus: pcm2-bus-pins {
		samsung,pins = "gpb1-0", "gpb1-1", "gpb1-2", "gpb1-3",
			       "gpb1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	spdif_bus: spdif-bus-pins {
		samsung,pins = "gpb1-0", "gpb1-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_4>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	spi2_bus: spi2-bus-pins {
		samsung,pins = "gpb1-1", "gpb1-3", "gpb1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_5>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2c6_hs_bus: i2c6-hs-bus-pins {
		samsung,pins = "gpb1-3", "gpb1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_4>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	pwm0_out: pwm0-out-pins {
		samsung,pins = "gpb2-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	pwm1_out: pwm1-out-pins {
		samsung,pins = "gpb2-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	pwm2_out: pwm2-out-pins {
		samsung,pins = "gpb2-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	pwm3_out: pwm3-out-pins {
		samsung,pins = "gpb2-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2c7_hs_bus: i2c7-hs-bus-pins {
		samsung,pins = "gpb2-2", "gpb2-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2c0_bus: i2c0-bus-pins {
		samsung,pins = "gpb3-0", "gpb3-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2c1_bus: i2c1-bus-pins {
		samsung,pins = "gpb3-2", "gpb3-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2c8_hs_bus: i2c8-hs-bus-pins {
		samsung,pins = "gpb3-4", "gpb3-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2c9_hs_bus: i2c9-hs-bus-pins {
		samsung,pins = "gpb3-6", "gpb3-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	i2c10_hs_bus: i2c10-hs-bus-pins {
		samsung,pins = "gpb4-0", "gpb4-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};

&pinctrl_4 {
	gpz: gpz-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	i2s0_bus: i2s0-bus-pins {
		samsung,pins = "gpz-0", "gpz-1", "gpz-2", "gpz-3",
				"gpz-4", "gpz-5", "gpz-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};
