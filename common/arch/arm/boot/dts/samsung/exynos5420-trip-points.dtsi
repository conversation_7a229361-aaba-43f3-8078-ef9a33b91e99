// SPDX-License-Identifier: GPL-2.0
/*
 * Device tree sources for default Exynos5420 thermal zone definition
 *
 * Copyright (c) 2014 <PERSON><PERSON><PERSON> <<EMAIL>>
 */

polling-delay-passive = <0>;
polling-delay = <0>;
trips {
	cpu-alert-0 {
		temperature = <85000>; /* millicelsius */
		hysteresis = <10000>; /* millicelsius */
		type = "active";
	};
	cpu-alert-1 {
		temperature = <103000>; /* millicelsius */
		hysteresis = <10000>; /* millicelsius */
		type = "active";
	};
	cpu-alert-2 {
		temperature = <110000>; /* millicelsius */
		hysteresis = <10000>; /* millicelsius */
		type = "active";
	};
	cpu-crit-0 {
		temperature = <120000>; /* millicelsius */
		hysteresis = <0>; /* millicelsius */
		type = "critical";
	};
};
