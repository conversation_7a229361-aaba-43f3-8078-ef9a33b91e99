// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos4412 SoC device tree source
 *
 * Copyright (c) 2012 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Samsung's Exynos4412 SoC device nodes are listed in this file. Exynos4412
 * based board files can include this file and provide values for board specific
 * bindings.
 *
 * Note: This file does not include device nodes for all the controllers in
 * Exynos4412 SoC. As device tree coverage for Exynos4412 increases, additional
 * nodes can be added to this file.
 */

#include "exynos4x12.dtsi"

/ {
	compatible = "samsung,exynos4412", "samsung,exynos4";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu-map {
			cluster0 {
				core0 {
					cpu = <&cpu0>;
				};
				core1 {
					cpu = <&cpu1>;
				};
				core2 {
					cpu = <&cpu2>;
				};
				core3 {
					cpu = <&cpu3>;
				};
			};
		};

		cpu0: cpu@a00 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0xa00>;
			clocks = <&clock CLK_ARM_CLK>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
		};

		cpu1: cpu@a01 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0xa01>;
			clocks = <&clock CLK_ARM_CLK>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
		};

		cpu2: cpu@a02 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0xa02>;
			clocks = <&clock CLK_ARM_CLK>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
		};

		cpu3: cpu@a03 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0xa03>;
			clocks = <&clock CLK_ARM_CLK>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
		};
	};

	cpu0_opp_table: opp-table-0 {
		compatible = "operating-points-v2";
		opp-shared;

		opp-200000000 {
			opp-hz = /bits/ 64 <200000000>;
			opp-microvolt = <900000>;
			clock-latency-ns = <200000>;
		};
		opp-300000000 {
			opp-hz = /bits/ 64 <300000000>;
			opp-microvolt = <900000>;
			clock-latency-ns = <200000>;
		};
		opp-400000000 {
			opp-hz = /bits/ 64 <400000000>;
			opp-microvolt = <925000>;
			clock-latency-ns = <200000>;
		};
		opp-500000000 {
			opp-hz = /bits/ 64 <500000000>;
			opp-microvolt = <950000>;
			clock-latency-ns = <200000>;
		};
		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <975000>;
			clock-latency-ns = <200000>;
		};
		opp-700000000 {
			opp-hz = /bits/ 64 <700000000>;
			opp-microvolt = <987500>;
			clock-latency-ns = <200000>;
		};
		opp-800000000 {
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt = <1000000>;
			clock-latency-ns = <200000>;
			opp-suspend;
		};
		opp-900000000 {
			opp-hz = /bits/ 64 <900000000>;
			opp-microvolt = <1037500>;
			clock-latency-ns = <200000>;
		};
		opp-1000000000 {
			opp-hz = /bits/ 64 <1000000000>;
			opp-microvolt = <1087500>;
			clock-latency-ns = <200000>;
		};
		opp-1100000000 {
			opp-hz = /bits/ 64 <1100000000>;
			opp-microvolt = <1137500>;
			clock-latency-ns = <200000>;
		};
		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <1187500>;
			clock-latency-ns = <200000>;
		};
		opp-1300000000 {
			opp-hz = /bits/ 64 <1300000000>;
			opp-microvolt = <1250000>;
			clock-latency-ns = <200000>;
		};
		opp-1400000000 {
			opp-hz = /bits/ 64 <1400000000>;
			opp-microvolt = <1287500>;
			clock-latency-ns = <200000>;
		};
		cpu0_opp_1500: opp-1500000000 {
			opp-hz = /bits/ 64 <1500000000>;
			opp-microvolt = <1350000>;
			clock-latency-ns = <200000>;
			turbo-mode;
		};
	};
};

&clock {
	compatible = "samsung,exynos4412-clock";
};

&combiner {
	samsung,combiner-nr = <20>;
};

&gic {
	cpu-offset = <0x4000>;
};

&pmu {
	interrupts = <2 2>, <3 2>, <18 2>, <19 2>;
	interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
	status = "okay";
};

&pmu_system_controller {
	compatible = "samsung,exynos4412-pmu", "simple-mfd", "syscon";
};
