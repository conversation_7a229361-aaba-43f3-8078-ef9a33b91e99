// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung XYREF5260 board device tree source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

/dts-v1/;
#include "exynos5260.dtsi"

/ {
	model = "Samsung XYREF5260 board based on Exynos5260";
	compatible = "samsung,xyref5260", "samsung,exynos5260", "samsung,exynos5";

	memory@20000000 {
		device_type = "memory";
		reg = <0x20000000 0x80000000>;
	};

	aliases {
		mmc0 = &mmc_0;
		mmc1 = &mmc_2;
	};

	chosen {
		stdout-path = "serial2:115200n8";
	};

	fin_pll: xxti {
		compatible = "fixed-clock";
		clock-frequency = <24000000>;
		clock-output-names = "fin_pll";
		#clock-cells = <0>;
	};

	ioclk_pcm: clock-pcm-ext {
		compatible = "fixed-clock";
		clock-frequency = <2048000>;
		clock-output-names = "ioclk_pcm_extclk";
		#clock-cells = <0>;
	};

	ioclk_i2s: clock-i2s-cd {
		compatible = "fixed-clock";
		clock-frequency = <147456000>;
		clock-output-names = "ioclk_i2s_cdclk";
		#clock-cells = <0>;
	};

	ioclk_spdif: clock-spdif-ext {
		compatible = "fixed-clock";
		clock-frequency = <49152000>;
		clock-output-names = "ioclk_spdif_extclk";
		#clock-cells = <0>;
	};

	xrtcxti: xrtcxti {
		compatible = "fixed-clock";
		clock-frequency = <32768>;
		clock-output-names = "xrtcxti";
		#clock-cells = <0>;
	};
};

&pinctrl_0 {
	hdmi_hpd_irq: hdmi-hpd-irq-pins {
		samsung,pins = "gpx3-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_DOWN>;
		samsung,pin-drv = <EXYNOS5260_PIN_DRV_LV1>;
	};
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&mmc_0 {
	status = "okay";
	broken-cd;
	cap-mmc-highspeed;
	mmc-hs200-1_8v;
	card-detect-delay = <200>;
	mmc-ddr-1_8v;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <0 4>;
	samsung,dw-mshc-ddr-timing = <0 2>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_rdqs &sd0_clk &sd0_cmd &sd0_bus1 &sd0_bus4 &sd0_bus8>;
	bus-width = <8>;
};

&mmc_2 {
	status = "okay";
	cap-sd-highspeed;
	card-detect-delay = <200>;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_cd &sd2_bus1 &sd2_bus4>;
	bus-width = <4>;
	disable-wp;
};
