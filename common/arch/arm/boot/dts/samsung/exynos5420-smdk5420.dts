// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung SMDK5420 board device tree source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

/dts-v1/;
#include "exynos5420.dtsi"
#include "exynos5420-cpus.dtsi"
#include <dt-bindings/clock/samsung,s2mps11.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Samsung SMDK5420 board based on Exynos5420";
	compatible = "samsung,smdk5420", "samsung,exynos5420", "samsung,exynos5";

	memory@20000000 {
		device_type = "memory";
		reg = <0x20000000 0x80000000>;
	};

	aliases {
		mmc0 = &mmc_0;
		mmc1 = &mmc_2;
	};

	chosen {
		bootargs = "init=/linuxrc";
		stdout-path = "serial2:115200n8";
	};

	fixed-rate-clocks {
		oscclk {
			compatible = "samsung,exynos5420-oscclk";
			clock-frequency = <24000000>;
		};
	};

	vdd: regulator-0 {
		compatible = "regulator-fixed";
		regulator-name = "vdd-supply";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	dbvdd: regulator-1 {
		compatible = "regulator-fixed";
		regulator-name = "dbvdd-supply";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	spkvdd: regulator-2 {
		compatible = "regulator-fixed";
		regulator-name = "spkvdd-supply";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};

	usb300_vbus_reg: regulator-3 {
		compatible = "regulator-fixed";
		regulator-name = "VBUS0";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&gpg0 5 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&usb300_vbus_en>;
		enable-active-high;
	};

	usb301_vbus_reg: regulator-4 {
		compatible = "regulator-fixed";
		regulator-name = "VBUS1";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&gpg1 4 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&usb301_vbus_en>;
		enable-active-high;
	};

};

&cpu0 {
	cpu-supply = <&buck2_reg>;
};

&cpu4 {
	cpu-supply = <&buck6_reg>;
};

&dp {
	pinctrl-names = "default";
	pinctrl-0 = <&dp_hpd>;
	samsung,color-space = <0>;
	samsung,color-depth = <1>;
	samsung,link-rate = <0x0a>;
	samsung,lane-count = <4>;
	status = "okay";

	display-timings {
		native-mode = <&timing0>;
		timing0: timing {
			clock-frequency = <50000>;
			hactive = <2560>;
			vactive = <1600>;
			hfront-porch = <48>;
			hback-porch = <80>;
			hsync-len = <32>;
			vback-porch = <16>;
			vfront-porch = <8>;
			vsync-len = <6>;
		};
	};
};

&fimd {
	status = "okay";
};

&hdmi {
	status = "okay";
	ddc = <&i2c_2>;
	hpd-gpios = <&gpx3 7 GPIO_ACTIVE_HIGH>;
	pinctrl-names = "default";
	pinctrl-0 = <&hdmi_hpd_irq>;
	vdd-supply = <&ldo6_reg>;
	vdd_osc-supply = <&ldo7_reg>;
	vdd_pll-supply = <&ldo6_reg>;
};

&hsi2c_4 {
	status = "okay";

	pmic@66 {
		compatible = "samsung,s2mps11-pmic";
		reg = <0x66>;
		wakeup-source;

		s2mps11_osc: clocks {
			compatible = "samsung,s2mps11-clk";
			#clock-cells = <1>;
			clock-output-names = "s2mps11_ap",
					"s2mps11_cp", "s2mps11_bt";
		};

		regulators {
			ldo1_reg: LDO1 {
				regulator-name = "vdd_ldo1";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo3_reg: LDO3 {
				regulator-name = "vdd_ldo3";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo5_reg: LDO5 {
				regulator-name = "vdd_ldo5";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo6_reg: LDO6 {
				regulator-name = "vdd_ldo6";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo7_reg: LDO7 {
				regulator-name = "vdd_ldo7";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo8_reg: LDO8 {
				regulator-name = "vdd_ldo8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo9_reg: LDO9 {
				regulator-name = "vdd_ldo9";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-always-on;
			};

			ldo10_reg: LDO10 {
				regulator-name = "vdd_ldo10";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo11_reg: LDO11 {
				regulator-name = "vdd_ldo11";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo12_reg: LDO12 {
				regulator-name = "vdd_ldo12";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo13_reg: LDO13 {
				regulator-name = "vdd_ldo13";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
			};

			ldo15_reg: LDO15 {
				regulator-name = "vdd_ldo15";
				regulator-min-microvolt = <3100000>;
				regulator-max-microvolt = <3100000>;
				regulator-always-on;
			};

			ldo16_reg: LDO16 {
				regulator-name = "vdd_ldo16";
				regulator-min-microvolt = <2200000>;
				regulator-max-microvolt = <2200000>;
				regulator-always-on;
			};

			ldo17_reg: LDO17 {
				regulator-name = "tsp_avdd";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			ldo19_reg: LDO19 {
				regulator-name = "vdd_sd";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
			};

			ldo24_reg: LDO24 {
				regulator-name = "tsp_io";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
			};

			buck1_reg: BUCK1 {
				regulator-name = "vdd_mif";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1300000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck2_reg: BUCK2 {
				regulator-name = "vdd_arm";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck3_reg: BUCK3 {
				regulator-name = "vdd_int";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1400000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck4_reg: BUCK4 {
				regulator-name = "vdd_g3d";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1400000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck5_reg: BUCK5 {
				regulator-name = "vdd_mem";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1400000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck6_reg: BUCK6 {
				regulator-name = "vdd_kfc";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck7_reg: BUCK7 {
				regulator-name = "vdd_1.0v_ldo";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck8_reg: BUCK8 {
				regulator-name = "vdd_1.8v_ldo";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck9_reg: BUCK9 {
				regulator-name = "vdd_2.8v_ldo";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3750000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck10_reg: BUCK10 {
				regulator-name = "vdd_vmem";
				regulator-min-microvolt = <2850000>;
				regulator-max-microvolt = <2850000>;
				regulator-always-on;
				regulator-boot-on;
			};
		};
	};
};

&i2c_2 {
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <66000>;
	/* used by HDMI DDC */
	status = "okay";
};

&mixer {
	status = "okay";
};

&mmc_0 {
	status = "okay";
	broken-cd;
	card-detect-delay = <200>;
	mmc-ddr-1_8v;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <0 4>;
	samsung,dw-mshc-ddr-timing = <0 2>;
	samsung,dw-mshc-hs400-timing = <0 2>;
	samsung,read-strobe-delay = <90>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_bus1 &sd0_bus4 &sd0_bus8
		     &sd0_rclk>;
	bus-width = <8>;
	cap-mmc-highspeed;
};

&mmc_2 {
	status = "okay";
	card-detect-delay = <200>;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_cd &sd2_bus1 &sd2_bus4>;
	bus-width = <4>;
	cap-sd-highspeed;
};

&pinctrl_0 {
	hdmi_hpd_irq: hdmi-hpd-irq-pins {
		samsung,pins = "gpx3-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_DOWN>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};

&pinctrl_2 {
	usb300_vbus_en: usb300-vbus-en-pins {
		samsung,pins = "gpg0-5";
		samsung,pin-function = <EXYNOS_PIN_FUNC_OUTPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	usb301_vbus_en: usb301-vbus-en-pins {
		samsung,pins = "gpg1-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_OUTPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&s2mps11_osc S2MPS11_CLK_AP>;
	clock-names = "rtc", "rtc_src";
};

&usbdrd3_0 {
	vdd10-supply = <&ldo11_reg>;
	vdd33-supply = <&ldo9_reg>;
};

&usbdrd3_1 {
	vdd10-supply = <&ldo11_reg>;
	vdd33-supply = <&ldo9_reg>;
};

&usbdrd_phy0 {
	vbus-supply = <&usb300_vbus_reg>;
};

&usbdrd_phy1 {
	vbus-supply = <&usb301_vbus_reg>;
};
