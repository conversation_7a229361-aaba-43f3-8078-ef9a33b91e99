// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos5420 Klimt WiFi board device tree source
 *
 * Copyright (c) 2012-2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 * Copyright (c) 2022 <PERSON>
 */

/dts-v1/;
#include "exynos5420-galaxy-tab-common.dtsi"

/ {
	model = "Samsung Klimt WiFi based on Exynos5420";
	compatible = "samsung,klimt-wifi", "samsung,exynos5420", \
		     "samsung,exynos5";
};

&ldo15_reg {
	/* Unused */
	regulator-name = "VDD_LDO15";
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
};

&ldo17_reg {
	regulator-name = "VDD_VCI_3V0";
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;

	regulator-state-mem {
		regulator-off-in-suspend;
	};
};

&ldo28_reg {
	regulator-name = "VDD3_1V8";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;

	regulator-state-mem {
		regulator-off-in-suspend;
	};
};

&ldo29_reg {
	regulator-name = "VDDR_1V6";
	regulator-min-microvolt = <1600000>;
	regulator-max-microvolt = <1600000>;

	regulator-state-mem {
		regulator-off-in-suspend;
	};
};

&ldo31_reg {
	/* Unused */
	regulator-name = "VDD_LDO31";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
};

&ldo32_reg {
	regulator-name = "VDD_TSP_1V8";
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;

	regulator-state-mem {
		regulator-off-in-suspend;
	};
};

&mmc_2 {
	sd-uhs-sdr104;
};
