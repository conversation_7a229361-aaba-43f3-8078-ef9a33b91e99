// SPDX-License-Identifier: GPL-2.0
/*
 * Device tree sources for Exynos4412 PPMU common device tree
 *
 * Copyright (C) 2015 Samsung Electronics
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 */

&ppmu_dmc0 {
	status = "okay";

	events {
		ppmu_dmc0_3: ppmu-event3-dmc0 {
			event-name = "ppmu-event3-dmc0";
		};
	};
};

&ppmu_dmc1 {
	status = "okay";

	events {
		ppmu_dmc1_3: ppmu-event3-dmc1 {
			event-name = "ppmu-event3-dmc1";
		};
	};
};

&ppmu_leftbus {
	status = "okay";

	events {
		ppmu_leftbus_3: ppmu-event3-leftbus {
			event-name = "ppmu-event3-leftbus";
		};
	};
};

&ppmu_rightbus {
	status = "okay";

	events {
		ppmu_rightbus_3: ppmu-event3-rightbus {
			event-name = "ppmu-event3-rightbus";
		};
	};
};
