// SPDX-License-Identifier: GPL-2.0
/*
 * Hardkernel Odroid XU4 board device tree source
 *
 * Copyright (c) 2015 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Copyright (c) 2014 Collabora Ltd.
 * Copyright (c) 2013-2015 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

/dts-v1/;
#include <dt-bindings/leds/common.h>
#include <dt-bindings/sound/samsung-i2s.h>
#include "exynos5422-odroidxu3-common.dtsi"

/ {
	model = "Hardkernel Odroid XU4";
	compatible = "hardkernel,odroid-xu4", "samsung,exynos5800", \
		     "samsung,exynos5";

	led-controller {
		compatible = "pwm-leds";

		led-1 {
			function = LED_FUNCTION_HEARTBEAT;
			color = <LED_COLOR_ID_BLUE>;
			pwms = <&pwm 2 2000000 0>;
			pwm-names = "pwm2";
			max-brightness = <255>;
			linux,default-trigger = "heartbeat";
		};
	};

	sound: sound {
		compatible = "samsung,odroid-xu3-audio";
		model = "Odroid-XU4";

		samsung,audio-routing = "I2S Playback", "Mixer DAI TX";

		cpu {
			sound-dai = <&i2s0 0>, <&i2s0 1>;
		};

		codec {
			sound-dai = <&hdmi>;
		};
	};
};

&i2s0 {
	status = "okay";

	assigned-clocks = <&clock CLK_MOUT_EPLL>,
			  <&clock CLK_MOUT_MAU_EPLL>,
			  <&clock CLK_MOUT_USER_MAU_EPLL>,
			  <&clock_audss EXYNOS_MOUT_AUDSS>,
			  <&clock_audss EXYNOS_MOUT_I2S>,
			  <&i2s0 CLK_I2S_RCLK_SRC>,
			  <&clock_audss EXYNOS_DOUT_SRP>,
			  <&clock_audss EXYNOS_DOUT_AUD_BUS>,
			  <&clock_audss EXYNOS_DOUT_I2S>;

	assigned-clock-parents = <&clock CLK_FOUT_EPLL>,
				 <&clock CLK_MOUT_EPLL>,
				 <&clock CLK_MOUT_MAU_EPLL>,
				 <&clock CLK_MAU_EPLL>,
				 <&clock_audss EXYNOS_MOUT_AUDSS>,
				 <&clock_audss EXYNOS_SCLK_I2S>;

	assigned-clock-rates = <0>,
			       <0>,
			       <0>,
			       <0>,
			       <0>,
			       <0>,
			       <*********>,
			       <(********* / 2)>,
			       <*********>;
};

&pwm {
	/*
	 * PWM 0 -- fan
	 * PWM 2 -- Blue LED
	 */
	pinctrl-0 = <&pwm0_out &pwm2_out>;
	pinctrl-names = "default";
	samsung,pwm-outputs = <0>, <2>;
	status = "okay";
};

&usbdrd_dwc3_1 {
	dr_mode = "host";
};
