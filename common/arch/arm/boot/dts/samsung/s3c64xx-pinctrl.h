/* SPDX-License-Identifier: GPL-2.0 */
/*
 * Samsung S3C64xx DTS pinctrl constants
 *
 * Copyright (c) 2016 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 * Copyright (c) 2022 Linaro Ltd
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 */

#ifndef __DTS_ARM_SAMSUNG_S3C64XX_PINCTRL_H__
#define __DTS_ARM_SAMSUNG_S3C64XX_PINCTRL_H__

#define S3C64XX_PIN_PULL_NONE		0
#define S3C64XX_PIN_PULL_DOWN		1
#define S3C64XX_PIN_PULL_UP		2

#define S3C64XX_PIN_FUNC_INPUT		0
#define S3C64XX_PIN_FUNC_OUTPUT		1
#define S3C64XX_PIN_FUNC_2		2
#define S3C64XX_PIN_FUNC_3		3
#define S3C64XX_PIN_FUNC_4		4
#define S3C64XX_PIN_FUNC_5		5
#define S3C64XX_PIN_FUNC_6		6
#define S3C64XX_PIN_FUNC_EINT		7

#endif /* __DTS_ARM_SAMSUNG_S3C64XX_PINCTRL_H__ */
