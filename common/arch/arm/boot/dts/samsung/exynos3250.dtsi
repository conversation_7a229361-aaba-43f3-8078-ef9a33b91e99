// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos3250 SoC device tree source
 *
 * Copyright (c) 2014 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Samsung's Exynos3250 SoC device nodes are listed in this file. Exynos3250
 * based board files can include this file and provide values for board specific
 * bindings.
 *
 * Note: This file does not include device nodes for all the controllers in
 * Exynos3250 SoC. As device tree coverage for Exynos3250 increases, additional
 * nodes can be added to this file.
 */

#include "exynos4-cpu-thermal.dtsi"
#include <dt-bindings/clock/exynos3250.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	compatible = "samsung,exynos3250";
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	aliases {
		pinctrl0 = &pinctrl_0;
		pinctrl1 = &pinctrl_1;
		spi0 = &spi_0;
		spi1 = &spi_1;
		i2c0 = &i2c_0;
		i2c1 = &i2c_1;
		i2c2 = &i2c_2;
		i2c3 = &i2c_3;
		i2c4 = &i2c_4;
		i2c5 = &i2c_5;
		i2c6 = &i2c_6;
		i2c7 = &i2c_7;
		serial0 = &serial_0;
		serial1 = &serial_1;
		serial2 = &serial_2;
	};

	bus_dmc: bus-dmc {
		compatible = "samsung,exynos-bus";
		clocks = <&cmu_dmc CLK_DIV_DMC>;
		clock-names = "bus";
		operating-points-v2 = <&bus_dmc_opp_table>;
		status = "disabled";

		bus_dmc_opp_table: opp-table {
			compatible = "operating-points-v2";

			opp-50000000 {
				opp-hz = /bits/ 64 <50000000>;
				opp-microvolt = <800000>;
			};
			opp-100000000 {
				opp-hz = /bits/ 64 <100000000>;
				opp-microvolt = <800000>;
			};
			opp-134000000 {
				opp-hz = /bits/ 64 <134000000>;
				opp-microvolt = <800000>;
			};
			opp-200000000 {
				opp-hz = /bits/ 64 <200000000>;
				opp-microvolt = <825000>;
			};
			opp-400000000 {
				opp-hz = /bits/ 64 <400000000>;
				opp-microvolt = <875000>;
			};
		};
	};

	bus_fsys: bus-fsys {
		compatible = "samsung,exynos-bus";
		clocks = <&cmu CLK_DIV_ACLK_200>;
		clock-names = "bus";
		operating-points-v2 = <&bus_leftbus_opp_table>;
		status = "disabled";
	};

	bus_isp: bus-isp {
		compatible = "samsung,exynos-bus";
		clocks = <&cmu CLK_DIV_ACLK_266>;
		clock-names = "bus";
		operating-points-v2 = <&bus_isp_opp_table>;
		status = "disabled";

		bus_isp_opp_table: opp-table {
			compatible = "operating-points-v2";

			opp-50000000 {
				opp-hz = /bits/ 64 <50000000>;
			};
			opp-80000000 {
				opp-hz = /bits/ 64 <80000000>;
			};
			opp-100000000 {
				opp-hz = /bits/ 64 <100000000>;
			};
			opp-200000000 {
				opp-hz = /bits/ 64 <200000000>;
			};
			opp-300000000 {
				opp-hz = /bits/ 64 <300000000>;
			};
		};
	};

	bus_lcd0: bus-lcd0 {
		compatible = "samsung,exynos-bus";
		clocks = <&cmu CLK_DIV_ACLK_160>;
		clock-names = "bus";
		operating-points-v2 = <&bus_leftbus_opp_table>;
		status = "disabled";
	};

	bus_leftbus: bus-leftbus {
		compatible = "samsung,exynos-bus";
		clocks = <&cmu CLK_DIV_GDL>;
		clock-names = "bus";
		operating-points-v2 = <&bus_leftbus_opp_table>;
		status = "disabled";
	};

	bus_mcuisp: bus-mcuisp {
		compatible = "samsung,exynos-bus";
		clocks = <&cmu CLK_DIV_ACLK_400_MCUISP>;
		clock-names = "bus";
		operating-points-v2 = <&bus_mcuisp_opp_table>;
		status = "disabled";

		bus_mcuisp_opp_table: opp-table {
			compatible = "operating-points-v2";

			opp-50000000 {
				opp-hz = /bits/ 64 <50000000>;
			};
			opp-80000000 {
				opp-hz = /bits/ 64 <80000000>;
			};
			opp-100000000 {
				opp-hz = /bits/ 64 <100000000>;
			};
			opp-200000000 {
				opp-hz = /bits/ 64 <200000000>;
			};
			opp-400000000 {
				opp-hz = /bits/ 64 <400000000>;
			};
		};
	};

	bus_mfc: bus-mfc {
		compatible = "samsung,exynos-bus";
		clocks = <&cmu CLK_SCLK_MFC>;
		clock-names = "bus";
		operating-points-v2 = <&bus_leftbus_opp_table>;
		status = "disabled";
	};

	bus_peril: bus-peril {
		compatible = "samsung,exynos-bus";
		clocks = <&cmu CLK_DIV_ACLK_100>;
		clock-names = "bus";
		operating-points-v2 = <&bus_peril_opp_table>;
		status = "disabled";

		bus_peril_opp_table: opp-table {
			compatible = "operating-points-v2";

			opp-50000000 {
				opp-hz = /bits/ 64 <50000000>;
			};
			opp-80000000 {
				opp-hz = /bits/ 64 <80000000>;
			};
			opp-100000000 {
				opp-hz = /bits/ 64 <100000000>;
			};
		};
	};

	bus_rightbus: bus-rightbus {
		compatible = "samsung,exynos-bus";
		clocks = <&cmu CLK_DIV_GDR>;
		clock-names = "bus";
		operating-points-v2 = <&bus_leftbus_opp_table>;
		status = "disabled";
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu-map {
			cluster0 {
				core0 {
					cpu = <&cpu0>;
				};
				core1 {
					cpu = <&cpu1>;
				};
			};
		};

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0>;
			clock-frequency = <1000000000>;
			clocks = <&cmu CLK_ARM_CLK>;
			clock-names = "cpu";
			#cooling-cells = <2>;

			operating-points = <
				1000000 1150000
				900000  1112500
				800000  1075000
				700000  1037500
				600000  1000000
				500000  962500
				400000  925000
				300000  887500
				200000  850000
				100000  850000
			>;
		};

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <1>;
			clock-frequency = <1000000000>;
			clocks = <&cmu CLK_ARM_CLK>;
			clock-names = "cpu";
			#cooling-cells = <2>;

			operating-points = <
				1000000 1150000
				900000  1112500
				800000  1075000
				700000  1037500
				600000  1000000
				500000  962500
				400000  925000
				300000  887500
				200000  850000
				100000  850000
			>;
		};
	};

	xusbxti: clock-0 {
		compatible = "fixed-clock";
		clock-frequency = <0>;
		#clock-cells = <0>;
		clock-output-names = "xusbxti";
	};

	xxti: clock-1 {
		compatible = "fixed-clock";
		clock-frequency = <0>;
		#clock-cells = <0>;
		clock-output-names = "xxti";
	};

	xtcxo: clock-2 {
		compatible = "fixed-clock";
		clock-frequency = <0>;
		#clock-cells = <0>;
		clock-output-names = "xtcxo";
	};

	bus_leftbus_opp_table: opp-table-0 {
		compatible = "operating-points-v2";

		opp-50000000 {
			opp-hz = /bits/ 64 <50000000>;
			opp-microvolt = <900000>;
		};
		opp-80000000 {
			opp-hz = /bits/ 64 <80000000>;
			opp-microvolt = <900000>;
		};
		opp-100000000 {
			opp-hz = /bits/ 64 <100000000>;
			opp-microvolt = <1000000>;
		};
		opp-134000000 {
			opp-hz = /bits/ 64 <134000000>;
			opp-microvolt = <1000000>;
		};
		opp-200000000 {
			opp-hz = /bits/ 64 <200000000>;
			opp-microvolt = <1000000>;
		};
	};

	pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
	};

	soc: soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		sram@2020000 {
			compatible = "mmio-sram";
			reg = <0x02020000 0x40000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x02020000 0x40000>;

			smp-sram@0 {
				compatible = "samsung,exynos4210-sysram";
				reg = <0x0 0x1000>;
			};

			smp-sram@3f000 {
				compatible = "samsung,exynos4210-sysram-ns";
				reg = <0x3f000 0x1000>;
			};
		};

		chipid@10000000 {
			compatible = "samsung,exynos4210-chipid";
			reg = <0x10000000 0x100>;
		};

		sys_reg: syscon@10010000 {
			compatible = "samsung,exynos3-sysreg", "syscon";
			reg = <0x10010000 0x400>;
		};

		pmu_system_controller: system-controller@10020000 {
			compatible = "samsung,exynos3250-pmu", "simple-mfd", "syscon";
			reg = <0x10020000 0x4000>;
			interrupt-controller;
			#interrupt-cells = <3>;
			interrupt-parent = <&gic>;
			clock-names = "clkout8";
			clocks = <&cmu CLK_FIN_PLL>;
			#clock-cells = <1>;

			mipi_phy: mipi-phy {
				compatible = "samsung,s5pv210-mipi-video-phy";
				#phy-cells = <1>;
			};
		};

		pd_cam: power-domain@10023c00 {
			compatible = "samsung,exynos4210-pd";
			reg = <0x10023c00 0x20>;
			#power-domain-cells = <0>;
			label = "CAM";
		};

		pd_mfc: power-domain@10023c40 {
			compatible = "samsung,exynos4210-pd";
			reg = <0x10023c40 0x20>;
			#power-domain-cells = <0>;
			label = "MFC";
		};

		pd_g3d: power-domain@10023c60 {
			compatible = "samsung,exynos4210-pd";
			reg = <0x10023c60 0x20>;
			#power-domain-cells = <0>;
			label = "G3D";
		};

		pd_lcd0: power-domain@10023c80 {
			compatible = "samsung,exynos4210-pd";
			reg = <0x10023c80 0x20>;
			#power-domain-cells = <0>;
			label = "LCD0";
		};

		pd_isp: power-domain@10023ca0 {
			compatible = "samsung,exynos4210-pd";
			reg = <0x10023ca0 0x20>;
			#power-domain-cells = <0>;
			label = "ISP";
		};

		cmu: clock-controller@10030000 {
			compatible = "samsung,exynos3250-cmu";
			reg = <0x10030000 0x20000>;
			#clock-cells = <1>;
			assigned-clocks = <&cmu CLK_MOUT_ACLK_400_MCUISP_SUB>,
					  <&cmu CLK_MOUT_ACLK_266_SUB>;
			assigned-clock-parents = <&cmu CLK_FIN_PLL>,
						 <&cmu CLK_FIN_PLL>;
		};

		cmu_dmc: clock-controller@105c0000 {
			compatible = "samsung,exynos3250-cmu-dmc";
			reg = <0x105c0000 0x2000>;
			#clock-cells = <1>;
		};

		rtc: rtc@10070000 {
			compatible = "samsung,s3c6410-rtc";
			reg = <0x10070000 0x100>;
			interrupts = <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-parent = <&pmu_system_controller>;
			status = "disabled";
		};

		tmu: tmu@100c0000 {
			compatible = "samsung,exynos3250-tmu";
			reg = <0x100c0000 0x100>;
			interrupts = <GIC_SPI 216 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_TMU_APBIF>;
			clock-names = "tmu_apbif";
			#thermal-sensor-cells = <0>;
			status = "disabled";
		};

		gic: interrupt-controller@10481000 {
			compatible = "arm,cortex-a15-gic";
			#interrupt-cells = <3>;
			interrupt-controller;
			reg = <0x10481000 0x1000>,
			      <0x10482000 0x2000>,
			      <0x10484000 0x2000>,
			      <0x10486000 0x2000>;
			interrupts = <GIC_PPI 9
					(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		};

		timer@10050000 {
			compatible = "samsung,exynos3250-mct",
				     "samsung,exynos4210-mct";
			reg = <0x10050000 0x800>;
			interrupts = <GIC_SPI 218 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 219 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 220 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 221 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 223 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 226 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 227 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 228 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_FIN_PLL>, <&cmu CLK_MCT>;
			clock-names = "fin_pll", "mct";
		};

		pinctrl_1: pinctrl@11000000 {
			compatible = "samsung,exynos3250-pinctrl";
			reg = <0x11000000 0x1000>;
			interrupts = <GIC_SPI 225 IRQ_TYPE_LEVEL_HIGH>;

			wakeup-interrupt-controller {
				compatible = "samsung,exynos4210-wakeup-eint";
				interrupts = <GIC_SPI 48 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		pinctrl_0: pinctrl@11400000 {
			compatible = "samsung,exynos3250-pinctrl";
			reg = <0x11400000 0x1000>;
			interrupts = <GIC_SPI 240 IRQ_TYPE_LEVEL_HIGH>;
		};

		jpeg: codec@11830000 {
			compatible = "samsung,exynos3250-jpeg";
			reg = <0x11830000 0x1000>;
			interrupts = <GIC_SPI 171 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_JPEG>, <&cmu CLK_SCLK_JPEG>;
			clock-names = "jpeg", "sclk";
			power-domains = <&pd_cam>;
			assigned-clocks = <&cmu CLK_MOUT_CAM_BLK>, <&cmu CLK_SCLK_JPEG>;
			assigned-clock-rates = <0>, <150000000>;
			assigned-clock-parents = <&cmu CLK_DIV_MPLL_PRE>;
			iommus = <&sysmmu_jpeg>;
			status = "disabled";
		};

		sysmmu_jpeg: sysmmu@11a60000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x11a60000 0x1000>;
			interrupts = <GIC_SPI 156 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "sysmmu", "master";
			clocks = <&cmu CLK_SMMUJPEG>, <&cmu CLK_JPEG>;
			power-domains = <&pd_cam>;
			#iommu-cells = <0>;
		};

		fimd: fimd@11c00000 {
			compatible = "samsung,exynos3250-fimd";
			reg = <0x11c00000 0x30000>;
			interrupt-names = "fifo", "vsync", "lcd_sys";
			interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_SCLK_FIMD0>, <&cmu CLK_FIMD0>;
			clock-names = "sclk_fimd", "fimd";
			power-domains = <&pd_lcd0>;
			iommus = <&sysmmu_fimd0>;
			samsung,sysreg = <&sys_reg>;
			status = "disabled";
		};

		dsi_0: dsi@11c80000 {
			compatible = "samsung,exynos3250-mipi-dsi";
			reg = <0x11c80000 0x10000>;
			interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
			samsung,phy-type = <0>;
			power-domains = <&pd_lcd0>;
			phys = <&mipi_phy 1>;
			phy-names = "dsim";
			clocks = <&cmu CLK_DSIM0>, <&cmu CLK_SCLK_MIPI0>;
			clock-names = "bus_clk", "pll_clk";
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		sysmmu_fimd0: sysmmu@11e20000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x11e20000 0x1000>;
			interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "sysmmu", "master";
			clocks = <&cmu CLK_SMMUFIMD0>, <&cmu CLK_FIMD0>;
			power-domains = <&pd_lcd0>;
			#iommu-cells = <0>;
		};

		hsotg: usb@12480000 {
			compatible = "samsung,s3c6400-hsotg";
			reg = <0x12480000 0x20000>;
			interrupts = <GIC_SPI 141 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_USBOTG>;
			clock-names = "otg";
			phys = <&exynos_usbphy 0>;
			phy-names = "usb2-phy";
			status = "disabled";
		};

		mshc_0: mmc@12510000 {
			compatible = "samsung,exynos5420-dw-mshc";
			reg = <0x12510000 0x1000>;
			interrupts = <GIC_SPI 142 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_SDMMC0>, <&cmu CLK_SCLK_MMC0>;
			clock-names = "biu", "ciu";
			fifo-depth = <0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		mshc_1: mmc@12520000 {
			compatible = "samsung,exynos5420-dw-mshc";
			reg = <0x12520000 0x1000>;
			interrupts = <GIC_SPI 143 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_SDMMC1>, <&cmu CLK_SCLK_MMC1>;
			clock-names = "biu", "ciu";
			fifo-depth = <0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		mshc_2: mmc@12530000 {
			compatible = "samsung,exynos5250-dw-mshc";
			reg = <0x12530000 0x1000>;
			interrupts = <GIC_SPI 144 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_SDMMC2>, <&cmu CLK_SCLK_MMC2>;
			clock-names = "biu", "ciu";
			fifo-depth = <0x80>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		exynos_usbphy: usb-phy@125b0000 {
			compatible = "samsung,exynos3250-usb2-phy";
			reg = <0x125b0000 0x100>;
			samsung,pmureg-phandle = <&pmu_system_controller>;
			clocks = <&cmu CLK_USBOTG>, <&cmu CLK_SCLK_UPLL>;
			clock-names = "phy", "ref";
			#phy-cells = <1>;
			status = "disabled";
		};

		pdma0: dma-controller@12680000 {
			compatible = "arm,pl330", "arm,primecell";
			reg = <0x12680000 0x1000>;
			interrupts = <GIC_SPI 138 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_PDMA0>;
			clock-names = "apb_pclk";
			#dma-cells = <1>;
		};

		pdma1: dma-controller@12690000 {
			compatible = "arm,pl330", "arm,primecell";
			reg = <0x12690000 0x1000>;
			interrupts = <GIC_SPI 139 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_PDMA1>;
			clock-names = "apb_pclk";
			#dma-cells = <1>;
		};

		adc: adc@126c0000 {
			compatible = "samsung,exynos3250-adc";
			reg = <0x126c0000 0x100>;
			interrupts = <GIC_SPI 137 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "adc", "sclk";
			clocks = <&cmu CLK_TSADC>, <&cmu CLK_SCLK_TSADC>;
			#io-channel-cells = <1>;
			samsung,syscon-phandle = <&pmu_system_controller>;
			status = "disabled";
		};

		gpu: gpu@13000000 {
			compatible = "samsung,exynos4210-mali", "arm,mali-400";
			reg = <0x13000000 0x10000>;
			interrupts = <GIC_SPI 187 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 182 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 183 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 178 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 184 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 179 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 185 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 180 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 186 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 181 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 177 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "gp",
					  "gpmmu",
					  "pp0",
					  "ppmmu0",
					  "pp1",
					  "ppmmu1",
					  "pp2",
					  "ppmmu2",
					  "pp3",
					  "ppmmu3",
					  "pmu";
			clocks = <&cmu CLK_G3D>,
				 <&cmu CLK_SCLK_G3D>;
			clock-names = "bus", "core";
			power-domains = <&pd_g3d>;
			status = "disabled";
			/* TODO: operating points for DVFS, assigned clock as 134 MHz */
		};

		mfc: codec@13400000 {
			compatible = "samsung,exynos3250-mfc", "samsung,mfc-v7";
			reg = <0x13400000 0x10000>;
			interrupts = <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "mfc", "sclk_mfc";
			clocks = <&cmu CLK_MFC>, <&cmu CLK_SCLK_MFC>;
			power-domains = <&pd_mfc>;
			iommus = <&sysmmu_mfc>;
		};

		sysmmu_mfc: sysmmu@13620000 {
			compatible = "samsung,exynos-sysmmu";
			reg = <0x13620000 0x1000>;
			interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "sysmmu", "master";
			clocks = <&cmu CLK_SMMUMFC_L>, <&cmu CLK_MFC>;
			power-domains = <&pd_mfc>;
			#iommu-cells = <0>;
		};

		serial_0: serial@13800000 {
			compatible = "samsung,exynos4210-uart";
			reg = <0x13800000 0x100>;
			interrupts = <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_UART0>, <&cmu CLK_SCLK_UART0>;
			clock-names = "uart", "clk_uart_baud0";
			pinctrl-names = "default";
			pinctrl-0 = <&uart0_data &uart0_fctl>;
			status = "disabled";
		};

		serial_1: serial@13810000 {
			compatible = "samsung,exynos4210-uart";
			reg = <0x13810000 0x100>;
			interrupts = <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_UART1>, <&cmu CLK_SCLK_UART1>;
			clock-names = "uart", "clk_uart_baud0";
			pinctrl-names = "default";
			pinctrl-0 = <&uart1_data>;
			status = "disabled";
		};

		serial_2: serial@13820000 {
			compatible = "samsung,exynos4210-uart";
			reg = <0x13820000 0x100>;
			interrupts = <GIC_SPI 111 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_UART2>, <&cmu CLK_SCLK_UART2>;
			clock-names = "uart", "clk_uart_baud0";
			pinctrl-names = "default";
			pinctrl-0 = <&uart2_data>;
			status = "disabled";
		};

		i2c_0: i2c@13860000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "samsung,s3c2440-i2c";
			reg = <0x13860000 0x100>;
			interrupts = <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_I2C0>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c0_bus>;
			status = "disabled";
		};

		i2c_1: i2c@13870000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "samsung,s3c2440-i2c";
			reg = <0x13870000 0x100>;
			interrupts = <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_I2C1>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c1_bus>;
			status = "disabled";
		};

		i2c_2: i2c@13880000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "samsung,s3c2440-i2c";
			reg = <0x13880000 0x100>;
			interrupts = <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_I2C2>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c2_bus>;
			status = "disabled";
		};

		i2c_3: i2c@13890000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "samsung,s3c2440-i2c";
			reg = <0x13890000 0x100>;
			interrupts = <GIC_SPI 116 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_I2C3>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c3_bus>;
			status = "disabled";
		};

		i2c_4: i2c@138a0000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "samsung,s3c2440-i2c";
			reg = <0x138a0000 0x100>;
			interrupts = <GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_I2C4>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c4_bus>;
			status = "disabled";
		};

		i2c_5: i2c@138b0000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "samsung,s3c2440-i2c";
			reg = <0x138b0000 0x100>;
			interrupts = <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_I2C5>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c5_bus>;
			status = "disabled";
		};

		i2c_6: i2c@138c0000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "samsung,s3c2440-i2c";
			reg = <0x138c0000 0x100>;
			interrupts = <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_I2C6>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c6_bus>;
			status = "disabled";
		};

		i2c_7: i2c@138d0000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "samsung,s3c2440-i2c";
			reg = <0x138d0000 0x100>;
			interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_I2C7>;
			clock-names = "i2c";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c7_bus>;
			status = "disabled";
		};

		spi_0: spi@13920000 {
			compatible = "samsung,exynos4210-spi";
			reg = <0x13920000 0x100>;
			interrupts = <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&pdma0 7>, <&pdma0 6>;
			dma-names = "tx", "rx";
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cmu CLK_SPI0>, <&cmu CLK_SCLK_SPI0>;
			clock-names = "spi", "spi_busclk0";
			samsung,spi-src-clk = <0>;
			pinctrl-names = "default";
			pinctrl-0 = <&spi0_bus>;
			status = "disabled";
		};

		spi_1: spi@13930000 {
			compatible = "samsung,exynos4210-spi";
			reg = <0x13930000 0x100>;
			interrupts = <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&pdma1 7>, <&pdma1 6>;
			dma-names = "tx", "rx";
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cmu CLK_SPI1>, <&cmu CLK_SCLK_SPI1>;
			clock-names = "spi", "spi_busclk0";
			samsung,spi-src-clk = <0>;
			pinctrl-names = "default";
			pinctrl-0 = <&spi1_bus>;
			status = "disabled";
		};

		i2s2: i2s@13970000 {
			compatible = "samsung,s3c6410-i2s";
			reg = <0x13970000 0x100>;
			interrupts = <GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_I2S>, <&cmu CLK_SCLK_I2S>;
			clock-names = "iis", "i2s_opclk0";
			dmas = <&pdma0 14>, <&pdma0 13>;
			dma-names = "tx", "rx";
			pinctrl-0 = <&i2s2_bus>;
			pinctrl-names = "default";
			status = "disabled";
		};

		pwm: pwm@139d0000 {
			compatible = "samsung,exynos4210-pwm";
			reg = <0x139d0000 0x1000>;
			interrupts = <GIC_SPI 104 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 105 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>;
			#pwm-cells = <3>;
			status = "disabled";
		};

		ppmu_dmc0: ppmu@106a0000 {
			compatible = "samsung,exynos-ppmu";
			reg = <0x106a0000 0x2000>;
			status = "disabled";
		};

		ppmu_dmc1: ppmu@106b0000 {
			compatible = "samsung,exynos-ppmu";
			reg = <0x106b0000 0x2000>;
			status = "disabled";
		};

		ppmu_cpu: ppmu@106c0000 {
			compatible = "samsung,exynos-ppmu";
			reg = <0x106c0000 0x2000>;
			status = "disabled";
		};

		ppmu_rightbus: ppmu@112a0000 {
			compatible = "samsung,exynos-ppmu";
			reg = <0x112a0000 0x2000>;
			clocks = <&cmu CLK_PPMURIGHT>;
			clock-names = "ppmu";
			status = "disabled";
		};

		ppmu_leftbus: ppmu@116a0000 {
			compatible = "samsung,exynos-ppmu";
			reg = <0x116a0000 0x2000>;
			clocks = <&cmu CLK_PPMULEFT>;
			clock-names = "ppmu";
			status = "disabled";
		};

		ppmu_camif: ppmu@11ac0000 {
			compatible = "samsung,exynos-ppmu";
			reg = <0x11ac0000 0x2000>;
			clocks = <&cmu CLK_PPMUCAMIF>;
			clock-names = "ppmu";
			status = "disabled";
		};

		ppmu_lcd0: ppmu@11e40000 {
			compatible = "samsung,exynos-ppmu";
			reg = <0x11e40000 0x2000>;
			clocks = <&cmu CLK_PPMULCD0>;
			clock-names = "ppmu";
			status = "disabled";
		};

		ppmu_fsys: ppmu@12630000 {
			compatible = "samsung,exynos-ppmu";
			reg = <0x12630000 0x2000>;
			clocks = <&cmu CLK_PPMUFILE>;
			clock-names = "ppmu";
			status = "disabled";
		};

		ppmu_g3d: ppmu@13220000 {
			compatible = "samsung,exynos-ppmu";
			reg = <0x13220000 0x2000>;
			clocks = <&cmu CLK_PPMUG3D>;
			clock-names = "ppmu";
			status = "disabled";
		};

		ppmu_mfc: ppmu@13660000 {
			compatible = "samsung,exynos-ppmu";
			reg = <0x13660000 0x2000>;
			clocks = <&cmu CLK_PPMUMFC_L>;
			clock-names = "ppmu";
			status = "disabled";
		};
	};
};

#include "exynos3250-pinctrl.dtsi"
#include "exynos-syscon-restart.dtsi"
