// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos3250 based ARTIK5 evaluation board device tree source
 *
 * Copyright (c) 2016 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Device tree source file for Samsung's ARTIK5 evaluation board
 * which is based on Samsung Exynos3250 SoC.
 */

/dts-v1/;
#include "exynos3250-artik5.dtsi"

/ {
	model = "Samsung ARTIK5 evaluation board";
	compatible = "samsung,artik5-eval", "samsung,artik5",
			"samsung,exynos3250", "samsung,exynos3";

	aliases {
		mmc0 = &mshc_2;
	};
};

&mshc_2 {
	cap-sd-highspeed;
	disable-wp;
	vqmmc-supply = <&ldo3_reg>;
	card-detect-delay = <200>;
	clock-frequency = <100000000>;
	max-frequency = <100000000>;
	samsung,dw-mshc-ciu-div = <1>;
	samsung,dw-mshc-sdr-timing = <0 1>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd2_cmd &sd2_clk &sd2_cd &sd2_bus1 &sd2_bus4>;
	bus-width = <4>;
	status = "okay";
};

&serial_2 {
	status = "okay";
};

&spi_0 {
	status = "okay";
	cs-gpios = <&gpx3 4 GPIO_ACTIVE_LOW>, <0>;

	assigned-clocks = <&cmu CLK_MOUT_SPI0>, <&cmu CLK_DIV_SPI0>,
			  <&cmu CLK_DIV_SPI0_PRE>, <&cmu CLK_SCLK_SPI0>;
	assigned-clock-parents = <&cmu CLK_DIV_MPLL_PRE>, /* for: CLK_MOUT_SPI0 */
				 <&cmu CLK_MOUT_SPI0>,    /* for: CLK_DIV_SPI0 */
				 <&cmu CLK_DIV_SPI0>,     /* for: CLK_DIV_SPI0_PRE */
				 <&cmu CLK_DIV_SPI0_PRE>; /* for: CLK_SCLK_SPI0 */

	ethernet@0 {
		compatible = "asix,ax88796c";
		reg = <0x0>;
		local-mac-address = [00 00 00 00 00 00]; /* Filled in by a boot-loader */
		interrupt-parent = <&gpx2>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
		spi-max-frequency = <40000000>;
		reset-gpios = <&gpe0 2 GPIO_ACTIVE_LOW>;

		controller-data {
			samsung,spi-feedback-delay = <2>;
		};
	};
};
