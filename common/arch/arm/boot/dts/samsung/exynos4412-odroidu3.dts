// SPDX-License-Identifier: GPL-2.0
/*
 * Hardkernel's Exynos4412 based ODROID-U3 board device tree source
 *
 * Copyright (c) 2014 <PERSON><PERSON> <<EMAIL>>
 *
 * Device tree source file for Hardkernel's ODROID-U3 board which is based
 * on Samsung's Exynos4412 SoC.
 */

/dts-v1/;
#include <dt-bindings/leds/common.h>
#include "exynos4412-odroid-common.dtsi"
#include "exynos4412-prime.dtsi"

/ {
	model = "Hardkernel ODROID-U3 board based on Exynos4412";
	compatible = "hardkernel,odroid-u3", "samsung,exynos4412", "samsung,exynos4";

	aliases {
		ethernet = &ethernet;
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x7ff00000>;
	};

	vbus_otg_reg: regulator-1 {
		compatible = "regulator-fixed";
		regulator-name = "VBUS_VDD_5.0V";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&gpl2 0 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	leds {
		compatible = "gpio-leds";
		led1 {
			function = LED_FUNCTION_HEARTBEAT;
			color = <LED_COLOR_ID_BLUE>;
			gpios = <&gpc1 0 GPIO_ACTIVE_LOW>;
			default-state = "on";
			linux,default-trigger = "heartbeat";
		};
	};

	fan0: pwm-fan {
		compatible = "pwm-fan";
		pwms = <&pwm 0 10000 0>;
		#cooling-cells = <2>;
		cooling-levels = <0 102 170 230>;
	};
};

&adc {
	vdd-supply = <&ldo10_reg>;
	/* Nothing connected to ADC inputs, keep it disabled */
};

/* Supply for LAN9730/SMSC95xx */
&buck8_reg {
	regulator-name = "BUCK8_P3V3";
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
};

&cpu_thermal {
	cooling-maps {
		map0 {
			trip = <&cpu_alert1>;
			cooling-device = <&cpu0 9 9>, <&cpu1 9 9>,
					 <&cpu2 9 9>, <&cpu3 9 9>,
					 <&fan0 1 2>;
		};
		map1 {
			trip = <&cpu_alert2>;
			cooling-device = <&cpu0 15 15>, <&cpu1 15 15>,
					 <&cpu2 15 15>, <&cpu3 15 15>,
					 <&fan0 2 3>;
		};
		map2 {
			trip = <&cpu_alert0>;
			cooling-device = <&fan0 0 1>;
		};
	};
};

&hdmicec {
	needs-hpd;
};

/* VDDQ for MSHC (eMMC card) */
&ldo22_reg {
	regulator-name = "LDO22_VDDQ_MMC4_2.8V";
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
};

&mshc_0 {
	vqmmc-supply = <&ldo22_reg>;
};

&pwm {
	pinctrl-0 = <&pwm0_out>;
	pinctrl-names = "default";
	samsung,pwm-outputs = <0>;
	status = "okay";
};

&usb3503 {
	clock-names = "refclk";
	clocks = <&pmu_system_controller 0>;
	refclk-frequency = <********>;
};

&ehci {
	#address-cells = <1>;
	#size-cells = <0>;
	phys = <&exynos_usbphy 2>, <&exynos_usbphy 3>;
	phy-names = "hsic0", "hsic1";

	ethernet: ethernet@2 {
		compatible = "usb424,9730";
		reg = <2>;
		local-mac-address = [00 00 00 00 00 00]; /* Filled in by a bootloader */
	};
};

&hsotg {
	dr_mode = "otg";
	vbus-supply = <&vbus_otg_reg>;
};

&sound {
	model = "Odroid-U3";
	samsung,audio-widgets =
		"Headphone", "Headphone Jack",
		"Speakers", "Speakers";
	samsung,audio-routing =
		"Headphone Jack", "HPL",
		"Headphone Jack", "HPR",
		"Headphone Jack", "MICBIAS",
		"IN1", "Headphone Jack",
		"Speakers", "SPKL",
		"Speakers", "SPKR";
};

&spi_1 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi1_bus>;
	cs-gpios = <&gpb 5 GPIO_ACTIVE_HIGH>;
	status = "okay";
};
