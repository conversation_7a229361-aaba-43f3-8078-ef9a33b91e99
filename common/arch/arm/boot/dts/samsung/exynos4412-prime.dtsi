// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos4412 Prime SoC device tree source
 *
 * Copyright (c) 2016 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

/*
 * Exynos4412 Prime SoC revision supports higher CPU frequencies than
 * non-Prime version.  Therefore we need to update OPPs table and
 * thermal maps accordingly.
 */

&cpu0_opp_1500 {
	/delete-property/turbo-mode;
};

&cpu0_opp_table {
	opp-********** {
		opp-hz = /bits/ 64 <**********>;
		opp-microvolt = <1350000>;
		clock-latency-ns = <200000>;
	};
	opp-********** {
		opp-hz = /bits/ 64 <**********>;
		opp-microvolt = <1350000>;
		clock-latency-ns = <200000>;
	};
};

&cooling_map0 {
	cooling-device = <&cpu0 9 9>, <&cpu1 9 9>,
			 <&cpu2 9 9>, <&cpu3 9 9>;
};

&cooling_map1 {
	cooling-device = <&cpu0 15 15>, <&cpu1 15 15>,
			 <&cpu2 15 15>, <&cpu3 15 15>;
};

&gpu_opp_table {
	opp-533000000 {
		opp-hz = /bits/ 64 <533000000>;
		opp-microvolt = <1075000>;
	};
};
