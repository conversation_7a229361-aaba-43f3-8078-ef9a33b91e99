// SPDX-License-Identifier: GPL-2.0
/*
 * Hardkernel Odroid XU3/XU3-Lite/XU4 boards common device tree source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 * Copyright (c) 2014 Collabora Ltd.
 * Copyright (c) 2015 <PERSON><PERSON><PERSON> <<EMAIL>>
 *                    <PERSON> <<EMAIL>>
 */

#include <dt-bindings/input/input.h>
#include "exynos5422-odroid-core.dtsi"

/ {
	aliases {
		mmc0 = &mmc_0;
	};

	gpio-keys {
		compatible = "gpio-keys";
		pinctrl-names = "default";
		pinctrl-0 = <&power_key>;

		power-key {
			/*
			 * The power button (SW2) is connected to the PWRON
			 * pin (active high) of the S2MPS11 PMIC, which acts
			 * as a 16ms debouce filter and signal inverter with
			 * output on ONOB pin (active low). ONOB PMIC pin is
			 * then connected to XEINT3 SoC pin.
			 */
			gpios = <&gpx0 3 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			label = "power key";
			debounce-interval = <0>;
			wakeup-source;
		};
	};

	emmc_pwrseq: pwrseq {
		pinctrl-0 = <&emmc_nrst_pin>;
		pinctrl-names = "default";
		compatible = "mmc-pwrseq-emmc";
		reset-gpios = <&gpd1 0 GPIO_ACTIVE_LOW>;
	};

	fan0: pwm-fan {
		compatible = "pwm-fan";
		pwms = <&pwm 0 20972 0>;
		#cooling-cells = <2>;
		cooling-levels = <0 130 170 230>;
	};

	thermal-zones {
		cpu0_thermal: cpu0-thermal {
			thermal-sensors = <&tmu_cpu0>;
			polling-delay-passive = <250>;
			polling-delay = <0>;
			trips {
				cpu0_alert0: cpu-alert-0 {
					temperature = <50000>; /* millicelsius */
					hysteresis = <5000>; /* millicelsius */
					type = "active";
				};
				cpu0_alert1: cpu-alert-1 {
					temperature = <60000>; /* millicelsius */
					hysteresis = <5000>; /* millicelsius */
					type = "active";
				};
				cpu0_alert2: cpu-alert-2 {
					temperature = <70000>; /* millicelsius */
					hysteresis = <5000>; /* millicelsius */
					type = "active";
				};
				cpu0_crit0: cpu-crit-0 {
					temperature = <120000>; /* millicelsius */
					hysteresis = <0>; /* millicelsius */
					type = "critical";
				};
				/*
				 * Exynos542x supports only 4 trip-points
				 * so for these polling mode is required.
				 * Start polling at temperature level of last
				 * interrupt-driven trip: cpu0_alert2
				 */
				cpu0_alert3: cpu-alert-3 {
					temperature = <70000>; /* millicelsius */
					hysteresis = <10000>; /* millicelsius */
					type = "passive";
				};
				cpu0_alert4: cpu-alert-4 {
					temperature = <85000>; /* millicelsius */
					hysteresis = <10000>; /* millicelsius */
					type = "passive";
				};
			};
			cooling-maps {
				map0 {
					trip = <&cpu0_alert0>;
					cooling-device = <&fan0 0 1>;
				};
				map1 {
					trip = <&cpu0_alert1>;
					cooling-device = <&fan0 1 2>;
				};
				map2 {
					trip = <&cpu0_alert2>;
					cooling-device = <&fan0 2 3>;
				};
				/*
				 * When reaching cpu0_alert3, reduce CPU
				 * by 2 steps. On Exynos5422/5800 that would
				 * (usually) be: 1800 MHz and 1200 MHz.
				 */
				map3 {
					trip = <&cpu0_alert3>;
					cooling-device = <&cpu0 0 2>,
							 <&cpu1 0 2>,
							 <&cpu2 0 2>,
							 <&cpu3 0 2>,
							 <&cpu4 0 2>,
							 <&cpu5 0 2>,
							 <&cpu6 0 2>,
							 <&cpu7 0 2>;
				};
				/*
				 * When reaching cpu0_alert4, reduce CPU
				 * further, down to 600 MHz (14 steps for big,
				 * 8 steps for LITTLE).
				 */
				cpu0_cooling_map4: map4 {
					trip = <&cpu0_alert4>;
					cooling-device = <&cpu0 3 8>,
							 <&cpu1 3 8>,
							 <&cpu2 3 8>,
							 <&cpu3 3 8>,
							 <&cpu4 3 14>,
							 <&cpu5 3 14>,
							 <&cpu6 3 14>,
							 <&cpu7 3 14>;
				};
			};
		};
		cpu1_thermal: cpu1-thermal {
			thermal-sensors = <&tmu_cpu1>;
			polling-delay-passive = <250>;
			polling-delay = <0>;
			trips {
				cpu1_alert0: cpu-alert-0 {
					temperature = <50000>;
					hysteresis = <5000>;
					type = "active";
				};
				cpu1_alert1: cpu-alert-1 {
					temperature = <60000>;
					hysteresis = <5000>;
					type = "active";
				};
				cpu1_alert2: cpu-alert-2 {
					temperature = <70000>;
					hysteresis = <5000>;
					type = "active";
				};
				cpu1_crit0: cpu-crit-0 {
					temperature = <120000>;
					hysteresis = <0>;
					type = "critical";
				};
				cpu1_alert3: cpu-alert-3 {
					temperature = <70000>;
					hysteresis = <10000>;
					type = "passive";
				};
				cpu1_alert4: cpu-alert-4 {
					temperature = <85000>;
					hysteresis = <10000>;
					type = "passive";
				};
			};
			cooling-maps {
				map0 {
					trip = <&cpu1_alert0>;
					cooling-device = <&fan0 0 1>;
				};
				map1 {
					trip = <&cpu1_alert1>;
					cooling-device = <&fan0 1 2>;
				};
				map2 {
					trip = <&cpu1_alert2>;
					cooling-device = <&fan0 2 3>;
				};
				map3 {
					trip = <&cpu1_alert3>;
					cooling-device = <&cpu0 0 2>,
							 <&cpu1 0 2>,
							 <&cpu2 0 2>,
							 <&cpu3 0 2>,
							 <&cpu4 0 2>,
							 <&cpu5 0 2>,
							 <&cpu6 0 2>,
							 <&cpu7 0 2>;
				};
				cpu1_cooling_map4: map4 {
					trip = <&cpu1_alert4>;
					cooling-device = <&cpu0 3 8>,
							 <&cpu1 3 8>,
							 <&cpu2 3 8>,
							 <&cpu3 3 8>,
							 <&cpu4 3 14>,
							 <&cpu5 3 14>,
							 <&cpu6 3 14>,
							 <&cpu7 3 14>;
				};
			};
		};
		cpu2_thermal: cpu2-thermal {
			thermal-sensors = <&tmu_cpu2>;
			polling-delay-passive = <250>;
			polling-delay = <0>;
			trips {
				cpu2_alert0: cpu-alert-0 {
					temperature = <50000>;
					hysteresis = <5000>;
					type = "active";
				};
				cpu2_alert1: cpu-alert-1 {
					temperature = <60000>;
					hysteresis = <5000>;
					type = "active";
				};
				cpu2_alert2: cpu-alert-2 {
					temperature = <70000>;
					hysteresis = <5000>;
					type = "active";
				};
				cpu2_crit0: cpu-crit-0 {
					temperature = <120000>;
					hysteresis = <0>;
					type = "critical";
				};
				cpu2_alert3: cpu-alert-3 {
					temperature = <70000>;
					hysteresis = <10000>;
					type = "passive";
				};
				cpu2_alert4: cpu-alert-4 {
					temperature = <85000>;
					hysteresis = <10000>;
					type = "passive";
				};
			};
			cooling-maps {
				map0 {
					trip = <&cpu2_alert0>;
					cooling-device = <&fan0 0 1>;
				};
				map1 {
					trip = <&cpu2_alert1>;
					cooling-device = <&fan0 1 2>;
				};
				map2 {
					trip = <&cpu2_alert2>;
					cooling-device = <&fan0 2 3>;
				};
				map3 {
					trip = <&cpu2_alert3>;
					cooling-device = <&cpu0 0 2>,
							 <&cpu1 0 2>,
							 <&cpu2 0 2>,
							 <&cpu3 0 2>,
							 <&cpu4 0 2>,
							 <&cpu5 0 2>,
							 <&cpu6 0 2>,
							 <&cpu7 0 2>;
				};
				cpu2_cooling_map4: map4 {
					trip = <&cpu2_alert4>;
					cooling-device = <&cpu0 3 8>,
							 <&cpu1 3 8>,
							 <&cpu2 3 8>,
							 <&cpu3 3 8>,
							 <&cpu4 3 14>,
							 <&cpu5 3 14>,
							 <&cpu6 3 14>,
							 <&cpu7 3 14>;
				};
			};
		};
		cpu3_thermal: cpu3-thermal {
			thermal-sensors = <&tmu_cpu3>;
			polling-delay-passive = <250>;
			polling-delay = <0>;
			trips {
				cpu3_alert0: cpu-alert-0 {
					temperature = <50000>;
					hysteresis = <5000>;
					type = "active";
				};
				cpu3_alert1: cpu-alert-1 {
					temperature = <60000>;
					hysteresis = <5000>;
					type = "active";
				};
				cpu3_alert2: cpu-alert-2 {
					temperature = <70000>;
					hysteresis = <5000>;
					type = "active";
				};
				cpu3_crit0: cpu-crit-0 {
					temperature = <120000>;
					hysteresis = <0>;
					type = "critical";
				};
				cpu3_alert3: cpu-alert-3 {
					temperature = <70000>;
					hysteresis = <10000>;
					type = "passive";
				};
				cpu3_alert4: cpu-alert-4 {
					temperature = <85000>;
					hysteresis = <10000>;
					type = "passive";
				};
			};
			cooling-maps {
				map0 {
					trip = <&cpu3_alert0>;
					cooling-device = <&fan0 0 1>;
				};
				map1 {
					trip = <&cpu3_alert1>;
					cooling-device = <&fan0 1 2>;
				};
				map2 {
					trip = <&cpu3_alert2>;
					cooling-device = <&fan0 2 3>;
				};
				map3 {
					trip = <&cpu3_alert3>;
					cooling-device = <&cpu0 0 2>,
							 <&cpu1 0 2>,
							 <&cpu2 0 2>,
							 <&cpu3 0 2>,
							 <&cpu4 0 2>,
							 <&cpu5 0 2>,
							 <&cpu6 0 2>,
							 <&cpu7 0 2>;
				};
				cpu3_cooling_map4: map4 {
					trip = <&cpu3_alert4>;
					cooling-device = <&cpu0 3 8>,
							 <&cpu1 3 8>,
							 <&cpu2 3 8>,
							 <&cpu3 3 8>,
							 <&cpu4 3 14>,
							 <&cpu5 3 14>,
							 <&cpu6 3 14>,
							 <&cpu7 3 14>;
				};
			};
		};
		gpu_thermal: gpu-thermal {
			thermal-sensors = <&tmu_gpu>;
			polling-delay-passive = <250>;
			polling-delay = <0>;
			trips {
				gpu_alert0: gpu-alert-0 {
					temperature = <50000>;
					hysteresis = <5000>;
					type = "active";
				};
				gpu_alert1: gpu-alert-1 {
					temperature = <60000>;
					hysteresis = <5000>;
					type = "active";
				};
				gpu_alert2: gpu-alert-2 {
					temperature = <70000>;
					hysteresis = <5000>;
					type = "active";
				};
				gpu_crit0: gpu-crit-0 {
					temperature = <120000>;
					hysteresis = <0>;
					type = "critical";
				};
				gpu_alert3: gpu-alert-3 {
					temperature = <70000>;
					hysteresis = <10000>;
					type = "passive";
				};
				gpu_alert4: gpu-alert-4 {
					temperature = <85000>;
					hysteresis = <10000>;
					type = "passive";
				};
			};
			cooling-maps {
				map0 {
					trip = <&gpu_alert0>;
					cooling-device = <&fan0 0 1>;
				};
				map1 {
					trip = <&gpu_alert1>;
					cooling-device = <&fan0 1 2>;
				};
				map2 {
					trip = <&gpu_alert2>;
					cooling-device = <&fan0 2 3>;
				};
				map3 {
					trip = <&gpu_alert3>;
					cooling-device = <&gpu 0 2>;
				};
				map4 {
					trip = <&gpu_alert4>;
					cooling-device = <&gpu 3 6>;
				};
			};
		};
	};
};

&buck10_reg {
	/* Supplies vmmc-supply of mmc_0 */
	regulator-always-on;
	regulator-boot-on;
};

&hdmi {
	status = "okay";
	ddc = <&i2c_2>;
	hpd-gpios = <&gpx3 7 GPIO_ACTIVE_HIGH>;
	pinctrl-names = "default";
	pinctrl-0 = <&hdmi_hpd_irq>;

	vdd_osc-supply = <&ldo7_reg>;
	vdd_pll-supply = <&ldo6_reg>;
	vdd-supply = <&ldo6_reg>;
};

&hdmicec {
	status = "okay";
	needs-hpd;
};

&i2c_2 {
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <66000>;
	/* used by HDMI DDC */
	status = "okay";
};

&ldo26_reg {
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-always-on;
};

&mixer {
	status = "okay";
};

&mmc_0 {
	status = "okay";
	mmc-pwrseq = <&emmc_pwrseq>;
	card-detect-delay = <200>;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <0 4>;
	samsung,dw-mshc-ddr-timing = <0 2>;
	samsung,dw-mshc-hs400-timing = <0 2>;
	samsung,read-strobe-delay = <90>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_bus1 &sd0_bus4 &sd0_bus8 &sd0_cd &sd0_rclk>;
	bus-width = <8>;
	cap-mmc-highspeed;
	mmc-ddr-1_8v;
	mmc-hs200-1_8v;
	mmc-hs400-1_8v;
	max-frequency = <200000000>;
	vmmc-supply = <&ldo18_reg>;
	vqmmc-supply = <&ldo3_reg>;
};

&pinctrl_0 {
	power_key: power-key-pins {
		samsung,pins = "gpx0-3";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	hdmi_hpd_irq: hdmi-hpd-irq-pins {
		samsung,pins = "gpx3-7";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_DOWN>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};

&pinctrl_1 {
	emmc_nrst_pin: emmc-nrst-pins {
		samsung,pins = "gpd1-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_INPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};
