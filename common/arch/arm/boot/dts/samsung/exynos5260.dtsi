// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung Exynos5260 SoC device tree source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

#include <dt-bindings/clock/exynos5260-clk.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	compatible = "samsung,exynos5260", "samsung,exynos5";
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	aliases {
		i2c0 = &hsi2c_0;
		i2c1 = &hsi2c_1;
		i2c2 = &hsi2c_2;
		i2c3 = &hsi2c_3;
		pinctrl0 = &pinctrl_0;
		pinctrl1 = &pinctrl_1;
		pinctrl2 = &pinctrl_2;
		serial0 = &uart0;
		serial1 = &uart1;
		serial2 = &uart2;
		serial3 = &uart3;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu-map {
			cluster0 {
				core0 {
					cpu = <&cpu0>;
				};
				core1 {
					cpu = <&cpu1>;
				};
			};

			cluster1 {
				core0 {
					cpu = <&cpu2>;
				};
				core1 {
					cpu = <&cpu3>;
				};
				core2 {
					cpu = <&cpu4>;
				};
				core3 {
					cpu = <&cpu5>;
				};
			};
		};

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <0x0>;
			cci-control-port = <&cci_control1>;
		};

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <0x1>;
			cci-control-port = <&cci_control1>;
		};

		cpu2: cpu@100 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x100>;
			cci-control-port = <&cci_control0>;
		};

		cpu3: cpu@101 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x101>;
			cci-control-port = <&cci_control0>;
		};

		cpu4: cpu@102 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x102>;
			cci-control-port = <&cci_control0>;
		};

		cpu5: cpu@103 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x103>;
			cci-control-port = <&cci_control0>;
		};
	};

	soc: soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		clock_top: clock-controller@10010000 {
			compatible = "samsung,exynos5260-clock-top";
			reg = <0x10010000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>,
				 <&clock_mif MIF_DOUT_MEM_PLL>,
				 <&clock_mif MIF_DOUT_BUS_PLL>,
				 <&clock_mif MIF_DOUT_MEDIA_PLL>;
			clock-names = "fin_pll",
				      "dout_mem_pll",
				      "dout_bus_pll",
				      "dout_media_pll";
		};

		clock_peri: clock-controller@10200000 {
			compatible = "samsung,exynos5260-clock-peri";
			reg = <0x10200000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>,
				 <&ioclk_pcm>,
				 <&ioclk_i2s>,
				 <&ioclk_spdif>,
				 <&fin_pll>,
				 <&clock_top TOP_DOUT_ACLK_PERI_66>,
				 <&clock_top TOP_DOUT_SCLK_PERI_UART0>,
				 <&clock_top TOP_DOUT_SCLK_PERI_UART1>,
				 <&clock_top TOP_DOUT_SCLK_PERI_UART2>,
				 <&clock_top TOP_DOUT_SCLK_PERI_SPI0_B>,
				 <&clock_top TOP_DOUT_SCLK_PERI_SPI1_B>,
				 <&clock_top TOP_DOUT_SCLK_PERI_SPI2_B>,
				 <&clock_top TOP_DOUT_ACLK_PERI_AUD>;
			clock-names = "fin_pll",
				      "ioclk_pcm_extclk",
				      "ioclk_i2s_cdclk",
				      "ioclk_spdif_extclk",
				      "phyclk_hdmi_phy_ref_cko",
				      "dout_aclk_peri_66",
				      "dout_sclk_peri_uart0",
				      "dout_sclk_peri_uart1",
				      "dout_sclk_peri_uart2",
				      "dout_sclk_peri_spi0_b",
				      "dout_sclk_peri_spi1_b",
				      "dout_sclk_peri_spi2_b",
				      "dout_aclk_peri_aud";
		};

		clock_egl: clock-controller@10600000 {
			compatible = "samsung,exynos5260-clock-egl";
			reg = <0x10600000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>,
				 <&clock_mif MIF_DOUT_BUS_PLL>;
			clock-names = "fin_pll",
				      "dout_bus_pll";
		};

		clock_kfc: clock-controller@10700000 {
			compatible = "samsung,exynos5260-clock-kfc";
			reg = <0x10700000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>,
				 <&clock_mif MIF_DOUT_MEDIA_PLL>;
			clock-names = "fin_pll",
				      "dout_media_pll";
		};

		clock_g2d: clock-controller@10a00000 {
			compatible = "samsung,exynos5260-clock-g2d";
			reg = <0x10a00000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>,
				 <&clock_top TOP_DOUT_ACLK_G2D_333>;
			clock-names = "fin_pll",
				      "dout_aclk_g2d_333";
		};

		clock_mif: clock-controller@10ce0000 {
			compatible = "samsung,exynos5260-clock-mif";
			reg = <0x10ce0000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>;
			clock-names = "fin_pll";
		};

		clock_mfc: clock-controller@11090000 {
			compatible = "samsung,exynos5260-clock-mfc";
			reg = <0x11090000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>,
				 <&clock_top TOP_DOUT_ACLK_MFC_333>;
			clock-names = "fin_pll",
				      "dout_aclk_mfc_333";
		};

		clock_g3d: clock-controller@11830000 {
			compatible = "samsung,exynos5260-clock-g3d";
			reg = <0x11830000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>;
			clock-names = "fin_pll";
		};

		clock_fsys: clock-controller@122e0000 {
			compatible = "samsung,exynos5260-clock-fsys";
			reg = <0x122e0000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&clock_top TOP_DOUT_ACLK_FSYS_200>;
			clock-names = "fin_pll",
				      "phyclk_usbhost20_phy_phyclock",
				      "phyclk_usbhost20_phy_freeclk",
				      "phyclk_usbhost20_phy_clk48mohci",
				      "phyclk_usbdrd30_udrd30_pipe_pclk",
				      "phyclk_usbdrd30_udrd30_phyclock",
				      "dout_aclk_fsys_200";
		};

		clock_aud: clock-controller@128c0000 {
			compatible = "samsung,exynos5260-clock-aud";
			reg = <0x128c0000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>,
				 <&clock_top TOP_FOUT_AUD_PLL>,
				 <&ioclk_i2s>,
				 <&ioclk_pcm>;
			clock-names = "fin_pll",
				      "fout_aud_pll",
				      "ioclk_i2s_cdclk",
				      "ioclk_pcm_extclk";
		};

		clock_isp: clock-controller@133c0000 {
			compatible = "samsung,exynos5260-clock-isp";
			reg = <0x133c0000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>,
				 <&clock_top TOP_DOUT_ACLK_ISP1_266>,
				 <&clock_top TOP_DOUT_ACLK_ISP1_400>,
				 <&clock_top TOP_MOUT_ACLK_ISP1_266>;
			clock-names = "fin_pll",
				      "dout_aclk_isp1_266",
				      "dout_aclk_isp1_400",
				      "mout_aclk_isp1_266";
		};

		clock_gscl: clock-controller@13f00000 {
			compatible = "samsung,exynos5260-clock-gscl";
			reg = <0x13f00000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>,
				 <&clock_top TOP_DOUT_ACLK_GSCL_400>,
				 <&clock_top TOP_DOUT_ACLK_GSCL_333>;
			clock-names = "fin_pll",
				      "dout_aclk_gscl_400",
				      "dout_aclk_gscl_333";
		};

		clock_disp: clock-controller@14550000 {
			compatible = "samsung,exynos5260-clock-disp";
			reg = <0x14550000 0x10000>;
			#clock-cells = <1>;
			clocks = <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&fin_pll>,
				 <&ioclk_spdif>,
				 <&clock_top TOP_DOUT_ACLK_PERI_AUD>,
				 <&clock_top TOP_DOUT_ACLK_DISP_222>,
				 <&clock_top TOP_DOUT_SCLK_DISP_PIXEL>,
				 <&clock_top TOP_DOUT_ACLK_DISP_333>;
			clock-names = "fin_pll",
				      "phyclk_dptx_phy_ch3_txd_clk",
				      "phyclk_dptx_phy_ch2_txd_clk",
				      "phyclk_dptx_phy_ch1_txd_clk",
				      "phyclk_dptx_phy_ch0_txd_clk",
				      "phyclk_hdmi_phy_tmds_clko",
				      "phyclk_hdmi_phy_ref_clko",
				      "phyclk_hdmi_phy_pixel_clko",
				      "phyclk_hdmi_link_o_tmds_clkhi",
				      "phyclk_mipi_dphy_4l_m_txbyte_clkhs",
				      "phyclk_dptx_phy_o_ref_clk_24m",
				      "phyclk_dptx_phy_clk_div2",
				      "phyclk_mipi_dphy_4l_m_rxclkesc0",
				      "phyclk_hdmi_phy_ref_cko",
				      "ioclk_spdif_extclk",
				      "dout_aclk_peri_aud",
				      "dout_aclk_disp_222",
				      "dout_sclk_disp_pixel",
				      "dout_aclk_disp_333";
		};

		gic: interrupt-controller@10481000 {
			compatible = "arm,gic-400", "arm,cortex-a15-gic";
			#interrupt-cells = <3>;
			interrupt-controller;
			reg = <0x10481000 0x1000>,
				<0x10482000 0x2000>,
				<0x10484000 0x2000>,
				<0x10486000 0x2000>;
			interrupts = <GIC_PPI 9
					(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		};

		chipid: chipid@10000000 {
			compatible = "samsung,exynos4210-chipid";
			reg = <0x10000000 0x100>;
		};

		mct: timer@100b0000 {
			compatible = "samsung,exynos5260-mct",
				     "samsung,exynos4210-mct";
			reg = <0x100b0000 0x1000>;
			clocks = <&fin_pll>, <&clock_peri PERI_CLK_MCT>;
			clock-names = "fin_pll", "mct";
			interrupts = <GIC_SPI 104 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 105 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 128 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 129 IRQ_TYPE_LEVEL_HIGH>;
		};

		cci: cci@10f00000 {
			compatible = "arm,cci-400";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x10f00000 0x1000>;
			ranges = <0x0 0x10f00000 0x6000>;

			cci_control0: slave-if@4000 {
				compatible = "arm,cci-400-ctrl-if";
				interface-type = "ace";
				reg = <0x4000 0x1000>;
			};

			cci_control1: slave-if@5000 {
				compatible = "arm,cci-400-ctrl-if";
				interface-type = "ace";
				reg = <0x5000 0x1000>;
			};
		};

		pinctrl_0: pinctrl@11600000 {
			compatible = "samsung,exynos5260-pinctrl";
			reg = <0x11600000 0x1000>;
			interrupts = <GIC_SPI 79 IRQ_TYPE_LEVEL_HIGH>;

			wakeup-interrupt-controller {
				compatible = "samsung,exynos4210-wakeup-eint";
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 48 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		pinctrl_1: pinctrl@12290000 {
			compatible = "samsung,exynos5260-pinctrl";
			reg = <0x12290000 0x1000>;
			interrupts = <GIC_SPI 157 IRQ_TYPE_LEVEL_HIGH>;
		};

		pinctrl_2: pinctrl@128b0000 {
			compatible = "samsung,exynos5260-pinctrl";
			reg = <0x128b0000 0x1000>;
			interrupts = <GIC_SPI 243 IRQ_TYPE_LEVEL_HIGH>;
		};

		pmu_system_controller: system-controller@10d50000 {
			compatible = "samsung,exynos5260-pmu", "syscon";
			reg = <0x10d50000 0x10000>;
		};

		uart0: serial@12c00000 {
			compatible = "samsung,exynos4210-uart";
			reg = <0x12c00000 0x100>;
			interrupts = <GIC_SPI 146 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock_peri PERI_CLK_UART0>, <&clock_peri PERI_SCLK_UART0>;
			clock-names = "uart", "clk_uart_baud0";
			status = "disabled";
		};

		uart1: serial@12c10000 {
			compatible = "samsung,exynos4210-uart";
			reg = <0x12c10000 0x100>;
			interrupts = <GIC_SPI 147 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock_peri PERI_CLK_UART1>, <&clock_peri PERI_SCLK_UART1>;
			clock-names = "uart", "clk_uart_baud0";
			status = "disabled";
		};

		uart2: serial@12c20000 {
			compatible = "samsung,exynos4210-uart";
			reg = <0x12c20000 0x100>;
			interrupts = <GIC_SPI 148 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock_peri PERI_CLK_UART2>, <&clock_peri PERI_SCLK_UART2>;
			clock-names = "uart", "clk_uart_baud0";
			status = "disabled";
		};

		uart3: serial@12860000 {
			compatible = "samsung,exynos4210-uart";
			reg = <0x12860000 0x100>;
			interrupts = <GIC_SPI 145 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clock_aud AUD_CLK_AUD_UART>, <&clock_aud AUD_SCLK_AUD_UART>;
			clock-names = "uart", "clk_uart_baud0";
			status = "disabled";
		};

		mmc_0: mmc@12140000 {
			compatible = "samsung,exynos5250-dw-mshc";
			reg = <0x12140000 0x2000>;
			interrupts = <GIC_SPI 156 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock_fsys FSYS_CLK_MMC0>, <&clock_top TOP_SCLK_MMC0>;
			clock-names = "biu", "ciu";
			assigned-clocks =
				<&clock_top TOP_MOUT_SCLK_FSYS_MMC0_SDCLKIN_A>,
				<&clock_top TOP_MOUT_SCLK_FSYS_MMC0_SDCLKIN_B>,
				<&clock_top TOP_SCLK_MMC0>;
			assigned-clock-parents =
				<&clock_top TOP_MOUT_BUSTOP_PLL_USER>,
				<&clock_top TOP_MOUT_SCLK_FSYS_MMC0_SDCLKIN_A>;
			assigned-clock-rates = <0>, <0>, <800000000>;
			fifo-depth = <64>;
			status = "disabled";
		};

		mmc_1: mmc@12150000 {
			compatible = "samsung,exynos5250-dw-mshc";
			reg = <0x12150000 0x2000>;
			interrupts = <GIC_SPI 158 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock_fsys FSYS_CLK_MMC1>, <&clock_top TOP_SCLK_MMC1>;
			clock-names = "biu", "ciu";
			assigned-clocks =
				<&clock_top TOP_MOUT_SCLK_FSYS_MMC1_SDCLKIN_A>,
				<&clock_top TOP_MOUT_SCLK_FSYS_MMC1_SDCLKIN_B>,
				<&clock_top TOP_SCLK_MMC1>;
			assigned-clock-parents =
				<&clock_top TOP_MOUT_BUSTOP_PLL_USER>,
				<&clock_top TOP_MOUT_SCLK_FSYS_MMC1_SDCLKIN_A>;
			assigned-clock-rates = <0>, <0>, <800000000>;
			fifo-depth = <64>;
			status = "disabled";
		};

		mmc_2: mmc@12160000 {
			compatible = "samsung,exynos5250-dw-mshc";
			reg = <0x12160000 0x2000>;
			interrupts = <GIC_SPI 159 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&clock_fsys FSYS_CLK_MMC2>, <&clock_top TOP_SCLK_MMC2>;
			clock-names = "biu", "ciu";
			assigned-clocks =
				<&clock_top TOP_MOUT_SCLK_FSYS_MMC2_SDCLKIN_A>,
				<&clock_top TOP_MOUT_SCLK_FSYS_MMC2_SDCLKIN_B>,
				<&clock_top TOP_SCLK_MMC2>;
			assigned-clock-parents =
				<&clock_top TOP_MOUT_BUSTOP_PLL_USER>,
				<&clock_top TOP_MOUT_SCLK_FSYS_MMC2_SDCLKIN_A>;
			assigned-clock-rates = <0>, <0>, <800000000>;
			fifo-depth = <64>;
			status = "disabled";
		};

		hsi2c_0: i2c@12da0000 {
			compatible = "samsung,exynos5260-hsi2c";
			reg = <0x12da0000 0x1000>;
			interrupts = <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c0_hs_bus>;
			clocks = <&clock_peri PERI_CLK_HSIC0>;
			clock-names = "hsi2c";
			status = "disabled";
		};

		hsi2c_1: i2c@12db0000 {
			compatible = "samsung,exynos5260-hsi2c";
			reg = <0x12db0000 0x1000>;
			interrupts = <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c1_hs_bus>;
			clocks = <&clock_peri PERI_CLK_HSIC1>;
			clock-names = "hsi2c";
			status = "disabled";
		};

		hsi2c_2: i2c@12dc0000 {
			compatible = "samsung,exynos5260-hsi2c";
			reg = <0x12dc0000 0x1000>;
			interrupts = <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c2_hs_bus>;
			clocks = <&clock_peri PERI_CLK_HSIC2>;
			clock-names = "hsi2c";
			status = "disabled";
		};

		hsi2c_3: i2c@12dd0000 {
			compatible = "samsung,exynos5260-hsi2c";
			reg = <0x12dd0000 0x1000>;
			interrupts = <GIC_SPI 111 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c3_hs_bus>;
			clocks = <&clock_peri PERI_CLK_HSIC3>;
			clock-names = "hsi2c";
			status = "disabled";
		};
	};
};

#include "exynos5260-pinctrl.dtsi"
