// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos5250 based Arndale board device tree source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

/dts-v1/;
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/clock/samsung,s2mps11.h>
#include <dt-bindings/sound/samsung-i2s.h>
#include "exynos5250.dtsi"

/ {
	model = "Insignal Arndale evaluation board based on Exynos5250";
	compatible = "insignal,arndale", "samsung,exynos5250", "samsung,exynos5";

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x80000000>;
	};

	aliases {
		mmc0 = &mmc_0;
		mmc1 = &mmc_2;
	};

	chosen {
		stdout-path = "serial2:115200n8";
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-menu {
			label = "SW-TACT2";
			gpios = <&gpx1 4 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_MENU>;
			wakeup-source;
		};

		key-home {
			label = "SW-TACT3";
			gpios = <&gpx1 5 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_HOME>;
			wakeup-source;
		};

		key-up {
			label = "SW-TACT4";
			gpios = <&gpx1 6 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_UP>;
			wakeup-source;
		};

		key-down {
			label = "SW-TACT5";
			gpios = <&gpx1 7 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_DOWN>;
			wakeup-source;
		};

		key-back {
			label = "SW-TACT6";
			gpios = <&gpx2 0 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_BACK>;
			wakeup-source;
		};

		key-wakeup {
			label = "SW-TACT7";
			gpios = <&gpx2 1 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_WAKEUP>;
			wakeup-source;
		};
	};

	/*
	 * For unknown reasons HDMI-DDC does not work with Exynos I2C
	 * controllers. Lets use software I2C over GPIO pins as a workaround.
	 */
	i2c_ddc: i2c-10 {
		compatible = "i2c-gpio";
		pinctrl-names = "default";
		pinctrl-0 = <&i2c2_gpio_bus>;
		sda-gpios = <&gpa0 6 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpa0 7 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		i2c-gpio,delay-us = <2>;
	};

	panel: panel {
		compatible = "boe,hv070wsa-100";
		power-supply = <&vcc_3v3_reg>;
		enable-gpios = <&gpd1 3 GPIO_ACTIVE_HIGH>;
		port {
			panel_ep: endpoint {
				remote-endpoint = <&bridge_out_ep>;
			};
		};
	};

	main_dc_reg: regulator-0 {
		compatible = "regulator-fixed";
		regulator-name = "MAIN_DC";
		regulator-always-on;
	};

	mmc_reg: regulator-1 {
		compatible = "regulator-fixed";
		regulator-name = "VDD_MMC";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		regulator-always-on;
	};

	reg_hdmi_en: regulator-2 {
		compatible = "regulator-fixed";
		regulator-name = "hdmi-en";
		regulator-always-on;
	};

	vcc_1v2_reg: regulator-3 {
		compatible = "regulator-fixed";
		regulator-name = "VCC_1V2";
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		regulator-always-on;
	};

	vcc_1v8_reg: regulator-4 {
		compatible = "regulator-fixed";
		regulator-name = "VCC_1V8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	vcc_3v3_reg: regulator-5 {
		compatible = "regulator-fixed";
		regulator-name = "VCC_3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	sound {
		compatible = "samsung,arndale-wm1811";
		samsung,audio-cpu = <&i2s0>;
		samsung,audio-codec = <&wm1811>;
	};

	fixed-rate-clocks {
		xxti {
			compatible = "samsung,clock-xxti";
			clock-frequency = <24000000>;
		};
	};

	// SMSC USB3503 connected in hardware only mode as a PHY
	usb_hub: usb-hub {
		compatible = "smsc,usb3503a";

		reset-gpios = <&gpx3 5 GPIO_ACTIVE_LOW>;
		connect-gpios = <&gpd1 7 GPIO_ACTIVE_HIGH>;
	};
};

&clock {
	assigned-clocks = <&clock CLK_FOUT_EPLL>;
	assigned-clock-rates = <49152000>;
};

&clock_audss {
	assigned-clocks = <&clock_audss EXYNOS_MOUT_AUDSS>;
	assigned-clock-parents = <&clock CLK_FOUT_EPLL>;
};

&cpu0 {
	cpu0-supply = <&buck2_reg>;
};

&dsi_0 {
	vddcore-supply = <&ldo8_reg>;
	vddio-supply = <&ldo10_reg>;
	samsung,pll-clock-frequency = <24000000>;
	samsung,burst-clock-frequency = <320000000>;
	samsung,esc-clock-frequency = <10000000>;
	status = "okay";

	bridge@0 {
		reg = <0>;
		compatible = "toshiba,tc358764";
		vddc-supply = <&vcc_1v2_reg>;
		vddio-supply = <&vcc_1v8_reg>;
		vddlvds-supply = <&vcc_3v3_reg>;
		reset-gpios = <&gpd1 6 GPIO_ACTIVE_LOW>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;
			port@1 {
				reg = <1>;
				bridge_out_ep: endpoint {
					remote-endpoint = <&panel_ep>;
				};
			};
		};
	};
};

&fimd {
	status = "okay";
};

&hdmi {
	pinctrl-names = "default";
	pinctrl-0 = <&hdmi_hpd>;
	status = "okay";
	ddc = <&i2c_ddc>;
	hpd-gpios = <&gpx3 7 GPIO_ACTIVE_HIGH>;
	vdd_osc-supply = <&ldo10_reg>;
	vdd_pll-supply = <&ldo8_reg>;
	vdd-supply = <&ldo8_reg>;
};

&i2c_0 {
	status = "okay";
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <20000>;
	samsung,i2c-slave-addr = <0x66>;

	pmic@66 {
		compatible = "samsung,s5m8767-pmic";
		reg = <0x66>;
		interrupt-parent = <&gpx3>;
		interrupts = <2 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&s5m8767_irq>;
		wakeup-source;

		vinb1-supply = <&main_dc_reg>;
		vinb2-supply = <&main_dc_reg>;
		vinb3-supply = <&main_dc_reg>;
		vinb4-supply = <&main_dc_reg>;
		vinb5-supply = <&main_dc_reg>;
		vinb6-supply = <&main_dc_reg>;
		vinb7-supply = <&main_dc_reg>;
		vinb8-supply = <&main_dc_reg>;
		vinb9-supply = <&main_dc_reg>;

		vinl1-supply = <&buck7_reg>;
		vinl2-supply = <&buck7_reg>;
		vinl3-supply = <&buck7_reg>;
		vinl4-supply = <&main_dc_reg>;
		vinl5-supply = <&main_dc_reg>;
		vinl6-supply = <&main_dc_reg>;
		vinl7-supply = <&main_dc_reg>;
		vinl8-supply = <&buck8_reg>;
		vinl9-supply = <&buck8_reg>;

		s5m8767,pmic-buck-dvs-gpios = <&gpd1 0 GPIO_ACTIVE_HIGH>,
					      <&gpd1 1 GPIO_ACTIVE_HIGH>,
					      <&gpd1 2 GPIO_ACTIVE_HIGH>;
		s5m8767,pmic-buck-ds-gpios = <&gpx2 3 GPIO_ACTIVE_HIGH>,
					     <&gpx2 4 GPIO_ACTIVE_HIGH>,
					     <&gpx2 5 GPIO_ACTIVE_HIGH>;

		s5m8767_osc: clocks {
			compatible = "samsung,s5m8767-clk";
			#clock-cells = <1>;
			clock-output-names = "s5m8767_ap", "unused1", "unused2";
		};

		regulators {
			ldo1_reg: LDO1 {
				regulator-name = "VDD_ALIVE_1.0V";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo2_reg: LDO2 {
				regulator-name = "VDD_28IO_DP_1.35V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo3_reg: LDO3 {
				regulator-name = "VDD_COMMON1_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo4_reg: LDO4 {
				regulator-name = "VDD_IOPERI_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				op_mode = <1>;
			};

			ldo5_reg: LDO5 {
				regulator-name = "VDD_EXT_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo6_reg: LDO6 {
				regulator-name = "VDD_MPLL_1.1V";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo7_reg: LDO7 {
				regulator-name = "VDD_XPLL_1.1V";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo8_reg: LDO8 {
				regulator-name = "VDD_COMMON2_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo9_reg: LDO9 {
				regulator-name = "VDD_33ON_3.0V";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				op_mode = <1>;
			};

			ldo10_reg: LDO10 {
				regulator-name = "VDD_COMMON3_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo11_reg: LDO11 {
				regulator-name = "VDD_ABB2_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo12_reg: LDO12 {
				regulator-name = "VDD_USB_3.0V";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo13_reg: LDO13 {
				regulator-name = "VDDQ_C2C_W_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo14_reg: LDO14 {
				regulator-name = "VDD18_ABB0_3_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo15_reg: LDO15 {
				regulator-name = "VDD10_COMMON4_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo16_reg: LDO16 {
				regulator-name = "VDD18_HSIC_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo17_reg: LDO17 {
				regulator-name = "VDDQ_MMC2_3_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			ldo18_reg: LDO18 {
				regulator-name = "VDD_33ON_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				op_mode = <1>;
			};

			ldo22_reg: LDO22 {
				regulator-name = "EXT_33_OFF";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				op_mode = <1>;
			};

			ldo23_reg: LDO23 {
				regulator-name = "EXT_28_OFF";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				op_mode = <1>;
			};

			ldo25_reg: LDO25 {
				regulator-name = "PVDD_LDO25";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				op_mode = <1>;
			};

			ldo26_reg: LDO26 {
				regulator-name = "EXT_18_OFF";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				op_mode = <1>;
			};

			buck1_reg: BUCK1 {
				regulator-name = "VDD_MIF";
				regulator-min-microvolt = <950000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			buck2_reg: BUCK2 {
				regulator-name = "VDD_ARM";
				regulator-min-microvolt = <912500>;
				regulator-max-microvolt = <1300000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			buck3_reg: BUCK3 {
				regulator-name = "VDD_INT";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			buck4_reg: BUCK4 {
				regulator-name = "VDD_G3D";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1300000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			buck5_reg: BUCK5 {
				regulator-name = "VDD_MEM_1.35V";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1355000>;
				regulator-always-on;
				regulator-boot-on;
				op_mode = <1>;
			};

			buck7_reg: BUCK7 {
				regulator-name = "PVDD_BUCK7";
				regulator-always-on;
				op_mode = <1>;
			};

			buck8_reg: BUCK8 {
				regulator-name = "PVDD_BUCK8";
				regulator-always-on;
				op_mode = <1>;
			};

			buck9_reg: BUCK9 {
				regulator-name = "VDD_33_OFF_EXT1";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <3000000>;
				op_mode = <1>;
			};
		};
	};
};

&i2c_3 {
	status = "okay";

	wm1811: audio-codec@1a {
		compatible = "wlf,wm1811";
		reg = <0x1a>;
		clocks = <&i2s0 CLK_I2S_CDCLK>;
		clock-names = "MCLK1";

		AVDD2-supply = <&main_dc_reg>;
		CPVDD-supply = <&main_dc_reg>;
		DBVDD1-supply = <&main_dc_reg>;
		DBVDD2-supply = <&main_dc_reg>;
		DBVDD3-supply = <&main_dc_reg>;
		LDO1VDD-supply = <&main_dc_reg>;
		SPKVDD1-supply = <&main_dc_reg>;
		SPKVDD2-supply = <&main_dc_reg>;

		wlf,ldo1ena-gpios = <&gpb0 0 GPIO_ACTIVE_HIGH>;
		wlf,ldo2ena-gpios = <&gpb0 1 GPIO_ACTIVE_HIGH>;
	};
};

&i2c_8 {
	status = "okay";
	/* used by HDMI PHY */
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <66000>;
};

&i2c_9 {
	status = "okay";
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-max-bus-freq = <40000>;
};

&i2s0 {
	assigned-clocks = <&i2s0 CLK_I2S_RCLK_SRC>;
	assigned-clock-parents = <&clock_audss EXYNOS_I2S_BUS>;
	status = "okay";
};

&i2s0_bus {
	samsung,pin-drv = <EXYNOS4_PIN_DRV_LV2>;
};

&mali {
	mali-supply = <&buck4_reg>;
	status = "okay";
};

&mixer {
	status = "okay";
};

&mmc_0 {
	status = "okay";
	broken-cd;
	card-detect-delay = <200>;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	vmmc-supply = <&mmc_reg>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_bus4 &sd0_bus8>;
	bus-width = <8>;
	cap-mmc-highspeed;
	mmc-ddr-1_8v;
};

&mmc_2 {
	status = "okay";
	card-detect-delay = <200>;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	vmmc-supply = <&mmc_reg>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_cd &sd2_bus4>;
	bus-width = <4>;
	disable-wp;
	cap-sd-highspeed;
};

&pinctrl_0 {
	s5m8767_irq: s5m8767-irq-pins {
		samsung,pins = "gpx3-2";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};
};

&rtc {
	clocks = <&clock CLK_RTC>, <&s5m8767_osc S2MPS11_CLK_AP>;
	clock-names = "rtc", "rtc_src";
	status = "okay";
};

&sata {
	status = "okay";
};

&sata_phy {
	status = "okay";
	samsung,exynos-sataphy-i2c-phandle = <&sata_phy_i2c>;
};

&sata_phy_i2c {
	status = "okay";
};

&usbdrd {
	vdd10-supply = <&ldo15_reg>;
	vdd33-supply = <&ldo12_reg>;
};
