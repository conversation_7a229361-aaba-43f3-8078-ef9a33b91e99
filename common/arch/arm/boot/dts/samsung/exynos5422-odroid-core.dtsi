// SPDX-License-Identifier: GPL-2.0
/*
 * Hardkernel Odroid XU3/XU3-Lite/XU4/HC1 boards core device tree source
 *
 * Copyright (c) 2017 <PERSON><PERSON>
 * Copyright (c) 2013-2017 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 */

#include <dt-bindings/clock/samsung,s2mps11.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>
#include "exynos5800.dtsi"
#include "exynos5422-cpus.dtsi"

/ {
	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x7ea00000>;
	};

	aliases {
		mmc2 = &mmc_2;
	};

	chosen {
		stdout-path = "serial2:115200n8";
	};

	firmware@2073000 {
		compatible = "samsung,secure-firmware";
		reg = <0x02073000 0x1000>;
	};

	fixed-rate-clocks {
		oscclk {
			compatible = "samsung,exynos5420-oscclk";
			clock-frequency = <24000000>;
		};
	};

	bus_wcore_opp_table: opp-table-2 {
		compatible = "operating-points-v2";

		/* derived from 532MHz MPLL */
		opp00 {
			opp-hz = /bits/ 64 <88700000>;
			opp-microvolt = <925000 925000 1400000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <133000000>;
			opp-microvolt = <950000 950000 1400000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <177400000>;
			opp-microvolt = <950000 950000 1400000>;
		};
		opp03 {
			opp-hz = /bits/ 64 <266000000>;
			opp-microvolt = <950000 950000 1400000>;
		};
		opp04 {
			opp-hz = /bits/ 64 <532000000>;
			opp-microvolt = <1000000 1000000 1400000>;
		};
	};

	bus_noc_opp_table: opp-table-3 {
		compatible = "operating-points-v2";

		/* derived from 666MHz CPLL */
		opp00 {
			opp-hz = /bits/ 64 <66600000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <74000000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <83250000>;
		};
		opp03 {
			opp-hz = /bits/ 64 <111000000>;
		};
	};

	bus_fsys_apb_opp_table: opp-table-4 {
		compatible = "operating-points-v2";

		/* derived from 666MHz CPLL */
		opp00 {
			opp-hz = /bits/ 64 <111000000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <222000000>;
		};
	};

	bus_fsys2_opp_table: opp-table-5 {
		compatible = "operating-points-v2";

		/* derived from 600MHz DPLL */
		opp00 {
			opp-hz = /bits/ 64 <75000000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <120000000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <200000000>;
		};
	};

	bus_mfc_opp_table: opp-table-6 {
		compatible = "operating-points-v2";

		/* derived from 666MHz CPLL */
		opp00 {
			opp-hz = /bits/ 64 <83250000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <111000000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <166500000>;
		};
		opp03 {
			opp-hz = /bits/ 64 <222000000>;
		};
		opp04 {
			opp-hz = /bits/ 64 <333000000>;
		};
	};

	bus_gen_opp_table: opp-table-7 {
		compatible = "operating-points-v2";

		/* derived from 532MHz MPLL */
		opp00 {
			opp-hz = /bits/ 64 <88700000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <133000000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <178000000>;
		};
		opp03 {
			opp-hz = /bits/ 64 <266000000>;
		};
	};

	bus_peri_opp_table: opp-table-8 {
		compatible = "operating-points-v2";

		/* derived from 666MHz CPLL */
		opp00 {
			opp-hz = /bits/ 64 <66600000>;
		};
	};

	bus_g2d_opp_table: opp-table-9 {
		compatible = "operating-points-v2";

		/* derived from 666MHz CPLL */
		opp00 {
			opp-hz = /bits/ 64 <83250000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <111000000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <166500000>;
		};
		opp03 {
			opp-hz = /bits/ 64 <222000000>;
		};
		opp04 {
			opp-hz = /bits/ 64 <333000000>;
		};
	};

	bus_g2d_acp_opp_table: opp-table-10 {
		compatible = "operating-points-v2";

		/* derived from 532MHz MPLL */
		opp00 {
			opp-hz = /bits/ 64 <66500000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <133000000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <178000000>;
		};
		opp03 {
			opp-hz = /bits/ 64 <266000000>;
		};
	};

	bus_jpeg_opp_table: opp-table-11 {
		compatible = "operating-points-v2";

		/* derived from 600MHz DPLL */
		opp00 {
			opp-hz = /bits/ 64 <75000000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <150000000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <200000000>;
		};
		opp03 {
			opp-hz = /bits/ 64 <300000000>;
		};
	};

	bus_jpeg_apb_opp_table: opp-table-12 {
		compatible = "operating-points-v2";

		/* derived from 666MHz CPLL */
		opp00 {
			opp-hz = /bits/ 64 <83250000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <111000000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <133000000>;
		};
		opp03 {
			opp-hz = /bits/ 64 <166500000>;
		};
	};

	bus_disp1_fimd_opp_table: opp-table-13 {
		compatible = "operating-points-v2";

		/* derived from 600MHz DPLL */
		opp00 {
			opp-hz = /bits/ 64 <120000000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <200000000>;
		};
	};

	bus_disp1_opp_table: opp-table-14 {
		compatible = "operating-points-v2";

		/* derived from 600MHz DPLL */
		opp00 {
			opp-hz = /bits/ 64 <120000000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <200000000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <300000000>;
		};
	};

	bus_gscl_opp_table: opp-table-15 {
		compatible = "operating-points-v2";

		/* derived from 600MHz DPLL */
		opp00 {
			opp-hz = /bits/ 64 <150000000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <200000000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <300000000>;
		};
	};

	bus_mscl_opp_table: opp-table-16 {
		compatible = "operating-points-v2";

		/* derived from 666MHz CPLL */
		opp00 {
			opp-hz = /bits/ 64 <84000000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <167000000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <222000000>;
		};
		opp03 {
			opp-hz = /bits/ 64 <333000000>;
		};
		opp04 {
			opp-hz = /bits/ 64 <666000000>;
		};
	};

	dmc_opp_table: opp-table-17 {
		compatible = "operating-points-v2";

		opp00 {
			opp-hz = /bits/ 64 <165000000>;
			opp-microvolt = <875000>;
		};
		opp01 {
			opp-hz = /bits/ 64 <206000000>;
			opp-microvolt = <875000>;
		};
		opp02 {
			opp-hz = /bits/ 64 <275000000>;
			opp-microvolt = <875000>;
		};
		opp03 {
			opp-hz = /bits/ 64 <413000000>;
			opp-microvolt = <887500>;
		};
		opp04 {
			opp-hz = /bits/ 64 <543000000>;
			opp-microvolt = <937500>;
		};
		opp05 {
			opp-hz = /bits/ 64 <633000000>;
			opp-microvolt = <1012500>;
		};
		opp06 {
			opp-hz = /bits/ 64 <728000000>;
			opp-microvolt = <1037500>;
		};
		opp07 {
			opp-hz = /bits/ 64 <825000000>;
			opp-microvolt = <1050000>;
		};
	};

	samsung_K3QF2F20DB: lpddr3 {
		compatible	= "samsung,K3QF2F20DB", "jedec,lpddr3";
		density		= <16384>;
		io-width	= <32>;

		tRFC-min-tck		= <17>;
		tRRD-min-tck		= <2>;
		tRPab-min-tck		= <2>;
		tRPpb-min-tck		= <2>;
		tRCD-min-tck		= <3>;
		tRC-min-tck		= <6>;
		tRAS-min-tck		= <5>;
		tWTR-min-tck		= <2>;
		tWR-min-tck		= <7>;
		tRTP-min-tck		= <2>;
		tW2W-C2C-min-tck	= <0>;
		tR2R-C2C-min-tck	= <0>;
		tWL-min-tck		= <8>;
		tDQSCK-min-tck		= <5>;
		tRL-min-tck		= <14>;
		tFAW-min-tck		= <5>;
		tXSR-min-tck		= <12>;
		tXP-min-tck		= <2>;
		tCKE-min-tck		= <2>;
		tCKESR-min-tck		= <2>;
		tMRD-min-tck		= <5>;

		timings_samsung_K3QF2F20DB_800mhz: timings {
			compatible	= "jedec,lpddr3-timings";
			max-freq	= <800000000>;
			min-freq	= <100000000>;
			tRFC		= <65000>;
			tRRD		= <6000>;
			tRPab		= <12000>;
			tRPpb		= <12000>;
			tRCD		= <10000>;
			tRC		= <33750>;
			tRAS		= <23000>;
			tWTR		= <3750>;
			tWR		= <7500>;
			tRTP		= <3750>;
			tW2W-C2C	= <0>;
			tR2R-C2C	= <0>;
			tFAW		= <25000>;
			tXSR		= <70000>;
			tXP		= <3750>;
			tCKE		= <3750>;
			tCKESR		= <3750>;
			tMRD		= <7000>;
		};
	};
};

&adc {
	vdd-supply = <&ldo4_reg>;
	status = "okay";
};

&bus_wcore {
	operating-points-v2 = <&bus_wcore_opp_table>;
	devfreq-events = <&nocp_mem0_0>, <&nocp_mem0_1>,
			<&nocp_mem1_0>, <&nocp_mem1_1>;
	vdd-supply = <&buck3_reg>;
	exynos,saturation-ratio = <100>;
	status = "okay";
};

&bus_noc {
	operating-points-v2 = <&bus_noc_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_fsys_apb {
	operating-points-v2 = <&bus_fsys_apb_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_fsys2 {
	operating-points-v2 = <&bus_fsys2_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_mfc {
	operating-points-v2 = <&bus_mfc_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_gen {
	operating-points-v2 = <&bus_gen_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_peri {
	operating-points-v2 = <&bus_peri_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_g2d {
	operating-points-v2 = <&bus_g2d_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_g2d_acp {
	operating-points-v2 = <&bus_g2d_acp_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_jpeg {
	operating-points-v2 = <&bus_jpeg_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_jpeg_apb {
	operating-points-v2 = <&bus_jpeg_apb_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_disp1_fimd {
	operating-points-v2 = <&bus_disp1_fimd_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_disp1 {
	operating-points-v2 = <&bus_disp1_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_gscl_scaler {
	operating-points-v2 = <&bus_gscl_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&bus_mscl {
	operating-points-v2 = <&bus_mscl_opp_table>;
	devfreq = <&bus_wcore>;
	status = "okay";
};

&cpu0 {
	cpu-supply = <&buck6_reg>;
};

&cpu4 {
	cpu-supply = <&buck2_reg>;
};

&dmc {
	devfreq-events = <&ppmu_event3_dmc0_0>,	<&ppmu_event3_dmc0_1>,
			<&ppmu_event3_dmc1_0>, <&ppmu_event3_dmc1_1>;
	device-handle = <&samsung_K3QF2F20DB>;
	operating-points-v2 = <&dmc_opp_table>;
	vdd-supply = <&buck1_reg>;
	status = "okay";
};

&hsi2c_4 {
	status = "okay";

	pmic@66 {
		compatible = "samsung,s2mps11-pmic";
		reg = <0x66>;
		samsung,s2mps11-acokb-ground;

		interrupt-parent = <&gpx0>;
		interrupts = <4 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&s2mps11_irq>;
		wakeup-source;

		s2mps11_osc: clocks {
			compatible = "samsung,s2mps11-clk";
			#clock-cells = <1>;
			clock-output-names = "s2mps11_ap",
					"s2mps11_cp", "s2mps11_bt";
		};

		regulators {
			ldo1_reg: LDO1 {
				regulator-name = "vdd_ldo1";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo2_reg: LDO2 {
				regulator-name = "vdd_ldo2";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo3_reg: LDO3 {
				regulator-name = "vddq_mmc0";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo4_reg: LDO4 {
				regulator-name = "vdd_adc";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo5_reg: LDO5 {
				regulator-name = "vdd_ldo5";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo6_reg: LDO6 {
				regulator-name = "vdd_ldo6";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo7_reg: LDO7 {
				regulator-name = "vdd_ldo7";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo8_reg: LDO8 {
				regulator-name = "vdd_ldo8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo9_reg: LDO9 {
				regulator-name = "vdd_ldo9";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo10_reg: LDO10 {
				regulator-name = "vdd_ldo10";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo11_reg: LDO11 {
				regulator-name = "vdd_ldo11";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo12_reg: LDO12 {
				/* Unused */
				regulator-name = "vdd_ldo12";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <2375000>;
			};

			ldo13_reg: LDO13 {
				regulator-name = "vddq_mmc2";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <2800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo14_reg: LDO14 {
				/* Unused */
				regulator-name = "vdd_ldo14";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo15_reg: LDO15 {
				regulator-name = "vdd_ldo15";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo16_reg: LDO16 {
				/* Unused */
				regulator-name = "vdd_ldo16";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo17_reg: LDO17 {
				regulator-name = "vdd_ldo17";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo18_reg: LDO18 {
				regulator-name = "vdd_emmc_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo19_reg: LDO19 {
				regulator-name = "vdd_sd";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo20_reg: LDO20 {
				/* Unused */
				regulator-name = "vdd_ldo20";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo21_reg: LDO21 {
				/* Unused */
				regulator-name = "vdd_ldo21";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo22_reg: LDO22 {
				/* Unused */
				regulator-name = "vdd_ldo22";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <2375000>;
			};

			ldo23_reg: LDO23 {
				regulator-name = "vdd_mifs";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo24_reg: LDO24 {
				/* Unused */
				regulator-name = "vdd_ldo24";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo25_reg: LDO25 {
				/* Unused */
				regulator-name = "vdd_ldo25";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo26_reg: LDO26 {
				/* Used on XU3, XU3-Lite and XU4 */
				regulator-name = "vdd_ldo26";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo27_reg: LDO27 {
				regulator-name = "vdd_g3ds";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo28_reg: LDO28 {
				/* Used on XU3 */
				regulator-name = "vdd_ldo28";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo29_reg: LDO29 {
				/* Unused */
				regulator-name = "vdd_ldo29";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo30_reg: LDO30 {
				/* Unused */
				regulator-name = "vdd_ldo30";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo31_reg: LDO31 {
				/* Unused */
				regulator-name = "vdd_ldo31";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo32_reg: LDO32 {
				/* Unused */
				regulator-name = "vdd_ldo32";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo33_reg: LDO33 {
				/* Unused */
				regulator-name = "vdd_ldo33";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo34_reg: LDO34 {
				/* Unused */
				regulator-name = "vdd_ldo34";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo35_reg: LDO35 {
				/* Unused */
				regulator-name = "vdd_ldo35";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <2375000>;
			};

			ldo36_reg: LDO36 {
				/* Unused */
				regulator-name = "vdd_ldo36";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo37_reg: LDO37 {
				/* Unused */
				regulator-name = "vdd_ldo37";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			ldo38_reg: LDO38 {
				/* Unused */
				regulator-name = "vdd_ldo38";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3950000>;
			};

			buck1_reg: BUCK1 {
				regulator-name = "vdd_mif";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1300000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck2_reg: BUCK2 {
				regulator-name = "vdd_arm";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-coupled-with = <&buck3_reg>;
				regulator-coupled-max-spread = <300000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck3_reg: BUCK3 {
				regulator-name = "vdd_int";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1400000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-coupled-with = <&buck2_reg>;
				regulator-coupled-max-spread = <300000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck4_reg: BUCK4 {
				regulator-name = "vdd_g3d";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1400000>;
				regulator-boot-on;
				regulator-always-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck5_reg: BUCK5 {
				regulator-name = "vdd_mem";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1400000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck6_reg: BUCK6 {
				regulator-name = "vdd_kfc";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck7_reg: BUCK7 {
				regulator-name = "vdd_1.35v_ldo";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck8_reg: BUCK8 {
				regulator-name = "vdd_2.0v_ldo";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <2100000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck9_reg: BUCK9 {
				regulator-name = "vdd_2.8v_ldo";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3750000>;
				regulator-always-on;
				regulator-boot-on;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck10_reg: BUCK10 {
				regulator-name = "vdd_vmem";
				regulator-min-microvolt = <2850000>;
				regulator-max-microvolt = <2850000>;

				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};
		};
	};
};

&mmc_2 {
	status = "okay";
	card-detect-delay = <200>;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <0 4>;
	samsung,dw-mshc-ddr-timing = <0 2>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_cd &sd2_wp &sd2_bus1 &sd2_bus4>;
	bus-width = <4>;
	cap-sd-highspeed;
	max-frequency = <200000000>;
	vmmc-supply = <&ldo19_reg>;
	vqmmc-supply = <&ldo13_reg>;
	sd-uhs-sdr50;
	sd-uhs-sdr104;
	sd-uhs-ddr50;
};

&nocp_mem0_0 {
	status = "okay";
};

&nocp_mem0_1 {
	status = "okay";
};

&nocp_mem1_0 {
	status = "okay";
};

&nocp_mem1_1 {
	status = "okay";
};

&pinctrl_0 {
	s2mps11_irq: s2mps11-irq-pins {
		samsung,pins = "gpx0-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_F>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};

&ppmu_dmc0_0 {
	status = "okay";
};

&ppmu_dmc0_1 {
	status = "okay";
};

&ppmu_dmc1_0 {
	status = "okay";
};

&ppmu_dmc1_1 {
	status = "okay";
};

&tmu_cpu0 {
	vtmu-supply = <&ldo7_reg>;
};

&tmu_cpu1 {
	vtmu-supply = <&ldo7_reg>;
};

&tmu_cpu2 {
	vtmu-supply = <&ldo7_reg>;
};

&tmu_cpu3 {
	vtmu-supply = <&ldo7_reg>;
};

&tmu_gpu {
	vtmu-supply = <&ldo7_reg>;
};

&gpu {
	mali-supply = <&buck4_reg>;
	status = "okay";
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&s2mps11_osc S2MPS11_CLK_AP>;
	clock-names = "rtc", "rtc_src";
};

&usbdrd_dwc3_0 {
	dr_mode = "host";
};

/* usbdrd_dwc3_1 mode customized in each board */

&usbdrd3_0 {
	vdd33-supply = <&ldo9_reg>;
	vdd10-supply = <&ldo11_reg>;
};

&usbdrd3_1 {
	vdd33-supply = <&ldo9_reg>;
	vdd10-supply = <&ldo11_reg>;
};
