// SPDX-License-Identifier: GPL-2.0
/*
 * Device tree sources for Exynos4 thermal zone
 *
 * Copyright (c) 2014 <PERSON><PERSON><PERSON> <<EMAIL>>
 */

#include <dt-bindings/thermal/thermal.h>

/ {
thermal-zones {
	cpu_thermal: cpu-thermal {
		thermal-sensors = <&tmu>;
		polling-delay-passive = <0>;
		polling-delay = <0>;
		trips {
			cpu_alert0: cpu-alert-0 {
				temperature = <70000>; /* millicelsius */
				hysteresis = <10000>; /* millicelsius */
				type = "active";
			};
			cpu_alert1: cpu-alert-1 {
				temperature = <95000>; /* millicelsius */
				hysteresis = <10000>; /* millicelsius */
				type = "active";
			};
			cpu_alert2: cpu-alert-2 {
				temperature = <110000>; /* millicelsius */
				hysteresis = <10000>; /* millicelsius */
				type = "active";
			};
			cpu_crit0: cpu-crit-0 {
				temperature = <120000>; /* millicelsius */
				hysteresis = <0>; /* millicelsius */
				type = "critical";
			};
		};
		cooling-maps {
			map0 {
				trip = <&cpu_alert0>;
			};
			map1 {
				trip = <&cpu_alert1>;
			};
		};
	};
};
};
