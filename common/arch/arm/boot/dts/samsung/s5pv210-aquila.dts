// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's S5PV210 SoC device tree source
 *
 * Copyright (c) 2013-2014 Samsung Electronics, Co. Ltd.
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 * <PERSON><PERSON> <<EMAIL>>
 *
 * Board device tree source for Samsung Aquila board.
 */

/dts-v1/;
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include "s5pv210.dtsi"

/ {
	model = "Samsung Aquila based on S5PC110";
	compatible = "samsung,aquila", "samsung,s5pv210";

	aliases {
		i2c3 = &i2c_pmic;
	};

	chosen {
		bootargs = "console=ttySAC2,115200n8 root=/dev/mmcblk1p5 rw rootwait ignore_loglevel earlyprintk";
	};

	memory@30000000 {
		device_type = "memory";
		reg = <0x30000000 0x05000000>, <0x40000000 0x18000000>;
	};

	pmic_ap_clk: clock-0 {
		/* Workaround for missing clock on PMIC */
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <32768>;
	};

	vtf_reg: regulator-0 {
		compatible = "regulator-fixed";
		regulator-name = "V_TF_2.8V";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		gpio = <&mp05 4 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	pda_reg: regulator-1 {
		compatible = "regulator-fixed";
		regulator-name = "VCC_1.8V_PDA";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
	};

	bat_reg: regulator-2 {
		compatible = "regulator-fixed";
		regulator-name = "V_BAT";
		regulator-min-microvolt = <3700000>;
		regulator-max-microvolt = <3700000>;
	};

	i2c_pmic: i2c-pmic {
		compatible = "i2c-gpio";
		sda-gpios = <&gpj4 0 GPIO_ACTIVE_HIGH>;
		scl-gpios = <&gpj4 3 GPIO_ACTIVE_HIGH>;
		i2c-gpio,delay-us = <2>;        /* ~100 kHz */
		#address-cells = <1>;
		#size-cells = <0>;

		pmic@66 {
			compatible = "national,lp3974";
			reg = <0x66>;

			max8998,pmic-buck1-default-dvs-idx = <0>;
			max8998,pmic-buck1-dvs-gpios = <&gph0 3 GPIO_ACTIVE_HIGH>,
							<&gph0 4 GPIO_ACTIVE_HIGH>;
			max8998,pmic-buck1-dvs-voltage = <1200000>, <1200000>,
							<1200000>, <1200000>;

			max8998,pmic-buck2-default-dvs-idx = <0>;
			max8998,pmic-buck2-dvs-gpio = <&gph0 5 GPIO_ACTIVE_HIGH>;
			max8998,pmic-buck2-dvs-voltage = <1200000>, <1200000>;

			regulators {
				ldo2_reg: LDO2 {
					regulator-name = "VALIVE_1.1V";
					regulator-min-microvolt = <1100000>;
					regulator-max-microvolt = <1100000>;
					regulator-always-on;
				};

				ldo3_reg: LDO3 {
					regulator-name = "VUSB+MIPI_1.1V";
					regulator-min-microvolt = <1100000>;
					regulator-max-microvolt = <1100000>;
					regulator-always-on;
				};

				ldo4_reg: LDO4 {
					regulator-name = "VADC_3.3V";
					regulator-min-microvolt = <3300000>;
					regulator-max-microvolt = <3300000>;
				};

				ldo5_reg: LDO5 {
					regulator-name = "VTF_2.8V";
					regulator-min-microvolt = <2800000>;
					regulator-max-microvolt = <2800000>;
					regulator-always-on;
				};

				ldo6_reg: LDO6 {
					regulator-name = "VCC_3.3V";
					regulator-min-microvolt = <3300000>;
					regulator-max-microvolt = <3300000>;
					regulator-always-on;
				};

				ldo7_reg: LDO7 {
					regulator-name = "VCC_3.0V";
					regulator-min-microvolt = <3000000>;
					regulator-max-microvolt = <3000000>;
					regulator-always-on;
					regulator-boot-on;
				};

				ldo8_reg: LDO8 {
					regulator-name = "VUSB+VDAC_3.3V";
					regulator-min-microvolt = <3300000>;
					regulator-max-microvolt = <3300000>;
					regulator-always-on;
				};

				ldo9_reg: LDO9 {
					regulator-name = "VCC+VCAM_2.8V";
					regulator-min-microvolt = <2800000>;
					regulator-max-microvolt = <2800000>;
					regulator-always-on;
				};

				ldo10_reg: LDO10 {
					regulator-name = "VPLL_1.1V";
					regulator-min-microvolt = <1100000>;
					regulator-max-microvolt = <1100000>;
					regulator-always-on;
					regulator-boot-on;
				};

				ldo11_reg: LDO11 {
					regulator-name = "CAM_IO_2.8V";
					regulator-min-microvolt = <2800000>;
					regulator-max-microvolt = <2800000>;
					regulator-always-on;
				};

				ldo12_reg: LDO12 {
					regulator-name = "CAM_ISP_1.2V";
					regulator-min-microvolt = <1200000>;
					regulator-max-microvolt = <1200000>;
					regulator-always-on;
				};

				ldo13_reg: LDO13 {
					regulator-name = "CAM_A_2.8V";
					regulator-min-microvolt = <2800000>;
					regulator-max-microvolt = <2800000>;
					regulator-always-on;
				};

				ldo14_reg: LDO14 {
					regulator-name = "CAM_CIF_1.8V";
					regulator-min-microvolt = <1800000>;
					regulator-max-microvolt = <1800000>;
					regulator-always-on;
				};

				ldo15_reg: LDO15 {
					regulator-name = "CAM_AF_3.3V";
					regulator-min-microvolt = <3300000>;
					regulator-max-microvolt = <3300000>;
					regulator-always-on;
				};

				ldo16_reg: LDO16 {
					regulator-name = "VMIPI_1.8V";
					regulator-min-microvolt = <1800000>;
					regulator-max-microvolt = <1800000>;
					regulator-always-on;
				};

				ldo17_reg: LDO17 {
					regulator-name = "CAM_8M_1.8V";
					regulator-min-microvolt = <1800000>;
					regulator-max-microvolt = <1800000>;
					regulator-always-on;
				};

				buck1_reg: BUCK1 {
					regulator-name = "VARM_1.2V";
					regulator-min-microvolt = <1200000>;
					regulator-max-microvolt = <1200000>;
					regulator-always-on;
				};

				buck2_reg: BUCK2 {
					regulator-name = "VINT_1.2V";
					regulator-min-microvolt = <1200000>;
					regulator-max-microvolt = <1200000>;
					regulator-always-on;
				};

				buck3_reg: BUCK3 {
					regulator-name = "VCC_1.8V";
					regulator-min-microvolt = <1800000>;
					regulator-max-microvolt = <1800000>;
					regulator-always-on;
				};

				buck4_reg: BUCK4 {
					regulator-name = "CAM_CORE_1.2V";
					regulator-min-microvolt = <1200000>;
					regulator-max-microvolt = <1200000>;
					regulator-always-on;
				};

				ap32khz_reg: EN32KHz-AP {
					regulator-name = "32KHz AP";
					regulator-always-on;
				};

				vichg_reg: ENVICHG {
					regulator-name = "VICHG";
				};

				safeout1_reg: ESAFEOUT1 {
					regulator-name = "SAFEOUT1";
					regulator-always-on;
				};

				safeout2_reg: ESAFEOUT2 {
					regulator-name = "SAFEOUT2";
					regulator-boot-on;
				};
			};
		};

	};

	gpio-keys {
		compatible = "gpio-keys";

		power-key {
			gpios = <&gph2 6 1>;
			linux,code = <KEY_POWER>;
			label = "power";
			debounce-interval = <1>;
			wakeup-source;
		};
	};
};

&xusbxti {
	clock-frequency = <24000000>;
};

&keypad {
	linux,input-no-autorepeat;
	wakeup-source;
	samsung,keypad-num-rows = <3>;
	samsung,keypad-num-columns = <3>;
	pinctrl-names = "default";
	pinctrl-0 = <&keypad_row0>, <&keypad_row1>, <&keypad_row2>,
			<&keypad_col0>, <&keypad_col1>, <&keypad_col2>;
	status = "okay";

	key-1 {
		keypad,row = <0>;
		keypad,column = <1>;
		linux,code = <KEY_CONNECT>;
	};

	key-2 {
		keypad,row = <0>;
		keypad,column = <2>;
		linux,code = <KEY_BACK>;
	};

	key-3 {
		keypad,row = <1>;
		keypad,column = <1>;
		linux,code = <KEY_CAMERA_FOCUS>;
	};

	key-4 {
		keypad,row = <1>;
		keypad,column = <2>;
		linux,code = <KEY_VOLUMEUP>;
	};

	key-5 {
		keypad,row = <2>;
		keypad,column = <1>;
		linux,code = <KEY_CAMERA>;
	};

	key-6 {
		keypad,row = <2>;
		keypad,column = <2>;
		linux,code = <KEY_VOLUMEDOWN>;
	};
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&rtc {
	clocks = <&clocks CLK_RTC>, <&pmic_ap_clk>;
	clock-names = "rtc", "rtc_src";
};

&sdhci0 {
	bus-width = <4>;
	non-removable;
	status = "okay";
	vmmc-supply = <&ldo5_reg>;
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_bus4>;
	pinctrl-names = "default";
};

&sdhci2 {
	bus-width = <4>;
	cd-gpios = <&gph3 4 1>;
	vmmc-supply = <&vtf_reg>;
	cd-inverted;
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_bus4 &t_flash_detect>;
	pinctrl-names = "default";
	status = "okay";
};

&onenand {
	status = "okay";
};

&hsotg {
	vusb_a-supply = <&ldo3_reg>;
	vusb_d-supply = <&ldo8_reg>;
	dr_mode = "peripheral";
	status = "okay";
};

&usbphy {
	status = "okay";
};

&fimd {
	pinctrl-0 = <&lcd_clk &lcd_data24 &pwm1_out>;
	pinctrl-names = "default";
	status = "okay";

	display-timings {
		native-mode = <&timing0>;
		timing0: timing {
			clock-frequency = <0>;
			hactive = <800>;
			vactive = <480>;
			hfront-porch = <16>;
			hback-porch = <16>;
			hsync-len = <2>;
			vback-porch = <3>;
			vfront-porch = <28>;
			vsync-len = <1>;
		};
	};
};

&pinctrl0 {
	t_flash_detect: t-flash-detect-pins {
		samsung,pins = "gph3-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_INPUT>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
	};
};
