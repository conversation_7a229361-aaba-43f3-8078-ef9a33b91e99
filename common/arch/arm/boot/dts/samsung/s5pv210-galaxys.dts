// SPDX-License-Identifier: GPL-2.0

/dts-v1/;
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include "s5pv210-aries.dtsi"

/ {
	model = "Samsung Galaxy S1 (GT-I9000) based on S5PV210";
	compatible = "samsung,galaxys", "samsung,aries", "samsung,s5pv210";
	chassis-type = "handset";

	chosen {
		stdout-path = &uart2;
	};

	nand_pwrseq: nand-pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&gpj2 7 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&massmemory_en>;
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-power {
			label = "power";
			gpios = <&gph2 6 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			wakeup-source;
		};

		key-vol-down {
			label = "volume_down";
			gpios = <&gph3 1 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_VOLUMEDOWN>;
		};

		key-vol-up {
			label = "volume_up";
			gpios = <&gph3 2 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_VOLUMEUP>;
		};

		key-home {
			label = "home";
			gpios = <&gph3 5 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_HOME>;
			wakeup-source;
		};
	};

	i2c_fmradio: i2c-gpio-8 {
		compatible = "i2c-gpio";
		sda-gpios = <&gpd1 2 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpd1 3 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		i2c-gpio,delay-us = <2>;
		#address-cells = <1>;
		#size-cells = <0>;

		pinctrl-names = "default";
		pinctrl-0 = <&fm_i2c_pins>;

		fmradio@10 {
			compatible = "silabs,si470x";
			reg = <0x10>;
			interrupt-parent = <&gpj2>;
			interrupts = <4 IRQ_TYPE_EDGE_FALLING>;
			reset-gpios = <&gpj2 5 GPIO_ACTIVE_HIGH>;

			pinctrl-names = "default";
			pinctrl-0 = <&fm_irq &fm_rst>;
		};
	};

	micbias_reg: regulator-fixed-3 {
		compatible = "regulator-fixed";
		regulator-name = "MICBIAS";
		gpio = <&gpj4 2 GPIO_ACTIVE_HIGH>;
		enable-active-high;

		pinctrl-names = "default";
		pinctrl-0 = <&micbias_reg_ena>;
	};

	sound {
		compatible = "samsung,aries-wm8994";

		model = "Aries";

		extcon = <&fsa9480>;

		main-micbias-supply = <&micbias_reg>;
		headset-micbias-supply = <&micbias_reg>;

		earpath-sel-gpios = <&gpj2 6 GPIO_ACTIVE_HIGH>;

		io-channels = <&adc 3>;
		io-channel-names = "headset-detect";
		headset-detect-gpios = <&gph0 6 GPIO_ACTIVE_LOW>;
		headset-key-gpios = <&gph3 6 GPIO_ACTIVE_HIGH>;

		samsung,audio-routing =
			"HP", "HPOUT1L",
			"HP", "HPOUT1R",

			"SPK", "SPKOUTLN",
			"SPK", "SPKOUTLP",

			"RCV", "HPOUT2N",
			"RCV", "HPOUT2P",

			"LINE", "LINEOUT2N",
			"LINE", "LINEOUT2P",

			"IN1LP", "Main Mic",
			"IN1LN", "Main Mic",

			"IN1RP", "Headset Mic",
			"IN1RN", "Headset Mic",

			"IN2LN", "FM In",
			"IN2RN", "FM In",

			"Modem Out", "Modem TX",
			"Modem RX", "Modem In",

			"Bluetooth SPK", "TX",
			"RX", "Bluetooth Mic";

		pinctrl-names = "default";
		pinctrl-0 = <&headset_det &earpath_sel>;

		cpu {
			sound-dai = <&i2s0>, <&bt_codec>;
		};

		codec {
			sound-dai = <&wm8994>;
		};
	};
};

&aliases {
	i2c8 = &i2c_fmradio;
};

&pinctrl0 {
	pinctrl-names = "default";
	pinctrl-0 = <&sleep_cfg>;

	fm_i2c_pins: fm-i2c-pins {
		samsung,pins = "gpd1-2", "gpd1-3";
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	headset_det: headset-det-pins {
		samsung,pins = "gph0-6", "gph3-6";
		samsung,pin-function = <S5PV210_PIN_FUNC_F>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
	};

	fm_irq: fm-irq-pins {
		samsung,pins = "gpj2-4";
		samsung,pin-function = <S5PV210_PIN_FUNC_INPUT>;
		samsung,pin-pud = <S5PV210_PIN_PULL_UP>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	fm_rst: fm-rst-pins {
		samsung,pins = "gpj2-5";
		samsung,pin-function = <S5PV210_PIN_FUNC_OUTPUT>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	earpath_sel: earpath-sel-pins {
		samsung,pins = "gpj2-6";
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	massmemory_en: massmemory-en-pins {
		samsung,pins = "gpj2-7";
		samsung,pin-function = <S5PV210_PIN_FUNC_OUTPUT>;
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	micbias_reg_ena: micbias-reg-ena-pins {
		samsung,pins = "gpj4-2";
		samsung,pin-pud = <S5PV210_PIN_PULL_NONE>;
		samsung,pin-drv = <S5PV210_PIN_DRV_LV1>;
	};

	/* Based on CyanogenMod 3.0.101 kernel */
	sleep_cfg: sleep-state {
		PIN_SLP(gpa0-0, PREV, NONE);
		PIN_SLP(gpa0-1, PREV, NONE);
		PIN_SLP(gpa0-2, PREV, NONE);
		PIN_SLP(gpa0-3, OUT1, NONE);
		PIN_SLP(gpa0-4, INPUT, DOWN);
		PIN_SLP(gpa0-5, OUT0, NONE);
		PIN_SLP(gpa0-6, INPUT, DOWN);
		PIN_SLP(gpa0-7, OUT1, NONE);

		PIN_SLP(gpa1-0, INPUT, DOWN);
		PIN_SLP(gpa1-1, OUT0, NONE);
		PIN_SLP(gpa1-2, INPUT, NONE);
		PIN_SLP(gpa1-3, OUT0, NONE);

		PIN_SLP(gpb-0, OUT0, NONE);
		PIN_SLP(gpb-1, OUT1, NONE);
		PIN_SLP(gpb-2, OUT0, NONE);
		PIN_SLP(gpb-3, PREV, NONE);
		PIN_SLP(gpb-4, INPUT, NONE);
		PIN_SLP(gpb-5, PREV, NONE);
		PIN_SLP(gpb-6, INPUT, DOWN);
		PIN_SLP(gpb-7, OUT0, NONE);

		PIN_SLP(gpc0-0, OUT0, NONE);
		PIN_SLP(gpc0-1, INPUT, DOWN);
		PIN_SLP(gpc0-2, OUT0, NONE);
		PIN_SLP(gpc0-3, INPUT, NONE);
		PIN_SLP(gpc0-4, OUT0, NONE);

		PIN_SLP(gpc1-0, INPUT, DOWN);
		PIN_SLP(gpc1-1, INPUT, DOWN);
		PIN_SLP(gpc1-2, INPUT, DOWN);
		PIN_SLP(gpc1-3, INPUT, DOWN);
		PIN_SLP(gpc1-4, INPUT, DOWN);

		PIN_SLP(gpd0-0, INPUT, DOWN);
		PIN_SLP(gpd0-1, OUT0, NONE);
		PIN_SLP(gpd0-2, INPUT, DOWN);
		PIN_SLP(gpd0-3, INPUT, DOWN);

		PIN_SLP(gpd1-0, INPUT, NONE);
		PIN_SLP(gpd1-1, INPUT, NONE);
		PIN_SLP(gpd1-2, INPUT, NONE);
		PIN_SLP(gpd1-3, INPUT, NONE);
		PIN_SLP(gpd1-4, INPUT, DOWN);
		PIN_SLP(gpd1-5, INPUT, DOWN);

		PIN_SLP(gpe0-0, INPUT, DOWN);
		PIN_SLP(gpe0-1, INPUT, DOWN);
		PIN_SLP(gpe0-2, INPUT, DOWN);
		PIN_SLP(gpe0-3, INPUT, DOWN);
		PIN_SLP(gpe0-4, INPUT, DOWN);
		PIN_SLP(gpe0-5, INPUT, DOWN);
		PIN_SLP(gpe0-6, INPUT, DOWN);
		PIN_SLP(gpe0-7, INPUT, DOWN);

		PIN_SLP(gpe1-0, INPUT, DOWN);
		PIN_SLP(gpe1-1, INPUT, DOWN);
		PIN_SLP(gpe1-2, INPUT, DOWN);
		PIN_SLP(gpe1-3, OUT0, NONE);
		PIN_SLP(gpe1-4, INPUT, DOWN);

		PIN_SLP(gpf0-0, OUT0, NONE);
		PIN_SLP(gpf0-1, OUT0, NONE);
		PIN_SLP(gpf0-2, OUT0, NONE);
		PIN_SLP(gpf0-3, OUT0, NONE);
		PIN_SLP(gpf0-4, OUT0, NONE);
		PIN_SLP(gpf0-5, OUT0, NONE);
		PIN_SLP(gpf0-6, OUT0, NONE);
		PIN_SLP(gpf0-7, OUT0, NONE);

		PIN_SLP(gpf1-0, OUT0, NONE);
		PIN_SLP(gpf1-1, OUT0, NONE);
		PIN_SLP(gpf1-2, OUT0, NONE);
		PIN_SLP(gpf1-3, OUT0, NONE);
		PIN_SLP(gpf1-4, OUT0, NONE);
		PIN_SLP(gpf1-5, OUT0, NONE);
		PIN_SLP(gpf1-6, OUT0, NONE);
		PIN_SLP(gpf1-7, OUT0, NONE);

		PIN_SLP(gpf2-0, OUT0, NONE);
		PIN_SLP(gpf2-1, OUT0, NONE);
		PIN_SLP(gpf2-2, OUT0, NONE);
		PIN_SLP(gpf2-3, OUT0, NONE);
		PIN_SLP(gpf2-4, OUT0, NONE);
		PIN_SLP(gpf2-5, OUT0, NONE);
		PIN_SLP(gpf2-6, OUT0, NONE);
		PIN_SLP(gpf2-7, OUT0, NONE);

		PIN_SLP(gpf3-0, OUT0, NONE);
		PIN_SLP(gpf3-1, OUT0, NONE);
		PIN_SLP(gpf3-2, OUT0, NONE);
		PIN_SLP(gpf3-3, OUT0, NONE);
		PIN_SLP(gpf3-4, PREV, NONE);
		PIN_SLP(gpf3-5, INPUT, DOWN);

		PIN_SLP(gpg0-0, OUT0, NONE);
		PIN_SLP(gpg0-1, INPUT, NONE);
		PIN_SLP(gpg0-2, INPUT, NONE);
		PIN_SLP(gpg0-3, INPUT, NONE);
		PIN_SLP(gpg0-4, INPUT, NONE);
		PIN_SLP(gpg0-5, INPUT, NONE);
		PIN_SLP(gpg0-6, INPUT, NONE);

		PIN_SLP(gpg1-0, OUT0, NONE);
		PIN_SLP(gpg1-1, OUT1, NONE);
		PIN_SLP(gpg1-2, PREV, NONE);
		PIN_SLP(gpg1-3, OUT1, NONE);
		PIN_SLP(gpg1-4, OUT1, NONE);
		PIN_SLP(gpg1-5, OUT1, NONE);
		PIN_SLP(gpg1-6, OUT1, NONE);

		PIN_SLP(gpg2-0, OUT0, NONE);
		PIN_SLP(gpg2-1, OUT0, NONE);
		PIN_SLP(gpg2-2, INPUT, NONE);
		PIN_SLP(gpg2-3, OUT0, NONE);
		PIN_SLP(gpg2-4, OUT0, NONE);
		PIN_SLP(gpg2-5, OUT0, NONE);
		PIN_SLP(gpg2-6, OUT0, NONE);

		PIN_SLP(gpg3-0, OUT1, NONE);
		PIN_SLP(gpg3-1, OUT0, NONE);
		PIN_SLP(gpg3-2, INPUT, NONE);
		PIN_SLP(gpg3-3, INPUT, DOWN);
		PIN_SLP(gpg3-4, OUT0, NONE);
		PIN_SLP(gpg3-5, OUT0, NONE);
		PIN_SLP(gpg3-6, INPUT, DOWN);

		PIN_SLP(gpi-0, PREV, NONE);
		PIN_SLP(gpi-1, INPUT, DOWN);
		PIN_SLP(gpi-2, PREV, NONE);
		PIN_SLP(gpi-3, PREV, NONE);
		PIN_SLP(gpi-4, PREV, NONE);
		PIN_SLP(gpi-5, INPUT, DOWN);
		PIN_SLP(gpi-6, INPUT, DOWN);

		PIN_SLP(gpj0-0, INPUT, NONE);
		PIN_SLP(gpj0-1, INPUT, NONE);
		PIN_SLP(gpj0-2, INPUT, NONE);
		PIN_SLP(gpj0-3, INPUT, NONE);
		PIN_SLP(gpj0-4, INPUT, NONE);
		PIN_SLP(gpj0-5, INPUT, DOWN);
		PIN_SLP(gpj0-6, OUT0, NONE);
		PIN_SLP(gpj0-7, INPUT, NONE);

		PIN_SLP(gpj1-0, INPUT, DOWN);
		PIN_SLP(gpj1-1, OUT0, NONE);
		PIN_SLP(gpj1-2, INPUT, DOWN);
		PIN_SLP(gpj1-3, PREV, NONE);
		PIN_SLP(gpj1-4, PREV, NONE);
		PIN_SLP(gpj1-5, OUT0, NONE);

		PIN_SLP(gpj2-0, INPUT, DOWN);
		PIN_SLP(gpj2-1, INPUT, DOWN);
		PIN_SLP(gpj2-2, OUT0, NONE);
		PIN_SLP(gpj2-3, INPUT, DOWN);
		PIN_SLP(gpj2-4, INPUT, UP);
		PIN_SLP(gpj2-5, PREV, NONE);
		PIN_SLP(gpj2-6, PREV, NONE);
		PIN_SLP(gpj2-7, OUT1, NONE);

		PIN_SLP(gpj3-0, INPUT, NONE);
		PIN_SLP(gpj3-1, INPUT, NONE);
		PIN_SLP(gpj3-2, OUT0, NONE);
		PIN_SLP(gpj3-3, INPUT, DOWN);
		PIN_SLP(gpj3-4, INPUT, NONE);
		PIN_SLP(gpj3-5, INPUT, NONE);
		PIN_SLP(gpj3-6, INPUT, NONE);
		PIN_SLP(gpj3-7, INPUT, NONE);

		PIN_SLP(gpj4-0, INPUT, NONE);
		PIN_SLP(gpj4-1, INPUT, DOWN);
		PIN_SLP(gpj4-2, PREV, NONE);
		PIN_SLP(gpj4-3, INPUT, NONE);
		PIN_SLP(gpj4-4, INPUT, DOWN);

		PIN_SLP(mp01-0, INPUT, DOWN);
		PIN_SLP(mp01-1, OUT0, NONE);
		PIN_SLP(mp01-2, INPUT, DOWN);
		PIN_SLP(mp01-3, INPUT, DOWN);
		PIN_SLP(mp01-4, OUT1, NONE);
		PIN_SLP(mp01-5, INPUT, DOWN);
		PIN_SLP(mp01-6, INPUT, DOWN);
		PIN_SLP(mp01-7, INPUT, DOWN);

		PIN_SLP(mp02-0, INPUT, DOWN);
		PIN_SLP(mp02-1, INPUT, DOWN);
		PIN_SLP(mp02-2, INPUT, NONE);
		PIN_SLP(mp02-3, INPUT, DOWN);

		PIN_SLP(mp03-0, INPUT, DOWN);
		PIN_SLP(mp03-1, INPUT, DOWN);
		PIN_SLP(mp03-2, OUT1, NONE);
		PIN_SLP(mp03-3, OUT0, NONE);
		PIN_SLP(mp03-4, INPUT, NONE);
		PIN_SLP(mp03-5, OUT1, NONE);
		PIN_SLP(mp03-6, INPUT, DOWN);
		PIN_SLP(mp03-7, INPUT, DOWN);

		PIN_SLP(mp04-0, INPUT, DOWN);
		PIN_SLP(mp04-1, OUT0, NONE);
		PIN_SLP(mp04-2, INPUT, DOWN);
		PIN_SLP(mp04-3, OUT0, NONE);
		PIN_SLP(mp04-4, INPUT, DOWN);
		PIN_SLP(mp04-5, INPUT, DOWN);
		PIN_SLP(mp04-6, OUT0, NONE);
		PIN_SLP(mp04-7, INPUT, DOWN);

		PIN_SLP(mp05-0, INPUT, NONE);
		PIN_SLP(mp05-1, INPUT, NONE);
		PIN_SLP(mp05-2, INPUT, NONE);
		PIN_SLP(mp05-3, INPUT, NONE);
		PIN_SLP(mp05-4, INPUT, DOWN);
		PIN_SLP(mp05-5, OUT0, NONE);
		PIN_SLP(mp05-6, INPUT, DOWN);
		PIN_SLP(mp05-7, PREV, NONE);

		PIN_SLP(mp06-0, INPUT, DOWN);
		PIN_SLP(mp06-1, INPUT, DOWN);
		PIN_SLP(mp06-2, INPUT, DOWN);
		PIN_SLP(mp06-3, INPUT, DOWN);
		PIN_SLP(mp06-4, INPUT, DOWN);
		PIN_SLP(mp06-5, INPUT, DOWN);
		PIN_SLP(mp06-6, INPUT, DOWN);
		PIN_SLP(mp06-7, INPUT, DOWN);

		PIN_SLP(mp07-0, INPUT, DOWN);
		PIN_SLP(mp07-1, INPUT, DOWN);
		PIN_SLP(mp07-2, INPUT, DOWN);
		PIN_SLP(mp07-3, INPUT, DOWN);
		PIN_SLP(mp07-4, INPUT, DOWN);
		PIN_SLP(mp07-5, INPUT, DOWN);
		PIN_SLP(mp07-6, INPUT, DOWN);
		PIN_SLP(mp07-7, INPUT, DOWN);
	};
};

&sdhci0 {
	bus-width = <4>;
	non-removable;
	mmc-pwrseq = <&nand_pwrseq>;
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_bus4>;
	pinctrl-names = "default";
	status = "okay";

	assigned-clocks = <&clocks MOUT_MMC0>, <&clocks SCLK_MMC0>;
	assigned-clock-rates = <0>, <52000000>;
	assigned-clock-parents = <&clocks MOUT_MPLL>;
};
