// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's S3C64xx SoC series common device tree source
 * - pin control-related definitions
 *
 * Copyright (c) 2013 Tomasz <PERSON>ga <<EMAIL>>
 *
 * Samsung's S3C64xx SoCs pin banks, pin-mux and pin-config options are
 * listed as device tree nodes in this file.
 */

#include "s3c64xx-pinctrl.h"

&pinctrl0 {
	/*
	 * Pin banks
	 */

	gpa: gpa-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpb: gpb-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpc: gpc-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpd: gpd-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpe: gpe-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpf: gpf-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpg: gpg-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gph: gph-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpi: gpi-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpj: gpj-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpk: gpk-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpl: gpl-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpm: gpm-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpn: gpn-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpo: gpo-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpp: gpp-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpq: gpq-gpio-bank {
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	/*
	 * Pin groups
	 */

	uart0_data: uart0-data-pins {
		samsung,pins = "gpa-0", "gpa-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	uart0_fctl: uart0-fctl-pins {
		samsung,pins = "gpa-2", "gpa-3";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	uart1_data: uart1-data-pins {
		samsung,pins = "gpa-4", "gpa-5";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	uart1_fctl: uart1-fctl-pins {
		samsung,pins = "gpa-6", "gpa-7";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	uart2_data: uart2-data-pins {
		samsung,pins = "gpb-0", "gpb-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	uart3_data: uart3-data-pins {
		samsung,pins = "gpb-2", "gpb-3";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	ext_dma_0: ext-dma-0-pins {
		samsung,pins = "gpb-0", "gpb-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	ext_dma_1: ext-dma-1-pins {
		samsung,pins = "gpb-2", "gpb-3";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	irda_data_0: irda-data-0-pins {
		samsung,pins = "gpb-0", "gpb-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	irda_data_1: irda-data-1-pins {
		samsung,pins = "gpb-2", "gpb-3";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	irda_sdbw: irda-sdbw-pins {
		samsung,pins = "gpb-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	i2c0_bus: i2c0-bus-pins {
		samsung,pins = "gpb-5", "gpb-6";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_UP>;
	};

	i2c1_bus: i2c1-bus-pins {
		/* S3C6410-only */
		samsung,pins = "gpb-2", "gpb-3";
		samsung,pin-function = <S3C64XX_PIN_FUNC_6>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_UP>;
	};

	spi0_bus: spi0-bus-pins {
		samsung,pins = "gpc-0", "gpc-1", "gpc-2";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_UP>;
	};

	spi0_cs: spi0-cs-pins {
		samsung,pins = "gpc-3";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	spi1_bus: spi1-bus-pins {
		samsung,pins = "gpc-4", "gpc-5", "gpc-6";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_UP>;
	};

	spi1_cs: spi1-cs-pins {
		samsung,pins = "gpc-7";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd0_cmd: sd0-cmd-pins {
		samsung,pins = "gpg-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd0_clk: sd0-clk-pins {
		samsung,pins = "gpg-0";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd0_bus1: sd0-bus1-pins {
		samsung,pins = "gpg-2";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd0_bus4: sd0-bus4-pins {
		samsung,pins = "gpg-2", "gpg-3", "gpg-4", "gpg-5";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd0_cd: sd0-cd-pins {
		samsung,pins = "gpg-6";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_UP>;
	};

	sd1_cmd: sd1-cmd-pins {
		samsung,pins = "gph-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd1_clk: sd1-clk-pins {
		samsung,pins = "gph-0";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd1_bus1: sd1-bus1-pins {
		samsung,pins = "gph-2";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd1_bus4: sd1-bus4-pins {
		samsung,pins = "gph-2", "gph-3", "gph-4", "gph-5";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd1_bus8: sd1-bus8-pins {
		samsung,pins = "gph-2", "gph-3", "gph-4", "gph-5",
				"gph-6", "gph-7", "gph-8", "gph-9";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd1_cd: sd1-cd-pins {
		samsung,pins = "gpg-6";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_UP>;
	};

	sd2_cmd: sd2-cmd-pins {
		samsung,pins = "gpc-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd2_clk: sd2-clk-pins {
		samsung,pins = "gpc-5";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd2_bus1: sd2-bus1-pins {
		samsung,pins = "gph-6";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	sd2_bus4: sd2-bus4-pins {
		samsung,pins = "gph-6", "gph-7", "gph-8", "gph-9";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	i2s0_bus: i2s0-bus-pins {
		samsung,pins = "gpd-0", "gpd-2", "gpd-3", "gpd-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	i2s0_cdclk: i2s0-cdclk-pins {
		samsung,pins = "gpd-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	i2s1_bus: i2s1-bus-pins {
		samsung,pins = "gpe-0", "gpe-2", "gpe-3", "gpe-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	i2s1_cdclk: i2s1-cdclk-pins {
		samsung,pins = "gpe-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	i2s2_bus: i2s2-bus-pins {
		/* S3C6410-only */
		samsung,pins = "gpc-4", "gpc-5", "gpc-6", "gph-6",
				"gph-8", "gph-9";
		samsung,pin-function = <S3C64XX_PIN_FUNC_5>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	i2s2_cdclk: i2s2-cdclk-pins {
		/* S3C6410-only */
		samsung,pins = "gph-7";
		samsung,pin-function = <S3C64XX_PIN_FUNC_5>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	pcm0_bus: pcm0-bus-pins {
		samsung,pins = "gpd-0", "gpd-2", "gpd-3", "gpd-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	pcm0_extclk: pcm0-extclk-pins {
		samsung,pins = "gpd-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	pcm1_bus: pcm1-bus-pins {
		samsung,pins = "gpe-0", "gpe-2", "gpe-3", "gpe-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	pcm1_extclk: pcm1-extclk-pins {
		samsung,pins = "gpe-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	ac97_bus_0: ac97-bus-0-pins {
		samsung,pins = "gpd-0", "gpd-1", "gpd-2", "gpd-3", "gpd-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	ac97_bus_1: ac97-bus-1-pins {
		samsung,pins = "gpe-0", "gpe-1", "gpe-2", "gpe-3", "gpe-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	cam_port: cam-port-pins {
		samsung,pins = "gpf-0", "gpf-1", "gpf-2", "gpf-4",
				"gpf-5", "gpf-6", "gpf-7", "gpf-8",
				"gpf-9", "gpf-10", "gpf-11", "gpf-12";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	cam_rst: cam-rst-pins {
		samsung,pins = "gpf-3";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	cam_field: cam-field-pins {
		/* S3C6410-only */
		samsung,pins = "gpb-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	pwm_extclk: pwm-extclk-pins {
		samsung,pins = "gpf-13";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	pwm0_out: pwm0-out-pins {
		samsung,pins = "gpf-14";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	pwm1_out: pwm1-out-pins {
		samsung,pins = "gpf-15";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	clkout0: clkout-0-pins {
		samsung,pins = "gpf-14";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col0_0: keypad-col0-0-pins {
		samsung,pins = "gph-0";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col1_0: keypad-col1-0-pins {
		samsung,pins = "gph-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col2_0: keypad-col2-0-pins {
		samsung,pins = "gph-2";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col3_0: keypad-col3-0-pins {
		samsung,pins = "gph-3";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col4_0: keypad-col4-0-pins {
		samsung,pins = "gph-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col5_0: keypad-col5-0-pins {
		samsung,pins = "gph-5";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col6_0: keypad-col6-0-pins {
		samsung,pins = "gph-6";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col7_0: keypad-col7-0-pins {
		samsung,pins = "gph-7";
		samsung,pin-function = <S3C64XX_PIN_FUNC_4>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col0_1: keypad-col0-1-pins {
		samsung,pins = "gpl-0";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col1_1: keypad-col1-1-pins {
		samsung,pins = "gpl-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col2_1: keypad-col2-1-pins {
		samsung,pins = "gpl-2";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col3_1: keypad-col3-1-pins {
		samsung,pins = "gpl-3";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col4_1: keypad-col4-1-pins {
		samsung,pins = "gpl-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col5_1: keypad-col5-1-pins {
		samsung,pins = "gpl-5";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col6_1: keypad-col6-1-pins {
		samsung,pins = "gpl-6";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_col7_1: keypad-col7-1-pins {
		samsung,pins = "gpl-7";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row0_0: keypad-row0-0-pins {
		samsung,pins = "gpk-8";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row1_0: keypad-row1-0-pins {
		samsung,pins = "gpk-9";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row2_0: keypad-row2-0-pins {
		samsung,pins = "gpk-10";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row3_0: keypad-row3-0-pins {
		samsung,pins = "gpk-11";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row4_0: keypad-row4-0-pins {
		samsung,pins = "gpk-12";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row5_0: keypad-row5-0-pins {
		samsung,pins = "gpk-13";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row6_0: keypad-row6-0-pins {
		samsung,pins = "gpk-14";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row7_0: keypad-row7-0-pins {
		samsung,pins = "gpk-15";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row0_1: keypad-row0-1-pins {
		samsung,pins = "gpn-0";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row1_1: keypad-row1-1-pins {
		samsung,pins = "gpn-1";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row2_1: keypad-row2-1-pins {
		samsung,pins = "gpn-2";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row3_1: keypad-row3-1-pins {
		samsung,pins = "gpn-3";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row4_1: keypad-row4-1-pins {
		samsung,pins = "gpn-4";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row5_1: keypad-row5-1-pins {
		samsung,pins = "gpn-5";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row6_1: keypad-row6-1-pins {
		samsung,pins = "gpn-6";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	keypad_row7_1: keypad-row7-1-pins {
		samsung,pins = "gpn-7";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	lcd_ctrl: lcd-ctrl-pins {
		samsung,pins = "gpj-8", "gpj-9", "gpj-10", "gpj-11";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	lcd_data16: lcd-data-width16-pins {
		samsung,pins = "gpi-3", "gpi-4", "gpi-5", "gpi-6",
				"gpi-7", "gpi-10", "gpi-11", "gpi-12",
				"gpi-13", "gpi-14", "gpi-15", "gpj-3",
				"gpj-4", "gpj-5", "gpj-6", "gpj-7";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	lcd_data18: lcd-data-width18-pins {
		samsung,pins = "gpi-2", "gpi-3", "gpi-4", "gpi-5",
				"gpi-6", "gpi-7", "gpi-10", "gpi-11",
				"gpi-12", "gpi-13", "gpi-14", "gpi-15",
				"gpj-2", "gpj-3", "gpj-4", "gpj-5",
				"gpj-6", "gpj-7";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	lcd_data24: lcd-data-width24-pins {
		samsung,pins = "gpi-0", "gpi-1", "gpi-2", "gpi-3",
				"gpi-4", "gpi-5", "gpi-6", "gpi-7",
				"gpi-8", "gpi-9", "gpi-10", "gpi-11",
				"gpi-12", "gpi-13", "gpi-14", "gpi-15",
				"gpj-0", "gpj-1", "gpj-2", "gpj-3",
				"gpj-4", "gpj-5", "gpj-6", "gpj-7";
		samsung,pin-function = <S3C64XX_PIN_FUNC_2>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};

	hsi_bus: hsi-bus-pins {
		samsung,pins = "gpk-0", "gpk-1", "gpk-2", "gpk-3",
				"gpk-4", "gpk-5", "gpk-6", "gpk-7";
		samsung,pin-function = <S3C64XX_PIN_FUNC_3>;
		samsung,pin-pud = <S3C64XX_PIN_PULL_NONE>;
	};
};
