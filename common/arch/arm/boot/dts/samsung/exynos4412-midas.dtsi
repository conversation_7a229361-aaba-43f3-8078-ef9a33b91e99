// SPDX-License-Identifier: GPL-2.0
/*
 * Samsung's Exynos4412 based Trats 2 board device tree source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 *
 * Device tree source file for Samsung's Trats 2 board which is based on
 * Samsung's Exynos4412 SoC.
 */

/dts-v1/;
#include "exynos4412.dtsi"
#include "exynos4412-ppmu-common.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/clock/maxim,max77686.h>
#include "exynos-pinctrl.h"

/ {
	compatible = "samsung,midas", "samsung,exynos4412", "samsung,exynos4";

	aliases {
		i2c11 = &i2c_max77693;
		i2c12 = &i2c_max77693_fuel;
		mmc0 = &mshc_0;
		mmc2 = &sdhci_2;
		mmc3 = &sdhci_3;
	};

	chosen {
		stdout-path = &serial_2;
	};

	firmware@204f000 {
		compatible = "samsung,secure-firmware";
		reg = <0x0204f000 0x1000>;
	};

	fixed-rate-clocks {
		xxti {
			compatible = "samsung,clock-xxti";
			clock-frequency = <0>;
		};

		xusbxti {
			compatible = "samsung,clock-xusbxti";
			clock-frequency = <24000000>;
		};
	};

	cam_io_reg: voltage-regulator-1 {
		compatible = "regulator-fixed";
		regulator-name = "CAM_SENSOR_A";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		enable-active-high;
		status = "disabled";
	};

	cam_af_reg: voltage-regulator-2 {
		compatible = "regulator-fixed";
		regulator-name = "CAM_AF";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		enable-active-high;
		status = "disabled";
	};

	vsil12: voltage-regulator-3 {
		compatible = "regulator-fixed";
		regulator-name = "VSIL_1.2V";
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		gpio = <&gpl0 4 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		vin-supply = <&buck7_reg>;
	};

	vcc33mhl: voltage-regulator-4 {
		compatible = "regulator-fixed";
		regulator-name = "VCC_3.3_MHL";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpl0 4 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	vcc18mhl: voltage-regulator-5 {
		compatible = "regulator-fixed";
		regulator-name = "VCC_1.8_MHL";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		gpio = <&gpl0 4 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	touchkey_reg: voltage-regulator-6 {
		compatible = "regulator-fixed";
		regulator-name = "LED_VDD_3.3V";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		enable-active-high;
		status = "disabled";
	};

	vbatt_reg: voltage-regulator-7 {
		compatible = "regulator-fixed";
		regulator-name = "VBATT";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};

	mic_bias_reg: voltage-regulator-8 {
		compatible = "regulator-fixed";
		regulator-name = "MICBIAS_LDO_2.8V";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		gpio = <&gpf1 7 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	submic_bias_reg: voltage-regulator-9 {
		compatible = "regulator-fixed";
		regulator-name = "SUB_MICBIAS_LDO_2.8V";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
	};

	gpio-keys {
		compatible = "gpio-keys";
		pinctrl-names = "default";
		pinctrl-0 = <&gpio_keys>;

		key-down {
			gpios = <&gpx3 3 GPIO_ACTIVE_LOW>;
			linux,code = <114>;
			label = "volume down";
			debounce-interval = <10>;
		};

		key-up {
			gpios = <&gpx2 2 GPIO_ACTIVE_LOW>;
			linux,code = <115>;
			label = "volume up";
			debounce-interval = <10>;
		};

		key-power {
			gpios = <&gpx2 7 GPIO_ACTIVE_LOW>;
			linux,code = <116>;
			label = "power";
			debounce-interval = <10>;
			wakeup-source;
		};

		key-ok {
			gpios = <&gpx0 1 GPIO_ACTIVE_LOW>;
			linux,code = <139>;
			label = "ok";
			debounce-interval = <10>;
			wakeup-source;
		};
	};

	i2c_max77693: i2c-gpio-1 {
		compatible = "i2c-gpio";
		sda-gpios = <&gpm2 0 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpm2 1 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		i2c-gpio,delay-us = <2>;
		#address-cells = <1>;
		#size-cells = <0>;

		pmic@66 {
			compatible = "maxim,max77693";
			interrupt-parent = <&gpx1>;
			interrupts = <5 IRQ_TYPE_LEVEL_LOW>;
			pinctrl-names = "default";
			pinctrl-0 = <&max77693_irq>;
			reg = <0x66>;

			muic {
				compatible = "maxim,max77693-muic";

				connector {
					compatible = "samsung,usb-connector-11pin",
						     "usb-b-connector";
					label = "micro-USB";
					type = "micro";

					ports {
						#address-cells = <1>;
						#size-cells = <0>;

						port@0 {
							reg = <0>;

							muic_to_usb: endpoint {
								remote-endpoint = <&usb_to_muic>;
							};
						};

						port@3 {
							reg = <3>;

							muic_to_mhl: endpoint {
								remote-endpoint = <&mhl_to_muic>;
							};
						};
					};
				};
			};

			regulators {
				esafeout1_reg: ESAFEOUT1 {
					regulator-name = "ESAFEOUT1";
				};
				esafeout2_reg: ESAFEOUT2 {
					regulator-name = "ESAFEOUT2";
				};
				charger_reg: CHARGER {
					regulator-name = "CHARGER";
					regulator-min-microamp = <60000>;
					regulator-max-microamp = <2580000>;
				};
			};

			motor-driver {
				compatible = "maxim,max77693-haptic";
				haptic-supply = <&ldo26_reg>;
				pwms = <&pwm 0 38022 0>;
			};

			charger {
				compatible = "maxim,max77693-charger";

				maxim,constant-microvolt = <4350000>;
				maxim,min-system-microvolt = <3600000>;
				maxim,thermal-regulation-celsius = <100>;
				maxim,battery-overcurrent-microamp = <3500000>;
				maxim,charge-input-threshold-microvolt = <4300000>;
			};
		};
	};

	i2c_max77693_fuel: i2c-gpio-3 {
		compatible = "i2c-gpio";
		sda-gpios = <&gpf1 5 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpf1 4 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		i2c-gpio,delay-us = <2>;
		#address-cells = <1>;
		#size-cells = <0>;

		fuel-gauge@36 {
			compatible = "maxim,max17047";
			interrupt-parent = <&gpx2>;
			interrupts = <3 IRQ_TYPE_LEVEL_LOW>;
			pinctrl-names = "default";
			pinctrl-0 = <&max77693_fuel_irq>;
			reg = <0x36>;

			maxim,over-heat-temp = <700>;
			maxim,over-volt = <4500>;
		};
	};

	i2c-gpio-4 {
		compatible = "i2c-gpio";
		sda-gpios = <&gpl0 2 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpl0 1 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		i2c-gpio,delay-us = <2>;
		#address-cells = <1>;
		#size-cells = <0>;

		touchkey@20 {
			compatible = "cypress,midas-touchkey";
			reg = <0x20>;
			vdd-supply = <&touchkey_reg>;
			vcc-supply = <&ldo5_reg>;
			interrupt-parent = <&gpj0>;
			interrupts = <3 IRQ_TYPE_EDGE_FALLING>;
			linux,keycodes = <KEY_BACK KEY_MENU>;
		};
	};

	i2c-mhl {
		compatible = "i2c-gpio";
		sda-gpios = <&gpf0 4 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		scl-gpios = <&gpf0 6 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;
		i2c-gpio,delay-us = <100>;
		#address-cells = <1>;
		#size-cells = <0>;

		pinctrl-0 = <&i2c_mhl_bus>;
		pinctrl-names = "default";

		sii9234: hdmi-bridge@39 {
			compatible = "sil,sii9234";
			avcc33-supply = <&vcc33mhl>;
			iovcc18-supply = <&vcc18mhl>;
			avcc12-supply = <&vsil12>;
			cvcc12-supply = <&vsil12>;
			reset-gpios = <&gpf3 4 GPIO_ACTIVE_LOW>;
			interrupt-parent = <&gpf3>;
			interrupts = <5 IRQ_TYPE_LEVEL_HIGH>;
			reg = <0x39>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;

					mhl_to_hdmi: endpoint {
						remote-endpoint = <&hdmi_to_mhl>;
					};
				};

				port@1 {
					reg = <1>;

					mhl_to_muic: endpoint {
						remote-endpoint = <&muic_to_mhl>;
					};
				};
			};
		};
	};

	wlan_pwrseq: sdhci3-pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&gpj0 0 GPIO_ACTIVE_LOW>;
		clocks = <&max77686 MAX77686_CLK_PMIC>;
		clock-names = "ext_clock";
	};

	sound: sound {
		compatible = "samsung,midas-audio";
		model = "Midas";
		mic-bias-supply = <&mic_bias_reg>;
		submic-bias-supply = <&submic_bias_reg>;

		cpu {
			sound-dai = <&i2s0 0>;
		};
		codec {
			sound-dai = <&wm1811>;
		};
	};

	thermistor-ap {
		compatible = "murata,ncp15wb473";
		pullup-uv = <1800000>;	 /* VCC_1.8V_AP */
		pullup-ohm = <100000>;	 /* 100K */
		pulldown-ohm = <100000>; /* 100K */
		io-channels = <&adc 1>;  /* AP temperature */
	};

	thermistor-battery {
		compatible = "murata,ncp15wb473";
		pullup-uv = <1800000>;	 /* VCC_1.8V_AP */
		pullup-ohm = <100000>;	 /* 100K */
		pulldown-ohm = <100000>; /* 100K */
		io-channels = <&adc 2>;  /* Battery temperature */
	};
};

&adc {
	vdd-supply = <&ldo3_reg>;
	status = "okay";
};

&bus_dmc {
	devfreq-events = <&ppmu_dmc0_3>, <&ppmu_dmc1_3>;
	vdd-supply = <&buck1_reg>;
	status = "okay";
};

&bus_acp {
	devfreq = <&bus_dmc>;
	status = "okay";
};

&bus_c2c {
	devfreq = <&bus_dmc>;
	status = "okay";
};

&bus_leftbus {
	devfreq-events = <&ppmu_leftbus_3>, <&ppmu_rightbus_3>;
	vdd-supply = <&buck3_reg>;
	status = "okay";
};

&bus_rightbus {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_display {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_fsys {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_peri {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&bus_mfc {
	devfreq = <&bus_leftbus>;
	status = "okay";
};

&camera {
	pinctrl-0 = <&cam_port_a_clk_active &cam_port_b_clk_active>;
	pinctrl-names = "default";
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_CAM0>,
		<&clock CLK_MOUT_CAM1>;
	assigned-clock-parents = <&clock CLK_XUSBXTI>,
		<&clock CLK_XUSBXTI>;
};

&cpu0 {
	cpu0-supply = <&buck2_reg>;
};

&cpu_thermal {
	cooling-maps {
		map0 {
			/* Corresponds to 800MHz at freq_table */
			cooling-device = <&cpu0 7 7>, <&cpu1 7 7>,
					 <&cpu2 7 7>, <&cpu3 7 7>;
		};
		map1 {
			/* Corresponds to 200MHz at freq_table */
			cooling-device = <&cpu0 13 13>, <&cpu1 13 13>,
					 <&cpu2 13 13>, <&cpu3 13 13>;
		};
	};
};

&csis_0 {
	status = "okay";
	vddcore-supply = <&ldo8_reg>;
	vddio-supply = <&ldo10_reg>;
	assigned-clocks = <&clock CLK_MOUT_CSIS0>,
			<&clock CLK_SCLK_CSIS0>;
	assigned-clock-parents = <&clock CLK_MOUT_MPLL_USER_T>;
	assigned-clock-rates = <0>, <176000000>;

	/* Camera C (3) MIPI CSI-2 (CSIS0) */
	port@3 {
		reg = <3>;
		csis0_ep: endpoint {
			remote-endpoint = <&s5c73m3_ep>;
			data-lanes = <1 2 3 4>;
			samsung,csis-hs-settle = <12>;
		};
	};
};

&csis_1 {
	status = "okay";
	vddcore-supply = <&ldo8_reg>;
	vddio-supply = <&ldo10_reg>;
	assigned-clocks = <&clock CLK_MOUT_CSIS1>,
			<&clock CLK_SCLK_CSIS1>;
	assigned-clock-parents = <&clock CLK_MOUT_MPLL_USER_T>;
	assigned-clock-rates = <0>, <176000000>;

	/* Camera D (4) MIPI CSI-2 (CSIS1) */
	port@4 {
		reg = <4>;
		csis1_ep: endpoint {
			remote-endpoint = <&is_s5k6a3_ep>;
			data-lanes = <1>;
			samsung,csis-hs-settle = <18>;
			samsung,csis-wclk;
		};
	};
};

&dsi_0 {
	vddcore-supply = <&ldo8_reg>;
	vddio-supply = <&ldo10_reg>;
	samsung,burst-clock-frequency = <500000000>;
	samsung,esc-clock-frequency = <20000000>;
	samsung,pll-clock-frequency = <24000000>;
};

&exynos_usbphy {
	vbus-supply = <&esafeout1_reg>;
	status = "okay";
};

&fimc_0 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC0>,
			<&clock CLK_SCLK_FIMC0>;
	assigned-clock-parents = <&clock CLK_MOUT_MPLL_USER_T>;
	assigned-clock-rates = <0>, <176000000>;
};

&fimc_1 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC1>,
			<&clock CLK_SCLK_FIMC1>;
	assigned-clock-parents = <&clock CLK_MOUT_MPLL_USER_T>;
	assigned-clock-rates = <0>, <176000000>;
};

&fimc_2 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC2>,
			<&clock CLK_SCLK_FIMC2>;
	assigned-clock-parents = <&clock CLK_MOUT_MPLL_USER_T>;
	assigned-clock-rates = <0>, <176000000>;
};

&fimc_3 {
	status = "okay";
	assigned-clocks = <&clock CLK_MOUT_FIMC3>,
			<&clock CLK_SCLK_FIMC3>;
	assigned-clock-parents = <&clock CLK_MOUT_MPLL_USER_T>;
	assigned-clock-rates = <0>, <176000000>;
};

&fimc_is {
	pinctrl-0 = <&fimc_is_uart>;
	pinctrl-names = "default";
	status = "okay";
};

&fimc_lite_0 {
	status = "okay";
};

&fimc_lite_1 {
	status = "okay";
};

&fimd {
	status = "okay";
};

&gpu {
	mali-supply = <&buck4_reg>;
	status = "okay";
};

&hdmi {
	hpd-gpios = <&gpx3 7 GPIO_ACTIVE_HIGH>;
	pinctrl-names = "default";
	pinctrl-0 = <&hdmi_hpd>;
	vdd-supply = <&ldo3_reg>;
	vdd_osc-supply = <&ldo4_reg>;
	vdd_pll-supply = <&ldo3_reg>;
	ddc = <&i2c_5>;
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@1 {
			reg = <1>;
			hdmi_to_mhl: endpoint {
				remote-endpoint = <&mhl_to_hdmi>;
			};
		};
	};
};

&hsotg {
	vusb_d-supply = <&ldo15_reg>;
	vusb_a-supply = <&ldo12_reg>;
	dr_mode = "otg";
	role-switch-default-mode = "peripheral";
	usb-role-switch;
	status = "okay";

	port {
		usb_to_muic: endpoint {
			remote-endpoint = <&muic_to_usb>;
		};
	};
};

&i2c_0 {
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-slave-addr = <0x10>;
	samsung,i2c-max-bus-freq = <400000>;
	pinctrl-0 = <&i2c0_bus>;
	pinctrl-names = "default";
	status = "okay";

	s5c73m3: image-sensor@3c {
		compatible = "samsung,s5c73m3";
		reg = <0x3c>;
		xshutdown-gpios = <&gpf1 3 GPIO_ACTIVE_LOW>; /* ISP_RESET */
		vdd-int-supply = <&buck9_reg>;
		vddio-cis-supply = <&ldo9_reg>;
		vddio-host-supply = <&ldo18_reg>;
		vdd-af-supply = <&cam_af_reg>;
		vdd-reg-supply = <&cam_io_reg>;
		clock-frequency = <24000000>;
		/* CAM_A_CLKOUT */
		clocks = <&camera 0>;
		clock-names = "cis_extclk";
		status = "disabled";
		port {
			s5c73m3_ep: endpoint {
				remote-endpoint = <&csis0_ep>;
				data-lanes = <1 2 3 4>;
			};
		};
	};
};

&i2c1_isp {
	pinctrl-0 = <&fimc_is_i2c1>;
	pinctrl-names = "default";

	image-sensor@10 {
		compatible = "samsung,s5k6a3";
		reg = <0x10>;
		svdda-supply = <&cam_io_reg>;
		svddio-supply = <&ldo19_reg>;
		afvdd-supply = <&ldo19_reg>;
		clock-frequency = <24000000>;
		/* CAM_B_CLKOUT */
		clocks = <&camera 1>;
		clock-names = "extclk";
		gpios = <&gpm1 6 GPIO_ACTIVE_LOW>;

		port {
			is_s5k6a3_ep: endpoint {
				remote-endpoint = <&csis1_ep>;
				data-lanes = <1>;
			};
		};
	};
};

&i2c_3 {
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-slave-addr = <0x10>;
	samsung,i2c-max-bus-freq = <400000>;
	pinctrl-0 = <&i2c3_bus>;
	pinctrl-names = "default";
	status = "okay";
};

&i2c_4 {
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-slave-addr = <0x10>;
	samsung,i2c-max-bus-freq = <100000>;
	pinctrl-0 = <&i2c4_bus>;
	pinctrl-names = "default";
	status = "okay";

	wm1811: audio-codec@1a {
		compatible = "wlf,wm1811";
		reg = <0x1a>;
		clocks = <&pmu_system_controller 0>,
			<&max77686 MAX77686_CLK_PMIC>;
		clock-names = "MCLK1", "MCLK2";
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupt-parent = <&gpx3>;
		interrupts = <6 IRQ_TYPE_LEVEL_HIGH>;

		gpio-controller;
		#gpio-cells = <2>;
		#sound-dai-cells = <0>;

		wlf,gpio-cfg = <0x3 0x0 0x0 0x0 0x0 0x0
			0x0 0x8000 0x0 0x0 0x0>;
		wlf,micbias-cfg = <0x2f 0x2b>;

		wlf,lineout1-feedback;
		wlf,lineout1-se;
		wlf,lineout2-se;
		wlf,ldoena-always-driven;

		AVDD2-supply = <&vbatt_reg>;
		DBVDD1-supply = <&ldo3_reg>;
		DBVDD2-supply = <&vbatt_reg>;
		DBVDD3-supply = <&vbatt_reg>;
		DCVDD-supply = <&ldo3_reg>;
		CPVDD-supply = <&vbatt_reg>;
		SPKVDD1-supply = <&vbatt_reg>;
		SPKVDD2-supply = <&vbatt_reg>;
		wlf,ldo1ena-gpios = <&gpj0 4 GPIO_ACTIVE_HIGH>;
		wlf,ldo2ena-gpios = <&gpj0 4 GPIO_ACTIVE_HIGH>;
	};
};

&i2c_5 {
	status = "okay";
};

&i2c_7 {
	samsung,i2c-sda-delay = <100>;
	samsung,i2c-slave-addr = <0x10>;
	samsung,i2c-max-bus-freq = <100000>;
	pinctrl-0 = <&i2c7_bus>;
	pinctrl-names = "default";
	status = "okay";

	max77686: pmic@9 {
		compatible = "maxim,max77686";
		interrupt-parent = <&gpx0>;
		interrupts = <7 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-0 = <&max77686_irq>;
		pinctrl-names = "default";
		wakeup-source;
		reg = <0x09>;
		#clock-cells = <1>;

		voltage-regulators {
			ldo1_reg: LDO1 {
				regulator-name = "VALIVE_1.0V_AP";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo2_reg: LDO2 {
				regulator-name = "VM1M2_1.2V_AP";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo3_reg: LDO3 {
				regulator-name = "VCC_1.8V_AP";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo4_reg: LDO4 {
				regulator-name = "VCC_2.8V_AP";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
			};

			ldo5_reg: LDO5 {
				regulator-name = "VCC_1.8V_IO";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo6_reg: LDO6 {
				regulator-name = "VMPLL_1.0V_AP";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo7_reg: LDO7 {
				regulator-name = "VPLL_1.0V_AP";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo8_reg: LDO8 {
				regulator-name = "VMIPI_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo9_reg: LDO9 {
				regulator-name = "CAM_ISP_MIPI_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
			};

			ldo10_reg: LDO10 {
				regulator-name = "VMIPI_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo11_reg: LDO11 {
				regulator-name = "VABB1_1.95V";
				regulator-min-microvolt = <1950000>;
				regulator-max-microvolt = <1950000>;
				regulator-always-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo12_reg: LDO12 {
				regulator-name = "VUOTG_3.0V";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo13_reg: LDO13 {
				regulator-name = "NFC_AVDD_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo14_reg: LDO14 {
				regulator-name = "VABB2_1.95V";
				regulator-min-microvolt = <1950000>;
				regulator-max-microvolt = <1950000>;
				regulator-always-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			ldo15_reg: LDO15 {
				regulator-name = "VHSIC_1.0V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo16_reg: LDO16 {
				regulator-name = "VHSIC_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			ldo17_reg: LDO17 {
				regulator-name = "CAM_SENSOR_CORE_1.2V";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
			};

			ldo18_reg: LDO18 {
				regulator-name = "CAM_ISP_SEN_IO_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo19_reg: LDO19 {
				regulator-name = "VT_CAM_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo20_reg: LDO20 {
				regulator-name = "VDDQ_PRE_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo21_reg: LDO21 {
				regulator-name = "VTF_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				maxim,ena-gpios = <&gpy2 0 GPIO_ACTIVE_HIGH>;
			};

			ldo22_reg: LDO22 {
				regulator-name = "VMEM_VDD_2.8V";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				maxim,ena-gpios = <&gpk0 2 GPIO_ACTIVE_HIGH>;
			};

			ldo23_reg: LDO23 {
				regulator-name = "TSP_AVDD_3.3V";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
			};

			ldo24_reg: LDO24 {
				regulator-name = "TSP_VDD_1.8V";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo25_reg: LDO25 {
				regulator-name = "LDO25";
			};

			ldo26_reg: LDO26 {
				regulator-name = "MOTOR_VCC_3.0V";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
			};

			buck1_reg: BUCK1 {
				regulator-name = "VDD_MIF";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck2_reg: BUCK2 {
				regulator-name = "VDD_ARM";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			buck3_reg: BUCK3 {
				regulator-name = "VDD_INT";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1150000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck4_reg: BUCK4 {
				regulator-name = "VDD_G3D";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1150000>;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			buck5_reg: BUCK5 {
				regulator-name = "VMEM_1.2V_AP";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			buck6_reg: BUCK6 {
				regulator-name = "VCC_SUB_1.35V";
				regulator-min-microvolt = <1350000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
			};

			buck7_reg: BUCK7 {
				regulator-name = "VCC_SUB_2.0V";
				regulator-min-microvolt = <2000000>;
				regulator-max-microvolt = <2000000>;
				regulator-always-on;
			};

			buck8_reg: BUCK8 {
				regulator-name = "VMEM_VDDF_3.0V";
				regulator-min-microvolt = <2850000>;
				regulator-max-microvolt = <2850000>;
				maxim,ena-gpios = <&gpk0 2 GPIO_ACTIVE_HIGH>;
			};

			buck9_reg: BUCK9 {
				regulator-name = "CAM_ISP_CORE_1.2V";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1200000>;
			};
		};
	};
};

&i2c_8 {
	status = "okay";
};

&i2s0 {
	pinctrl-0 = <&i2s0_bus>;
	pinctrl-names = "default";
	status = "okay";
};

&mixer {
	status = "okay";
};

&mshc_0 {
	broken-cd;
	non-removable;
	card-detect-delay = <200>;
	vmmc-supply = <&ldo22_reg>;
	clock-frequency = <400000000>;
	samsung,dw-mshc-ciu-div = <0>;
	samsung,dw-mshc-sdr-timing = <2 3>;
	samsung,dw-mshc-ddr-timing = <1 2>;
	mmc-ddr-1_8v;
	pinctrl-0 = <&sd4_clk &sd4_cmd &sd4_bus4 &sd4_bus8>;
	pinctrl-names = "default";
	status = "okay";
	bus-width = <8>;
	cap-mmc-highspeed;
};

&pmu_system_controller {
	assigned-clocks = <&pmu_system_controller 0>;
	assigned-clock-parents = <&clock CLK_XUSBXTI>;
};

&pinctrl_0 {
	pinctrl-names = "default";
	pinctrl-0 = <&sleep0>;

	mhl_int: mhl-int-pins {
		samsung,pins = "gpf3-5";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	i2c_mhl_bus: i2c-mhl-bus-pins {
		samsung,pins = "gpf0-4", "gpf0-6";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_DOWN>;
		samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
	};

	sleep0: sleep-state {
		PIN_SLP(gpa0-0, INPUT, NONE);
		PIN_SLP(gpa0-1, OUT0, NONE);
		PIN_SLP(gpa0-2, INPUT, NONE);
		PIN_SLP(gpa0-3, INPUT, UP);
		PIN_SLP(gpa0-4, INPUT, NONE);
		PIN_SLP(gpa0-5, INPUT, DOWN);
		PIN_SLP(gpa0-6, INPUT, DOWN);
		PIN_SLP(gpa0-7, INPUT, UP);

		PIN_SLP(gpa1-0, INPUT, DOWN);
		PIN_SLP(gpa1-1, INPUT, DOWN);
		PIN_SLP(gpa1-2, INPUT, DOWN);
		PIN_SLP(gpa1-3, INPUT, DOWN);
		PIN_SLP(gpa1-4, INPUT, DOWN);
		PIN_SLP(gpa1-5, INPUT, DOWN);

		PIN_SLP(gpb-0, INPUT, NONE);
		PIN_SLP(gpb-1, INPUT, NONE);
		PIN_SLP(gpb-2, INPUT, NONE);
		PIN_SLP(gpb-3, INPUT, NONE);
		PIN_SLP(gpb-4, INPUT, DOWN);
		PIN_SLP(gpb-5, INPUT, UP);
		PIN_SLP(gpb-6, INPUT, DOWN);
		PIN_SLP(gpb-7, INPUT, DOWN);

		PIN_SLP(gpc0-0, INPUT, DOWN);
		PIN_SLP(gpc0-1, INPUT, DOWN);
		PIN_SLP(gpc0-2, INPUT, DOWN);
		PIN_SLP(gpc0-3, INPUT, DOWN);
		PIN_SLP(gpc0-4, INPUT, DOWN);

		PIN_SLP(gpc1-0, INPUT, NONE);
		PIN_SLP(gpc1-1, PREV, NONE);
		PIN_SLP(gpc1-2, INPUT, NONE);
		PIN_SLP(gpc1-3, INPUT, NONE);
		PIN_SLP(gpc1-4, INPUT, NONE);

		PIN_SLP(gpd0-0, INPUT, DOWN);
		PIN_SLP(gpd0-1, INPUT, DOWN);
		PIN_SLP(gpd0-2, INPUT, NONE);
		PIN_SLP(gpd0-3, INPUT, NONE);

		PIN_SLP(gpd1-0, INPUT, DOWN);
		PIN_SLP(gpd1-1, INPUT, DOWN);
		PIN_SLP(gpd1-2, INPUT, NONE);
		PIN_SLP(gpd1-3, INPUT, NONE);

		PIN_SLP(gpf0-0, INPUT, NONE);
		PIN_SLP(gpf0-1, INPUT, NONE);
		PIN_SLP(gpf0-2, INPUT, DOWN);
		PIN_SLP(gpf0-3, INPUT, DOWN);
		PIN_SLP(gpf0-4, INPUT, NONE);
		PIN_SLP(gpf0-5, INPUT, DOWN);
		PIN_SLP(gpf0-6, INPUT, NONE);
		PIN_SLP(gpf0-7, INPUT, DOWN);

		PIN_SLP(gpf1-0, INPUT, DOWN);
		PIN_SLP(gpf1-1, INPUT, DOWN);
		PIN_SLP(gpf1-2, INPUT, DOWN);
		PIN_SLP(gpf1-3, INPUT, DOWN);
		PIN_SLP(gpf1-4, INPUT, NONE);
		PIN_SLP(gpf1-5, INPUT, NONE);
		PIN_SLP(gpf1-6, INPUT, DOWN);
		PIN_SLP(gpf1-7, PREV, NONE);

		PIN_SLP(gpf2-0, PREV, NONE);
		PIN_SLP(gpf2-1, INPUT, DOWN);
		PIN_SLP(gpf2-2, INPUT, DOWN);
		PIN_SLP(gpf2-3, INPUT, DOWN);
		PIN_SLP(gpf2-4, INPUT, DOWN);
		PIN_SLP(gpf2-5, INPUT, DOWN);
		PIN_SLP(gpf2-6, INPUT, NONE);
		PIN_SLP(gpf2-7, INPUT, NONE);

		PIN_SLP(gpf3-0, INPUT, NONE);
		PIN_SLP(gpf3-1, PREV, NONE);
		PIN_SLP(gpf3-2, PREV, NONE);
		PIN_SLP(gpf3-3, PREV, NONE);
		PIN_SLP(gpf3-4, OUT1, NONE);
		PIN_SLP(gpf3-5, INPUT, DOWN);

		PIN_SLP(gpj0-0, PREV, NONE);
		PIN_SLP(gpj0-1, PREV, NONE);
		PIN_SLP(gpj0-2, PREV, NONE);
		PIN_SLP(gpj0-3, INPUT, DOWN);
		PIN_SLP(gpj0-4, PREV, NONE);
		PIN_SLP(gpj0-5, PREV, NONE);
		PIN_SLP(gpj0-6, INPUT, DOWN);
		PIN_SLP(gpj0-7, INPUT, DOWN);

		PIN_SLP(gpj1-0, INPUT, DOWN);
		PIN_SLP(gpj1-1, PREV, NONE);
		PIN_SLP(gpj1-2, PREV, NONE);
		PIN_SLP(gpj1-3, INPUT, DOWN);
		PIN_SLP(gpj1-4, INPUT, DOWN);
	};
};

&pinctrl_1 {
	pinctrl-names = "default";
	pinctrl-0 = <&sleep1>;

	gpio_keys: gpio-keys-pins {
		samsung,pins = "gpx0-1", "gpx2-2", "gpx2-7", "gpx3-3";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	bt_shutdown: bt-shutdown-pins {
		samsung,pins = "gpl0-6";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	bt_host_wakeup: bt-host-wakeup-pins {
		samsung,pins = "gpx2-6";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	bt_device_wakeup: bt-device-wakeup-pins {
		samsung,pins = "gpx3-1";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	max77686_irq: max77686-irq-pins {
		samsung,pins = "gpx0-7";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	max77693_irq: max77693-irq-pins {
		samsung,pins = "gpx1-5";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	max77693_fuel_irq: max77693-fuel-irq-pins {
		samsung,pins = "gpx2-3";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	sdhci2_cd: sdhci2-cd-irq-pins {
		samsung,pins = "gpx3-4";
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
	};

	hdmi_hpd: hdmi-hpd-pins {
		samsung,pins = "gpx3-7";
		samsung,pin-pud = <EXYNOS_PIN_PULL_DOWN>;
	};

	sleep1: sleep-state {
		PIN_SLP(gpk0-0, PREV, NONE);
		PIN_SLP(gpk0-1, PREV, NONE);
		PIN_SLP(gpk0-2, OUT0, NONE);
		PIN_SLP(gpk0-3, PREV, NONE);
		PIN_SLP(gpk0-4, PREV, NONE);
		PIN_SLP(gpk0-5, PREV, NONE);
		PIN_SLP(gpk0-6, PREV, NONE);

		PIN_SLP(gpk1-0, INPUT, DOWN);
		PIN_SLP(gpk1-1, INPUT, DOWN);
		PIN_SLP(gpk1-2, INPUT, DOWN);
		PIN_SLP(gpk1-3, PREV, NONE);
		PIN_SLP(gpk1-4, PREV, NONE);
		PIN_SLP(gpk1-5, PREV, NONE);
		PIN_SLP(gpk1-6, PREV, NONE);

		PIN_SLP(gpk2-0, INPUT, DOWN);
		PIN_SLP(gpk2-1, INPUT, DOWN);
		PIN_SLP(gpk2-2, INPUT, DOWN);
		PIN_SLP(gpk2-3, INPUT, DOWN);
		PIN_SLP(gpk2-4, INPUT, DOWN);
		PIN_SLP(gpk2-5, INPUT, DOWN);
		PIN_SLP(gpk2-6, INPUT, DOWN);

		PIN_SLP(gpk3-0, OUT0, NONE);
		PIN_SLP(gpk3-1, INPUT, NONE);
		PIN_SLP(gpk3-2, INPUT, DOWN);
		PIN_SLP(gpk3-3, INPUT, NONE);
		PIN_SLP(gpk3-4, INPUT, NONE);
		PIN_SLP(gpk3-5, INPUT, NONE);
		PIN_SLP(gpk3-6, INPUT, NONE);

		PIN_SLP(gpl0-0, INPUT, DOWN);
		PIN_SLP(gpl0-1, INPUT, DOWN);
		PIN_SLP(gpl0-2, INPUT, DOWN);
		PIN_SLP(gpl0-3, INPUT, DOWN);
		PIN_SLP(gpl0-4, PREV, NONE);
		PIN_SLP(gpl0-6, PREV, NONE);

		PIN_SLP(gpl1-0, INPUT, DOWN);
		PIN_SLP(gpl1-1, INPUT, DOWN);
		PIN_SLP(gpl2-0, INPUT, DOWN);
		PIN_SLP(gpl2-1, INPUT, DOWN);
		PIN_SLP(gpl2-2, INPUT, DOWN);
		PIN_SLP(gpl2-3, INPUT, DOWN);
		PIN_SLP(gpl2-4, INPUT, DOWN);
		PIN_SLP(gpl2-5, INPUT, DOWN);
		PIN_SLP(gpl2-6, PREV, NONE);
		PIN_SLP(gpl2-7, INPUT, DOWN);

		PIN_SLP(gpm0-0, INPUT, DOWN);
		PIN_SLP(gpm0-1, INPUT, DOWN);
		PIN_SLP(gpm0-2, INPUT, DOWN);
		PIN_SLP(gpm0-3, INPUT, DOWN);
		PIN_SLP(gpm0-4, INPUT, DOWN);
		PIN_SLP(gpm0-5, INPUT, DOWN);
		PIN_SLP(gpm0-6, INPUT, DOWN);
		PIN_SLP(gpm0-7, INPUT, DOWN);

		PIN_SLP(gpm1-0, INPUT, DOWN);
		PIN_SLP(gpm1-1, INPUT, DOWN);
		PIN_SLP(gpm1-2, INPUT, NONE);
		PIN_SLP(gpm1-3, INPUT, NONE);
		PIN_SLP(gpm1-4, INPUT, NONE);
		PIN_SLP(gpm1-5, INPUT, NONE);
		PIN_SLP(gpm1-6, INPUT, DOWN);

		PIN_SLP(gpm2-0, INPUT, NONE);
		PIN_SLP(gpm2-1, INPUT, NONE);
		PIN_SLP(gpm2-2, INPUT, DOWN);
		PIN_SLP(gpm2-3, INPUT, DOWN);
		PIN_SLP(gpm2-4, INPUT, DOWN);

		PIN_SLP(gpm3-0, PREV, NONE);
		PIN_SLP(gpm3-1, PREV, NONE);
		PIN_SLP(gpm3-2, PREV, NONE);
		PIN_SLP(gpm3-3, OUT1, NONE);
		PIN_SLP(gpm3-4, INPUT, DOWN);
		PIN_SLP(gpm3-5, INPUT, DOWN);
		PIN_SLP(gpm3-6, INPUT, DOWN);
		PIN_SLP(gpm3-7, INPUT, DOWN);

		PIN_SLP(gpm4-0, INPUT, DOWN);
		PIN_SLP(gpm4-1, INPUT, DOWN);
		PIN_SLP(gpm4-2, INPUT, DOWN);
		PIN_SLP(gpm4-3, INPUT, DOWN);
		PIN_SLP(gpm4-4, INPUT, DOWN);
		PIN_SLP(gpm4-5, INPUT, DOWN);
		PIN_SLP(gpm4-6, INPUT, DOWN);
		PIN_SLP(gpm4-7, INPUT, DOWN);

		PIN_SLP(gpy0-0, INPUT, DOWN);
		PIN_SLP(gpy0-1, INPUT, DOWN);
		PIN_SLP(gpy0-2, INPUT, DOWN);
		PIN_SLP(gpy0-3, INPUT, DOWN);
		PIN_SLP(gpy0-4, INPUT, DOWN);
		PIN_SLP(gpy0-5, INPUT, DOWN);

		PIN_SLP(gpy1-0, INPUT, DOWN);
		PIN_SLP(gpy1-1, INPUT, DOWN);
		PIN_SLP(gpy1-2, INPUT, DOWN);
		PIN_SLP(gpy1-3, INPUT, DOWN);

		PIN_SLP(gpy2-0, PREV, NONE);
		PIN_SLP(gpy2-1, INPUT, DOWN);
		PIN_SLP(gpy2-2, INPUT, NONE);
		PIN_SLP(gpy2-3, INPUT, NONE);
		PIN_SLP(gpy2-4, INPUT, NONE);
		PIN_SLP(gpy2-5, INPUT, NONE);

		PIN_SLP(gpy3-0, INPUT, DOWN);
		PIN_SLP(gpy3-1, INPUT, DOWN);
		PIN_SLP(gpy3-2, INPUT, DOWN);
		PIN_SLP(gpy3-3, INPUT, DOWN);
		PIN_SLP(gpy3-4, INPUT, DOWN);
		PIN_SLP(gpy3-5, INPUT, DOWN);
		PIN_SLP(gpy3-6, INPUT, DOWN);
		PIN_SLP(gpy3-7, INPUT, DOWN);

		PIN_SLP(gpy4-0, INPUT, DOWN);
		PIN_SLP(gpy4-1, INPUT, DOWN);
		PIN_SLP(gpy4-2, INPUT, DOWN);
		PIN_SLP(gpy4-3, INPUT, DOWN);
		PIN_SLP(gpy4-4, INPUT, DOWN);
		PIN_SLP(gpy4-5, INPUT, DOWN);
		PIN_SLP(gpy4-6, INPUT, DOWN);
		PIN_SLP(gpy4-7, INPUT, DOWN);

		PIN_SLP(gpy5-0, INPUT, DOWN);
		PIN_SLP(gpy5-1, INPUT, DOWN);
		PIN_SLP(gpy5-2, INPUT, DOWN);
		PIN_SLP(gpy5-3, INPUT, DOWN);
		PIN_SLP(gpy5-4, INPUT, DOWN);
		PIN_SLP(gpy5-5, INPUT, DOWN);
		PIN_SLP(gpy5-6, INPUT, DOWN);
		PIN_SLP(gpy5-7, INPUT, DOWN);

		PIN_SLP(gpy6-0, INPUT, DOWN);
		PIN_SLP(gpy6-1, INPUT, DOWN);
		PIN_SLP(gpy6-2, INPUT, DOWN);
		PIN_SLP(gpy6-3, INPUT, DOWN);
		PIN_SLP(gpy6-4, INPUT, DOWN);
		PIN_SLP(gpy6-5, INPUT, DOWN);
		PIN_SLP(gpy6-6, INPUT, DOWN);
		PIN_SLP(gpy6-7, INPUT, DOWN);
	};
};

&pinctrl_2 {
	pinctrl-names = "default";
	pinctrl-0 = <&sleep2>;

	sleep2: sleep-state {
		PIN_SLP(gpz-0, INPUT, DOWN);
		PIN_SLP(gpz-1, INPUT, DOWN);
		PIN_SLP(gpz-2, INPUT, DOWN);
		PIN_SLP(gpz-3, INPUT, DOWN);
		PIN_SLP(gpz-4, INPUT, DOWN);
		PIN_SLP(gpz-5, INPUT, DOWN);
		PIN_SLP(gpz-6, INPUT, DOWN);
	};
};

&pinctrl_3 {
	pinctrl-names = "default";
	pinctrl-0 = <&sleep3>;

	sleep3: sleep-state {
		PIN_SLP(gpv0-0, INPUT, DOWN);
		PIN_SLP(gpv0-1, INPUT, DOWN);
		PIN_SLP(gpv0-2, INPUT, DOWN);
		PIN_SLP(gpv0-3, INPUT, DOWN);
		PIN_SLP(gpv0-4, INPUT, DOWN);
		PIN_SLP(gpv0-5, INPUT, DOWN);
		PIN_SLP(gpv0-6, INPUT, DOWN);
		PIN_SLP(gpv0-7, INPUT, DOWN);

		PIN_SLP(gpv1-0, INPUT, DOWN);
		PIN_SLP(gpv1-1, INPUT, DOWN);
		PIN_SLP(gpv1-2, INPUT, DOWN);
		PIN_SLP(gpv1-3, INPUT, DOWN);
		PIN_SLP(gpv1-4, INPUT, DOWN);
		PIN_SLP(gpv1-5, INPUT, DOWN);
		PIN_SLP(gpv1-6, INPUT, DOWN);
		PIN_SLP(gpv1-7, INPUT, DOWN);

		PIN_SLP(gpv2-0, INPUT, DOWN);
		PIN_SLP(gpv2-1, INPUT, DOWN);
		PIN_SLP(gpv2-2, INPUT, DOWN);
		PIN_SLP(gpv2-3, INPUT, DOWN);
		PIN_SLP(gpv2-4, INPUT, DOWN);
		PIN_SLP(gpv2-5, INPUT, DOWN);
		PIN_SLP(gpv2-6, INPUT, DOWN);
		PIN_SLP(gpv2-7, INPUT, DOWN);

		PIN_SLP(gpv3-0, INPUT, DOWN);
		PIN_SLP(gpv3-1, INPUT, DOWN);
		PIN_SLP(gpv3-2, INPUT, DOWN);
		PIN_SLP(gpv3-3, INPUT, DOWN);
		PIN_SLP(gpv3-4, INPUT, DOWN);
		PIN_SLP(gpv3-5, INPUT, DOWN);
		PIN_SLP(gpv3-6, INPUT, DOWN);
		PIN_SLP(gpv3-7, INPUT, DOWN);

		PIN_SLP(gpv4-0, INPUT, DOWN);
	};
};

&pwm {
	pinctrl-0 = <&pwm0_out>;
	pinctrl-names = "default";
	samsung,pwm-outputs = <0>;
	status = "okay";
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&max77686 MAX77686_CLK_AP>;
	clock-names = "rtc", "rtc_src";
};

&sdhci_2 {
	bus-width = <4>;
	cd-gpios = <&gpx3 4 GPIO_ACTIVE_LOW>;
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_bus4 &sdhci2_cd>;
	pinctrl-names = "default";
	vmmc-supply = <&ldo21_reg>;
	status = "okay";
};

&sdhci_3 {
	#address-cells = <1>;
	#size-cells = <0>;
	non-removable;
	bus-width = <4>;

	mmc-pwrseq = <&wlan_pwrseq>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd3_clk &sd3_cmd &sd3_bus4>;
	status = "okay";

	brcmf: wifi@1 {
		reg = <1>;
		compatible = "brcm,bcm4329-fmac";
		interrupt-parent = <&gpx2>;
		interrupts = <5 IRQ_TYPE_NONE>;
		interrupt-names = "host-wake";
	};
};

&serial_0 {
	pinctrl-0 = <&uart0_data &uart0_fctl>;
	pinctrl-names = "default";
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm4330-bt";
		pinctrl-0 = <&bt_shutdown &bt_device_wakeup &bt_host_wakeup>;
		pinctrl-names = "default";
		max-speed = <3000000>;
		shutdown-gpios = <&gpl0 6 GPIO_ACTIVE_HIGH>;
		device-wakeup-gpios = <&gpx3 1 GPIO_ACTIVE_HIGH>;
		host-wakeup-gpios = <&gpx2 6 GPIO_ACTIVE_HIGH>;
		clocks = <&max77686 MAX77686_CLK_PMIC>;
	};
};

&serial_1 {
	status = "okay";
};

&serial_2 {
	status = "okay";
};

&serial_3 {
	status = "okay";
};

&spi_1 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi1_bus>;
	cs-gpios = <&gpb 5 GPIO_ACTIVE_HIGH>;
	status = "okay";

	s5c73m3_spi: image-sensor@0 {
		compatible = "samsung,s5c73m3";
		spi-max-frequency = <50000000>;
		reg = <0>;
		controller-data {
			samsung,spi-feedback-delay = <2>;
		};
	};
};

&tmu {
	vtmu-supply = <&ldo10_reg>;
	status = "okay";
};
