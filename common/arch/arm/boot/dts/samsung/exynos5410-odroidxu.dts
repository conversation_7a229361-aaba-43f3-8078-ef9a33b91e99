// SPDX-License-Identifier: GPL-2.0
/*
 * Hardkernel Odroid XU board device tree source
 *
 * Copyright (c) 2013 Samsung Electronics Co., Ltd.
 *		http://www.samsung.com
 * Copyright (c) 2016 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

/dts-v1/;
#include "exynos5410.dtsi"
#include <dt-bindings/clock/maxim,max77802.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/sound/samsung-i2s.h>
#include "exynos54xx-odroidxu-leds.dtsi"

/ {
	model = "Hardkernel Odroid XU";
	compatible = "hardkernel,odroid-xu", "samsung,exynos5410", "samsung,exynos5";

	aliases {
		ethernet = &ethernet;
		mmc0 = &mmc_0;
		mmc1 = &mmc_2;
	};

	memory@40000000 {
		device_type = "memory";
		reg = <0x40000000 0x7ea00000>;
	};

	chosen {
		stdout-path = "serial2:115200n8";
	};

	emmc_pwrseq: pwrseq {
		pinctrl-0 = <&emmc_nrst_pin>;
		pinctrl-names = "default";
		compatible = "mmc-pwrseq-emmc";
		reset-gpios = <&gpd1 0 GPIO_ACTIVE_LOW>;
	};

	fan0: pwm-fan {
		compatible = "pwm-fan";
		pwms = <&pwm 0 20972 0>;
		#cooling-cells = <2>;
		cooling-levels = <0 130 170 230>;
	};

	fin_pll: xxti {
		compatible = "fixed-clock";
		clock-frequency = <********>;
		clock-output-names = "fin_pll";
		#clock-cells = <0>;
	};

	firmware@2073000 {
		compatible = "samsung,secure-firmware";
		reg = <0x02073000 0x1000>;
	};

	sound: sound {
		compatible = "simple-audio-card";

		simple-audio-card,name = "Odroid-XU";
		simple-audio-card,widgets =
			"Headphone", "Headphone Jack",
			"Speakers", "Speakers";
		simple-audio-card,routing =
			"Headphone Jack", "HPL",
			"Headphone Jack", "HPR",
			"Headphone Jack", "MICBIAS",
			"IN1", "Headphone Jack",
			"Speakers", "SPKL",
			"Speakers", "SPKR";

		simple-audio-card,format = "i2s";
		simple-audio-card,bitclock-master = <&link0_codec>;
		simple-audio-card,frame-master = <&link0_codec>;

		simple-audio-card,cpu {
			sound-dai = <&audi2s0 0>;
			system-clock-frequency = <********>;
		};

		link0_codec: simple-audio-card,codec {
			sound-dai = <&max98090>;
			clocks = <&audi2s0 CLK_I2S_CDCLK>;
		};
	};
};

&adc {
	vdd-supply = <&ldo10_reg>;
	status = "okay";
};

&audi2s0 {
	status = "okay";
};

&clock {
	clocks = <&fin_pll>;
	assigned-clocks = <&clock CLK_FOUT_EPLL>;
	assigned-clock-rates = <*********>;
};

&clock_audss {
	assigned-clocks = <&clock_audss EXYNOS_MOUT_AUDSS>,
			<&clock_audss EXYNOS_MOUT_I2S>,
			<&clock_audss EXYNOS_DOUT_SRP>,
			<&clock_audss EXYNOS_DOUT_AUD_BUS>;

	assigned-clock-parents = <&clock CLK_FOUT_EPLL>,
			<&clock_audss EXYNOS_MOUT_AUDSS>;

	assigned-clock-rates = <0>,
			       <0>,
			       <96000000>,
			       <********>;
};

&cpu0_thermal {
	polling-delay-passive = <0>;
	polling-delay = <0>;

	trips {
		cpu_alert0: cpu-alert-0 {
			temperature = <50000>; /* millicelsius */
			hysteresis = <5000>; /* millicelsius */
			type = "active";
		};
		cpu_alert1: cpu-alert-1 {
			temperature = <60000>; /* millicelsius */
			hysteresis = <5000>; /* millicelsius */
			type = "active";
		};
		cpu_alert2: cpu-alert-2 {
			temperature = <70000>; /* millicelsius */
			hysteresis = <5000>; /* millicelsius */
			type = "active";
		};
		cpu_crit0: cpu-crit-0 {
			temperature = <120000>; /* millicelsius */
			hysteresis = <0>; /* millicelsius */
			type = "critical";
		};
	};

	cooling-maps {
		map0 {
			trip = <&cpu_alert0>;
			cooling-device = <&fan0 0 1>;
		};
		map1 {
			trip = <&cpu_alert1>;
			cooling-device = <&fan0 1 2>;
		};
		map2 {
			trip = <&cpu_alert2>;
			cooling-device = <&fan0 2 3>;
		};
	};
};

&hsi2c_4 {
	clock-frequency = <400000>;
	status = "okay";

	usb3503: usb-hub@8 {
		compatible = "smsc,usb3503";
		reg = <0x08>;

		intn-gpios = <&gpx0 7 GPIO_ACTIVE_HIGH>;
		connect-gpios = <&gpx0 6 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpx1 4 GPIO_ACTIVE_LOW>;
		initial-mode = <1>;

		clock-names = "refclk";
		clocks = <&pmu_system_controller 0>;
		refclk-frequency = <********>;
	};

	max77802: pmic@9 {
		compatible = "maxim,max77802";
		reg = <0x9>;
		interrupt-parent = <&gpx0>;
		interrupts = <4 IRQ_TYPE_NONE>;
		pinctrl-names = "default";
		pinctrl-0 = <&max77802_irq>, <&pmic_dvs_1>, <&pmic_dvs_2>;
		wakeup-source;
		#clock-cells = <1>;

		inl1-supply = <&buck5_reg>;
		inl2-supply = <&buck7_reg>;
		inl3-supply = <&buck9_reg>;
		inl4-supply = <&buck9_reg>;
		inl5-supply = <&buck9_reg>;
		inl6-supply = <&buck10_reg>;
		inl7-supply = <&buck9_reg>;
		/* inl9 supply is BOOST, not configured here */
		inl10-supply = <&buck7_reg>;

		regulators {
			buck1_reg: BUCK1 {
				regulator-name = "vdd_mif";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1300000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck2_reg: BUCK2 {
				regulator-name = "vdd_arm";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck3_reg: BUCK3 {
				regulator-name = "vdd_int";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1400000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck4_reg: BUCK4 {
				regulator-name = "vdd_g3d";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1400000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck5_reg: BUCK5 {
				regulator-name = "vdd_mem";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck6_reg: BUCK6 {
				regulator-name = "vdd_kfc";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck7_reg: BUCK7 {
				regulator-name = "buck7";
				regulator-min-microvolt = <1300000>;
				regulator-max-microvolt = <1300000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck8_reg: BUCK8 {
				/* vdd_mmc0 */
				regulator-name = "vddf_2v85";
				regulator-min-microvolt = <2850000>;
				regulator-max-microvolt = <2850000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck9_reg: BUCK9 {
				regulator-name = "buck9";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-always-on;
				regulator-boot-on;
			};

			buck10_reg: BUCK10 {
				regulator-name = "buck10";
				regulator-min-microvolt = <2950000>;
				regulator-max-microvolt = <2950000>;
				regulator-always-on;
				regulator-boot-on;
			};

			ldo1_reg: LDO1 {
				regulator-name = "vdd_alive";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo2_reg: LDO2 {
				regulator-name = "vddq_m1_m2";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			ldo3_reg: LDO3 {
				regulator-name = "vddq_gpio";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo4_reg: LDO4 {
				regulator-name = "vddq_mmc2";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3000000>;
				/* Having it off prevents reboot */
				regulator-always-on;
			};

			ldo5_reg: LDO5 {
				regulator-name = "vdd18_hsic";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo6_reg: LDO6 {
				regulator-name = "vdd18_bpll";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo7_reg: LDO7 {
				regulator-name = "vddq_lcd";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				/* Supplies also GPK and GPJ */
				regulator-always-on;
			};

			ldo8_reg: LDO8 {
				regulator-name = "vdd10_hdmi";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo9_reg: LDO9 {
				regulator-name = "ldo9";
			};

			ldo10_reg: LDO10 {
				regulator-name = "vdd18_mipi";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo11_reg: LDO11 {
				regulator-name = "vddq_mmc01";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				/*
				 * Having it off prevents accessing MMC after
				 * reboot with error:
				 * MMC Device 1: Clock OFF has been failed.
				 */
				regulator-always-on;
			};

			ldo12_reg: LDO12 {
				regulator-name = "vdd33_usb3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			ldo13_reg: LDO13 {
				regulator-name = "vddq_abbg0";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo14_reg: LDO14 {
				regulator-name = "vddq_abbg1";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			ldo15_reg: LDO15 {
				regulator-name = "vdd10_usb3";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			ldo17_reg: LDO17 {
				regulator-name = "cam_sensor_core";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
			};

			ldo18_reg: LDO18 {
				regulator-name = "ldo18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo19_reg: LDO19 {
				regulator-name = "ldo19";
			};

			ldo20_reg: LDO20 {
				regulator-name = "vdd_mmc0";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo21_reg: LDO21 {
				/* vdd_mmc2 */
				regulator-name = "vddf_2v8";
				regulator-min-microvolt = <2850000>;
				regulator-max-microvolt = <2850000>;
			};

			ldo23_reg: LDO23 {
				regulator-name = "dp_p3v3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			ldo24_reg: LDO24 {
				regulator-name = "cam_af";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
			};

			ldo25_reg: LDO25 {
				regulator-name = "eth_p3v3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			ldo26_reg: LDO26 {
				regulator-name = "usb30_extclk";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			ldo27_reg: LDO27 {
				regulator-name = "ldo27";
			};

			ldo28_reg: LDO28 {
				regulator-name = "ldo28";
			};

			ldo29_reg: LDO29 {
				regulator-name = "ldo29";
			};

			ldo30_reg: LDO30 {
				regulator-name = "vddq_e1_e2";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			/* On revisions with ti,ina231 this is sensor VS */
			ldo32_reg: LDO32 {
				regulator-name = "vs_power_meter";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
			};

			ldo33_reg: LDO33 {
				regulator-name = "ldo33";
			};

			ldo34_reg: LDO34 {
				regulator-name = "ldo34";
			};

			ldo35_reg: LDO35 {
				regulator-name = "ldo35";
			};
		};
	};
};

&i2c_1 {
	status = "okay";
	max98090: audio-codec@10 {
		compatible = "maxim,max98090";
		reg = <0x10>;
		interrupt-parent = <&gpj3>;
		interrupts = <0 IRQ_TYPE_NONE>;
		clocks = <&audi2s0 CLK_I2S_CDCLK>;
		clock-names = "mclk";
		#sound-dai-cells = <0>;
	};
};

&mmc_0 {
	status = "okay";
	mmc-pwrseq = <&emmc_pwrseq>;
	cd-gpios = <&gpc0 2 GPIO_ACTIVE_LOW>;
	card-detect-delay = <200>;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <0 4>;
	samsung,dw-mshc-ddr-timing = <0 2>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_bus1 &sd0_bus4 &sd0_bus8 &sd0_cd>;
	bus-width = <8>;
	cap-mmc-highspeed;
	mmc-ddr-1_8v;
	mmc-hs200-1_8v;
	vmmc-supply = <&ldo20_reg>;
	vqmmc-supply = <&ldo11_reg>;
};

&mmc_2 {
	status = "okay";
	card-detect-delay = <200>;
	samsung,dw-mshc-ciu-div = <3>;
	samsung,dw-mshc-sdr-timing = <0 4>;
	samsung,dw-mshc-ddr-timing = <0 2>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd2_clk &sd2_cmd &sd2_cd &sd2_bus1 &sd2_bus4 &sd2_wp>;
	bus-width = <4>;
	cap-sd-highspeed;
	vmmc-supply = <&ldo21_reg>;
	vqmmc-supply = <&ldo4_reg>;
};

&pinctrl_0 {
	emmc_nrst_pin: emmc-nrst-pins {
		samsung,pins = "gpd1-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	sd2_wp: sd2-wp-pins {
		samsung,pins = "gpm5-0";
		samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
		/* Pin is floating so be sure to disable write-protect */
		samsung,pin-pud = <EXYNOS_PIN_PULL_DOWN>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV4>;
	};

	pmic_dvs_2: pmic-dvs-2-pins {
		samsung,pins = "gpx0-0", "gpx0-1";
		samsung,pin-function = <EXYNOS_PIN_FUNC_OUTPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};

	pmic_dvs_1: pmic-dvs-1-pins {
		samsung,pins = "gpx0-2";
		samsung,pin-function = <EXYNOS_PIN_FUNC_OUTPUT>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
		samsung,pin-val = <1>;
	};

	max77802_irq: max77802-irq-pins {
		samsung,pins = "gpx0-4";
		samsung,pin-function = <EXYNOS_PIN_FUNC_F>;
		samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
		samsung,pin-drv = <EXYNOS5420_PIN_DRV_LV1>;
	};
};

&pwm {
	/*
	 * PWM 0 -- fan
	 * PWM 1 -- Green LED
	 * PWM 2 -- Blue LED
	 * PWM 3 -- on MIPI connector for backlight
	 */
	pinctrl-0 = <&pwm0_out &pwm1_out &pwm2_out &pwm3_out>;
	pinctrl-names = "default";
	status = "okay";
};

&rtc {
	status = "okay";
	clocks = <&clock CLK_RTC>, <&max77802 MAX77802_CLK_32K_AP>;
	clock-names = "rtc", "rtc_src";
};

&serial_0 {
	status = "okay";
};

&serial_1 {
	status = "okay";
};

&serial_2 {
	status = "okay";
};

&serial_3 {
	status = "okay";
};

&tmu_cpu0 {
	vtmu-supply = <&ldo10_reg>;
};

&tmu_cpu1 {
	vtmu-supply = <&ldo10_reg>;
};

&tmu_cpu2 {
	vtmu-supply = <&ldo10_reg>;
};

&tmu_cpu3 {
	vtmu-supply = <&ldo10_reg>;
};

&usb3_0_oc {
	/* External pull up */
	samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
};

&usb3_1_oc {
	/* External pull up */
	samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
};

&usbdrd_dwc3_0 {
	dr_mode = "peripheral";
};

&usbdrd_dwc3_1 {
	dr_mode = "host";
};

&usbdrd3_0 {
	vdd33-supply = <&ldo12_reg>;
	vdd10-supply = <&ldo15_reg>;
};

&usbdrd3_1 {
	vdd33-supply = <&ldo12_reg>;
	vdd10-supply = <&ldo15_reg>;
};

&usbhost2 {
	#address-cells = <1>;
	#size-cells = <0>;

	ethernet: ethernet@2 {
		compatible = "usb424,9730";
		reg = <2>;
		local-mac-address = [00 00 00 00 00 00]; /* Filled in by a bootloader */
	};
};
