// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2017 Fuzhou Rockchip Electronics Co., Ltd.
 */

#include "rk3288.dtsi"
#include <dt-bindings/input/input.h>
#include <dt-bindings/clock/rockchip,rk808.h>

/ {
	chosen {
		stdout-path = "serial2:115200n8";
	};

	memory {
		reg = <0x0 0x0 0x0 0x80000000>;
		device_type = "memory";
	};

	ext_gmac: external-gmac-clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <125000000>;
		clock-output-names = "ext_gmac";
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;

		pinctrl-names = "default";
		pinctrl-0 = <&pwrbtn>;

		button {
			gpios = <&gpio0 RK_PA5 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			label = "GPIO Key Power";
			linux,input-type = <1>;
			wakeup-source;
			debounce-interval = <100>;
		};
	};

	gpio-leds {
		compatible = "gpio-leds";

		act_led: led-0 {
			gpios = <&gpio1 RK_PD0 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "mmc0";
		};

		heartbeat_led: led-1 {
			gpios = <&gpio1 RK_PD1 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
		};

		pwr_led: led-2 {
			gpios = <&gpio0 RK_PA3 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "default-on";
		};
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		clocks = <&rk808 RK808_CLKOUT1>;
		clock-names = "ext_clock";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_enable>;
		reset-gpios = <&gpio4 RK_PD3 GPIO_ACTIVE_LOW>,
			<&gpio4 RK_PD4 GPIO_ACTIVE_LOW>;
	};

	sound {
		compatible = "simple-audio-card";
		simple-audio-card,format = "i2s";
		simple-audio-card,name = "rockchip,tinker-codec";
		simple-audio-card,mclk-fs = <512>;

		simple-audio-card,codec {
			sound-dai = <&hdmi>;
		};

		simple-audio-card,cpu {
			sound-dai = <&i2s>;
		};
	};

	vcc_sys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc_sys";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	vcc_sd: sdmmc-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio7 11 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdmmc_pwr>;
		regulator-name = "vcc_sd";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc_io>;
	};
};

&cpu0 {
	cpu0-supply = <&vdd_cpu>;
};

&cpu_opp_table {
	opp-1704000000 {
		opp-hz = /bits/ 64 <1704000000>;
		opp-microvolt = <1350000>;
	};
	opp-1800000000 {
		opp-hz = /bits/ 64 <1800000000>;
		opp-microvolt = <1400000>;
	};
};

&gmac {
	assigned-clocks = <&cru SCLK_MAC>;
	assigned-clock-parents = <&ext_gmac>;
	clock_in_out = "input";
	phy-mode = "rgmii";
	phy-supply = <&vcc33_lan>;
	pinctrl-names = "default";
	pinctrl-0 = <&rgmii_pins>;
	snps,reset-gpio = <&gpio4 7 0>;
	snps,reset-active-low;
	snps,reset-delays-us = <0 10000 1000000>;
	tx_delay = <0x30>;
	rx_delay = <0x10>;
	status = "okay";
};

&gpu {
	mali-supply = <&vdd_gpu>;
	status = "okay";
};

&hdmi {
	ddc-i2c-bus = <&i2c5>;
	status = "okay";
};

&i2c0 {
	clock-frequency = <400000>;
	status = "okay";

	rk808: pmic@1b {
		compatible = "rockchip,rk808";
		reg = <0x1b>;
		interrupt-parent = <&gpio0>;
		interrupts = <4 IRQ_TYPE_LEVEL_LOW>;
		#clock-cells = <1>;
		clock-output-names = "xin32k", "rk808-clkout2";
		dvs-gpios = <&gpio0 11 GPIO_ACTIVE_HIGH>,
				<&gpio0 12 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&pmic_int &global_pwroff &dvs_1 &dvs_2>;
		rockchip,system-power-controller;
		wakeup-source;

		vcc1-supply = <&vcc_sys>;
		vcc2-supply = <&vcc_sys>;
		vcc3-supply = <&vcc_sys>;
		vcc4-supply = <&vcc_sys>;
		vcc6-supply = <&vcc_sys>;
		vcc7-supply = <&vcc_sys>;
		vcc8-supply = <&vcc_io>;
		vcc9-supply = <&vcc_io>;
		vcc10-supply = <&vcc_io>;
		vcc11-supply = <&vcc_sys>;
		vcc12-supply = <&vcc_io>;
		vddio-supply = <&vcc_io>;

		regulators {
			vdd_cpu: DCDC_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1400000>;
				regulator-name = "vdd_arm";
				regulator-ramp-delay = <6000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_gpu: DCDC_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1250000>;
				regulator-name = "vdd_gpu";
				regulator-ramp-delay = <6000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vcc_ddr";
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc_io: DCDC_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcc_io";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcc18_ldo1: LDO_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc18_ldo1";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc33_mipi: LDO_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcc33_mipi";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_10: LDO_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-name = "vdd_10";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc18_codec: LDO_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc18_codec";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vccio_sd: LDO_REG5 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vccio_sd";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vdd10_lcd: LDO_REG6 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-name = "vdd10_lcd";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc_18: LDO_REG7 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc_18";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc18_lcd: LDO_REG8 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc18_lcd";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc33_sd: SWITCH_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vcc33_sd";
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc33_lan: SWITCH_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vcc33_lan";
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};
		};
	};
};

&i2c2 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&i2s {
	#sound-dai-cells = <0>;
	status = "okay";
};

&io_domains {
	status = "okay";

	sdcard-supply = <&vccio_sd>;
	wifi-supply = <&vcc_18>;
};

&pinctrl {
	pcfg_pull_none_drv_8ma: pcfg-pull-none-drv-8ma {
		drive-strength = <8>;
	};

	pcfg_pull_up_drv_8ma: pcfg-pull-up-drv-8ma {
		bias-pull-up;
		drive-strength = <8>;
	};

	backlight {
		bl_en: bl-en {
			rockchip,pins = <7 RK_PA2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	buttons {
		pwrbtn: pwrbtn {
			rockchip,pins = <0 RK_PA5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	eth_phy {
		eth_phy_pwr: eth-phy-pwr {
			rockchip,pins = <0 RK_PA6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	pmic {
		pmic_int: pmic-int {
			rockchip,pins = <0 RK_PA4 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		dvs_1: dvs-1 {
			rockchip,pins = <0 RK_PB3 RK_FUNC_GPIO &pcfg_pull_down>;
		};

		dvs_2: dvs-2 {
			rockchip,pins = <0 RK_PB4 RK_FUNC_GPIO &pcfg_pull_down>;
		};
	};

	sdmmc {
		sdmmc_bus4: sdmmc-bus4 {
			rockchip,pins = <6 RK_PC0 1 &pcfg_pull_up_drv_8ma>,
					<6 RK_PC1 1 &pcfg_pull_up_drv_8ma>,
					<6 RK_PC2 1 &pcfg_pull_up_drv_8ma>,
					<6 RK_PC3 1 &pcfg_pull_up_drv_8ma>;
		};

		sdmmc_clk: sdmmc-clk {
			rockchip,pins = <6 RK_PC4 1 &pcfg_pull_none_drv_8ma>;
		};

		sdmmc_cmd: sdmmc-cmd {
			rockchip,pins = <6 RK_PC5 1 &pcfg_pull_up_drv_8ma>;
		};

		sdmmc_pwr: sdmmc-pwr {
			rockchip,pins = <7 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb {
		host_vbus_drv: host-vbus-drv {
			rockchip,pins = <0 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		pwr_3g: pwr-3g {
			rockchip,pins = <7 RK_PB0 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	sdio {
		wifi_enable: wifi-enable {
			rockchip,pins = <4 RK_PD3 RK_FUNC_GPIO &pcfg_pull_none>,
					<4 RK_PD4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&pwm0 {
	status = "okay";
};

&saradc {
	vref-supply = <&vcc18_ldo1>;
	status = "okay";
};

&sdmmc {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	broken-cd;
	disable-wp;			/* wp not hooked up */
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk &sdmmc_cmd &sdmmc_cd &sdmmc_bus4>;
	status = "okay";
	vmmc-supply = <&vcc33_sd>;
	vqmmc-supply = <&vccio_sd>;
};

&sdio0 {
	bus-width = <4>;
	cap-sd-highspeed;
	cap-sdio-irq;
	keep-power-in-suspend;
	max-frequency = <50000000>;
	mmc-pwrseq = <&sdio_pwrseq>;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&sdio0_bus4>, <&sdio0_cmd>, <&sdio0_clk>, <&sdio0_int>;
	sd-uhs-sdr12;
	sd-uhs-sdr25;
	sd-uhs-sdr50;
	vmmc-supply = <&vcc_io>;
	vqmmc-supply = <&vcc_18>;
	status = "okay";
};

&tsadc {
	rockchip,hw-tshut-mode = <1>; /* tshut mode 0:CRU 1:GPIO */
	rockchip,hw-tshut-polarity = <1>; /* tshut polarity 0:LOW 1:HIGH */
	status = "okay";
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&uart4 {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&usb_host0_ehci {
	status = "okay";
};

&usb_host1 {
	status = "okay";
};

&usb_otg {
	status = "okay";
};

&vopb {
	status = "okay";
};

&vopb_mmu {
	status = "okay";
};

&vopl {
	status = "okay";
};

&vopl_mmu {
	status = "okay";
};

&wdt {
	status = "okay";
};
