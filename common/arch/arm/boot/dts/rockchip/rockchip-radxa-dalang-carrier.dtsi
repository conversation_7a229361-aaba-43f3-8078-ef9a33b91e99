// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2019 Fuzhou Rockchip Electronics Co., Ltd
 * Copyright (c) 2019 Radxa Limited
 * Copyright (c) 2019 Amarula Solutions(India)
 */

#include <dt-bindings/pwm/pwm.h>

/ {
	clkin_gmac: external-gmac-clock {
		compatible = "fixed-clock";
		clock-frequency = <125000000>;
		clock-output-names = "clkin_gmac";
		#clock-cells = <0>;
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		clocks = <&hym8563>;
		clock-names = "ext_clock";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_enable_h>;
	};

	vcc12v_dcin: vcc12v-dcin-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc12v_dcin";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <12000000>;
		regulator-max-microvolt = <12000000>;
	};

	vcc5v0_sys: vcc5v0-sys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&vcc12v_dcin>;
	};

	vbus_host: vbus-host {
		compatible = "regulator-fixed";
		pinctrl-names = "default";
		pinctrl-0 = <&usb1_en_oc>;
		regulator-name = "vbus_host"; /* HOST-5V */
		regulator-always-on;
		vin-supply = <&vcc5v0_sys>;
	};

	vbus_typec: vbus-typec {
		compatible = "regulator-fixed";
		pinctrl-names = "default";
		pinctrl-0 = <&usb0_en_oc>;
		regulator-name = "vbus_typec";
		regulator-always-on;
		vin-supply = <&vcc5v0_sys>;
	};
};

&gmac {
	assigned-clock-parents = <&clkin_gmac>;
	clock_in_out = "input";
	phy-mode = "rgmii";
	pinctrl-names = "default";
	pinctrl-0 = <&rgmii_pins>;
	snps,reset-active-low;
	snps,reset-delays-us = <0 10000 50000>;
	tx_delay = <0x28>;
	rx_delay = <0x11>;
	status = "okay";
};

&hdmi {
	status = "okay";
};

&pwm0 {
	status = "okay";
};

&pwm2 {
	status = "okay";
};

&sdio0 {
	bus-width = <4>;
	cap-sd-highspeed;
	cap-sdio-irq;
	keep-power-in-suspend;
	mmc-pwrseq = <&sdio_pwrseq>;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&sdio0_bus4 &sdio0_cmd &sdio0_clk>;
	sd-uhs-sdr104;
	status = "okay";
};

&sdmmc {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	disable-wp;
	vqmmc-supply = <&vccio_sd>;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk &sdmmc_cmd &sdmmc_cd &sdmmc_bus4>;
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_xfer &uart0_cts>;
	status = "okay";
};

&uart2 {
	status = "okay";
};

&vopb {
	status = "okay";
};

&vopb_mmu {
	status = "okay";
};

&vopl {
	status = "okay";
};

&vopl_mmu {
	status = "okay";
};
