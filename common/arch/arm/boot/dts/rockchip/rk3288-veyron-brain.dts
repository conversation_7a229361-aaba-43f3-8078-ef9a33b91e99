// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Google Veyron Brain Rev 0 board device tree source
 *
 * Copyright 2014 Google, Inc
 */

/dts-v1/;
#include "rk3288-veyron.dtsi"
#include "rk3288-veyron-broadcom-bluetooth.dtsi"

/ {
	model = "Google Brain";
	compatible = "google,veyron-brain-rev0", "google,veyron-brain",
		     "google,veyron", "rockchip,rk3288";

	vcc33_sys: vcc33-sys {
		vin-supply = <&vcc_5v>;
	};

	vcc33_io: vcc33_io {
		compatible = "regulator-fixed";
		regulator-name = "vcc33_io";
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vcc33_sys>;
		/* This is gated by vcc_18 too */
	};

	/* This turns on vbus for host2 and otg (dwc2) */
	vcc5_host2: vcc5-host2-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB4 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&usb2_pwr_en>;
		regulator-name = "vcc5_host2";
		regulator-always-on;
		regulator-boot-on;
	};
};

&pinctrl {
	pinctrl-names = "default";
	pinctrl-0 = <
		/* Common for sleep and wake, but no owners */
		&ddr0_retention
		&ddrio_pwroff
		&global_pwroff
	>;

	hdmi {
		vcc50_hdmi_en: vcc50-hdmi-en {
			rockchip,pins = <7 RK_PA2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	pmic {
		dvs_1: dvs-1 {
			rockchip,pins = <7 RK_PB3 RK_FUNC_GPIO &pcfg_pull_down>;
		};

		dvs_2: dvs-2 {
			rockchip,pins = <7 RK_PB7 RK_FUNC_GPIO &pcfg_pull_down>;
		};
	};

	usb-host {
		usb2_pwr_en: usb2-pwr-en {
			rockchip,pins = <0 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&rk808 {
	pinctrl-names = "default";
	pinctrl-0 = <&pmic_int_l &dvs_1 &dvs_2>;
	dvs-gpios = <&gpio7 RK_PB3 GPIO_ACTIVE_HIGH>,
		    <&gpio7 RK_PB7 GPIO_ACTIVE_HIGH>;

	/delete-property/ vcc6-supply;

	regulators {
		/* vcc33_io is sourced directly from vcc33_sys */
		/delete-node/ LDO_REG1;

		/* This is not a pwren anymore, but the real power supply */
		vdd10_lcd: LDO_REG7 {
			regulator-always-on;
			regulator-boot-on;
			regulator-min-microvolt = <1000000>;
			regulator-max-microvolt = <1000000>;
			regulator-name = "vdd10_lcd";
			regulator-suspend-mem-disabled;
		};

		vcc18_hdmi: SWITCH_REG2 {
			regulator-always-on;
			regulator-boot-on;
			regulator-name = "vcc18_hdmi";
			regulator-suspend-mem-disabled;
		};
	};
};

&vcc50_hdmi {
	enable-active-high;
	gpio = <&gpio7 RK_PA2 GPIO_ACTIVE_HIGH>;
	pinctrl-names = "default";
	pinctrl-0 = <&vcc50_hdmi_en>;
};
