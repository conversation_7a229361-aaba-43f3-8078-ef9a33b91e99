// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Google Veyron Pinky Rev 2 board device tree source
 *
 * Copyright 2015 Google, Inc
 */

/dts-v1/;
#include "rk3288-veyron-chromebook.dtsi"
#include "../cros-ec-sbs.dtsi"

/ {
	model = "Google Pinky";
	compatible = "google,veyron-pinky-rev2", "google,veyron-pinky",
		     "google,veyron", "rockchip,rk3288";

	/delete-node/backlight-regulator;
	/delete-node/panel-regulator;
	/delete-node/emmc-pwrseq;
	/delete-node/vcc18-lcd;
};

&backlight {
	/delete-property/power-supply;
};

&emmc {
	/*
	 * Use a pullup instead of a drive since the output is 3.3V and
	 * really should be 1.8V (oops).  The external pulldown will help
	 * bring the voltage down if we only drive with a pullup here.
	 * Therefore disable the powerseq (and actual reset) for pinky.
	 */
	/delete-property/mmc-pwrseq;
	pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_bus8 &emmc_reset>;
};

&edp {
	/delete-property/pinctrl-names;
	/delete-property/pinctrl-0;

	force-hpd;
};

&lid_switch {
	pinctrl-0 = <&pwr_key_h &ap_lid_int_l>;

	key-power {
		gpios = <&gpio0 RK_PA5 GPIO_ACTIVE_HIGH>;
	};
};

/* Touchpad connector */
&i2c3 {
	status = "okay";

	clock-frequency = <400000>;
	i2c-scl-falling-time-ns = <50>;
	i2c-scl-rising-time-ns = <300>;
};

&panel {
	power-supply = <&vcc33_lcd>;
};

&pinctrl {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <
		/* Common for sleep and wake, but no owners */
		&ddr0_retention
		&ddrio_pwroff
		&global_pwroff

		/* Wake only */
		&suspend_l_wake
		&bt_dev_wake_awake
	>;
	pinctrl-1 = <
		/* Common for sleep and wake, but no owners */
		&ddr0_retention
		&ddrio_pwroff
		&global_pwroff

		/* Sleep only */
		&suspend_l_sleep
		&bt_dev_wake_sleep
	>;

	/delete-node/ lcd;

	backlight {
		/delete-node/ bl_pwr_en;
	};

	buttons {
		pwr_key_h: pwr-key-h {
			rockchip,pins = <0 RK_PA5 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	emmc {
		emmc_reset: emmc-reset {
			rockchip,pins = <7 RK_PB4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	sdmmc {
		sdmmc_wp_pin: sdmmc-wp-pin {
			rockchip,pins = <7 RK_PB2 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};
};

&rk808 {
	regulators {
		vcc18_lcd: SWITCH_REG2 {
			regulator-always-on;
			regulator-boot-on;
			regulator-name = "vcc18_lcd";
			regulator-state-mem {
				regulator-off-in-suspend;
			};
		};
	};
};

&sdmmc {
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk &sdmmc_cmd &sdmmc_cd_disabled &sdmmc_cd_pin
		     &sdmmc_wp_pin &sdmmc_bus4>;
	wp-gpios = <&gpio7 RK_PB2 GPIO_ACTIVE_HIGH>;
};

&tsadc {
	/* Some connection is flaky making the tsadc hang the system */
	status = "disabled";
};
