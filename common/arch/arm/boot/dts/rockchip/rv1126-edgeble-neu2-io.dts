// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2020 Rockchip Electronics Co., Ltd.
 * Copyright (c) 2022 Edgeble AI Technologies Pvt. Ltd.
 */

/dts-v1/;
#include "rv1126.dtsi"
#include "rv1126-edgeble-neu2.dtsi"

/ {
	model = "Edgeble Neu2 IO Board";
	compatible = "edgeble,neural-compute-module-2-io",
		     "edgeble,neural-compute-module-2", "rockchip,rv1126";

	aliases {
		serial2 = &uart2;
	};

	chosen {
		stdout-path = "serial2:1500000n8";
	};

	vcc12v_dcin: vcc12v-dcin-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc12v_dcin";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <12000000>;
		regulator-max-microvolt = <12000000>;
	};

	vcc5v0_sys: vcc5v0-sys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&vcc12v_dcin>;
	};

	v3v3_sys: v3v3-sys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "v3v3_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&vcc5v0_sys>;
	};
};

&gmac {
	assigned-clocks = <&cru CLK_GMAC_SRC>, <&cru CLK_GMAC_TX_RX>,
			  <&cru CLK_GMAC_ETHERNET_OUT>;
	assigned-clock-parents = <&cru CLK_GMAC_SRC_M1>, <&cru RGMII_MODE_CLK>;
	assigned-clock-rates = <125000000>, <0>, <25000000>;
	clock_in_out = "input";
	phy-handle = <&phy>;
	phy-mode = "rgmii";
	phy-supply = <&vcc_3v3>;
	pinctrl-names = "default";
	pinctrl-0 = <&rgmiim1_pins &clk_out_ethernetm1_pins>;
	tx_delay = <0x2a>;
	rx_delay = <0x1a>;
	status = "okay";
};

&mdio {
	phy: ethernet-phy@0 {
		compatible = "ethernet-phy-id001c.c916",
			     "ethernet-phy-ieee802.3-c22";
		reg = <0x0>;
		pinctrl-names = "default";
		pinctrl-0 = <&eth_phy_rst>;
		reset-assert-us = <20000>;
		reset-deassert-us = <100000>;
		reset-gpios = <&gpio0 RK_PB6 GPIO_ACTIVE_LOW>;
	};
};

&pinctrl {
	ethernet {
		eth_phy_rst: eth-phy-rst {
			rockchip,pins = <0 RK_PB6 RK_FUNC_GPIO &pcfg_pull_down>;
		};
	};
};

&sdmmc {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc0_clk &sdmmc0_cmd &sdmmc0_bus4 &sdmmc0_det>;
	rockchip,default-sample-phase = <90>;
	sd-uhs-sdr12;
	sd-uhs-sdr25;
	sd-uhs-sdr104;
	vqmmc-supply = <&vccio_sd>;
	status = "okay";
};

&uart2 {
	status = "okay";
};
