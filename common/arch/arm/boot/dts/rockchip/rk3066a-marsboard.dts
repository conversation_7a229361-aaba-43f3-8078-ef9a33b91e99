// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2014 <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "rk3066a.dtsi"

/ {
	model = "MarsBoard RK3066";
	compatible = "haoyu,marsboard-rk3066", "rockchip,rk3066a";

	aliases {
		mmc0 = &mmc0;
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x40000000>;
	};

	vdd_log: vdd-log {
		compatible = "pwm-regulator";
		pwms = <&pwm3 0 1000>;
		regulator-name = "vdd_log";
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		regulator-always-on;
		voltage-table = <1000000 100>,
				<1200000 42>;
		status = "okay";
	};

	vcc_sd0: sdmmc-regulator {
		compatible = "regulator-fixed";
		regulator-name = "sdmmc-supply";
		regulator-min-microvolt = <3000000>;
		regulator-max-microvolt = <3000000>;
		gpio = <&gpio3 RK_PA7 GPIO_ACTIVE_LOW>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc_io>;
	};

	vsys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vsys";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
	};
};

&cpu0 {
	cpu-supply = <&vdd_arm>;
};

&cpu1 {
	cpu-supply = <&vdd_arm>;
};

&i2c1 {
	status = "okay";
	clock-frequency = <400000>;

	tps: tps@2d {
		reg = <0x2d>;

		interrupt-parent = <&gpio6>;
		interrupts = <RK_PA4 IRQ_TYPE_LEVEL_LOW>;

		vcc1-supply = <&vsys>;
		vcc2-supply = <&vsys>;
		vcc3-supply = <&vsys>;
		vcc4-supply = <&vsys>;
		vcc5-supply = <&vcc_io>;
		vcc6-supply = <&vcc_io>;
		vcc7-supply = <&vsys>;
		vccio-supply = <&vsys>;

		regulators {
			vcc_rtc: regulator@0 {
				regulator-name = "vcc_rtc";
				regulator-always-on;
			};

			vcc_io: regulator@1 {
				regulator-name = "vcc_io";
				regulator-always-on;
			};

			vdd_arm: regulator@2 {
				regulator-name = "vdd_arm";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <1500000>;
				regulator-boot-on;
				regulator-always-on;
			};

			vcc_ddr: regulator@3 {
				regulator-name = "vcc_ddr";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <1500000>;
				regulator-boot-on;
				regulator-always-on;
			};

			vcc18_cif: regulator@5 {
				regulator-name = "vcc18_cif";
				regulator-always-on;
			};

			vdd_11: regulator@6 {
				regulator-name = "vdd_11";
				regulator-always-on;
			};

			vcc_25: regulator@7 {
				regulator-name = "vcc_25";
				regulator-always-on;
			};

			vcc_18: regulator@8 {
				regulator-name = "vcc_18";
				regulator-always-on;
			};

			vcc25_hdmi: regulator@9 {
				regulator-name = "vcc25_hdmi";
				regulator-always-on;
			};

			vcca_33: regulator@10 {
				regulator-name = "vcca_33";
				regulator-always-on;
			};

			vcc_rmii: regulator@11 {
				regulator-name = "vcc_rmii";
			};

			vcc28_cif: regulator@12 {
				regulator-name = "vcc28_cif";
				regulator-always-on;
			};
		};
	};
};

/* must be included after &tps gets defined */
#include "../tps65910.dtsi"

&emac {
	phy = <&phy0>;
	phy-supply = <&vcc_rmii>;
	pinctrl-names = "default";
	pinctrl-0 = <&emac_xfer>, <&emac_mdio>, <&phy_int>;
	status = "okay";

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;

		phy0: ethernet-phy@0 {
			reg = <0>;
			interrupt-parent = <&gpio1>;
			interrupts = <RK_PD2 IRQ_TYPE_LEVEL_LOW>;
		};
	};
};

&mmc0 {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk>, <&sd0_cmd>, <&sd0_cd>, <&sd0_bus4>;
	vmmc-supply = <&vcc_sd0>;
};

&pinctrl {
	lan8720a {
		phy_int: phy-int {
			rockchip,pins = <1 RK_PD2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&pwm3 {
	status = "okay";
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&usb_host {
	status = "okay";
};

&usb_otg {
	status = "okay";
};

&wdt {
	status = "okay";
};
