// SPDX-License-Identifier: (GPL-2.0+ OR MIT)

#include <dt-bindings/pwm/pwm.h>
#include "rk3288.dtsi"

/ {
	memory@0 {
		reg = <0x0 0x0 0x0 0x80000000>;
		device_type = "memory";
	};

	emmc_pwrseq: emmc-pwrseq {
		compatible = "mmc-pwrseq-emmc";
		pinctrl-0 = <&emmc_reset>;
		pinctrl-names = "default";
		reset-gpios = <&gpio3 RK_PB1 GPIO_ACTIVE_LOW>;
	};

	ext_gmac: external-gmac-clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <125000000>;
		clock-output-names = "ext_gmac";
	};

	vcc_flash: flash-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc_flash";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		startup-delay-us = <150>;
		vin-supply = <&vcc_io>;
	};

	vcc_sys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc_sys";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};
};

&cpu0 {
	cpu0-supply = <&vdd_cpu>;
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	disable-wp;
	non-removable;
	mmc-pwrseq = <&emmc_pwrseq>;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_bus8>;
	vmmc-supply = <&vcc_io>;
	vqmmc-supply = <&vcc_flash>;
	status = "okay";
};

&gmac {
	assigned-clocks = <&cru SCLK_MAC>;
	assigned-clock-parents = <&ext_gmac>;
	clock_in_out = "input";
	phy-mode = "rgmii";
	phy-supply = <&vccio_pmu>;
	pinctrl-names = "default";
	pinctrl-0 = <&rgmii_pins &phy_rst>;
	snps,reset-gpio = <&gpio4 RK_PB0 GPIO_ACTIVE_LOW>;
	snps,reset-active-low;
	snps,reset-delays-us = <0 10000 30000>;
	rx_delay = <0x10>;
	tx_delay = <0x30>;
};

&gpu {
	mali-supply = <&vdd_gpu>;
	status = "okay";
};

&i2c0 {
	status = "okay";

	act8846: act8846@5a {
		compatible = "active-semi,act8846";
		reg = <0x5a>;
		system-power-controller;
		inl1-supply = <&vcc_io>;
		inl2-supply = <&vcc_sys>;
		inl3-supply = <&vcc_20>;
		vp1-supply = <&vcc_sys>;
		vp2-supply = <&vcc_sys>;
		vp3-supply = <&vcc_sys>;
		vp4-supply = <&vcc_sys>;

		regulators {
			vcc_ddr: REG1 {
				regulator-name = "VCC_DDR";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			vcc_io: vccio_codec: REG2 {
				regulator-name = "VCC_IO";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd_log: REG3 {
				regulator-name = "VDD_LOG";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vcc_20: REG4 {
				regulator-name = "VCC_20";
				regulator-min-microvolt = <2000000>;
				regulator-max-microvolt = <2000000>;
				regulator-always-on;
			};

			vccio_sd: REG5 {
				regulator-name = "VCCIO_SD";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd10_lcd: REG6 {
				regulator-name = "VDD10_LCD";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vcca_codec: REG7 {
				regulator-name = "VCCA_CODEC";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vcca_tp: REG8 {
				regulator-name = "VCCA_TP";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vccio_pmu: REG9 {
				regulator-name = "VCCIO_PMU";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd_10: REG10 {
				regulator-name = "VDD_10";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vcc_18: REG11 {
				regulator-name = "VCC_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			vcc18_lcd: REG12 {
				regulator-name = "VCC18_LCD";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};
		};
	};

	vdd_cpu: syr827@40 {
		compatible = "silergy,syr827";
		reg = <0x40>;
		fcs,suspend-voltage-selector = <1>;
		regulator-always-on;
		regulator-boot-on;
		regulator-enable-ramp-delay = <300>;
		regulator-name = "vdd_cpu";
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <1350000>;
		regulator-ramp-delay = <8000>;
		vin-supply = <&vcc_sys>;
	};

	vdd_gpu: syr828@41 {
		compatible = "silergy,syr828";
		reg = <0x41>;
		fcs,suspend-voltage-selector = <1>;
		regulator-always-on;
		regulator-enable-ramp-delay = <300>;
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <1350000>;
		regulator-name = "vdd_gpu";
		regulator-ramp-delay = <8000>;
		vin-supply = <&vcc_sys>;
	};
};

&io_domains {
	status = "okay";

	audio-supply = <&vcc_io>;
	bb-supply = <&vcc_io>;
	dvp-supply = <&vcc_18>;
	flash0-supply = <&vcc_flash>;
	flash1-supply = <&vccio_pmu>;
	gpio30-supply = <&vccio_pmu>;
	gpio1830-supply = <&vcc_io>;
	lcdc-supply = <&vcc_io>;
	sdcard-supply = <&vccio_sd>;
	wifi-supply = <&vcc_18>;
};

&pinctrl {
	pcfg_output_high: pcfg-output-high {
		output-high;
	};

	emmc {
		emmc_reset: emmc-reset {
			rockchip,pins = <3 RK_PB1 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	gmac {
		phy_rst: phy-rst {
			rockchip,pins = <4 RK_PB0 RK_FUNC_GPIO &pcfg_output_high>;
		};
	};
};

&saradc {
	vref-supply = <&vcc_18>;
};

&tsadc {
	rockchip,hw-tshut-mode = <0>; /* tshut mode 0:CRU 1:GPIO */
	rockchip,hw-tshut-polarity = <0>; /* tshut polarity 0:LOW 1:HIGH */
	status = "okay";
};

&vopb {
	status = "okay";
};

&vopb_mmu {
	status = "okay";
};

&vopl {
	status = "okay";
};

&vopl_mmu {
	status = "okay";
};

&wdt {
	status = "okay";
};
