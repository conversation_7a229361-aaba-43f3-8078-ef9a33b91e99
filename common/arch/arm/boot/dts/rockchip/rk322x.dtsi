// SPDX-License-Identifier: (GPL-2.0+ OR MIT)

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/pinctrl/rockchip.h>
#include <dt-bindings/clock/rk3228-cru.h>
#include <dt-bindings/thermal/thermal.h>
#include <dt-bindings/power/rk3228-power.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	interrupt-parent = <&gic>;

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
		serial2 = &uart2;
		spi0 = &spi0;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@f00 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf00>;
			resets = <&cru SRST_CORE0>;
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
			clock-latency = <40000>;
			clocks = <&cru ARMCLK>;
			enable-method = "psci";
		};

		cpu1: cpu@f01 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf01>;
			resets = <&cru SRST_CORE1>;
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
			enable-method = "psci";
		};

		cpu2: cpu@f02 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf02>;
			resets = <&cru SRST_CORE2>;
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
			enable-method = "psci";
		};

		cpu3: cpu@f03 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf03>;
			resets = <&cru SRST_CORE3>;
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>; /* min followed by max */
			enable-method = "psci";
		};
	};

	cpu0_opp_table: opp-table-0 {
		compatible = "operating-points-v2";
		opp-shared;

		opp-408000000 {
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <950000>;
			clock-latency-ns = <40000>;
			opp-suspend;
		};
		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <975000>;
		};
		opp-816000000 {
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <1000000>;
		};
		opp-1008000000 {
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <1175000>;
		};
		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <1275000>;
		};
	};

	arm-pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 79 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
	};

	psci {
		compatible = "arm,psci-1.0", "arm,psci-0.2";
		method = "smc";
	};

	timer {
		compatible = "arm,armv7-timer";
		arm,cpu-registers-not-fw-configured;
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		clock-frequency = <24000000>;
	};

	xin24m: oscillator {
		compatible = "fixed-clock";
		clock-frequency = <24000000>;
		clock-output-names = "xin24m";
		#clock-cells = <0>;
	};

	display_subsystem: display-subsystem {
		compatible = "rockchip,display-subsystem";
		ports = <&vop_out>;
	};

	i2s1: i2s1@100b0000 {
		compatible = "rockchip,rk3228-i2s", "rockchip,rk3066-i2s";
		reg = <0x100b0000 0x4000>;
		interrupts = <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>;
		clock-names = "i2s_clk", "i2s_hclk";
		clocks = <&cru SCLK_I2S1>, <&cru HCLK_I2S1_8CH>;
		dmas = <&pdma 14>, <&pdma 15>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&i2s1_bus>;
		status = "disabled";
	};

	i2s0: i2s0@100c0000 {
		compatible = "rockchip,rk3228-i2s", "rockchip,rk3066-i2s";
		reg = <0x100c0000 0x4000>;
		interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
		clock-names = "i2s_clk", "i2s_hclk";
		clocks = <&cru SCLK_I2S0>, <&cru HCLK_I2S0_8CH>;
		dmas = <&pdma 11>, <&pdma 12>;
		dma-names = "tx", "rx";
		status = "disabled";
	};

	spdif: spdif@100d0000 {
		compatible = "rockchip,rk3228-spdif";
		reg = <0x100d0000 0x1000>;
		interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_SPDIF>, <&cru HCLK_SPDIF_8CH>;
		clock-names = "mclk", "hclk";
		dmas = <&pdma 10>;
		dma-names = "tx";
		pinctrl-names = "default";
		pinctrl-0 = <&spdif_tx>;
		status = "disabled";
	};

	i2s2: i2s2@100e0000 {
		compatible = "rockchip,rk3228-i2s", "rockchip,rk3066-i2s";
		reg = <0x100e0000 0x4000>;
		interrupts = <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>;
		clock-names = "i2s_clk", "i2s_hclk";
		clocks = <&cru SCLK_I2S2>, <&cru HCLK_I2S2_2CH>;
		dmas = <&pdma 0>, <&pdma 1>;
		dma-names = "tx", "rx";
		status = "disabled";
	};

	grf: syscon@11000000 {
		compatible = "rockchip,rk3228-grf", "syscon", "simple-mfd";
		reg = <0x11000000 0x1000>;
		#address-cells = <1>;
		#size-cells = <1>;

		io_domains: io-domains {
			compatible = "rockchip,rk3228-io-voltage-domain";
			status = "disabled";
		};

		power: power-controller {
			compatible = "rockchip,rk3228-power-controller";
			#power-domain-cells = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			power-domain@RK3228_PD_VIO {
				reg = <RK3228_PD_VIO>;
				clocks = <&cru ACLK_HDCP>,
					 <&cru SCLK_HDCP>,
					 <&cru ACLK_IEP>,
					 <&cru HCLK_IEP>,
					 <&cru ACLK_RGA>,
					 <&cru HCLK_RGA>,
					 <&cru SCLK_RGA>;
				pm_qos = <&qos_hdcp>,
					 <&qos_iep>,
					 <&qos_rga_r>,
					 <&qos_rga_w>;
				#power-domain-cells = <0>;
			};

			power-domain@RK3228_PD_VOP {
				reg = <RK3228_PD_VOP>;
				clocks =<&cru ACLK_VOP>,
					<&cru DCLK_VOP>,
					<&cru HCLK_VOP>;
				pm_qos = <&qos_vop>;
				#power-domain-cells = <0>;
			};

			power-domain@RK3228_PD_VPU {
				reg = <RK3228_PD_VPU>;
				clocks = <&cru ACLK_VPU>,
					 <&cru HCLK_VPU>;
				pm_qos = <&qos_vpu>;
				#power-domain-cells = <0>;
			};

			power-domain@RK3228_PD_RKVDEC {
				reg = <RK3228_PD_RKVDEC>;
				clocks = <&cru ACLK_RKVDEC>,
					 <&cru HCLK_RKVDEC>,
					 <&cru SCLK_VDEC_CABAC>,
					 <&cru SCLK_VDEC_CORE>;
				pm_qos = <&qos_rkvdec_r>,
					 <&qos_rkvdec_w>;
				#power-domain-cells = <0>;
			};

			power-domain@RK3228_PD_GPU {
				reg = <RK3228_PD_GPU>;
				clocks = <&cru ACLK_GPU>;
				pm_qos = <&qos_gpu>;
				#power-domain-cells = <0>;
			};
		};

		u2phy0: usb2phy@760 {
			compatible = "rockchip,rk3228-usb2phy";
			reg = <0x0760 0x0c>;
			clocks = <&cru SCLK_OTGPHY0>;
			clock-names = "phyclk";
			clock-output-names = "usb480m_phy0";
			#clock-cells = <0>;
			status = "disabled";

			u2phy0_otg: otg-port {
				interrupts = <GIC_SPI 59 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names = "otg-bvalid", "otg-id",
						  "linestate";
				#phy-cells = <0>;
				status = "disabled";
			};

			u2phy0_host: host-port {
				interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names = "linestate";
				#phy-cells = <0>;
				status = "disabled";
			};
		};

		u2phy1: usb2phy@800 {
			compatible = "rockchip,rk3228-usb2phy";
			reg = <0x0800 0x0c>;
			clocks = <&cru SCLK_OTGPHY1>;
			clock-names = "phyclk";
			clock-output-names = "usb480m_phy1";
			#clock-cells = <0>;
			status = "disabled";

			u2phy1_otg: otg-port {
				interrupts = <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names = "linestate";
				#phy-cells = <0>;
				status = "disabled";
			};

			u2phy1_host: host-port {
				interrupts = <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names = "linestate";
				#phy-cells = <0>;
				status = "disabled";
			};
		};
	};

	uart0: serial@11010000 {
		compatible = "snps,dw-apb-uart";
		reg = <0x11010000 0x100>;
		interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <24000000>;
		clocks = <&cru SCLK_UART0>, <&cru PCLK_UART0>;
		clock-names = "baudclk", "apb_pclk";
		pinctrl-names = "default";
		pinctrl-0 = <&uart0_xfer &uart0_cts &uart0_rts>;
		reg-shift = <2>;
		reg-io-width = <4>;
		status = "disabled";
	};

	uart1: serial@11020000 {
		compatible = "snps,dw-apb-uart";
		reg = <0x11020000 0x100>;
		interrupts = <GIC_SPI 56 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <24000000>;
		clocks = <&cru SCLK_UART1>, <&cru PCLK_UART1>;
		clock-names = "baudclk", "apb_pclk";
		pinctrl-names = "default";
		pinctrl-0 = <&uart1_xfer>;
		reg-shift = <2>;
		reg-io-width = <4>;
		status = "disabled";
	};

	uart2: serial@11030000 {
		compatible = "snps,dw-apb-uart";
		reg = <0x11030000 0x100>;
		interrupts = <GIC_SPI 57 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <24000000>;
		clocks = <&cru SCLK_UART2>, <&cru PCLK_UART2>;
		clock-names = "baudclk", "apb_pclk";
		pinctrl-names = "default";
		pinctrl-0 = <&uart2_xfer>;
		reg-shift = <2>;
		reg-io-width = <4>;
		status = "disabled";
	};

	efuse: efuse@11040000 {
		compatible = "rockchip,rk3228-efuse";
		reg = <0x11040000 0x20>;
		clocks = <&cru PCLK_EFUSE_256>;
		clock-names = "pclk_efuse";
		#address-cells = <1>;
		#size-cells = <1>;

		/* Data cells */
		efuse_id: id@7 {
			reg = <0x7 0x10>;
		};
		cpu_leakage: cpu_leakage@17 {
			reg = <0x17 0x1>;
		};
	};

	i2c0: i2c@11050000 {
		compatible = "rockchip,rk3228-i2c";
		reg = <0x11050000 0x1000>;
		interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clock-names = "i2c";
		clocks = <&cru PCLK_I2C0>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c0_xfer>;
		status = "disabled";
	};

	i2c1: i2c@11060000 {
		compatible = "rockchip,rk3228-i2c";
		reg = <0x11060000 0x1000>;
		interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clock-names = "i2c";
		clocks = <&cru PCLK_I2C1>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c1_xfer>;
		status = "disabled";
	};

	i2c2: i2c@11070000 {
		compatible = "rockchip,rk3228-i2c";
		reg = <0x11070000 0x1000>;
		interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clock-names = "i2c";
		clocks = <&cru PCLK_I2C2>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c2_xfer>;
		status = "disabled";
	};

	i2c3: i2c@11080000 {
		compatible = "rockchip,rk3228-i2c";
		reg = <0x11080000 0x1000>;
		interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clock-names = "i2c";
		clocks = <&cru PCLK_I2C3>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c3_xfer>;
		status = "disabled";
	};

	spi0: spi@11090000 {
		compatible = "rockchip,rk3228-spi";
		reg = <0x11090000 0x1000>;
		interrupts = <GIC_SPI 49 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&cru SCLK_SPI0>, <&cru PCLK_SPI0>;
		clock-names = "spiclk", "apb_pclk";
		pinctrl-names = "default";
		pinctrl-0 = <&spi0_clk &spi0_tx &spi0_rx &spi0_cs0 &spi0_cs1>;
		status = "disabled";
	};

	wdt: watchdog@110a0000 {
		compatible = "rockchip,rk3228-wdt", "snps,dw-wdt";
		reg = <0x110a0000 0x100>;
		interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_CPU>;
		status = "disabled";
	};

	pwm0: pwm@110b0000 {
		compatible = "rockchip,rk3288-pwm";
		reg = <0x110b0000 0x10>;
		#pwm-cells = <3>;
		clocks = <&cru PCLK_PWM>;
		pinctrl-names = "default";
		pinctrl-0 = <&pwm0_pin>;
		status = "disabled";
	};

	pwm1: pwm@110b0010 {
		compatible = "rockchip,rk3288-pwm";
		reg = <0x110b0010 0x10>;
		#pwm-cells = <3>;
		clocks = <&cru PCLK_PWM>;
		pinctrl-names = "default";
		pinctrl-0 = <&pwm1_pin>;
		status = "disabled";
	};

	pwm2: pwm@110b0020 {
		compatible = "rockchip,rk3288-pwm";
		reg = <0x110b0020 0x10>;
		#pwm-cells = <3>;
		clocks = <&cru PCLK_PWM>;
		pinctrl-names = "default";
		pinctrl-0 = <&pwm2_pin>;
		status = "disabled";
	};

	pwm3: pwm@110b0030 {
		compatible = "rockchip,rk3288-pwm";
		reg = <0x110b0030 0x10>;
		#pwm-cells = <2>;
		clocks = <&cru PCLK_PWM>;
		pinctrl-names = "default";
		pinctrl-0 = <&pwm3_pin>;
		status = "disabled";
	};

	timer: timer@110c0000 {
		compatible = "rockchip,rk3228-timer", "rockchip,rk3288-timer";
		reg = <0x110c0000 0x20>;
		interrupts = <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_TIMER>, <&xin24m>;
		clock-names = "pclk", "timer";
	};

	cru: clock-controller@110e0000 {
		compatible = "rockchip,rk3228-cru";
		reg = <0x110e0000 0x1000>;
		clocks = <&xin24m>;
		clock-names = "xin24m";
		rockchip,grf = <&grf>;
		#clock-cells = <1>;
		#reset-cells = <1>;
		assigned-clocks =
			<&cru PLL_GPLL>, <&cru ARMCLK>,
			<&cru PLL_CPLL>, <&cru ACLK_PERI>,
			<&cru HCLK_PERI>, <&cru PCLK_PERI>,
			<&cru ACLK_CPU>, <&cru HCLK_CPU>,
			<&cru PCLK_CPU>;
		assigned-clock-rates =
			<594000000>, <816000000>,
			<500000000>, <150000000>,
			<150000000>, <75000000>,
			<150000000>, <150000000>,
			<75000000>;
	};

	pdma: dma-controller@110f0000 {
		compatible = "arm,pl330", "arm,primecell";
		reg = <0x110f0000 0x4000>;
		interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
		#dma-cells = <1>;
		arm,pl330-periph-burst;
		clocks = <&cru ACLK_DMAC>;
		clock-names = "apb_pclk";
	};

	thermal-zones {
		cpu_thermal: cpu-thermal {
			polling-delay-passive = <100>; /* milliseconds */
			polling-delay = <5000>; /* milliseconds */

			thermal-sensors = <&tsadc 0>;

			trips {
				cpu_alert0: cpu_alert0 {
					temperature = <70000>; /* millicelsius */
					hysteresis = <2000>; /* millicelsius */
					type = "passive";
				};
				cpu_alert1: cpu_alert1 {
					temperature = <75000>; /* millicelsius */
					hysteresis = <2000>; /* millicelsius */
					type = "passive";
				};
				cpu_crit: cpu_crit {
					temperature = <90000>; /* millicelsius */
					hysteresis = <2000>; /* millicelsius */
					type = "critical";
				};
			};

			cooling-maps {
				map0 {
					trip = <&cpu_alert0>;
					cooling-device =
						<&cpu0 THERMAL_NO_LIMIT 6>,
						<&cpu1 THERMAL_NO_LIMIT 6>,
						<&cpu2 THERMAL_NO_LIMIT 6>,
						<&cpu3 THERMAL_NO_LIMIT 6>;
				};
				map1 {
					trip = <&cpu_alert1>;
					cooling-device =
						<&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};
		};
	};

	tsadc: tsadc@11150000 {
		compatible = "rockchip,rk3228-tsadc";
		reg = <0x11150000 0x100>;
		interrupts = <GIC_SPI 58 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_TSADC>, <&cru PCLK_TSADC>;
		clock-names = "tsadc", "apb_pclk";
		assigned-clocks = <&cru SCLK_TSADC>;
		assigned-clock-rates = <32768>;
		resets = <&cru SRST_TSADC>;
		reset-names = "tsadc-apb";
		pinctrl-names = "init", "default", "sleep";
		pinctrl-0 = <&otp_pin>;
		pinctrl-1 = <&otp_out>;
		pinctrl-2 = <&otp_pin>;
		#thermal-sensor-cells = <1>;
		rockchip,hw-tshut-temp = <95000>;
		status = "disabled";
	};

	hdmi_phy: hdmi-phy@12030000 {
		compatible = "rockchip,rk3228-hdmi-phy";
		reg = <0x12030000 0x10000>;
		clocks = <&cru PCLK_HDMI_PHY>, <&xin24m>, <&cru DCLK_HDMI_PHY>;
		clock-names = "sysclk", "refoclk", "refpclk";
		#clock-cells = <0>;
		clock-output-names = "hdmiphy_phy";
		#phy-cells = <0>;
		status = "disabled";
	};

	gpu: gpu@20000000 {
		compatible = "rockchip,rk3228-mali", "arm,mali-400";
		reg = <0x20000000 0x10000>;
		interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "gp",
				  "gpmmu",
				  "pp0",
				  "ppmmu0",
				  "pp1",
				  "ppmmu1";
		clocks = <&cru ACLK_GPU>, <&cru ACLK_GPU>;
		clock-names = "bus", "core";
		power-domains = <&power RK3228_PD_GPU>;
		resets = <&cru SRST_GPU_A>;
		status = "disabled";
	};

	vpu: video-codec@20020000 {
		compatible = "rockchip,rk3228-vpu", "rockchip,rk3399-vpu";
		reg = <0x20020000 0x800>;
		interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI  9 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "vepu", "vdpu";
		clocks = <&cru ACLK_VPU>, <&cru HCLK_VPU>;
		clock-names = "aclk", "hclk";
		iommus = <&vpu_mmu>;
		power-domains = <&power RK3228_PD_VPU>;
	};

	vpu_mmu: iommu@20020800 {
		compatible = "rockchip,iommu";
		reg = <0x20020800 0x100>;
		interrupts = <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_VPU>, <&cru HCLK_VPU>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3228_PD_VPU>;
		#iommu-cells = <0>;
	};

	vdec: video-codec@20030000 {
		compatible = "rockchip,rk3228-vdec", "rockchip,rk3399-vdec";
		reg = <0x20030000 0x480>;
		interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_RKVDEC>, <&cru HCLK_RKVDEC>,
			 <&cru SCLK_VDEC_CABAC>, <&cru SCLK_VDEC_CORE>;
		clock-names = "axi", "ahb", "cabac", "core";
		assigned-clocks = <&cru SCLK_VDEC_CABAC>, <&cru SCLK_VDEC_CORE>;
		assigned-clock-rates = <300000000>, <300000000>;
		iommus = <&vdec_mmu>;
		power-domains = <&power RK3228_PD_RKVDEC>;
	};

	vdec_mmu: iommu@20030480 {
		compatible = "rockchip,iommu";
		reg = <0x20030480 0x40>, <0x200304c0 0x40>;
		interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_RKVDEC>, <&cru HCLK_RKVDEC>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3228_PD_RKVDEC>;
		#iommu-cells = <0>;
	};

	vop: vop@20050000 {
		compatible = "rockchip,rk3228-vop";
		reg = <0x20050000 0x1ffc>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_VOP>, <&cru DCLK_VOP>, <&cru HCLK_VOP>;
		clock-names = "aclk_vop", "dclk_vop", "hclk_vop";
		resets = <&cru SRST_VOP_A>, <&cru SRST_VOP_H>, <&cru SRST_VOP_D>;
		reset-names = "axi", "ahb", "dclk";
		iommus = <&vop_mmu>;
		power-domains = <&power RK3228_PD_VOP>;
		status = "disabled";

		vop_out: port {
			#address-cells = <1>;
			#size-cells = <0>;

			vop_out_hdmi: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&hdmi_in_vop>;
			};
		};
	};

	vop_mmu: iommu@20053f00 {
		compatible = "rockchip,iommu";
		reg = <0x20053f00 0x100>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_VOP>, <&cru HCLK_VOP>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3228_PD_VOP>;
		#iommu-cells = <0>;
		status = "disabled";
	};

	rga: rga@20060000 {
		compatible = "rockchip,rk3228-rga", "rockchip,rk3288-rga";
		reg = <0x20060000 0x1000>;
		interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_RGA>, <&cru HCLK_RGA>, <&cru SCLK_RGA>;
		clock-names = "aclk", "hclk", "sclk";
		power-domains = <&power RK3228_PD_VIO>;
		resets = <&cru SRST_RGA>, <&cru SRST_RGA_A>, <&cru SRST_RGA_H>;
		reset-names = "core", "axi", "ahb";
	};

	iep_mmu: iommu@20070800 {
		compatible = "rockchip,iommu";
		reg = <0x20070800 0x100>;
		interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_IEP>, <&cru HCLK_IEP>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3228_PD_VIO>;
		#iommu-cells = <0>;
		status = "disabled";
	};

	hdmi: hdmi@200a0000 {
		compatible = "rockchip,rk3228-dw-hdmi";
		reg = <0x200a0000 0x20000>;
		reg-io-width = <4>;
		interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
		assigned-clocks = <&cru SCLK_HDMI_PHY>;
		assigned-clock-parents = <&hdmi_phy>;
		clocks = <&cru PCLK_HDMI_CTRL>, <&cru SCLK_HDMI_HDCP>, <&cru SCLK_HDMI_CEC>;
		clock-names = "iahb", "isfr", "cec";
		pinctrl-names = "default";
		pinctrl-0 = <&hdmii2c_xfer &hdmi_hpd &hdmi_cec>;
		resets = <&cru SRST_HDMI_P>;
		reset-names = "hdmi";
		phys = <&hdmi_phy>;
		phy-names = "hdmi";
		rockchip,grf = <&grf>;
		status = "disabled";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			hdmi_in: port@0 {
				reg = <0>;

				hdmi_in_vop: endpoint {
					remote-endpoint = <&vop_out_hdmi>;
				};
			};

			hdmi_out: port@1 {
				reg = <1>;
			};
		};
	};

	sdmmc: mmc@30000000 {
		compatible = "rockchip,rk3228-dw-mshc", "rockchip,rk3288-dw-mshc";
		reg = <0x30000000 0x4000>;
		interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_SDMMC>, <&cru SCLK_SDMMC>,
			 <&cru SCLK_SDMMC_DRV>, <&cru SCLK_SDMMC_SAMPLE>;
		clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
		fifo-depth = <0x100>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdmmc_clk &sdmmc_cmd &sdmmc_bus4>;
		status = "disabled";
	};

	sdio: mmc@30010000 {
		compatible = "rockchip,rk3228-dw-mshc", "rockchip,rk3288-dw-mshc";
		reg = <0x30010000 0x4000>;
		interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_SDIO>, <&cru SCLK_SDIO>,
			 <&cru SCLK_SDIO_DRV>, <&cru SCLK_SDIO_SAMPLE>;
		clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
		fifo-depth = <0x100>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdio_clk &sdio_cmd &sdio_bus4>;
		status = "disabled";
	};

	emmc: mmc@30020000 {
		compatible = "rockchip,rk3228-dw-mshc", "rockchip,rk3288-dw-mshc";
		reg = <0x30020000 0x4000>;
		interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <37500000>;
		max-frequency = <37500000>;
		clocks = <&cru HCLK_EMMC>, <&cru SCLK_EMMC>,
			 <&cru SCLK_EMMC_DRV>, <&cru SCLK_EMMC_SAMPLE>;
		clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
		bus-width = <8>;
		rockchip,default-sample-phase = <158>;
		fifo-depth = <0x100>;
		pinctrl-names = "default";
		pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_bus8>;
		resets = <&cru SRST_EMMC>;
		reset-names = "reset";
		status = "disabled";
	};

	usb_otg: usb@30040000 {
		compatible = "rockchip,rk3228-usb", "rockchip,rk3066-usb",
			     "snps,dwc2";
		reg = <0x30040000 0x40000>;
		interrupts = <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_OTG>;
		clock-names = "otg";
		dr_mode = "otg";
		g-np-tx-fifo-size = <16>;
		g-rx-fifo-size = <280>;
		g-tx-fifo-size = <256 128 128 64 32 16>;
		phys = <&u2phy0_otg>;
		phy-names = "usb2-phy";
		status = "disabled";
	};

	usb_host0_ehci: usb@30080000 {
		compatible = "generic-ehci";
		reg = <0x30080000 0x20000>;
		interrupts = <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_HOST0>, <&u2phy0>;
		phys = <&u2phy0_host>;
		phy-names = "usb";
		status = "disabled";
	};

	usb_host0_ohci: usb@300a0000 {
		compatible = "generic-ohci";
		reg = <0x300a0000 0x20000>;
		interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_HOST0>, <&u2phy0>;
		phys = <&u2phy0_host>;
		phy-names = "usb";
		status = "disabled";
	};

	usb_host1_ehci: usb@300c0000 {
		compatible = "generic-ehci";
		reg = <0x300c0000 0x20000>;
		interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_HOST1>, <&u2phy1>;
		phys = <&u2phy1_otg>;
		phy-names = "usb";
		status = "disabled";
	};

	usb_host1_ohci: usb@300e0000 {
		compatible = "generic-ohci";
		reg = <0x300e0000 0x20000>;
		interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_HOST1>, <&u2phy1>;
		phys = <&u2phy1_otg>;
		phy-names = "usb";
		status = "disabled";
	};

	usb_host2_ehci: usb@30100000 {
		compatible = "generic-ehci";
		reg = <0x30100000 0x20000>;
		interrupts = <GIC_SPI 66 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_HOST2>, <&u2phy1>;
		phys = <&u2phy1_host>;
		phy-names = "usb";
		status = "disabled";
	};

	usb_host2_ohci: usb@30120000 {
		compatible = "generic-ohci";
		reg = <0x30120000 0x20000>;
		interrupts = <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_HOST2>, <&u2phy1>;
		phys = <&u2phy1_host>;
		phy-names = "usb";
		status = "disabled";
	};

	gmac: ethernet@30200000 {
		compatible = "rockchip,rk3228-gmac";
		reg = <0x30200000 0x10000>;
		interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "macirq";
		clocks = <&cru SCLK_MAC>, <&cru SCLK_MAC_RX>,
			<&cru SCLK_MAC_TX>, <&cru SCLK_MAC_REF>,
			<&cru SCLK_MAC_REFOUT>, <&cru ACLK_GMAC>,
			<&cru PCLK_GMAC>;
		clock-names = "stmmaceth", "mac_clk_rx",
			"mac_clk_tx", "clk_mac_ref",
			"clk_mac_refout", "aclk_mac",
			"pclk_mac";
		resets = <&cru SRST_GMAC>;
		reset-names = "stmmaceth";
		rockchip,grf = <&grf>;
		status = "disabled";
	};

	qos_iep: qos@31030080 {
		compatible = "rockchip,rk3228-qos", "syscon";
		reg = <0x31030080 0x20>;
	};

	qos_rga_w: qos@31030100 {
		compatible = "rockchip,rk3228-qos", "syscon";
		reg = <0x31030100 0x20>;
	};

	qos_hdcp: qos@31030180 {
		compatible = "rockchip,rk3228-qos", "syscon";
		reg = <0x31030180 0x20>;
	};

	qos_rga_r: qos@31030200 {
		compatible = "rockchip,rk3228-qos", "syscon";
		reg = <0x31030200 0x20>;
	};

	qos_vpu: qos@31040000 {
		compatible = "rockchip,rk3228-qos", "syscon";
		reg = <0x31040000 0x20>;
	};

	qos_gpu: qos@31050000 {
		compatible = "rockchip,rk3228-qos", "syscon";
		reg = <0x31050000 0x20>;
	};

	qos_vop: qos@31060000 {
		compatible = "rockchip,rk3228-qos", "syscon";
		reg = <0x31060000 0x20>;
	};

	qos_rkvdec_r: qos@31070000 {
		compatible = "rockchip,rk3228-qos", "syscon";
		reg = <0x31070000 0x20>;
	};

	qos_rkvdec_w: qos@31070080 {
		compatible = "rockchip,rk3228-qos", "syscon";
		reg = <0x31070080 0x20>;
	};

	gic: interrupt-controller@******** {
		compatible = "arm,gic-400";
		interrupt-controller;
		#interrupt-cells = <3>;
		#address-cells = <0>;

		reg = <0x32011000 0x1000>,
		      <0x32012000 0x2000>,
		      <0x32014000 0x2000>,
		      <0x32016000 0x2000>;
		interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
	};

	pinctrl: pinctrl {
		compatible = "rockchip,rk3228-pinctrl";
		rockchip,grf = <&grf>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		gpio0: gpio@******** {
			compatible = "rockchip,gpio-bank";
			reg = <0x******** 0x100>;
			interrupts = <GIC_SPI 51 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO0>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio1: gpio@******** {
			compatible = "rockchip,gpio-bank";
			reg = <0x******** 0x100>;
			interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO1>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio2: gpio@******** {
			compatible = "rockchip,gpio-bank";
			reg = <0x******** 0x100>;
			interrupts = <GIC_SPI 53 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO2>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio3: gpio@******** {
			compatible = "rockchip,gpio-bank";
			reg = <0x******** 0x100>;
			interrupts = <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO3>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		pcfg_pull_up: pcfg-pull-up {
			bias-pull-up;
		};

		pcfg_pull_down: pcfg-pull-down {
			bias-pull-down;
		};

		pcfg_pull_none: pcfg-pull-none {
			bias-disable;
		};

		pcfg_pull_none_drv_12ma: pcfg-pull-none-drv-12ma {
			drive-strength = <12>;
		};

		sdmmc {
			sdmmc_clk: sdmmc-clk {
				rockchip,pins = <1 RK_PC0 1 &pcfg_pull_none_drv_12ma>;
			};

			sdmmc_cmd: sdmmc-cmd {
				rockchip,pins = <1 RK_PB7 1 &pcfg_pull_none_drv_12ma>;
			};

			sdmmc_bus4: sdmmc-bus4 {
				rockchip,pins = <1 RK_PC2 1 &pcfg_pull_none_drv_12ma>,
						<1 RK_PC3 1 &pcfg_pull_none_drv_12ma>,
						<1 RK_PC4 1 &pcfg_pull_none_drv_12ma>,
						<1 RK_PC5 1 &pcfg_pull_none_drv_12ma>;
			};
		};

		sdio {
			sdio_clk: sdio-clk {
				rockchip,pins = <3 RK_PA0 1 &pcfg_pull_none_drv_12ma>;
			};

			sdio_cmd: sdio-cmd {
				rockchip,pins = <3 RK_PA1 1 &pcfg_pull_none_drv_12ma>;
			};

			sdio_bus4: sdio-bus4 {
				rockchip,pins = <3 RK_PA2 1 &pcfg_pull_none_drv_12ma>,
						<3 RK_PA3 1 &pcfg_pull_none_drv_12ma>,
						<3 RK_PA4 1 &pcfg_pull_none_drv_12ma>,
						<3 RK_PA5 1 &pcfg_pull_none_drv_12ma>;
			};
		};

		emmc {
			emmc_clk: emmc-clk {
				rockchip,pins = <2 RK_PA7 2 &pcfg_pull_none>;
			};

			emmc_cmd: emmc-cmd {
				rockchip,pins = <1 RK_PC6 2 &pcfg_pull_none>;
			};

			emmc_bus8: emmc-bus8 {
				rockchip,pins = <1 RK_PD0 2 &pcfg_pull_none>,
						<1 RK_PD1 2 &pcfg_pull_none>,
						<1 RK_PD2 2 &pcfg_pull_none>,
						<1 RK_PD3 2 &pcfg_pull_none>,
						<1 RK_PD4 2 &pcfg_pull_none>,
						<1 RK_PD5 2 &pcfg_pull_none>,
						<1 RK_PD6 2 &pcfg_pull_none>,
						<1 RK_PD7 2 &pcfg_pull_none>;
			};
		};

		gmac {
			rgmii_pins: rgmii-pins {
				rockchip,pins = <2 RK_PB6 1 &pcfg_pull_none>,
						<2 RK_PB4 1 &pcfg_pull_none>,
						<2 RK_PD1 1 &pcfg_pull_none>,
						<2 RK_PC3 1 &pcfg_pull_none_drv_12ma>,
						<2 RK_PC2 1 &pcfg_pull_none_drv_12ma>,
						<2 RK_PC6 1 &pcfg_pull_none_drv_12ma>,
						<2 RK_PC7 1 &pcfg_pull_none_drv_12ma>,
						<2 RK_PB1 1 &pcfg_pull_none_drv_12ma>,
						<2 RK_PB5 1 &pcfg_pull_none_drv_12ma>,
						<2 RK_PC1 1 &pcfg_pull_none>,
						<2 RK_PC0 1 &pcfg_pull_none>,
						<2 RK_PC5 2 &pcfg_pull_none>,
						<2 RK_PC4 2 &pcfg_pull_none>,
						<2 RK_PB3 1 &pcfg_pull_none>,
						<2 RK_PB0 1 &pcfg_pull_none>;
			};

			rmii_pins: rmii-pins {
				rockchip,pins = <2 RK_PB6 1 &pcfg_pull_none>,
						<2 RK_PB4 1 &pcfg_pull_none>,
						<2 RK_PD1 1 &pcfg_pull_none>,
						<2 RK_PC3 1 &pcfg_pull_none_drv_12ma>,
						<2 RK_PC2 1 &pcfg_pull_none_drv_12ma>,
						<2 RK_PB5 1 &pcfg_pull_none_drv_12ma>,
						<2 RK_PC1 1 &pcfg_pull_none>,
						<2 RK_PC0 1 &pcfg_pull_none>,
						<2 RK_PB0 1 &pcfg_pull_none>,
						<2 RK_PB7 1 &pcfg_pull_none>;
			};

			phy_pins: phy-pins {
				rockchip,pins = <2 RK_PB6 2 &pcfg_pull_none>,
						<2 RK_PB0 2 &pcfg_pull_none>;
			};
		};

		hdmi {
			hdmi_hpd: hdmi-hpd {
				rockchip,pins = <0 RK_PB7 1 &pcfg_pull_down>;
			};

			hdmii2c_xfer: hdmii2c-xfer {
				rockchip,pins = <0 RK_PA6 2 &pcfg_pull_none>,
						<0 RK_PA7 2 &pcfg_pull_none>;
			};

			hdmi_cec: hdmi-cec {
				rockchip,pins = <0 RK_PC4 1 &pcfg_pull_none>;
			};
		};

		i2c0 {
			i2c0_xfer: i2c0-xfer {
				rockchip,pins = <0 RK_PA0 1 &pcfg_pull_none>,
						<0 RK_PA1 1 &pcfg_pull_none>;
			};
		};

		i2c1 {
			i2c1_xfer: i2c1-xfer {
				rockchip,pins = <0 RK_PA2 1 &pcfg_pull_none>,
						<0 RK_PA3 1 &pcfg_pull_none>;
			};
		};

		i2c2 {
			i2c2_xfer: i2c2-xfer {
				rockchip,pins = <2 RK_PC4 1 &pcfg_pull_none>,
						<2 RK_PC5 1 &pcfg_pull_none>;
			};
		};

		i2c3 {
			i2c3_xfer: i2c3-xfer {
				rockchip,pins = <0 RK_PA6 1 &pcfg_pull_none>,
						<0 RK_PA7 1 &pcfg_pull_none>;
			};
		};

		spi0 {
			spi0_clk: spi0-clk {
				rockchip,pins = <0 RK_PB1 2 &pcfg_pull_up>;
			};
			spi0_cs0: spi0-cs0 {
				rockchip,pins = <0 RK_PB6 2 &pcfg_pull_up>;
			};
			spi0_tx: spi0-tx {
				rockchip,pins = <0 RK_PB3 2 &pcfg_pull_up>;
			};
			spi0_rx: spi0-rx {
				rockchip,pins = <0 RK_PB5 2 &pcfg_pull_up>;
			};
			spi0_cs1: spi0-cs1 {
				rockchip,pins = <1 RK_PB4 1 &pcfg_pull_up>;
			};
		};

		spi1 {
			spi1_clk: spi1-clk {
				rockchip,pins = <0 RK_PC7 2 &pcfg_pull_up>;
			};
			spi1_cs0: spi1-cs0 {
				rockchip,pins = <2 RK_PA2 2 &pcfg_pull_up>;
			};
			spi1_rx: spi1-rx {
				rockchip,pins = <2 RK_PA0 2 &pcfg_pull_up>;
			};
			spi1_tx: spi1-tx {
				rockchip,pins = <2 RK_PA1 2 &pcfg_pull_up>;
			};
			spi1_cs1: spi1-cs1 {
				rockchip,pins = <2 RK_PA3 2 &pcfg_pull_up>;
			};
		};

		i2s1 {
			i2s1_bus: i2s1-bus {
				rockchip,pins = <0 RK_PB0 1 &pcfg_pull_none>,
						<0 RK_PB1 1 &pcfg_pull_none>,
						<0 RK_PB3 1 &pcfg_pull_none>,
						<0 RK_PB4 1 &pcfg_pull_none>,
						<0 RK_PB5 1 &pcfg_pull_none>,
						<0 RK_PB6 1 &pcfg_pull_none>,
						<1 RK_PA2 2 &pcfg_pull_none>,
						<1 RK_PA4 2 &pcfg_pull_none>,
						<1 RK_PA5 2 &pcfg_pull_none>;
			};
		};

		pwm0 {
			pwm0_pin: pwm0-pin {
				rockchip,pins = <3 RK_PC5 1 &pcfg_pull_none>;
			};
		};

		pwm1 {
			pwm1_pin: pwm1-pin {
				rockchip,pins = <0 RK_PD6 2 &pcfg_pull_none>;
			};
		};

		pwm2 {
			pwm2_pin: pwm2-pin {
				rockchip,pins = <1 RK_PB4 2 &pcfg_pull_none>;
			};
		};

		pwm3 {
			pwm3_pin: pwm3-pin {
				rockchip,pins = <1 RK_PB3 2 &pcfg_pull_none>;
			};
		};

		spdif {
			spdif_tx: spdif-tx {
				rockchip,pins = <3 RK_PD7 2 &pcfg_pull_none>;
			};
		};

		tsadc {
			otp_pin: otp-pin {
				rockchip,pins = <0 RK_PD0 RK_FUNC_GPIO &pcfg_pull_none>;
			};

			otp_out: otp-out {
				rockchip,pins = <0 RK_PD0 2 &pcfg_pull_none>;
			};
		};

		uart0 {
			uart0_xfer: uart0-xfer {
				rockchip,pins = <2 RK_PD2 1 &pcfg_pull_none>,
						<2 RK_PD3 1 &pcfg_pull_none>;
			};

			uart0_cts: uart0-cts {
				rockchip,pins = <2 RK_PD5 1 &pcfg_pull_none>;
			};

			uart0_rts: uart0-rts {
				rockchip,pins = <0 RK_PC1 1 &pcfg_pull_none>;
			};
		};

		uart1 {
			uart1_xfer: uart1-xfer {
				rockchip,pins = <1 RK_PB1 1 &pcfg_pull_none>,
						<1 RK_PB2 1 &pcfg_pull_none>;
			};

			uart1_cts: uart1-cts {
				rockchip,pins = <1 RK_PB0 1 &pcfg_pull_none>;
			};

			uart1_rts: uart1-rts {
				rockchip,pins = <1 RK_PB3 1 &pcfg_pull_none>;
			};
		};

		uart2 {
			uart2_xfer: uart2-xfer {
				rockchip,pins = <1 RK_PC2 2 &pcfg_pull_up>,
						<1 RK_PC3 2 &pcfg_pull_none>;
			};

			uart21_xfer: uart21-xfer {
				rockchip,pins = <1 RK_PB2 2 &pcfg_pull_up>,
						<1 RK_PB1 2 &pcfg_pull_none>;
			};

			uart2_cts: uart2-cts {
				rockchip,pins = <0 RK_PD1 1 &pcfg_pull_none>;
			};

			uart2_rts: uart2-rts {
				rockchip,pins = <0 RK_PD0 1 &pcfg_pull_none>;
			};
		};
	};
};
