// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Device tree file for Firefly Rockchip RK3288 Core board
 * Copyright (c) 2016 <PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "rk3288-firefly-reload-core.dtsi"

/ {
	model = "Firefly-RK3288-reload";
	compatible = "firefly,firefly-rk3288-reload", "rockchip,rk3288";

	adc-keys {
		compatible = "adc-keys";
		io-channels = <&saradc 1>;
		io-channel-names = "buttons";
		keyup-threshold-microvolt = <1800000>;

		button-recovery {
			label = "Recovery";
			linux,code = <KEY_VENDOR>;
			press-threshold-microvolt = <0>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-power {
			wakeup-source;
			gpios = <&gpio0 RK_PA5 GPIO_ACTIVE_LOW>;
			label = "GPIO Power";
			linux,code = <KEY_POWER>;
			pinctrl-names = "default";
			pinctrl-0 = <&pwr_key>;
		};
	};

	ir-receiver {
		compatible = "gpio-ir-receiver";
		gpios = <&gpio7 RK_PA0 GPIO_ACTIVE_LOW>;
	};

	leds {
		compatible = "gpio-leds";

		power_led: led-0 {
			gpios = <&gpio8 RK_PA2 GPIO_ACTIVE_LOW>;
			label = "firefly:blue:power";
			pinctrl-names = "default";
			pinctrl-0 = <&power_led_pin>;
			panic-indicator;
		};

		work_led: led-1 {
			gpios = <&gpio8 RK_PA1 GPIO_ACTIVE_LOW>;
			label = "firefly:blue:user";
			linux,default-trigger = "rc-feedback";
			pinctrl-names = "default";
			pinctrl-0 = <&work_led_pin>;
		};
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		clocks = <&hym8563>;
		clock-names = "ext_clock";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_enable>;
		reset-gpios = <&gpio4 RK_PD4 GPIO_ACTIVE_LOW>;
	};

	sound {
		compatible = "simple-audio-card";
		simple-audio-card,name = "SPDIF";
		simple-audio-card,dai-link@1 {  /* S/PDIF - S/PDIF */
			cpu { sound-dai = <&spdif>; };
			codec { sound-dai = <&spdif_out>; };
		};
	};

	spdif_out: spdif-out {
		compatible = "linux,spdif-dit";
		#sound-dai-cells = <0>;
	};

	vcc_host_5v: usb-host-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB6 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&host_vbus_drv>;
		regulator-name = "vcc_host_5v";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		vin-supply = <&vcc_5v>;
	};

	vcc_5v: vcc_sys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc_5v";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	vcc_sd: sdmmc-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio7 RK_PB3 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdmmc_pwr>;
		regulator-name = "vcc_sd";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc_io>;
	};

	vcc_otg_5v: usb-otg-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB4 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&otg_vbus_drv>;
		regulator-name = "vcc_otg_5v";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		vin-supply = <&vcc_5v>;
	};

	dovdd_1v8: dovdd-1v8-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB3 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&dvp_pwr>;
		regulator-name = "dovdd_1v8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&vcc_io>;
	};

	vcc28_dvp: vcc28-dvp-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB3 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&dvp_pwr>;
		regulator-name = "vcc28_dvp";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		vin-supply = <&vcc_io>;
	};

	af_28: af_28-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB3 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&dvp_pwr>;
		regulator-name = "af_28";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		vin-supply = <&vcc_io>;
	};

	dvdd_1v2: af_28-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio7 RK_PB4 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&cif_pwr>;
		regulator-name = "dvdd_1v2";
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		vin-supply = <&vcc_io>;
	};

	vbat_wl: wifi-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vbat_wl";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&vcc_io>;
	};
};

&hdmi {
	ddc-i2c-bus = <&i2c5>;
	pinctrl-names = "default";
	pinctrl-0 = <&hdmi_cec_c0>;
	status = "okay";
};

&i2c0 {
	hym8563: hym8563@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-frequency = <32768>;
		clock-output-names = "xin32k";
		interrupt-parent = <&gpio7>;
		interrupts = <RK_PA4 IRQ_TYPE_EDGE_FALLING>;
		pinctrl-names = "default";
		pinctrl-0 = <&rtc_int>;
	};
};

&i2c2 {
	status = "okay";

	codec: es8328@10 {
		compatible = "everest,es8328";
		DVDD-supply = <&vcca_33>;
		AVDD-supply = <&vcca_33>;
		PVDD-supply = <&vcca_33>;
		HPVDD-supply = <&vcca_33>;
		clocks = <&cru HCLK_I2S0>, <&cru SCLK_I2S0>;
		clock-names = "i2s_hclk", "i2s_clk";
		reg = <0x10>;
	};
};

&i2c5 {
	status = "okay";
};

&i2s {
	status = "okay";
};

&saradc {
	vref-supply = <&vcc_18>;
	status = "okay";
};

&sdmmc {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	disable-wp;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk>, <&sdmmc_cmd>, <&sdmmc_cd>, <&sdmmc_bus4>;
	vmmc-supply = <&vcc_sd>;
	vqmmc-supply = <&vccio_sd>;
	status = "okay";
};

&sdio0 {
	bus-width = <4>;
	cap-sd-highspeed;
	cap-sdio-irq;
	mmc-pwrseq = <&sdio_pwrseq>;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&sdio0_bus4>, <&sdio0_cmd>, <&sdio0_clk>, <&sdio0_int>;
	sd-uhs-sdr12;
	sd-uhs-sdr25;
	sd-uhs-sdr50;
	sd-uhs-ddr50;
	vmmc-supply = <&vbat_wl>;
	vqmmc-supply = <&vccio_wl>;
	status = "okay";
};

&spdif {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_xfer>, <&uart0_cts>, <&uart0_rts>;
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&usb_host1 {
	pinctrl-names = "default";
	pinctrl-0 = <&usbhub_rst>;
	status = "okay";
};

&usb_otg {
	status = "okay";
};

&pinctrl {
	ir {
		ir_int: ir-int {
			rockchip,pins = <7 RK_PA0 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	dvp {
		dvp_pwr: dvp-pwr {
			rockchip,pins = <0 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		cif_pwr: cif-pwr {
			rockchip,pins = <7 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	hym8563 {
		rtc_int: rtc-int {
			rockchip,pins = <7 RK_PA4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	keys {
		pwr_key: pwr-key {
			rockchip,pins = <0 RK_PA5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	leds {
		power_led_pin: power-led-pin {
			rockchip,pins = <8 RK_PA2 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		work_led_pin: work-led-pin {
			rockchip,pins = <8 RK_PA1 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	sdmmc {
		/*
		 * Default drive strength isn't enough to achieve even
		 * high-speed mode on firefly board so bump up to 12ma.
		 */
		sdmmc_bus4: sdmmc-bus4 {
			rockchip,pins = <6 RK_PC0 1 &pcfg_pull_up_drv_12ma>,
					<6 RK_PC1 1 &pcfg_pull_up_drv_12ma>,
					<6 RK_PC2 1 &pcfg_pull_up_drv_12ma>,
					<6 RK_PC3 1 &pcfg_pull_up_drv_12ma>;
		};

		sdmmc_clk: sdmmc-clk {
			rockchip,pins = <6 RK_PC4 1 &pcfg_pull_none_12ma>;
		};

		sdmmc_cmd: sdmmc-cmd {
			rockchip,pins = <6 RK_PC5 1 &pcfg_pull_up_drv_12ma>;
		};

		sdmmc_pwr: sdmmc-pwr {
			rockchip,pins = <7 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	sdio {
		wifi_enable: wifi-enable {
			rockchip,pins = <4 RK_PD4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb_host {
		host_vbus_drv: host-vbus-drv {
			rockchip,pins = <0 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		usbhub_rst: usbhub-rst {
			rockchip,pins = <8 RK_PA3 RK_FUNC_GPIO &pcfg_output_high>;
		};
	};

	usb_otg {
		otg_vbus_drv: otg-vbus-drv {
			rockchip,pins = <0 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};
