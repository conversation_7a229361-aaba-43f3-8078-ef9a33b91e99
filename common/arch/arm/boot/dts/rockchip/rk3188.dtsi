// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2013 MundoReader S.L.
 * Author: <PERSON><PERSON> <<EMAIL>>
 */

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pinctrl/rockchip.h>
#include <dt-bindings/clock/rk3188-cru.h>
#include <dt-bindings/power/rk3188-power.h>
#include "rk3xxx.dtsi"

/ {
	compatible = "rockchip,rk3188";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		enable-method = "rockchip,rk3066-smp";

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <0x0>;
			clock-latency = <40000>;
			clocks = <&cru ARMCLK>;
			operating-points-v2 = <&cpu0_opp_table>;
			resets = <&cru SRST_CORE0>;
		};
		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <0x1>;
			operating-points-v2 = <&cpu0_opp_table>;
			resets = <&cru SRST_CORE1>;
		};
		cpu2: cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <0x2>;
			operating-points-v2 = <&cpu0_opp_table>;
			resets = <&cru SRST_CORE2>;
		};
		cpu3: cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <0x3>;
			operating-points-v2 = <&cpu0_opp_table>;
			resets = <&cru SRST_CORE3>;
		};
	};

	cpu0_opp_table: opp-table-0 {
		compatible = "operating-points-v2";
		opp-shared;

		opp-312000000 {
			opp-hz = /bits/ 64 <312000000>;
			opp-microvolt = <875000>;
			clock-latency-ns = <40000>;
		};
		opp-504000000 {
			opp-hz = /bits/ 64 <504000000>;
			opp-microvolt = <925000>;
		};
		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <950000>;
			opp-suspend;
		};
		opp-816000000 {
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <975000>;
		};
		opp-1008000000 {
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <1075000>;
		};
		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <1150000>;
		};
		opp-1416000000 {
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <1250000>;
		};
		opp-1608000000 {
			opp-hz = /bits/ 64 <1608000000>;
			opp-microvolt = <1350000>;
		};
	};

	display-subsystem {
		compatible = "rockchip,display-subsystem";
		ports = <&vop0_out>, <&vop1_out>;
	};

	sram: sram@10080000 {
		compatible = "mmio-sram";
		reg = <0x10080000 0x8000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x10080000 0x8000>;

		smp-sram@0 {
			compatible = "rockchip,rk3066-smp-sram";
			reg = <0x0 0x50>;
		};
	};

	vop0: vop@1010c000 {
		compatible = "rockchip,rk3188-vop";
		reg = <0x1010c000 0x1000>;
		interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_LCDC0>, <&cru DCLK_LCDC0>, <&cru HCLK_LCDC0>;
		clock-names = "aclk_vop", "dclk_vop", "hclk_vop";
		power-domains = <&power RK3188_PD_VIO>;
		resets = <&cru SRST_LCDC0_AXI>, <&cru SRST_LCDC0_AHB>, <&cru SRST_LCDC0_DCLK>;
		reset-names = "axi", "ahb", "dclk";
		status = "disabled";

		vop0_out: port {
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};

	vop1: vop@1010e000 {
		compatible = "rockchip,rk3188-vop";
		reg = <0x1010e000 0x1000>;
		interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_LCDC1>, <&cru DCLK_LCDC1>, <&cru HCLK_LCDC1>;
		clock-names = "aclk_vop", "dclk_vop", "hclk_vop";
		power-domains = <&power RK3188_PD_VIO>;
		resets = <&cru SRST_LCDC1_AXI>, <&cru SRST_LCDC1_AHB>, <&cru SRST_LCDC1_DCLK>;
		reset-names = "axi", "ahb", "dclk";
		status = "disabled";

		vop1_out: port {
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};

	timer3: timer@2000e000 {
		compatible = "rockchip,rk3188-timer", "rockchip,rk3288-timer";
		reg = <0x2000e000 0x20>;
		interrupts = <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_TIMER3>, <&cru SCLK_TIMER3>;
		clock-names = "pclk", "timer";
	};

	timer6: timer@200380a0 {
		compatible = "rockchip,rk3188-timer", "rockchip,rk3288-timer";
		reg = <0x200380a0 0x20>;
		interrupts = <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_TIMER0>, <&cru SCLK_TIMER6>;
		clock-names = "pclk", "timer";
	};

	i2s0: i2s@1011a000 {
		compatible = "rockchip,rk3188-i2s", "rockchip,rk3066-i2s";
		reg = <0x1011a000 0x2000>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2s0_bus>;
		clocks = <&cru SCLK_I2S0>, <&cru HCLK_I2S0>;
		clock-names = "i2s_clk", "i2s_hclk";
		dmas = <&dmac1_s 6>, <&dmac1_s 7>;
		dma-names = "tx", "rx";
		rockchip,playback-channels = <2>;
		rockchip,capture-channels = <2>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	spdif: sound@1011e000 {
		compatible = "rockchip,rk3188-spdif", "rockchip,rk3066-spdif";
		reg = <0x1011e000 0x2000>;
		#sound-dai-cells = <0>;
		clocks = <&cru SCLK_SPDIF>, <&cru HCLK_SPDIF>;
		clock-names = "mclk", "hclk";
		dmas = <&dmac1_s 8>;
		dma-names = "tx";
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&spdif_tx>;
		status = "disabled";
	};

	cru: clock-controller@20000000 {
		compatible = "rockchip,rk3188-cru";
		reg = <0x20000000 0x1000>;
		clocks = <&xin24m>;
		clock-names = "xin24m";
		rockchip,grf = <&grf>;
		#clock-cells = <1>;
		#reset-cells = <1>;
	};

	efuse: efuse@******** {
		compatible = "rockchip,rk3188-efuse";
		reg = <0x******** 0x4000>;
		#address-cells = <1>;
		#size-cells = <1>;
		clocks = <&cru PCLK_EFUSE>;
		clock-names = "pclk_efuse";

		cpu_leakage: cpu_leakage@17 {
			reg = <0x17 0x1>;
		};
	};

	pinctrl: pinctrl {
		compatible = "rockchip,rk3188-pinctrl";
		rockchip,grf = <&grf>;
		rockchip,pmu = <&pmu>;

		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		gpio0: gpio@2000a000 {
			compatible = "rockchip,rk3188-gpio-bank0";
			reg = <0x2000a000 0x100>;
			interrupts = <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO0>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio1: gpio@2003c000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x2003c000 0x100>;
			interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO1>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio2: gpio@2003e000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x2003e000 0x100>;
			interrupts = <GIC_SPI 56 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO2>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio3: gpio@******** {
			compatible = "rockchip,gpio-bank";
			reg = <0x******** 0x100>;
			interrupts = <GIC_SPI 57 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO3>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		pcfg_pull_up: pcfg-pull-up {
			bias-pull-up;
		};

		pcfg_pull_down: pcfg-pull-down {
			bias-pull-down;
		};

		pcfg_pull_none: pcfg-pull-none {
			bias-disable;
		};

		emmc {
			emmc_clk: emmc-clk {
				rockchip,pins = <0 RK_PD0 2 &pcfg_pull_none>;
			};

			emmc_cmd: emmc-cmd {
				rockchip,pins = <0 RK_PD2 2 &pcfg_pull_up>;
			};

			emmc_rst: emmc-rst {
				rockchip,pins = <0 RK_PD3 2 &pcfg_pull_none>;
			};

			/*
			 * The data pins are shared between nandc and emmc and
			 * not accessible through pinctrl. Also they should've
			 * been already set correctly by firmware, as
			 * flash/emmc is the boot-device.
			 */
		};

		emac {
			emac_xfer: emac-xfer {
				rockchip,pins = <3 RK_PC0 2 &pcfg_pull_none>, /* tx_en */
						<3 RK_PC1 2 &pcfg_pull_none>, /* txd1 */
						<3 RK_PC2 2 &pcfg_pull_none>, /* txd0 */
						<3 RK_PC3 2 &pcfg_pull_none>, /* rxd0 */
						<3 RK_PC4 2 &pcfg_pull_none>, /* rxd1 */
						<3 RK_PC5 2 &pcfg_pull_none>, /* mac_clk */
						<3 RK_PC6 2 &pcfg_pull_none>, /* rx_err */
						<3 RK_PC7 2 &pcfg_pull_none>; /* crs_dvalid */
			};

			emac_mdio: emac-mdio {
				rockchip,pins = <3 RK_PD0 2 &pcfg_pull_none>,
						<3 RK_PD1 2 &pcfg_pull_none>;
			};
		};

		i2c0 {
			i2c0_xfer: i2c0-xfer {
				rockchip,pins = <1 RK_PD0 1 &pcfg_pull_none>,
						<1 RK_PD1 1 &pcfg_pull_none>;
			};
		};

		i2c1 {
			i2c1_xfer: i2c1-xfer {
				rockchip,pins = <1 RK_PD2 1 &pcfg_pull_none>,
						<1 RK_PD3 1 &pcfg_pull_none>;
			};
		};

		i2c2 {
			i2c2_xfer: i2c2-xfer {
				rockchip,pins = <1 RK_PD4 1 &pcfg_pull_none>,
						<1 RK_PD5 1 &pcfg_pull_none>;
			};
		};

		i2c3 {
			i2c3_xfer: i2c3-xfer {
				rockchip,pins = <3 RK_PB6 2 &pcfg_pull_none>,
						<3 RK_PB7 2 &pcfg_pull_none>;
			};
		};

		i2c4 {
			i2c4_xfer: i2c4-xfer {
				rockchip,pins = <1 RK_PD6 1 &pcfg_pull_none>,
						<1 RK_PD7 1 &pcfg_pull_none>;
			};
		};

		lcdc1 {
			lcdc1_dclk: lcdc1-dclk {
				rockchip,pins = <2 RK_PD0 1 &pcfg_pull_none>;
			};

			lcdc1_den: lcdc1-den {
				rockchip,pins = <2 RK_PD1 1 &pcfg_pull_none>;
			};

			lcdc1_hsync: lcdc1-hsync {
				rockchip,pins = <2 RK_PD2 1 &pcfg_pull_none>;
			};

			lcdc1_vsync: lcdc1-vsync {
				rockchip,pins = <2 RK_PD3 1 &pcfg_pull_none>;
			};

			lcdc1_rgb24: lcdc1-rgb24 {
				rockchip,pins = <2 RK_PA0 1 &pcfg_pull_none>,
						<2 RK_PA1 1 &pcfg_pull_none>,
						<2 RK_PA2 1 &pcfg_pull_none>,
						<2 RK_PA3 1 &pcfg_pull_none>,
						<2 RK_PA4 1 &pcfg_pull_none>,
						<2 RK_PA5 1 &pcfg_pull_none>,
						<2 RK_PA6 1 &pcfg_pull_none>,
						<2 RK_PA7 1 &pcfg_pull_none>,
						<2 RK_PB0 1 &pcfg_pull_none>,
						<2 RK_PB1 1 &pcfg_pull_none>,
						<2 RK_PB2 1 &pcfg_pull_none>,
						<2 RK_PB3 1 &pcfg_pull_none>,
						<2 RK_PB4 1 &pcfg_pull_none>,
						<2 RK_PB5 1 &pcfg_pull_none>,
						<2 RK_PB6 1 &pcfg_pull_none>,
						<2 RK_PB7 1 &pcfg_pull_none>,
						<2 RK_PC0 1 &pcfg_pull_none>,
						<2 RK_PC1 1 &pcfg_pull_none>,
						<2 RK_PC2 1 &pcfg_pull_none>,
						<2 RK_PC3 1 &pcfg_pull_none>,
						<2 RK_PC4 1 &pcfg_pull_none>,
						<2 RK_PC5 1 &pcfg_pull_none>,
						<2 RK_PC6 1 &pcfg_pull_none>,
						<2 RK_PC7 1 &pcfg_pull_none>;
			};
		};

		pwm0 {
			pwm0_out: pwm0-out {
				rockchip,pins = <3 RK_PD3 1 &pcfg_pull_none>;
			};
		};

		pwm1 {
			pwm1_out: pwm1-out {
				rockchip,pins = <3 RK_PD4 1 &pcfg_pull_none>;
			};
		};

		pwm2 {
			pwm2_out: pwm2-out {
				rockchip,pins = <3 RK_PD5 1 &pcfg_pull_none>;
			};
		};

		pwm3 {
			pwm3_out: pwm3-out {
				rockchip,pins = <3 RK_PD6 1 &pcfg_pull_none>;
			};
		};

		spi0 {
			spi0_clk: spi0-clk {
				rockchip,pins = <1 RK_PA6 2 &pcfg_pull_up>;
			};
			spi0_cs0: spi0-cs0 {
				rockchip,pins = <1 RK_PA7 2 &pcfg_pull_up>;
			};
			spi0_tx: spi0-tx {
				rockchip,pins = <1 RK_PA5 2 &pcfg_pull_up>;
			};
			spi0_rx: spi0-rx {
				rockchip,pins = <1 RK_PA4 2 &pcfg_pull_up>;
			};
			spi0_cs1: spi0-cs1 {
				rockchip,pins = <1 RK_PB7 1 &pcfg_pull_up>;
			};
		};

		spi1 {
			spi1_clk: spi1-clk {
				rockchip,pins = <0 RK_PD6 1 &pcfg_pull_up>;
			};
			spi1_cs0: spi1-cs0 {
				rockchip,pins = <0 RK_PD7 1 &pcfg_pull_up>;
			};
			spi1_rx: spi1-rx {
				rockchip,pins = <0 RK_PD4 1 &pcfg_pull_up>;
			};
			spi1_tx: spi1-tx {
				rockchip,pins = <0 RK_PD5 1 &pcfg_pull_up>;
			};
			spi1_cs1: spi1-cs1 {
				rockchip,pins = <1 RK_PB6 2 &pcfg_pull_up>;
			};
		};

		uart0 {
			uart0_xfer: uart0-xfer {
				rockchip,pins = <1 RK_PA0 1 &pcfg_pull_up>,
						<1 RK_PA1 1 &pcfg_pull_none>;
			};

			uart0_cts: uart0-cts {
				rockchip,pins = <1 RK_PA2 1 &pcfg_pull_none>;
			};

			uart0_rts: uart0-rts {
				rockchip,pins = <1 RK_PA3 1 &pcfg_pull_none>;
			};
		};

		uart1 {
			uart1_xfer: uart1-xfer {
				rockchip,pins = <1 RK_PA4 1 &pcfg_pull_up>,
						<1 RK_PA5 1 &pcfg_pull_none>;
			};

			uart1_cts: uart1-cts {
				rockchip,pins = <1 RK_PA6 1 &pcfg_pull_none>;
			};

			uart1_rts: uart1-rts {
				rockchip,pins = <1 RK_PA7 1 &pcfg_pull_none>;
			};
		};

		uart2 {
			uart2_xfer: uart2-xfer {
				rockchip,pins = <1 RK_PB0 1 &pcfg_pull_up>,
						<1 RK_PB1 1 &pcfg_pull_none>;
			};
			/* no rts / cts for uart2 */
		};

		uart3 {
			uart3_xfer: uart3-xfer {
				rockchip,pins = <1 RK_PB2 1 &pcfg_pull_up>,
						<1 RK_PB3 1 &pcfg_pull_none>;
			};

			uart3_cts: uart3-cts {
				rockchip,pins = <1 RK_PB4 1 &pcfg_pull_none>;
			};

			uart3_rts: uart3-rts {
				rockchip,pins = <1 RK_PB5 1 &pcfg_pull_none>;
			};
		};

		sd0 {
			sd0_clk: sd0-clk {
				rockchip,pins = <3 RK_PA2 1 &pcfg_pull_none>;
			};

			sd0_cmd: sd0-cmd {
				rockchip,pins = <3 RK_PA3 1 &pcfg_pull_none>;
			};

			sd0_cd: sd0-cd {
				rockchip,pins = <3 RK_PB0 1 &pcfg_pull_none>;
			};

			sd0_wp: sd0-wp {
				rockchip,pins = <3 RK_PB1 1 &pcfg_pull_none>;
			};

			sd0_pwr: sd0-pwr {
				rockchip,pins = <3 RK_PA1 1 &pcfg_pull_none>;
			};

			sd0_bus1: sd0-bus-width1 {
				rockchip,pins = <3 RK_PA4 1 &pcfg_pull_none>;
			};

			sd0_bus4: sd0-bus-width4 {
				rockchip,pins = <3 RK_PA4 1 &pcfg_pull_none>,
						<3 RK_PA5 1 &pcfg_pull_none>,
						<3 RK_PA6 1 &pcfg_pull_none>,
						<3 RK_PA7 1 &pcfg_pull_none>;
			};
		};

		sd1 {
			sd1_clk: sd1-clk {
				rockchip,pins = <3 RK_PC5 1 &pcfg_pull_none>;
			};

			sd1_cmd: sd1-cmd {
				rockchip,pins = <3 RK_PC0 1 &pcfg_pull_none>;
			};

			sd1_cd: sd1-cd {
				rockchip,pins = <3 RK_PC6 1 &pcfg_pull_none>;
			};

			sd1_wp: sd1-wp {
				rockchip,pins = <3 RK_PC7 1 &pcfg_pull_none>;
			};

			sd1_bus1: sd1-bus-width1 {
				rockchip,pins = <3 RK_PC1 1 &pcfg_pull_none>;
			};

			sd1_bus4: sd1-bus-width4 {
				rockchip,pins = <3 RK_PC1 1 &pcfg_pull_none>,
						<3 RK_PC2 1 &pcfg_pull_none>,
						<3 RK_PC3 1 &pcfg_pull_none>,
						<3 RK_PC4 1 &pcfg_pull_none>;
			};
		};

		i2s0 {
			i2s0_bus: i2s0-bus {
				rockchip,pins = <1 RK_PC0 1 &pcfg_pull_none>,
						<1 RK_PC1 1 &pcfg_pull_none>,
						<1 RK_PC2 1 &pcfg_pull_none>,
						<1 RK_PC3 1 &pcfg_pull_none>,
						<1 RK_PC4 1 &pcfg_pull_none>,
						<1 RK_PC5 1 &pcfg_pull_none>;
			};
		};

		spdif {
			spdif_tx: spdif-tx {
				rockchip,pins = <1 RK_PB6 1 &pcfg_pull_none>;
			};
		};
	};
};

&emac {
	compatible = "rockchip,rk3188-emac";
};

&global_timer {
	interrupts = <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_EDGE_RISING)>;
};

&local_timer {
	interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_EDGE_RISING)>;
};

&gpu {
	compatible = "rockchip,rk3188-mali", "arm,mali-400";
	interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
	interrupt-names = "gp",
			  "gpmmu",
			  "pp0",
			  "ppmmu0",
			  "pp1",
			  "ppmmu1",
			  "pp2",
			  "ppmmu2",
			  "pp3",
			  "ppmmu3";
	power-domains = <&power RK3188_PD_GPU>;
};

&grf {
	compatible = "rockchip,rk3188-grf", "syscon", "simple-mfd";

	io_domains: io-domains {
		compatible = "rockchip,rk3188-io-voltage-domain";
		status = "disabled";
	};

	usbphy: usbphy {
		compatible = "rockchip,rk3188-usb-phy";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";

		usbphy0: usb-phy@10c {
			reg = <0x10c>;
			clocks = <&cru SCLK_OTGPHY0>;
			clock-names = "phyclk";
			#clock-cells = <0>;
			#phy-cells = <0>;
		};

		usbphy1: usb-phy@11c {
			reg = <0x11c>;
			clocks = <&cru SCLK_OTGPHY1>;
			clock-names = "phyclk";
			#clock-cells = <0>;
			#phy-cells = <0>;
		};
	};
};

&i2c0 {
	compatible = "rockchip,rk3188-i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c0_xfer>;
};

&i2c1 {
	compatible = "rockchip,rk3188-i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_xfer>;
};

&i2c2 {
	compatible = "rockchip,rk3188-i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_xfer>;
};

&i2c3 {
	compatible = "rockchip,rk3188-i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_xfer>;
};

&i2c4 {
	compatible = "rockchip,rk3188-i2c";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4_xfer>;
};

&pmu {
	power: power-controller {
		compatible = "rockchip,rk3188-power-controller";
		#power-domain-cells = <1>;
		#address-cells = <1>;
		#size-cells = <0>;

		power-domain@RK3188_PD_VIO {
			reg = <RK3188_PD_VIO>;
			clocks = <&cru ACLK_LCDC0>,
				 <&cru ACLK_LCDC1>,
				 <&cru DCLK_LCDC0>,
				 <&cru DCLK_LCDC1>,
				 <&cru HCLK_LCDC0>,
				 <&cru HCLK_LCDC1>,
				 <&cru SCLK_CIF0>,
				 <&cru ACLK_CIF0>,
				 <&cru HCLK_CIF0>,
				 <&cru ACLK_IPP>,
				 <&cru HCLK_IPP>,
				 <&cru ACLK_RGA>,
				 <&cru HCLK_RGA>;
			pm_qos = <&qos_lcdc0>,
				 <&qos_lcdc1>,
				 <&qos_cif0>,
				 <&qos_ipp>,
				 <&qos_rga>;
			#power-domain-cells = <0>;
		};

		power-domain@RK3188_PD_VIDEO {
			reg = <RK3188_PD_VIDEO>;
			clocks = <&cru ACLK_VDPU>,
				 <&cru ACLK_VEPU>,
				 <&cru HCLK_VDPU>,
				 <&cru HCLK_VEPU>;
			pm_qos = <&qos_vpu>;
			#power-domain-cells = <0>;
		};

		power-domain@RK3188_PD_GPU {
			reg = <RK3188_PD_GPU>;
			clocks = <&cru ACLK_GPU>;
			pm_qos = <&qos_gpu>;
			#power-domain-cells = <0>;
		};
	};
};

&pwm0 {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm0_out>;
};

&pwm1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm1_out>;
};

&pwm2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm2_out>;
};

&pwm3 {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm3_out>;
};

&spi0 {
	compatible = "rockchip,rk3188-spi", "rockchip,rk3066-spi";
	pinctrl-names = "default";
	pinctrl-0 = <&spi0_clk &spi0_tx &spi0_rx &spi0_cs0>;
};

&spi1 {
	compatible = "rockchip,rk3188-spi", "rockchip,rk3066-spi";
	pinctrl-names = "default";
	pinctrl-0 = <&spi1_clk &spi1_tx &spi1_rx &spi1_cs0>;
};

&uart0 {
	compatible = "rockchip,rk3188-uart", "snps,dw-apb-uart";
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_xfer>;
};

&uart1 {
	compatible = "rockchip,rk3188-uart", "snps,dw-apb-uart";
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_xfer>;
};

&uart2 {
	compatible = "rockchip,rk3188-uart", "snps,dw-apb-uart";
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_xfer>;
};

&uart3 {
	compatible = "rockchip,rk3188-uart", "snps,dw-apb-uart";
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_xfer>;
};

&vpu {
	compatible = "rockchip,rk3188-vpu", "rockchip,rk3066-vpu";
	power-domains = <&power RK3188_PD_VIDEO>;
};

&wdt {
	compatible = "rockchip,rk3188-wdt", "snps,dw-wdt";
};
