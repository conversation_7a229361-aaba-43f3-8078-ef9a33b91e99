// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2013 MundoReader S.L.
 * Author: <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include <dt-bindings/input/input.h>
#include "rk3066a.dtsi"

/ {
	model = "bq Curie 2";
	compatible = "mundoreader,bq-curie2", "rockchip,rk3066a";

	aliases {
		mmc0 = &mmc0;
		mmc1 = &mmc1;
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x40000000>;
	};

	vdd_log: vdd-log {
		compatible = "pwm-regulator";
		pwms = <&pwm3 0 1000>;
		regulator-name = "vdd_log";
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		regulator-always-on;
		voltage-table = <1000000 100>,
				<1200000 42>;
		status = "okay";
	};

	vcc_sd0: fixed-regulator {
		compatible = "regulator-fixed";
		regulator-name = "sdmmc-supply";
		regulator-min-microvolt = <3000000>;
		regulator-max-microvolt = <3000000>;
		gpio = <&gpio3 RK_PA7 GPIO_ACTIVE_LOW>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc_io>;
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;

		key-power {
			gpios = <&gpio6 RK_PA2 GPIO_ACTIVE_LOW>; /* GPIO6_A2 */
			linux,code = <KEY_POWER>;
			label = "GPIO Key Power";
			linux,input-type = <1>;
			wakeup-source;
			debounce-interval = <100>;
		};
		key-volume-down {
			gpios = <&gpio4 RK_PC5 GPIO_ACTIVE_LOW>; /* GPIO4_C5 */
			linux,code = <KEY_VOLUMEDOWN>;
			label = "GPIO Key Vol-";
			linux,input-type = <1>;
			debounce-interval = <100>;
		};
		/* VOL+ comes somehow thru the ADC */
	};
};

&cpu0 {
	cpu-supply = <&vdd_arm>;
};

&cpu1 {
	cpu-supply = <&vdd_arm>;
};

&i2c1 {
	status = "okay";
	clock-frequency = <400000>;

	tps: tps@2d {
		reg = <0x2d>;

		interrupt-parent = <&gpio6>;
		interrupts = <RK_PA6 IRQ_TYPE_LEVEL_LOW>;

		vcc5-supply = <&vcc_io>;
		vcc6-supply = <&vcc_io>;

		regulators {
			vcc_rtc: regulator@0 {
				regulator-name = "vcc_rtc";
				regulator-always-on;
			};

			vcc_io: regulator@1 {
				regulator-name = "vcc_io";
				regulator-always-on;
			};

			vdd_arm: regulator@2 {
				regulator-name = "vdd_arm";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <1500000>;
				regulator-boot-on;
				regulator-always-on;
			};

			vcc_ddr: regulator@3 {
				regulator-name = "vcc_ddr";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <1500000>;
				regulator-boot-on;
				regulator-always-on;
			};

			vcc18_cif: regulator@5 {
				regulator-name = "vcc18_cif";
				regulator-always-on;
			};

			vdd_11: regulator@6 {
				regulator-name = "vdd_11";
				regulator-always-on;
			};

			vcc_25: regulator@7 {
				regulator-name = "vcc_25";
				regulator-always-on;
			};

			vcc_18: regulator@8 {
				regulator-name = "vcc_18";
				regulator-always-on;
			};

			vcc25_hdmi: regulator@9 {
				regulator-name = "vcc25_hdmi";
				regulator-always-on;
			};

			vcca_33: regulator@10 {
				regulator-name = "vcca_33";
				regulator-always-on;
			};

			vcc_tp: regulator@11 {
				regulator-name = "vcc_tp";
				regulator-always-on;
			};

			vcc28_cif: regulator@12 {
				regulator-name = "vcc28_cif";
				regulator-always-on;
			};
		};
	};
};

/* must be included after &tps gets defined */
#include "../tps65910.dtsi"

&mmc0 { /* sdmmc */
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk>, <&sd0_cmd>, <&sd0_cd>, <&sd0_bus4>;
	vmmc-supply = <&vcc_sd0>;
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	disable-wp;
};

&mmc1 { /* wifi */
	status = "okay";
	non-removable;

	pinctrl-names = "default";
	pinctrl-0 = <&sd1_clk &sd1_cmd &sd1_bus4>;

	bus-width = <4>;
};

&pwm3 {
	status = "okay";
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&wdt {
	status = "okay";
};
