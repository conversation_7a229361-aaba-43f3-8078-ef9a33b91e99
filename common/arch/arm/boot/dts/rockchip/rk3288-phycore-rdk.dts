// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Device tree file for Phytec PCM-947 carrier board
 * Copyright (C) 2017 PHYTEC Messtechnik GmbH
 * Author: <PERSON><PERSON><PERSON> <w.<PERSON><PERSON>@phytec.de>
 */

/dts-v1/;

#include <dt-bindings/input/input.h>
#include <dt-bindings/leds/leds-pca9532.h>
#include "rk3288-phycore-som.dtsi"

/ {
	model = "Phytec RK3288 PCM-947";
	compatible = "phytec,rk3288-pcm-947", "phytec,rk3288-phycore-som", "rockchip,rk3288";

	user_buttons: user-buttons {
		compatible = "gpio-keys";
		pinctrl-names = "default";
		pinctrl-0 = <&user_button_pins>;

		button-0 {
			label = "home";
			linux,code = <KEY_HOME>;
			gpios = <&gpio8 0 GPIO_ACTIVE_HIGH>;
			wakeup-source;
		};

		button-1 {
			label = "menu";
			linux,code = <KEY_MENU>;
			gpios = <&gpio8 3 GPIO_ACTIVE_HIGH>;
			wakeup-source;
		};
	};

	vcc_host0_5v: usb-host0-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio2 13 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&host0_vbus_drv>;
		regulator-name = "vcc_host0_5v";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		vin-supply = <&vdd_in_otg_out>;
	};

	vcc_host1_5v: usb-host1-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio2 0 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&host1_vbus_drv>;
		regulator-name = "vcc_host1_5v";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		vin-supply = <&vdd_in_otg_out>;
	};

	vcc_otg_5v: usb-otg-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio2 12 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&otg_vbus_drv>;
		regulator-name = "vcc_otg_5v";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		vin-supply = <&vdd_in_otg_out>;
	};
};

&gmac {
	status = "okay";
};

&hdmi {
	status = "okay";
};

&i2c1 {
	status = "okay";

	touchscreen@44 {
		compatible = "st,stmpe811";
		reg = <0x44>;
	};

	adc@64 {
		compatible = "maxim,max1037";
		reg = <0x64>;
	};

	i2c_rtc: rtc@68 {
		compatible = "rv4162";
		reg = <0x68>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c_rtc_int>;
		interrupt-parent = <&gpio5>;
		interrupts = <10 0>;
	};
};

&i2c3 {
	status = "okay";

	i2c_eeprom_cb: eeprom@51 {
		compatible = "atmel,24c32";
		reg = <0x51>;
		pagesize = <32>;
	};
};

&i2c4 {
	status = "okay";

	/* PCA9533 - 4-bit LED dimmer */
	leddim: leddimmer@62 {
		compatible = "nxp,pca9533";
		reg = <0x62>;

		led1 {
			label = "red:user1";
			linux,default-trigger = "none";
			type = <PCA9532_TYPE_LED>;
		};

		led2 {
			label = "green:user2";
			linux,default-trigger = "none";
			type = <PCA9532_TYPE_LED>;
		};

		led3 {
			label = "blue:user3";
			linux,default-trigger = "none";
			type = <PCA9532_TYPE_LED>;
		};

		led4 {
			label = "red:user4";
			linux,default-trigger = "none";
			type = <PCA9532_TYPE_LED>;
		};
	};
};

&i2c5 {
	status = "okay";
};

&pinctrl {
	pcfg_pull_up_drv_12ma: pcfg-pull-up-drv-12ma {
		bias-pull-up;
		drive-strength = <12>;
	};

	buttons {
		user_button_pins: user-button-pins {
			/* button 1 */
			rockchip,pins = <8 RK_PA3 RK_FUNC_GPIO &pcfg_pull_up>,
			/* button 2 */
					<8 RK_PA0 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	rv4162 {
		i2c_rtc_int: i2c-rtc-int {
			rockchip,pins = <5 RK_PB2 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	sdmmc {
		/*
		 * Default drive strength isn't enough to achieve even
		 * high-speed mode on pcm-947 board so bump up to 12 mA.
		 */
		sdmmc_bus4: sdmmc-bus4 {
			rockchip,pins = <6 RK_PC0 1 &pcfg_pull_up_drv_12ma>,
					<6 RK_PC1 1 &pcfg_pull_up_drv_12ma>,
					<6 RK_PC2 1 &pcfg_pull_up_drv_12ma>,
					<6 RK_PC3 1 &pcfg_pull_up_drv_12ma>;
		};

		sdmmc_clk: sdmmc-clk {
			rockchip,pins = <6 RK_PC4 1 &pcfg_pull_none_12ma>;
		};

		sdmmc_cmd: sdmmc-cmd {
			rockchip,pins = <6 RK_PC5 1 &pcfg_pull_up_drv_12ma>;
		};

		sdmmc_pwr: sdmmc-pwr {
			rockchip,pins = <7 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	touchscreen {
		ts_irq_pin: ts-irq-pin {
			rockchip,pins = <5 RK_PB7 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb_host {
		host0_vbus_drv: host0-vbus-drv {
			rockchip,pins = <2 RK_PB5 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		host1_vbus_drv: host1-vbus-drv {
			rockchip,pins = <2 RK_PA0 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb_otg {
		otg_vbus_drv: otg-vbus-drv {
			rockchip,pins = <2 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&sdmmc {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	disable-wp;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk &sdmmc_cmd &sdmmc_cd &sdmmc_bus4>;
	sd-uhs-sdr12;
	sd-uhs-sdr25;
	sd-uhs-sdr50;
	sd-uhs-sdr104;
	vmmc-supply = <&vdd_sd>;
	vqmmc-supply = <&vdd_io_sd>;
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_xfer &uart0_cts &uart0_rts>;
	status = "okay";
};

&uart2 {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&usb_host0_ehci {
	status = "okay";
};

&usb_host1 {
	status = "okay";
};

&usb_otg {
	status = "okay";
};
