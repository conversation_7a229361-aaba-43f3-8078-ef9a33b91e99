// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2015 <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include <dt-bindings/input/input.h>
#include <dt-bindings/pwm/pwm.h>
#include "rk3288.dtsi"

/ {
	model = "Netxeon R89";
	compatible = "netxeon,r89", "rockchip,rk3288";

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x0 0x0 0x80000000>;
	};

	ext_gmac: external-gmac-clock {
		compatible = "fixed-clock";
		clock-frequency = <125000000>;
		clock-output-names = "ext_gmac";
		#clock-cells = <0>;
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;

		pinctrl-names = "default";
		pinctrl-0 = <&pwrbtn>;

		key-power {
			gpios = <&gpio0 RK_PA5 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			label = "GPIO Key Power";
			linux,input-type = <1>;
			wakeup-source;
			debounce-interval = <100>;
		};
	};

	ir: ir-receiver {
		compatible = "gpio-ir-receiver";
		gpios = <&gpio7 RK_PA0 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&ir_int>;
	};

	vcc_host: vcc-host-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB6 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&host_vbus_drv>;
		regulator-name = "vcc_host";
		regulator-always-on;
		regulator-boot-on;
	};

	vcc_otg: vcc-otg-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB4 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&otg_vbus_drv>;
		regulator-name = "vcc_otg";
		regulator-always-on;
		regulator-boot-on;
	};

	vcc_sdmmc: sdmmc-regulator {
		compatible = "regulator-fixed";
		regulator-name = "sdmmc-supply";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio7 RK_PB3 GPIO_ACTIVE_LOW>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc_io>;
	};

	vcc_sys: sys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "sys-supply";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};
};

&cpu0 {
	cpu-supply = <&vdd_cpu>;
};

&cpu1 {
	cpu-supply = <&vdd_cpu>;
};

&cpu2 {
	cpu-supply = <&vdd_cpu>;
};

&cpu3 {
	cpu-supply = <&vdd_cpu>;
};

&gmac {
	phy-supply = <&vcc_lan>;
	phy-mode = "rgmii";
	clock_in_out = "input";
	snps,reset-gpio = <&gpio4 RK_PA7 GPIO_ACTIVE_HIGH>;
	snps,reset-active-low;
	snps,reset-delays-us = <0 10000 1000000>;
	assigned-clocks = <&cru SCLK_MAC>;
	assigned-clock-parents = <&ext_gmac>;
	pinctrl-names = "default";
	pinctrl-0 = <&rgmii_pins>;
	tx_delay = <0x30>;
	rx_delay = <0x10>;
	status = "okay";
};

&hdmi {
	status = "okay";
};

&i2c0 {
	status = "okay";

	vdd_cpu: pmic@40 {
		compatible = "silergy,syr827";
		reg = <0x40>;
		fcs,suspend-voltage-selector = <1>;
		regulator-name = "VDD_CPU";
		regulator-enable-ramp-delay = <300>;
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <1350000>;
		regulator-ramp-delay = <8000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vcc_sys>;
	};

	vdd_gpu: pmic@41 {
		compatible = "silergy,syr828";
		reg = <0x41>;
		fcs,suspend-voltage-selector = <1>;
		regulator-name = "VDD_GPU";
		regulator-enable-ramp-delay = <300>;
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <1350000>;
		regulator-ramp-delay = <8000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vcc_sys>;
	};

	rtc@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-output-names = "xin32k";
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PA4 IRQ_TYPE_EDGE_FALLING>;
		pinctrl-names = "default";
		pinctrl-0 = <&pmic_int>;
	};

	act8846: pmic@5a {
		compatible = "active-semi,act8846";
		reg = <0x5a>;
		pinctrl-names = "default";
		pinctrl-0 = <&pmic_vsel>, <&pwr_hold>;
		system-power-controller;

		regulators {
			vcc_ddr: REG1 {
				regulator-name = "VCC_DDR";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			vcc_io: REG2 {
				regulator-name = "VCC_IO";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd_log: REG3 {
				regulator-name = "VDD_LOG";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vcc_20: REG4 {
				regulator-name = "VCC_20";
				regulator-min-microvolt = <2000000>;
				regulator-max-microvolt = <2000000>;
				regulator-always-on;
			};

			vccio_sd: REG5 {
				regulator-name = "VCCIO_SD";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd10_lcd: REG6 {
				regulator-name = "VDD10_LCD";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vcc_wl: REG7 {
				regulator-name = "VCC_WL";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vcca_33: REG8 {
				regulator-name = "VCCA_33";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vcc_lan: REG9 {
				regulator-name = "VCC_LAN";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd_10: REG10 {
				regulator-name = "VDD_10";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vcc_18: REG11 {
				regulator-name = "VCC_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			vcc18_lcd: REG12 {
				regulator-name = "VCC18_LCD";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};
		};
	};
};

&i2c5 {
	status = "okay";
};

&pinctrl {
	pcfg_output_high: pcfg-output-high {
		output-high;
	};

	pcfg_output_low: pcfg-output-low {
		output-low;
	};

	act8846 {
		pmic_vsel: pmic-vsel {
			rockchip,pins = <7 RK_PA1 RK_FUNC_GPIO &pcfg_output_low>;
		};

		pwr_hold: pwr-hold {
			rockchip,pins = <0 RK_PA6 RK_FUNC_GPIO &pcfg_output_high>;
		};
	};

	buttons {
		pwrbtn: pwrbtn {
			rockchip,pins = <0 RK_PA5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	ir {
		ir_int: ir-int {
			rockchip,pins = <7 RK_PA0 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	pmic {
		pmic_int: pmic-int {
			rockchip,pins = <0 RK_PA4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	usb {
		host_vbus_drv: host-vbus-drv {
			rockchip,pins = <0 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		otg_vbus_drv: otg-vbus-drv {
			rockchip,pins = <0 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&pwm0 {
	status = "okay";
};

&saradc {
	vref-supply = <&vcc_18>;
	status = "okay";
};

&sdmmc {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	disable-wp;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk &sdmmc_cmd &sdmmc_cd &sdmmc_bus4>;
	vmmc-supply = <&vcc_sdmmc>;
	vqmmc-supply = <&vccio_sd>;
	status = "okay";
};

&tsadc {
	rockchip,hw-tshut-mode = <0>;
	rockchip,hw-tshut-polarity = <0>;
	status = "okay";
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&uart4 {
	status = "okay";
};

&usb_host0_ehci {
	status = "okay";
};

&usb_host1 {
	status = "okay";
};

&usb_otg {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&vopb {
	status = "okay";
};

&vopb_mmu {
	status = "okay";
};

&vopl {
	status = "okay";
};

&vopl_mmu {
	status = "okay";
};

&wdt {
	status = "okay";
};
