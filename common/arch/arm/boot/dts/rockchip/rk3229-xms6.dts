// SPDX-License-Identifier: (GPL-2.0+ OR MIT)

/dts-v1/;

#include <dt-bindings/input/input.h>
#include "rk3229.dtsi"

/ {
	model = "Mecer Xtreme Mini S6";
	compatible = "mecer,xms6", "rockchip,rk3229";

	aliases {
		mmc0 = &sdmmc;
		mmc1 = &sdio;
		mmc2 = &emmc;
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x40000000>;
	};

	dc_12v: dc-12v-regulator {
		compatible = "regulator-fixed";
		regulator-name = "dc_12v";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <12000000>;
		regulator-max-microvolt = <12000000>;
	};

	ext_gmac: ext_gmac {
		compatible = "fixed-clock";
		clock-frequency = <125000000>;
		clock-output-names = "ext_gmac";
		#clock-cells = <0>;
	};

	power-led {
		compatible = "gpio-leds";

		blue_led: led-0 {
			gpios = <&gpio3 21 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&gpio2 26 GPIO_ACTIVE_LOW>,
		              <&gpio2 29 GPIO_ACTIVE_LOW>;
	};

	vcc_host: vcc-host-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio3 RK_PC4 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&host_vbus_drv>;
		regulator-name = "vcc_host";
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vcc_sys>;
	};

	vcc_phy: vcc-phy-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		regulator-name = "vcc_phy";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vccio_1v8>;
	};

	vcc_sys: vcc-sys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&dc_12v>;
	};

	vccio_1v8: vccio-1v8-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vccio_1v8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
		vin-supply = <&vcc_sys>;
	};

	vccio_3v3: vccio-3v3-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vccio_3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		vin-supply = <&vcc_sys>;
	};

	vdd_arm: vdd-arm-regulator {
		compatible = "pwm-regulator";
		pwms = <&pwm1 0 25000 1>;
		pwm-supply = <&vcc_sys>;
		regulator-name = "vdd_arm";
		regulator-min-microvolt = <950000>;
		regulator-max-microvolt = <1400000>;
		regulator-always-on;
		regulator-boot-on;
	};

	vdd_log: vdd-log-regulator {
		compatible = "pwm-regulator";
		pwms = <&pwm2 0 25000 1>;
		pwm-supply = <&vcc_sys>;
		regulator-name = "vdd_log";
		regulator-min-microvolt = <1000000>;
		regulator-max-microvolt = <1300000>;
		regulator-always-on;
		regulator-boot-on;
	};
};

&cpu0 {
	cpu-supply = <&vdd_arm>;
};

&cpu1 {
	cpu-supply = <&vdd_arm>;
};

&cpu2 {
	cpu-supply = <&vdd_arm>;
};

&cpu3 {
	cpu-supply = <&vdd_arm>;
};

&emmc {
	cap-mmc-highspeed;
	non-removable;
	status = "okay";
};

&gmac {
	assigned-clocks = <&cru SCLK_MAC_SRC>;
	assigned-clock-rates = <50000000>;
	clock_in_out = "output";
	phy-handle = <&phy>;
	phy-mode = "rmii";
	phy-supply = <&vcc_phy>;
	status = "okay";

	mdio {
		compatible = "snps,dwmac-mdio";
		#address-cells = <1>;
		#size-cells = <0>;

		phy: ethernet-phy@0 {
			compatible = "ethernet-phy-id1234.d400",
			             "ethernet-phy-ieee802.3-c22";
			reg = <0>;
			clocks = <&cru SCLK_MAC_PHY>;
			phy-is-integrated;
			resets = <&cru SRST_MACPHY>;
		};
	};
};

&gpu {
	mali-supply = <&vdd_log>;
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_phy {
	status = "okay";
};

&iep_mmu {
	status = "okay";
};

&io_domains {
	status = "okay";

	vccio1-supply = <&vccio_3v3>;
	vccio2-supply = <&vccio_1v8>;
	vccio4-supply = <&vccio_3v3>;
};

&pinctrl {
	usb {
		host_vbus_drv: host-vbus-drv {
			rockchip,pins = <3 RK_PC4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&pwm1 {
	status = "okay";
};

&pwm2 {
	status = "okay";
};

&sdio {
	bus-width = <4>;
	cap-sd-highspeed;
	cap-sdio-irq;
	mmc-pwrseq = <&sdio_pwrseq>;
	non-removable;
	vqmmc-supply = <&vccio_1v8>;
	status = "okay";
};

&sdmmc {
	cap-mmc-highspeed;
	disable-wp;
	status = "okay";
};

&tsadc {
	rockchip,hw-tshut-mode = <0>;
	status = "okay";
};

&u2phy0 {
	status = "okay";

	u2phy0_host: host-port {
		phy-supply = <&vcc_host>;
		status = "okay";
	};

	u2phy0_otg: otg-port {
		phy-supply = <&vcc_host>;
		status = "okay";
	};
};

&u2phy1 {
	status = "okay";

	u2phy1_host: host-port {
		phy-supply = <&vcc_host>;
		status = "okay";
	};

	u2phy1_otg: otg-port {
		phy-supply = <&vcc_host>;
		status = "okay";
	};
};

&uart2 {
	pinctrl-0 = <&uart21_xfer>;
	status = "okay";
};

&usb_host0_ehci {
	status = "okay";
};

&usb_host0_ohci {
	status = "okay";
};

&usb_host1_ehci {
	status = "okay";
};

&usb_host1_ohci {
	status = "okay";
};

&usb_host2_ehci {
	status = "okay";
};

&usb_host2_ohci {
	status = "okay";
};

&usb_otg {
	status = "okay";
};

&vop {
	status = "okay";
};

&vop_mmu {
	status = "okay";
};
