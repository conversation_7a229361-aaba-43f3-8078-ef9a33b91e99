// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Google Veyron Speedy Rev 1+ board device tree source
 *
 * Copyright 2015 Google, Inc
 */

/dts-v1/;
#include "rk3288-veyron-chromebook.dtsi"
#include "rk3288-veyron-broadcom-bluetooth.dtsi"
#include "../cros-ec-sbs.dtsi"

/ {
	model = "Google Speedy";
	compatible = "google,veyron-speedy-rev9", "google,veyron-speedy-rev8",
		     "google,veyron-speedy-rev7", "google,veyron-speedy-rev6",
		     "google,veyron-speedy-rev5", "google,veyron-speedy-rev4",
		     "google,veyron-speedy-rev3", "google,veyron-speedy-rev2",
		     "google,veyron-speedy", "google,veyron", "rockchip,rk3288";
};

&cpu_alert0 {
	temperature = <65000>;
};

&cpu_alert1 {
	temperature = <70000>;
};

&cpu_crit {
	temperature = <90000>;
};

&edp {
	/delete-property/pinctrl-names;
	/delete-property/pinctrl-0;

	force-hpd;
};

&gpu_alert0 {
	temperature = <80000>;
};

&gpu_crit {
	temperature = <90000>;
};

&rk808 {
	pinctrl-names = "default";
	pinctrl-0 = <&pmic_int_l>;
};

&sdmmc {
	disable-wp;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk &sdmmc_cmd &sdmmc_cd_disabled &sdmmc_cd_pin
			&sdmmc_bus4>;
};

&vcc_5v {
	enable-active-high;
	gpio = <&gpio7 RK_PC5 GPIO_ACTIVE_HIGH>;
	pinctrl-names = "default";
	pinctrl-0 = <&drv_5v>;
};

&vcc50_hdmi {
	enable-active-high;
	gpio = <&gpio5 RK_PC3 GPIO_ACTIVE_HIGH>;
	pinctrl-names = "default";
	pinctrl-0 = <&vcc50_hdmi_en>;
};

&gpio0 {
	gpio-line-names = "PMIC_SLEEP_AP",
			  "DDRIO_PWROFF",
			  "DDRIO_RETEN",
			  "TS3A227E_INT_L",
			  "PMIC_INT_L",
			  "PWR_KEY_L",
			  "AP_LID_INT_L",
			  "EC_IN_RW",

			  "AC_PRESENT_AP",
			  /*
			   * RECOVERY_SW_L is Chrome OS ABI.  Schematics call
			   * it REC_MODE_L.
			   */
			  "RECOVERY_SW_L",
			  "OTP_OUT",
			  "HOST1_PWR_EN",
			  "USBOTG_PWREN_H",
			  "AP_WARM_RESET_H",
			  "nFALUT2",
			  "I2C0_SDA_PMIC",

			  "I2C0_SCL_PMIC",
			  "SUSPEND_L",
			  "USB_INT";
};

&gpio2 {
	gpio-line-names = "CONFIG0",
			  "CONFIG1",
			  "CONFIG2",
			  "",
			  "",
			  "",
			  "",
			  "CONFIG3",

			  "PWRLIMIT#_CPU",
			  "EMMC_RST_L",
			  "",
			  "",
			  "BL_PWR_EN",
			  "AVDD_1V8_DISP_EN";
};

&gpio3 {
	gpio-line-names = "FLASH0_D0",
			  "FLASH0_D1",
			  "FLASH0_D2",
			  "FLASH0_D3",
			  "FLASH0_D4",
			  "FLASH0_D5",
			  "FLASH0_D6",
			  "FLASH0_D7",

			  "",
			  "",
			  "",
			  "",
			  "",
			  "",
			  "",
			  "",

			  "FLASH0_CS2/EMMC_CMD",
			  "",
			  "FLASH0_DQS/EMMC_CLKO";
};

&gpio4 {
	gpio-line-names = "",
			  "",
			  "",
			  "",
			  "",
			  "",
			  "",
			  "",

			  "",
			  "",
			  "",
			  "",
			  "",
			  "",
			  "",
			  "",

			  "UART0_RXD",
			  "UART0_TXD",
			  "UART0_CTS",
			  "UART0_RTS",
			  "SDIO0_D0",
			  "SDIO0_D1",
			  "SDIO0_D2",
			  "SDIO0_D3",

			  "SDIO0_CMD",
			  "SDIO0_CLK",
			  "BT_DEV_WAKE",
			  "",
			  "WIFI_ENABLE_H",
			  "BT_ENABLE_L",
			  "WIFI_HOST_WAKE",
			  "BT_HOST_WAKE";
};

&gpio5 {
	gpio-line-names = "",
			  "",
			  "",
			  "",
			  "",
			  "",
			  "",
			  "",

			  "",
			  "",
			  "",
			  "",
			  "SPI0_CLK",
			  "SPI0_CS0",
			  "SPI0_TXD",
			  "SPI0_RXD",

			  "",
			  "",
			  "",
			  "VCC50_HDMI_EN";
};

&gpio6 {
	gpio-line-names = "I2S0_SCLK",
			  "I2S0_LRCK_RX",
			  "I2S0_LRCK_TX",
			  "I2S0_SDI",
			  "I2S0_SDO0",
			  "HP_DET_H",
			  "ALS_INT",		/* not connected */
			  "INT_CODEC",

			  "I2S0_CLK",
			  "I2C2_SDA",
			  "I2C2_SCL",
			  "MICDET",
			  "",
			  "",
			  "",
			  "",

			  "SDMMC_D0",
			  "SDMMC_D1",
			  "SDMMC_D2",
			  "SDMMC_D3",
			  "SDMMC_CLK",
			  "SDMMC_CMD";
};

&gpio7 {
	gpio-line-names = "LCDC_BL",
			  "PWM_LOG",
			  "BL_EN",
			  "TRACKPAD_INT",
			  "TPM_INT_H",
			  "SDMMC_DET_L",
			  /*
			   * AP_FLASH_WP_L is Chrome OS ABI.  Schematics call
			   * it FW_WP_AP.
			   */
			  "AP_FLASH_WP_L",
			  "EC_INT",

			  "CPU_NMI",
			  "DVS_OK",
			  "",
			  "EDP_HOTPLUG",
			  "DVS1",
			  "nFALUT1",
			  "LCD_EN",
			  "DVS2",

			  "VCC5V_GOOD_H",
			  "I2C4_SDA_TP",
			  "I2C4_SCL_TP",
			  "I2C5_SDA_HDMI",
			  "I2C5_SCL_HDMI",
			  "5V_DRV",
			  "UART2_RXD",
			  "UART2_TXD";
};

&gpio8 {
	gpio-line-names = "RAM_ID0",
			  "RAM_ID1",
			  "RAM_ID2",
			  "RAM_ID3",
			  "I2C1_SDA_TPM",
			  "I2C1_SCL_TPM",
			  "SPI2_CLK",
			  "SPI2_CS0",

			  "SPI2_RXD",
			  "SPI2_TXD";
};

&pinctrl {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <
		/* Common for sleep and wake, but no owners */
		&ddr0_retention
		&ddrio_pwroff
		&global_pwroff

		/* Wake only */
		&suspend_l_wake
	>;
	pinctrl-1 = <
		/* Common for sleep and wake, but no owners */
		&ddr0_retention
		&ddrio_pwroff
		&global_pwroff

		/* Sleep only */
		&suspend_l_sleep
	>;

	buck-5v {
		drv_5v: drv-5v {
			rockchip,pins = <7 RK_PC5 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	hdmi {
		vcc50_hdmi_en: vcc50-hdmi-en {
			rockchip,pins = <5 RK_PC3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	pmic {
		dvs_1: dvs-1 {
			rockchip,pins = <7 RK_PB4 RK_FUNC_GPIO &pcfg_pull_down>;
		};

		dvs_2: dvs-2 {
			rockchip,pins = <7 RK_PB7 RK_FUNC_GPIO &pcfg_pull_down>;
		};
	};
};
