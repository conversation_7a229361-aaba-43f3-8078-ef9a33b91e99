// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Google Veyron (and derivatives) fragment for the edp displays
 *
 * Copyright 2019 Google LLC
 */

/ {
	backlight_regulator: backlight-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio2 RK_PB4 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&bl_pwr_en>;
		regulator-name = "backlight_regulator";
		vin-supply = <&vcc33_sys>;
		startup-delay-us = <15000>;
	};

	panel_regulator: panel-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio7 RK_PB6 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&lcd_enable_h>;
		regulator-name = "panel_regulator";
		vin-supply = <&vcc33_sys>;
	};

	vcc18_lcd: vcc18-lcd {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio2 RK_PB5 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&avdd_1v8_disp_en>;
		regulator-name = "vcc18_lcd";
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vcc18_wl>;
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		brightness-levels = <0 255>;
		num-interpolated-steps = <255>;
		default-brightness-level = <128>;
		enable-gpios = <&gpio7 RK_PA2 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&bl_en>;
		pwms = <&pwm0 0 1000000 0>;
		post-pwm-on-delay-ms = <10>;
		pwm-off-delay-ms = <10>;
		power-supply = <&backlight_regulator>;
	};

	panel: panel {
		compatible = "innolux,n116bge";
		status = "okay";
		power-supply = <&panel_regulator>;
		backlight = <&backlight>;

		panel-timing {
			clock-frequency = <74250000>;
			hactive = <1366>;
			hfront-porch = <136>;
			hback-porch = <60>;
			hsync-len = <30>;
			hsync-active = <0>;
			vactive = <768>;
			vfront-porch = <8>;
			vback-porch = <12>;
			vsync-len = <12>;
			vsync-active = <0>;
		};

		ports {
			panel_in: port {
				panel_in_edp: endpoint {
					remote-endpoint = <&edp_out_panel>;
				};
			};
		};
	};
};

&edp {
	status = "okay";

	pinctrl-names = "default";
	pinctrl-0 = <&edp_hpd>;

	ports {
		edp_out: port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			edp_out_panel: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&panel_in_edp>;
			};
		};
	};
};

&edp_phy {
	status = "okay";
};

&pwm0 {
	status = "okay";
};

&vopl {
	status = "okay";
};

&vopl_mmu {
	status = "okay";
};

&pinctrl {
	backlight {
		bl_pwr_en: bl_pwr_en {
			rockchip,pins = <2 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bl_en: bl-en {
			rockchip,pins = <7 RK_PA2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	lcd {
		lcd_enable_h: lcd-en {
			rockchip,pins = <7 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		avdd_1v8_disp_en: avdd-1v8-disp-en {
			rockchip,pins = <2 RK_PB5 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};
