// SPDX-License-Identifier: (GPL-2.0+ OR MIT)

/dts-v1/;
#include <dt-bindings/input/input.h>
#include "rk3288-rock2-som.dtsi"

/ {
	model = "Radxa Rock 2 Square";
	compatible = "radxa,rock2-square", "rockchip,rk3288";

	chosen {
		stdout-path = "serial2:115200n8";
	};

	adc-keys {
		compatible = "adc-keys";
		io-channels = <&saradc 1>;
		io-channel-names = "buttons";
		keyup-threshold-microvolt = <1800000>;

		button-recovery {
			label = "Recovery";
			linux,code = <KEY_VENDOR>;
			press-threshold-microvolt = <0>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-power {
			gpios = <&gpio0 RK_PA5 GPIO_ACTIVE_LOW>;
			label = "GPIO Power";
			linux,code = <KEY_POWER>;
			pinctrl-names = "default";
			pinctrl-0 = <&pwr_key>;
			wakeup-source;
		};
	};

	gpio-leds {
		compatible = "gpio-leds";

		heartbeat_led: led-0 {
			gpios = <&gpio7 RK_PB7 GPIO_ACTIVE_LOW>;
			label = "rock2:green:state1";
			linux,default-trigger = "heartbeat";
		};

		mmc_led: led-1 {
			gpios = <&gpio0 RK_PB3 GPIO_ACTIVE_LOW>;
			label = "rock2:blue:state2";
			linux,default-trigger = "mmc0";
		};
	};

	ir: ir-receiver {
		compatible = "gpio-ir-receiver";
		gpios = <&gpio8 RK_PA1 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&ir_int>;
	};

	sound {
		compatible = "simple-audio-card";
		simple-audio-card,name = "SPDIF";
		simple-audio-card,dai-link@1 {  /* S/PDIF - S/PDIF */
			cpu { sound-dai = <&spdif>; };
			codec { sound-dai = <&spdif_out>; };
		};
	};

	sata_pwr: sata-prw-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 13 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&sata_pwr_en>;
		/* Always turn on the 5V sata power connector */
		regulator-always-on;
		regulator-name = "sata_pwr";
	};

	spdif_out: spdif-out {
		compatible = "linux,spdif-dit";
		#sound-dai-cells = <0>;
	};

	sound-i2s {
		compatible = "rockchip,rk3288-hdmi-analog";
		pinctrl-names = "default";
		pinctrl-0 = <&phone_ctl>, <&hp_det>;
		rockchip,audio-codec = <&es8388>;
		rockchip,hp-det-gpios = <&gpio7 7 GPIO_ACTIVE_HIGH>;
		rockchip,hp-en-gpios = <&gpio8 0 GPIO_ACTIVE_HIGH>;
		rockchip,i2s-controller = <&i2s>;
		rockchip,model = "I2S";
		rockchip,routing = "Analog", "LOUT2",
				   "Analog", "ROUT2";
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		clocks = <&hym8563>;
		clock-names = "ext_clock";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_enable>;
		reset-gpios = <&gpio4 RK_PD4 GPIO_ACTIVE_LOW>;
	};

	vcc_usb_host: vcc-host-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB6 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&host_vbus_drv>;
		regulator-name = "vcc_host";
	};

	vcc_sd: sdmmc-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio7 RK_PB3 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdmmc_pwr>;
		regulator-name = "vcc_sd";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&vcc_io>;
	};
};

&sdio0 {
	bus-width = <4>;
	cap-sd-highspeed;
	cap-sdio-irq;
	mmc-pwrseq = <&sdio_pwrseq>;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&sdio0_bus4 &sdio0_cmd &sdio0_clk &sdio0_int>;
	vmmc-supply = <&vcc_io>;
	vqmmc-supply = <&vcc_18>;
	status = "okay";
};

&sdmmc {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	disable-wp;	/* wp not hooked up */
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk &sdmmc_cmd &sdmmc_cd &sdmmc_bus4>;
	vmmc-supply = <&vcc_sd>;
	vqmmc-supply = <&vccio_sd>;
	status = "okay";
};

&gmac {
	status = "okay";
};

&hdmi {
	ddc-i2c-bus = <&i2c5>;
	status = "okay";
};

&i2c0 {
	hym8563: rtc@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-output-names = "xin32k";
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PA4 IRQ_TYPE_EDGE_FALLING>;
		pinctrl-names = "default";
		pinctrl-0 = <&pmic_int>;

	};
};

&i2c2 {
	status = "okay";

	es8388: es8388@10 {
		compatible = "everest,es8388", "everest,es8328";
		reg = <0x10>;
		AVDD-supply = <&vccio_codec>;
		DVDD-supply = <&vccio_codec>;
		HPVDD-supply = <&vccio_codec>;
		PVDD-supply = <&vccio_codec>;
		clocks = <&cru SCLK_I2S0_OUT>;
	};
};

&i2c5 {
	status = "okay";
};

&i2s {
	status = "okay";
};

&pinctrl {
	ir {
		ir_int: ir-int {
			rockchip,pins = <8 RK_PA1 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	keys {
		pwr_key: pwr-key {
			rockchip,pins = <0 RK_PA5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	pmic {
		pmic_int: pmic-int {
			rockchip,pins = <0 RK_PA4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	headphone {
		hp_det: hp-det {
			rockchip,pins = <7 RK_PA7 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		phone_ctl: phone-ctl {
			rockchip,pins = <8 RK_PA0 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	usb {
		host_vbus_drv: host-vbus-drv {
			rockchip,pins = <0 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	sata {
		sata_pwr_en: sata-pwr-en {
			rockchip,pins = <0 RK_PB5 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	sdmmc {
		sdmmc_pwr: sdmmc-pwr {
			rockchip,pins = <7 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	sdio {
		wifi_enable: wifi-enable {
			rockchip,pins = <4 RK_PD4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&saradc {
	status = "okay";
};

&spdif {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&usbphy1 {
	vbus-supply = <&vcc_usb_host>;
};

&usb_host0_ehci {
	status = "okay";
};

&usb_host1 {
	status = "okay";
};

&usb_otg {
	status = "okay";
};
