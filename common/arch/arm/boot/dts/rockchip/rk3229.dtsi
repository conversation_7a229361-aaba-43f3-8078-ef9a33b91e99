// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2017 Fuzhou Rockchip Electronics Co., Ltd
 */

#include "rk322x.dtsi"

/ {
	compatible = "rockchip,rk3229";

	/delete-node/ opp-table0;

	cpu0_opp_table: opp-table-0 {
		compatible = "operating-points-v2";
		opp-shared;

		opp-408000000 {
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <950000>;
			clock-latency-ns = <40000>;
			opp-suspend;
		};
		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <975000>;
		};
		opp-816000000 {
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <1000000>;
		};
		opp-********** {
			opp-hz = /bits/ 64 <**********>;
			opp-microvolt = <1175000>;
		};
		opp-********** {
			opp-hz = /bits/ 64 <**********>;
			opp-microvolt = <1275000>;
		};
		opp-********** {
			opp-hz = /bits/ 64 <**********>;
			opp-microvolt = <1325000>;
		};
		opp-********** {
			opp-hz = /bits/ 64 <**********>;
			opp-microvolt = <1375000>;
		};
		opp-********** {
			opp-hz = /bits/ 64 <**********>;
			opp-microvolt = <1400000>;
		};
	};
};
