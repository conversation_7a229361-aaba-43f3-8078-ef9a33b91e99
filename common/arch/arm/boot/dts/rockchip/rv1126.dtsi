// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2019 Fuzhou Rockchip Electronics Co., Ltd.
 */

#include <dt-bindings/clock/rockchip,rv1126-cru.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/pinctrl/rockchip.h>
#include <dt-bindings/power/rockchip,rv1126-power.h>
#include <dt-bindings/soc/rockchip,boot-mode.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	compatible = "rockchip,rv1126";

	interrupt-parent = <&gic>;

	aliases {
		i2c0 = &i2c0;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@f00 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf00>;
			enable-method = "psci";
			clocks = <&cru ARMCLK>;
		};

		cpu1: cpu@f01 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf01>;
			enable-method = "psci";
			clocks = <&cru ARMCLK>;
		};

		cpu2: cpu@f02 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf02>;
			enable-method = "psci";
			clocks = <&cru ARMCLK>;
		};

		cpu3: cpu@f03 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0xf03>;
			enable-method = "psci";
			clocks = <&cru ARMCLK>;
		};
	};

	arm-pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
	};

	psci {
		compatible = "arm,psci-1.0";
		method = "smc";
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		clock-frequency = <24000000>;
	};

	display_subsystem {
		compatible = "rockchip,display-subsystem";
		ports = <&vop_out>;
	};

	xin24m: oscillator {
		compatible = "fixed-clock";
		clock-frequency = <24000000>;
		clock-output-names = "xin24m";
		#clock-cells = <0>;
	};

	grf: syscon@fe000000 {
		compatible = "rockchip,rv1126-grf", "syscon", "simple-mfd";
		reg = <0xfe000000 0x20000>;
	};

	pmugrf: syscon@fe020000 {
		compatible = "rockchip,rv1126-pmugrf", "syscon", "simple-mfd";
		reg = <0xfe020000 0x1000>;

		pmu_io_domains: io-domains {
			compatible = "rockchip,rv1126-pmu-io-voltage-domain";
			status = "disabled";
		};
	};

	qos_emmc: qos@fe860000 {
		compatible = "rockchip,rv1126-qos", "syscon";
		reg = <0xfe860000 0x20>;
	};

	qos_nandc: qos@fe860080 {
		compatible = "rockchip,rv1126-qos", "syscon";
		reg = <0xfe860080 0x20>;
	};

	qos_sfc: qos@fe860200 {
		compatible = "rockchip,rv1126-qos", "syscon";
		reg = <0xfe860200 0x20>;
	};

	qos_sdio: qos@fe86c000 {
		compatible = "rockchip,rv1126-qos", "syscon";
		reg = <0xfe86c000 0x20>;
	};

	qos_iep: qos@fe8a0000 {
		compatible = "rockchip,rv1126-qos", "syscon";
		reg = <0xfe8a0000 0x20>;
	};

	qos_rga_rd: qos@fe8a0080 {
		compatible = "rockchip,rv1126-qos", "syscon";
		reg = <0xfe8a0080 0x20>;
	};

	qos_rga_wr: qos@fe8a0100 {
		compatible = "rockchip,rv1126-qos", "syscon";
		reg = <0xfe8a0100 0x20>;
	};

	qos_vop: qos@fe8a0180 {
		compatible = "rockchip,rv1126-qos", "syscon";
		reg = <0xfe8a0180 0x20>;
	};

	gic: interrupt-controller@feff0000 {
		compatible = "arm,gic-400";
		interrupt-controller;
		#interrupt-cells = <3>;
		#address-cells = <0>;

		reg = <0xfeff1000 0x1000>,
		      <0xfeff2000 0x2000>,
		      <0xfeff4000 0x2000>,
		      <0xfeff6000 0x2000>;
		interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
	};

	pmu: power-management@ff3e0000 {
		compatible = "rockchip,rv1126-pmu", "syscon", "simple-mfd";
		reg = <0xff3e0000 0x1000>;

		power: power-controller {
			compatible = "rockchip,rv1126-power-controller";
			#power-domain-cells = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			power-domain@RV1126_PD_NVM {
				reg = <RV1126_PD_NVM>;
				clocks = <&cru HCLK_EMMC>,
					 <&cru CLK_EMMC>,
					 <&cru HCLK_NANDC>,
					 <&cru CLK_NANDC>,
					 <&cru HCLK_SFC>,
					 <&cru HCLK_SFCXIP>,
					 <&cru SCLK_SFC>;
				pm_qos = <&qos_emmc>,
					 <&qos_nandc>,
					 <&qos_sfc>;
				#power-domain-cells = <0>;
			};

			power-domain@RV1126_PD_SDIO {
				reg = <RV1126_PD_SDIO>;
				clocks = <&cru HCLK_SDIO>,
					 <&cru CLK_SDIO>;
				pm_qos = <&qos_sdio>;
				#power-domain-cells = <0>;
			};

			power-domain@RV1126_PD_VO {
				reg = <RV1126_PD_VO>;
				clocks = <&cru ACLK_RGA>,
					 <&cru HCLK_RGA>,
					 <&cru CLK_RGA_CORE>,
					 <&cru ACLK_VOP>,
					 <&cru HCLK_VOP>,
					 <&cru DCLK_VOP>,
					 <&cru PCLK_DSIHOST>,
					 <&cru ACLK_IEP>,
					 <&cru HCLK_IEP>,
					 <&cru CLK_IEP_CORE>;
				pm_qos = <&qos_rga_rd>,
					 <&qos_rga_wr>,
					 <&qos_vop>,
					 <&qos_iep>;
				#power-domain-cells = <0>;
			};
		};
	};

	i2c0: i2c@ff3f0000 {
		compatible = "rockchip,rv1126-i2c", "rockchip,rk3399-i2c";
		reg = <0xff3f0000 0x1000>;
		interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
		rockchip,grf = <&pmugrf>;
		clocks = <&pmucru CLK_I2C0>, <&pmucru PCLK_I2C0>;
		clock-names = "i2c", "pclk";
		pinctrl-names = "default";
		pinctrl-0 = <&i2c0_xfer>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	uart1: serial@ff410000 {
		compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
		reg = <0xff410000 0x100>;
		interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <24000000>;
		clocks = <&pmucru SCLK_UART1>, <&pmucru PCLK_UART1>;
		clock-names = "baudclk", "apb_pclk";
		dmas = <&dmac 7>, <&dmac 6>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&uart1m0_xfer>;
		reg-shift = <2>;
		reg-io-width = <4>;
		status = "disabled";
	};

	pmucru: clock-controller@ff480000 {
		compatible = "rockchip,rv1126-pmucru";
		reg = <0xff480000 0x1000>;
		rockchip,grf = <&grf>;
		#clock-cells = <1>;
		#reset-cells = <1>;
	};

	cru: clock-controller@ff490000 {
		compatible = "rockchip,rv1126-cru";
		reg = <0xff490000 0x1000>;
		clocks = <&xin24m>;
		clock-names = "xin24m";
		rockchip,grf = <&grf>;
		#clock-cells = <1>;
		#reset-cells = <1>;
	};

	dmac: dma-controller@ff4e0000 {
		compatible = "arm,pl330", "arm,primecell";
		reg = <0xff4e0000 0x4000>;
		interrupts = <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
		#dma-cells = <1>;
		arm,pl330-periph-burst;
		clocks = <&cru ACLK_DMAC>;
		clock-names = "apb_pclk";
	};

	uart0: serial@ff560000 {
		compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
		reg = <0xff560000 0x100>;
		interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <24000000>;
		clocks = <&cru SCLK_UART0>, <&cru PCLK_UART0>;
		clock-names = "baudclk", "apb_pclk";
		dmas = <&dmac 5>, <&dmac 4>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&uart0_xfer>;
		reg-shift = <2>;
		reg-io-width = <4>;
		status = "disabled";
	};

	uart2: serial@ff570000 {
		compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
		reg = <0xff570000 0x100>;
		interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <24000000>;
		clocks = <&cru SCLK_UART2>, <&cru PCLK_UART2>;
		clock-names = "baudclk", "apb_pclk";
		dmas = <&dmac 9>, <&dmac 8>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&uart2m1_xfer>;
		reg-shift = <2>;
		reg-io-width = <4>;
		status = "disabled";
	};

	uart3: serial@ff580000 {
		compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
		reg = <0xff580000 0x100>;
		interrupts = <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <24000000>;
		clocks = <&cru SCLK_UART3>, <&cru PCLK_UART3>;
		clock-names = "baudclk", "apb_pclk";
		dmas = <&dmac 11>, <&dmac 10>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&uart3m0_xfer>;
		reg-shift = <2>;
		reg-io-width = <4>;
		status = "disabled";
	};

	uart4: serial@ff590000 {
		compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
		reg = <0xff590000 0x100>;
		interrupts = <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <24000000>;
		clocks = <&cru SCLK_UART4>, <&cru PCLK_UART4>;
		clock-names = "baudclk", "apb_pclk";
		dmas = <&dmac 13>, <&dmac 12>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&uart4m0_xfer>;
		reg-shift = <2>;
		reg-io-width = <4>;
		status = "disabled";
	};

	uart5: serial@ff5a0000 {
		compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
		reg = <0xff5a0000 0x100>;
		interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <24000000>;
		clocks = <&cru SCLK_UART5>, <&cru PCLK_UART5>;
		clock-names = "baudclk", "apb_pclk";
		dmas = <&dmac 15>, <&dmac 14>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&uart5m0_xfer>;
		reg-shift = <2>;
		reg-io-width = <4>;
		status = "disabled";
	};

	saradc: adc@ff5e0000 {
		compatible = "rockchip,rv1126-saradc", "rockchip,rk3399-saradc";
		reg = <0xff5e0000 0x100>;
		interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>;
		#io-channel-cells = <1>;
		clocks = <&cru CLK_SARADC>, <&cru PCLK_SARADC>;
		clock-names = "saradc", "apb_pclk";
		resets = <&cru SRST_SARADC_P>;
		reset-names = "saradc-apb";
		status = "disabled";
	};

	timer0: timer@ff660000 {
		compatible = "rockchip,rv1126-timer", "rockchip,rk3288-timer";
		reg = <0xff660000 0x20>;
		interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_TIMER>, <&cru CLK_TIMER0>;
		clock-names = "pclk", "timer";
	};

	vop: vop@ffb00000 {
		compatible = "rockchip,rv1126-vop";
		reg = <0xffb00000 0x200>, <0xffb00a00 0x400>;
		interrupts = <GIC_SPI 59 IRQ_TYPE_LEVEL_HIGH>;
		clock-names = "aclk_vop", "dclk_vop", "hclk_vop";
		clocks = <&cru ACLK_VOP>, <&cru DCLK_VOP>, <&cru HCLK_VOP>;
		reset-names = "axi", "ahb", "dclk";
		resets = <&cru SRST_VOP_A>, <&cru SRST_VOP_H>, <&cru SRST_VOP_D>;
		iommus = <&vop_mmu>;
		power-domains = <&power RV1126_PD_VO>;
		status = "disabled";

		vop_out: port {
			#address-cells = <1>;
			#size-cells = <0>;

			vop_out_rgb: endpoint@0 {
				reg = <0>;
			};

			vop_out_dsi: endpoint@1 {
				reg = <1>;
			};
		};
	};

	vop_mmu: iommu@ffb00f00 {
		compatible = "rockchip,iommu";
		reg = <0xffb00f00 0x100>;
		interrupts = <GIC_SPI 59 IRQ_TYPE_LEVEL_HIGH>;
		clock-names = "aclk", "iface";
		clocks = <&cru ACLK_VOP>, <&cru HCLK_VOP>;
		#iommu-cells = <0>;
		power-domains = <&power RV1126_PD_VO>;
		status = "disabled";
	};

	gmac: ethernet@ffc40000 {
		compatible = "rockchip,rv1126-gmac", "snps,dwmac-4.20a";
		reg = <0xffc40000 0x4000>;
		interrupts = <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "macirq", "eth_wake_irq";
		rockchip,grf = <&grf>;
		clocks = <&cru CLK_GMAC_SRC>, <&cru CLK_GMAC_TX_RX>,
			 <&cru CLK_GMAC_TX_RX>, <&cru CLK_GMAC_REF>,
			 <&cru ACLK_GMAC>, <&cru PCLK_GMAC>,
			 <&cru CLK_GMAC_TX_RX>, <&cru CLK_GMAC_PTPREF>;
		clock-names = "stmmaceth", "mac_clk_rx",
			      "mac_clk_tx", "clk_mac_ref",
			      "aclk_mac", "pclk_mac",
			      "clk_mac_speed", "ptp_ref";
		resets = <&cru SRST_GMAC_A>;
		reset-names = "stmmaceth";

		snps,mixed-burst;
		snps,tso;

		snps,axi-config = <&stmmac_axi_setup>;
		snps,mtl-rx-config = <&mtl_rx_setup>;
		snps,mtl-tx-config = <&mtl_tx_setup>;
		status = "disabled";

		mdio: mdio {
			compatible = "snps,dwmac-mdio";
			#address-cells = <0x1>;
			#size-cells = <0x0>;
		};

		stmmac_axi_setup: stmmac-axi-config {
			snps,wr_osr_lmt = <4>;
			snps,rd_osr_lmt = <8>;
			snps,blen = <0 0 0 0 16 8 4>;
		};

		mtl_rx_setup: rx-queues-config {
			snps,rx-queues-to-use = <1>;
			queue0 {};
		};

		mtl_tx_setup: tx-queues-config {
			snps,tx-queues-to-use = <1>;
			queue0 {};
		};
	};

	emmc: mmc@ffc50000 {
		compatible = "rockchip,rv1126-dw-mshc", "rockchip,rk3288-dw-mshc";
		reg = <0xffc50000 0x4000>;
		interrupts = <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_EMMC>, <&cru CLK_EMMC>,
			 <&cru SCLK_EMMC_DRV>, <&cru SCLK_EMMC_SAMPLE>;
		clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
		fifo-depth = <0x100>;
		max-frequency = <200000000>;
		power-domains = <&power RV1126_PD_NVM>;
		status = "disabled";
	};

	sdmmc: mmc@ffc60000 {
		compatible = "rockchip,rv1126-dw-mshc", "rockchip,rk3288-dw-mshc";
		reg = <0xffc60000 0x4000>;
		interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_SDMMC>, <&cru CLK_SDMMC>,
			 <&cru SCLK_SDMMC_DRV>, <&cru SCLK_SDMMC_SAMPLE>;
		clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
		fifo-depth = <0x100>;
		max-frequency = <200000000>;
		status = "disabled";
	};

	sdio: mmc@ffc70000 {
		compatible = "rockchip,rv1126-dw-mshc", "rockchip,rk3288-dw-mshc";
		reg = <0xffc70000 0x4000>;
		interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_SDIO>, <&cru CLK_SDIO>,
			 <&cru SCLK_SDIO_DRV>, <&cru SCLK_SDIO_SAMPLE>;
		clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
		fifo-depth = <0x100>;
		max-frequency = <200000000>;
		power-domains = <&power RV1126_PD_SDIO>;
		status = "disabled";
	};

	sfc: spi@ffc90000  {
		compatible = "rockchip,sfc";
		reg = <0xffc90000 0x4000>;
		interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
		assigned-clocks = <&cru SCLK_SFC>;
		assigned-clock-rates = <********>;
		clock-names = "clk_sfc", "hclk_sfc";
		clocks = <&cru SCLK_SFC>, <&cru HCLK_SFC>;
		power-domains = <&power RV1126_PD_NVM>;
		status = "disabled";
	};

	pinctrl: pinctrl {
		compatible = "rockchip,rv1126-pinctrl";
		rockchip,grf = <&grf>;
		rockchip,pmu = <&pmugrf>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		gpio0: gpio@ff460000 {
			compatible = "rockchip,gpio-bank";
			reg = <0xff460000 0x100>;
			interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&pmucru PCLK_GPIO0>, <&pmucru DBCLK_GPIO0>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio1: gpio@ff620000 {
			compatible = "rockchip,gpio-bank";
			reg = <0xff620000 0x100>;
			interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO1>, <&cru DBCLK_GPIO1>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio2: gpio@ff630000 {
			compatible = "rockchip,gpio-bank";
			reg = <0xff630000 0x100>;
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO2>, <&cru DBCLK_GPIO2>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio3: gpio@ff640000 {
			compatible = "rockchip,gpio-bank";
			reg = <0xff640000 0x100>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO3>, <&cru DBCLK_GPIO3>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio4: gpio@ff650000 {
			compatible = "rockchip,gpio-bank";
			reg = <0xff650000 0x100>;
			interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO4>, <&cru DBCLK_GPIO4>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};
	};
};

#include "rv1126-pinctrl.dtsi"
