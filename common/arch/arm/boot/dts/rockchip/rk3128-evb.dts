// SPDX-License-Identifier: GPL-2.0+
/*
 * (C) Copyright 2017 Rockchip Electronics Co., Ltd
 */

/dts-v1/;

#include "rk3128.dtsi"

/ {
	model = "Rockchip RK3128 Evaluation board";
	compatible = "rockchip,rk3128-evb", "rockchip,rk3128";

	aliases {
		gpio0 = &gpio0;
		gpio1 = &gpio1;
		gpio2 = &gpio2;
		gpio3 = &gpio3;
		i2c1 = &i2c1;
		mmc0 = &emmc;
	};

	chosen {
		stdout-path = &uart2;
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x40000000>;
	};

	vcc5v0_otg: vcc5v0-otg-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio0 26 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&otg_vbus_drv>;
		regulator-name = "vcc5v0_otg";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};

	vcc5v0_host: vcc5v0-host-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio2 23 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&host_vbus_drv>;
		regulator-name = "vcc5v0_host";
		regulator-always-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};
};

&emmc {
	bus-width = <8>;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_bus8>;
	status = "okay";
};

&i2c1 {
	status = "okay";

	hym8563: rtc@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-output-names = "xin32k";
	};
};

&usb2phy {
	status = "okay";
};

&usb2phy_host {
	status = "okay";
};

&usb2phy_otg {
	status = "okay";
};

&usb_host_ehci {
	status = "okay";
};

&usb_host_ohci {
	status = "okay";
};

&usb_otg {
	vbus-supply = <&vcc5v0_otg>;
	status = "okay";
};

&pinctrl {
	usb-host {
		host_vbus_drv: host-vbus-drv {
			rockchip,pins = <2 RK_PC7 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb-otg {
		otg_vbus_drv: otg-vbus-drv {
			rockchip,pins = <0 RK_PD2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};
