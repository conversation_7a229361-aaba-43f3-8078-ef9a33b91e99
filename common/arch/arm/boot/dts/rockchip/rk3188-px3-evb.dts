// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2016 <PERSON> <<EMAIL>>
 */

/dts-v1/;
#include <dt-bindings/input/input.h>
#include "rk3188.dtsi"

/ {
	model = "Rockchip PX3-EVB";
	compatible = "rockchip,px3-evb", "rockchip,px3", "rockchip,rk3188";

	aliases {
		mmc0 = &mmc0;
		mmc1 = &emmc;
	};

	chosen {
		stdout-path = "serial2:115200n8";
	};

	memory@60000000 {
		reg = <0x60000000 0x80000000>;
		device_type = "memory";
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;

		key-power {
			gpios = <&gpio0 RK_PA4 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			label = "GPIO Key Power";
			linux,input-type = <1>;
			wakeup-source;
			debounce-interval = <100>;
		};
	};

	vcc_sys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vsys";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
	};
};

&cpu0 {
	cpu-supply = <&vdd_cpu>;
};

&cpu1 {
	cpu-supply = <&vdd_cpu>;
};

&cpu2 {
	cpu-supply = <&vdd_cpu>;
};

&cpu3 {
	cpu-supply = <&vdd_cpu>;
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk>, <&emmc_cmd>, <&emmc_rst>;
	status = "okay";
};

&i2c0 {
	status = "okay";

	accelerometer@18 {
		compatible = "bosch,bma250";
		reg = <0x18>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PB7 IRQ_TYPE_LEVEL_LOW>;
	};
};

&i2c1 {
	status = "okay";
	clock-frequency = <400000>;

	rk808: pmic@1c {
		compatible = "rockchip,rk818";
		reg = <0x1c>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PB3 IRQ_TYPE_LEVEL_LOW>;
		rockchip,system-power-controller;
		wakeup-source;
		#clock-cells = <1>;
		clock-output-names = "xin32k", "rk808-clkout2";

		vcc1-supply = <&vcc_sys>;
		vcc2-supply = <&vcc_sys>;
		vcc3-supply = <&vcc_sys>;
		vcc4-supply = <&vcc_sys>;
		vcc6-supply = <&vcc_sys>;
		vcc7-supply = <&vcc_sys>;
		vcc8-supply = <&vcc_io>;
		vcc9-supply = <&vcc_io>;

		regulators {
			vdd_cpu: DCDC_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1350000>;
				regulator-name = "vdd_arm";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_gpu: DCDC_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1250000>;
				regulator-name = "vdd_gpu";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vcc_ddr";
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc_io: DCDC_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcc_io";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcc_cif: LDO_REG1 {
				 regulator-min-microvolt = <3300000>;
				 regulator-max-microvolt = <3300000>;
				 regulator-name = "vcc_cif";
			};

			vcc_jetta33: LDO_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcc_jetta33";
			};

			vdd_10: LDO_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-name = "vdd_10";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			lvds_12: LDO_REG4 {
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "lvds_12";
			};

			lvds_25: LDO_REG5 {
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "lvds_25";
			};

			cif_18: LDO_REG6 {
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-name = "cif_18";
			};

			vcc_sd: LDO_REG7 {
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcc_sd";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			wl_18: LDO_REG8 {
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "wl_18";
			};

			lcd_33: SWITCH_REG {
				regulator-name = "lcd_33";
			};
		};
	};

};

&i2c2 {
	gsl1680: touchscreen@40 {
		compatible = "silead,gsl1680";
		reg = <0x40>;
		interrupt-parent = <&gpio1>;
		interrupts = <RK_PB7 IRQ_TYPE_EDGE_FALLING>;
		power-gpios = <&gpio0 RK_PB6 GPIO_ACTIVE_HIGH>;
		touchscreen-size-x = <800>;
		touchscreen-size-y = <1280>;
		silead,max-fingers = <5>;
	};
};

&mmc0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk>, <&sd0_cmd>, <&sd0_cd>, <&sd0_bus4>;
	vmmc-supply = <&vcc_sd>;

	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	disable-wp;
};

&pinctrl {
	pcfg_output_low: pcfg-output-low {
		output-low;
	};

	usb {
		host_vbus_drv: host-vbus-drv {
			rockchip,pins = <0 RK_PA3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
		otg_vbus_drv: otg-vbus-drv {
			rockchip,pins = <2 RK_PD7 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&pwm1 {
	status = "okay";
};

&pwm2 {
	status = "okay";
};

&pwm3 {
	status = "okay";
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&usb_host {
	status = "okay";
};

&usb_otg {
	status = "okay";
};

&wdt {
	status = "okay";
};
