// SPDX-License-Identifier: GPL-2.0
/*
 * Google Veyron (and derivatives) fragment for the  max98090 audio
 * codec and analog headphone jack.
 *
 * Copyright 2016 Google, Inc
 */

/ {
	sound {
		compatible = "rockchip,rockchip-audio-max98090";
		pinctrl-names = "default";
		pinctrl-0 = <&mic_det>, <&hp_det>;
		rockchip,model = "VEYRON-I2S";
		rockchip,i2s-controller = <&i2s>;
		rockchip,audio-codec = <&max98090>;
		rockchip,hp-det-gpios = <&gpio6 RK_PA5 GPIO_ACTIVE_HIGH>;
		rockchip,mic-det-gpios = <&gpio6 RK_PB3 GPIO_ACTIVE_LOW>;
		rockchip,headset-codec = <&headsetcodec>;
		rockchip,hdmi-codec = <&hdmi>;
	};
};

&i2c2 {
	max98090: max98090@10 {
		compatible = "maxim,max98090";
		reg = <0x10>;
		interrupt-parent = <&gpio6>;
		interrupts = <RK_PA7 IRQ_TYPE_EDGE_FALLING>;
		clock-names = "mclk";
		clocks = <&cru SCLK_I2S0_OUT>;
		pinctrl-names = "default";
		pinctrl-0 = <&int_codec>;
	};
};

&i2c4 {
	headsetcodec: ts3a227e@3b {
		compatible = "ti,ts3a227e";
		reg = <0x3b>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PA3 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&ts3a227e_int_l>;
		ti,micbias = <7>;		/* MICBIAS = 2.8V */
	};
};

&i2s {
	status = "okay";
};

&io_domains {
	audio-supply = <&vcc18_codec>;
};

&rk808 {
	vcc10-supply = <&vcc33_sys>;

	regulators {
		vcc18_codec: LDO_REG6 {
			regulator-name = "vcc18_codec";
			regulator-always-on;
			regulator-boot-on;
			regulator-min-microvolt = <1800000>;
			regulator-max-microvolt = <1800000>;
			regulator-state-mem {
				regulator-off-in-suspend;
			};
		};
	};
};

&pinctrl {
	codec {
		hp_det: hp-det {
			rockchip,pins = <6 RK_PA5 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		/*
		 * HACK: We're going to _pull down_ this _active low_ interrupt
		 * so that it never fires.  We don't need this interrupt because
		 * we've got a ts3a227e chip but the driver requires it.
		 */
		int_codec: int-codec {
			rockchip,pins = <6 RK_PA7 RK_FUNC_GPIO &pcfg_pull_down>;
		};

		mic_det: mic-det {
			rockchip,pins = <6 RK_PB3 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	headset {
		ts3a227e_int_l: ts3a227e-int-l {
			rockchip,pins = <0 RK_PA3 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};
};
