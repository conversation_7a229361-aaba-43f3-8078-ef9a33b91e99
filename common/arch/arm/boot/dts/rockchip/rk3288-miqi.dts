// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2016 <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include <dt-bindings/input/input.h>
#include "rk3288.dtsi"

/ {
	model = "mqmaker MiQi";
	compatible = "mqmaker,miqi", "rockchip,rk3288";

	chosen {
		stdout-path = "serial2:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x0 0x0 0x80000000>;
	};

	ext_gmac: external-gmac-clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <125000000>;
		clock-output-names = "ext_gmac";
	};

	leds {
		compatible = "gpio-leds";

		work_led: led-0 {
			gpios = <&gpio7 RK_PA2 GPIO_ACTIVE_HIGH>;
			label = "miqi:green:user";
			linux,default-trigger = "timer";
		};
	};

	vcc_flash: flash-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc_flash";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&vcc_io>;
	};

	vcc_host: usb-host-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB6 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&host_vbus_drv>;
		regulator-name = "vcc_host";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		vin-supply = <&vcc_sys>;
	};

	vcc_sd: sdmmc-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio7 RK_PB3 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdmmc_pwr>;
		regulator-name = "vcc_sd";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc_io>;
	};

	vcc_sys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc_sys";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};
};

&cpu0 {
	cpu-supply = <&vdd_cpu>;
};

&cpu1 {
	cpu-supply = <&vdd_cpu>;
};

&cpu2 {
	cpu-supply = <&vdd_cpu>;
};

&cpu3 {
	cpu-supply = <&vdd_cpu>;
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk>, <&emmc_cmd>, <&emmc_pwr>, <&emmc_bus8>;
	vmmc-supply = <&vcc_io>;
	vqmmc-supply = <&vcc_flash>;
	status = "okay";
};

&gmac {
	assigned-clocks = <&cru SCLK_MAC>;
	assigned-clock-parents = <&ext_gmac>;
	clock_in_out = "input";
	pinctrl-names = "default";
	pinctrl-0 = <&rgmii_pins>, <&phy_rst>, <&phy_pmeb>, <&phy_int>;
	phy-supply = <&vcc_lan>;
	phy-mode = "rgmii";
	snps,reset-active-low;
	snps,reset-delays-us = <0 10000 1000000>;
	snps,reset-gpio = <&gpio4 RK_PB0 GPIO_ACTIVE_LOW>;
	tx_delay = <0x30>;
	rx_delay = <0x10>;
	status = "okay";
};

&gpu {
	mali-supply = <&vdd_gpu>;
	status = "okay";
};

&hdmi {
	ddc-i2c-bus = <&i2c5>;
	status = "okay";
};

&i2c0 {
	clock-frequency = <400000>;
	status = "okay";

	vdd_cpu: syr827@40 {
		compatible = "silergy,syr827";
		fcs,suspend-voltage-selector = <1>;
		reg = <0x40>;
		regulator-name = "vdd_cpu";
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <1350000>;
		regulator-always-on;
		regulator-boot-on;
		regulator-enable-ramp-delay = <300>;
		regulator-ramp-delay = <8000>;
		vin-supply = <&vcc_sys>;
	};

	vdd_gpu: syr828@41 {
		compatible = "silergy,syr828";
		fcs,suspend-voltage-selector = <1>;
		reg = <0x41>;
		regulator-name = "vdd_gpu";
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <1350000>;
		regulator-always-on;
		vin-supply = <&vcc_sys>;
	};

	hym8563: rtc@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-output-names = "xin32k";
	};

	act8846: act8846@5a {
		compatible = "active-semi,act8846";
		reg = <0x5a>;
		pinctrl-names = "default";
		pinctrl-0 = <&pmic_vsel>;
		system-power-controller;

		vp1-supply = <&vcc_sys>;
		vp2-supply = <&vcc_sys>;
		vp3-supply = <&vcc_sys>;
		vp4-supply = <&vcc_sys>;
		inl1-supply = <&vcc_sys>;
		inl2-supply = <&vcc_sys>;
		inl3-supply = <&vcc_20>;

		regulators {
			vcc_ddr: REG1 {
				regulator-name = "vcc_ddr";
				regulator-always-on;
			};

			vcc_io: REG2 {
				regulator-name = "vcc_io";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd_log: REG3 {
				regulator-name = "vdd_log";
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-always-on;
			};

			vcc_20: REG4 {
				regulator-name = "vcc_20";
				regulator-min-microvolt = <2000000>;
				regulator-max-microvolt = <2000000>;
				regulator-always-on;
			};

			vccio_sd: REG5 {
				regulator-name = "vccio_sd";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd10_lcd: REG6 {
				regulator-name = "vdd10_lcd";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vcca_18: REG7 {
				regulator-name = "vcca_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			vcca_33: REG8 {
				regulator-name = "vcca_33";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
			};

			vcc_lan: REG9 {
				regulator-name = "vcc_lan";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
			};

			vdd_10: REG10 {
				regulator-name = "vdd_10";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vcc_18: REG11 {
				regulator-name = "vcc_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			vcc18_lcd: REG12 {
				regulator-name = "vcc18_lcd";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};
		};
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&io_domains {
	status = "okay";

	audio-supply = <&vcca_33>;
	flash0-supply = <&vcc_flash>;
	flash1-supply = <&vcc_lan>;
	gpio30-supply = <&vcc_io>;
	gpio1830-supply = <&vcc_io>;
	lcdc-supply = <&vcc_io>;
	sdcard-supply = <&vccio_sd>;
	wifi-supply = <&vcc_18>;
};

&pinctrl {
	pcfg_output_high: pcfg-output-high {
		output-high;
	};

	pcfg_output_low: pcfg-output-low {
		output-low;
	};

	pcfg_pull_up_drv_12ma: pcfg-pull-up-drv-12ma {
		bias-pull-up;
		drive-strength = <12>;
	};

	act8846 {
		pmic_int: pmic-int {
			rockchip,pins = <0 RK_PA4 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		pmic_sleep: pmic-sleep {
			rockchip,pins = <0 RK_PA0 RK_FUNC_GPIO &pcfg_output_low>;
		};

		pmic_vsel: pmic-vsel {
			rockchip,pins = <7 RK_PA1 RK_FUNC_GPIO &pcfg_output_low>;
		};
	};

	gmac {
		phy_int: phy-int {
			rockchip,pins = <0 RK_PB1 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		phy_pmeb: phy-pmeb {
			rockchip,pins = <0 RK_PB0 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		phy_rst: phy-rst {
			rockchip,pins = <4 RK_PB0 RK_FUNC_GPIO &pcfg_output_high>;
		};
	};

	sdmmc {
		/*
		 * Default drive strength isn't enough to achieve even
		 * high-speed mode on firefly board so bump up to 12ma.
		 */
		sdmmc_bus4: sdmmc-bus4 {
			rockchip,pins = <6 RK_PC0 1 &pcfg_pull_up_drv_12ma>,
					<6 RK_PC1 1 &pcfg_pull_up_drv_12ma>,
					<6 RK_PC2 1 &pcfg_pull_up_drv_12ma>,
					<6 RK_PC3 1 &pcfg_pull_up_drv_12ma>;
		};

		sdmmc_clk: sdmmc-clk {
			rockchip,pins = <6 RK_PC4 1 &pcfg_pull_none_12ma>;
		};

		sdmmc_cmd: sdmmc-cmd {
			rockchip,pins = <6 RK_PC5 1 &pcfg_pull_up_drv_12ma>;
		};

		sdmmc_pwr: sdmmc-pwr {
			rockchip,pins = <7 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb_host {
		host_vbus_drv: host-vbus-drv {
			rockchip,pins = <0 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&saradc {
	vref-supply = <&vcc_18>;
	status = "okay";
};

&sdmmc {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	disable-wp;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk>, <&sdmmc_cmd>, <&sdmmc_cd>, <&sdmmc_bus4>;
	vmmc-supply = <&vcc_sd>;
	vqmmc-supply = <&vccio_sd>;
	status = "okay";
};

&tsadc {
	rockchip,hw-tshut-mode = <0>;
	rockchip,hw-tshut-polarity = <0>;
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&usb_host1 {
	status = "okay";
};

&usb_otg {
	/*
	 * The otg controller is the only system power source,
	 * so needs to always stay in device mode.
	 */
	dr_mode = "peripheral";
	status = "okay";
};

&vopb {
	status = "okay";
};

&vopb_mmu {
	status = "okay";
};

&vopl {
	status = "okay";
};

&vopl_mmu {
	status = "okay";
};

&wdt {
	status = "okay";
};
