// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2017 <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "rk3288.dtsi"

/ {
	model = "Amarula Vyasa-RK3288";
	compatible = "amarula,vyasa-rk3288", "rockchip,rk3288";

	chosen {
		stdout-path = &uart2;
	};

	memory {
		reg = <0x0 0x0 0x0 0x80000000>;
		device_type = "memory";
	};

	dc12_vbat: dc12-vbat {
		compatible = "regulator-fixed";
		regulator-name = "dc12_vbat";
		regulator-min-microvolt = <12000000>;
		regulator-max-microvolt = <12000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	vboot_3v3: vboot-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "vboot_3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&dc12_vbat>;
	};

	vcc_sys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc_sys";
		regulator-min-microvolt = <3700000>;
		regulator-max-microvolt = <3700000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&dc12_vbat>;
	};

	vboot_5v: vboot-5v {
		compatible = "regulator-fixed";
		regulator-name = "vboot_sv";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&dc12_vbat>;
	};

	v3g_3v3: v3g-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "v3g_3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&dc12_vbat>;
	};

	vsus_5v: vsus-5v {
		compatible = "regulator-fixed";
		regulator-name = "vsus_5v";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vcc_io>;
	};

	vcc50_hdmi: vcc50-hdmi {
		compatible = "regulator-fixed";
		regulator-name = "vcc50_hdmi";
		enable-active-high;
		gpio = <&gpio7 RK_PB4 GPIO_ACTIVE_HIGH>; /* HDMI_EN */
		pinctrl-names = "default";
		pinctrl-0 = <&vcc50_hdmi_en>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vsus_5v>;
	};

	vusb1_5v: vusb1-5v {
		compatible = "regulator-fixed";
		regulator-name = "vusb1_5v";
		enable-active-high;
		gpio = <&gpio0 RK_PB4 GPIO_ACTIVE_HIGH>; /* OTG_VBUS_DRV */
		pinctrl-names = "default";
		pinctrl-0 = <&otg_vbus_drv>;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&vsus_5v>;
	};

	vusb2_5v: vusb2-5v {
		compatible = "regulator-fixed";
		regulator-name = "vusb2_5v";
		enable-active-high;
		gpio = <&gpio8 RK_PB1 GPIO_ACTIVE_HIGH>; /* USB2_PWR_EN */
		pinctrl-names = "default";
		pinctrl-0 = <&usb2_pwr_en>;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vsus_5v>;
	};

	ext_gmac: external-gmac-clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <125000000>;
		clock-output-names = "ext_gmac";
	};
};

&cpu0 {
	cpu-supply = <&vdd_cpu>;
};

&cpu1 {
	cpu-supply = <&vdd_cpu>;
};

&cpu2 {
	cpu-supply = <&vdd_cpu>;
};

&cpu3 {
	cpu-supply = <&vdd_cpu>;
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_pwr &emmc_bus8>;
	vmmc-supply = <&vcc_io>;
	status = "okay";
};

&gmac {
	assigned-clocks = <&cru SCLK_MAC>;
	assigned-clock-parents = <&ext_gmac>;
	clock_in_out = "input";
	pinctrl-names = "default";
	pinctrl-0 = <&rgmii_pins>, <&phy_rst>, <&phy_pmeb>, <&phy_int>;
	phy-supply = <&vcc_lan>;
	phy-mode = "rgmii";
	snps,reset-active-low;
	snps,reset-delays-us = <0 10000 1000000>;
	snps,reset-gpio = <&gpio4 RK_PB0 GPIO_ACTIVE_LOW>;
	tx_delay = <0x30>;
	rx_delay = <0x10>;
	status = "okay";
};

&gpu {
	mali-supply = <&vdd_gpu>;
	status = "okay";
};

&hdmi {
	ddc-i2c-bus = <&i2c5>;
	status = "okay";
};

&i2c0 {
	clock-frequency = <400000>;
	status = "okay";

	rk808: pmic@1b {
		compatible = "rockchip,rk808";
		reg = <0x1b>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PA4 IRQ_TYPE_LEVEL_LOW>;
		#clock-cells = <1>;
		clock-output-names = "xin32k", "rk808-clkout2";
		pinctrl-names = "default";
		pinctrl-0 = <&pmic_int &global_pwroff>;
		rockchip,system-power-controller;
		wakeup-source;

		vcc1-supply = <&vcc_sys>;
		vcc2-supply = <&vcc_sys>;
		vcc3-supply = <&vcc_sys>;
		vcc4-supply = <&vcc_sys>;
		vcc6-supply = <&vcc_sys>;
		vcc7-supply = <&vcc_sys>;
		vcc8-supply = <&vcc_io>;
		vcc9-supply = <&vcc_sys>;
		vcc10-supply = <&vcc_sys>;
		vcc11-supply = <&vcc_sys>;
		vcc12-supply = <&vcc_io>;

		regulators {
			vdd_cpu: DCDC_REG1 {
				regulator-name = "vdd_arm";
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_gpu: DCDC_REG2 {
				regulator-name = "vdd_gpu";
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1250000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-name = "vcc_ddr";
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc_io: DCDC_REG4 {
				regulator-name = "vcc_io";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcca_tp: LDO_REG1 {
				regulator-name = "vcc_tp";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcc_codec: LDO_REG2 {
				regulator-name = "vcc_codec";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_10: LDO_REG3 {
				regulator-name = "vdd_10";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc_gps: LDO_REG4 {
				regulator-name = "vcc_gps";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vccio_sd: LDO_REG5 {
				regulator-name = "vccio_sd";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vdd10_lcd: LDO_REG6 {
				regulator-name = "vdd10_lcd";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc_18: LDO_REG7 {
				regulator-name = "vcc_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc18_lcd: LDO_REG8 {
				regulator-name = "vcc18_lcd";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc_sd: SWITCH_REG1 {
				regulator-name = "vcc_sd";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc_lan: SWITCH_REG2 {
				regulator-name = "vcc_lan";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};
		};
	};
};

&i2c5 {
	status = "okay";
};

&io_domains {
	status = "okay";

	audio-supply = <&vcc_18>;
	bb-supply = <&vcc_io>;
	dvp-supply = <&vcc_io>;
	flash0-supply = <&vcc_18>;
	flash1-supply = <&vcc_lan>;
	gpio30-supply = <&vcc_io>;
	gpio1830-supply = <&vcc_io>;
	lcdc-supply = <&vcc_io>;
	sdcard-supply = <&vccio_sd>;
	wifi-supply = <&vcc_18>;
};

&sdmmc {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	disable-wp;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk>, <&sdmmc_cmd>, <&sdmmc_cd>, <&sdmmc_bus4>;
	vmmc-supply = <&vcc_sd>;
	vqmmc-supply = <&vccio_sd>;
	status = "okay";
};

&tsadc {
	rockchip,hw-tshut-mode = <1>; /* tshut mode 0:CRU 1:GPIO */
	rockchip,hw-tshut-polarity = <1>; /* tshut polarity 0:LOW 1:HIGH */
	status = "okay";
};

&uart2 {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&usb_host0_ehci {
	status = "okay";
};

&usb_host1 {
	pinctrl-names = "default";
	pinctrl-0 = <&phy_pwr_en>;
	status = "okay";
};

&usb_otg {
	vbus-supply = <&vusb1_5v>;
	status = "okay";
};

&vopb {
	status = "okay";
};

&vopb_mmu {
	status = "okay";
};

&vopl {
	status = "okay";
};

&vopl_mmu {
	status = "okay";
};

&wdt {
	status = "okay";
};

&pinctrl {
	pcfg_output_high: pcfg-output-high {
		output-high;
	};

	gmac {
		phy_int: phy-int {
			rockchip,pins = <0 RK_PB1 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		phy_pmeb: phy-pmeb {
			rockchip,pins = <0 RK_PA6 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		phy_rst: phy-rst {
			rockchip,pins = <4 RK_PB0 RK_FUNC_GPIO &pcfg_output_high>;
		};
	};

	hdmi {
		vcc50_hdmi_en: vcc50-hdmi-en {
			rockchip,pins = <7 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	pmic {
		pmic_int: pmic-int {
			rockchip,pins = <0 RK_PA4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	usb_host {
		phy_pwr_en: phy-pwr-en {
			rockchip,pins = <2 RK_PB1 RK_FUNC_GPIO &pcfg_output_high>;
		};

		usb2_pwr_en: usb2-pwr-en {
			rockchip,pins = <8 RK_PB1 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb_otg {
		otg_vbus_drv: otg-vbus-drv {
			rockchip,pins = <0 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;

		};
	};
};
