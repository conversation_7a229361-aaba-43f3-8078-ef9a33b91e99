// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Device tree file for Phytec phyCORE-RK3288 SoM
 * Copyright (C) 2017 PHYTEC Messtechnik GmbH
 * Author: <PERSON>adi<PERSON> <w.<PERSON><PERSON>@phytec.de>
 */

#include <dt-bindings/net/ti-dp83867.h>
#include "rk3288.dtsi"

/ {
	model = "Phytec RK3288 phyCORE";
	compatible = "phytec,rk3288-phycore-som", "rockchip,rk3288";

	/*
	 * Set the minimum memory size here and
	 * let the bootloader set the real size.
	 */
	memory {
		device_type = "memory";
		reg = <0x0 0x0 0x0 0x8000000>;
	};

	aliases {
		rtc0 = &i2c_rtc;
		rtc1 = &rk818;
	};

	ext_gmac: external-gmac-clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <125000000>;
		clock-output-names = "ext_gmac";
	};

	leds: user-leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&user_led_pin>;

		user_led: led-0 {
			label = "green_led";
			gpios = <&gpio7 2 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
			default-state = "keep";
		};
	};

	vdd_emmc_io: vdd-emmc-io {
		compatible = "regulator-fixed";
		regulator-name = "vdd_emmc_io";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&vdd_3v3_io>;
	};

	vdd_in_otg_out: vdd-in-otg-out {
		compatible = "regulator-fixed";
		regulator-name = "vdd_in_otg_out";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};

	vdd_misc_1v8: vdd-misc-1v8 {
		compatible = "regulator-fixed";
		regulator-name = "vdd_misc_1v8";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
	};
};

&emmc {
	status = "okay";
	bus-width = <8>;
	cap-mmc-highspeed;
	disable-wp;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_pwr &emmc_bus8>;
	vmmc-supply = <&vdd_3v3_io>;
	vqmmc-supply = <&vdd_emmc_io>;
};

&gmac {
	assigned-clocks = <&cru SCLK_MAC>;
	assigned-clock-parents = <&ext_gmac>;
	clock_in_out = "input";
	pinctrl-names = "default";
	pinctrl-0 = <&rgmii_pins &phy_rst &phy_int>;
	phy-handle = <&phy0>;
	phy-supply = <&vdd_eth_2v5>;
	phy-mode = "rgmii-id";
	snps,reset-active-low;
	snps,reset-delays-us = <0 10000 1000000>;
	snps,reset-gpio = <&gpio4 8 GPIO_ACTIVE_HIGH>;
	tx_delay = <0x0>;
	rx_delay = <0x0>;

	mdio0 {
		compatible = "snps,dwmac-mdio";
		#address-cells = <1>;
		#size-cells = <0>;

		phy0: ethernet-phy@0 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <0>;
			interrupt-parent = <&gpio4>;
			interrupts = <2 IRQ_TYPE_EDGE_FALLING>;
			ti,rx-internal-delay = <DP83867_RGMIIDCTL_2_00_NS>;
			ti,tx-internal-delay = <DP83867_RGMIIDCTL_2_00_NS>;
			ti,fifo-depth = <DP83867_PHYCR_FIFO_DEPTH_4_B_NIB>;
			enet-phy-lane-no-swap;
			ti,clk-output-sel = <DP83867_CLK_O_SEL_CHN_A_TCLK>;
		};
	};
};

&hdmi {
	ddc-i2c-bus = <&i2c5>;
};

&io_domains {
	status = "okay";
	sdcard-supply = <&vdd_io_sd>;
	flash0-supply = <&vdd_emmc_io>;
	flash1-supply = <&vdd_misc_1v8>;
	gpio1830-supply = <&vdd_3v3_io>;
	gpio30-supply = <&vdd_3v3_io>;
	bb-supply = <&vdd_3v3_io>;
	dvp-supply = <&vdd_3v3_io>;
	lcdc-supply = <&vdd_3v3_io>;
	wifi-supply = <&vdd_3v3_io>;
	audio-supply = <&vdd_3v3_io>;
};

&i2c0 {
	status = "okay";
	clock-frequency = <400000>;

	rk818: pmic@1c {
		compatible = "rockchip,rk818";
		reg = <0x1c>;
		interrupt-parent = <&gpio0>;
		interrupts = <4 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&pmic_int>;
		rockchip,system-power-controller;
		wakeup-source;
		#clock-cells = <1>;

		vcc1-supply = <&vdd_sys>;
		vcc2-supply = <&vdd_sys>;
		vcc3-supply = <&vdd_sys>;
		vcc4-supply = <&vdd_sys>;
		boost-supply = <&vdd_in_otg_out>;
		vcc6-supply = <&vdd_sys>;
		vcc7-supply = <&vdd_misc_1v8>;
		vcc8-supply = <&vdd_misc_1v8>;
		vcc9-supply = <&vdd_3v3_io>;
		vddio-supply = <&vdd_3v3_io>;

		regulators {
			vdd_log: DCDC_REG1 {
				regulator-name = "vdd_log";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_gpu: DCDC_REG2 {
				regulator-name = "vdd_gpu";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1250000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-name = "vcc_ddr";
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vdd_3v3_io: DCDC_REG4 {
				regulator-name = "vdd_3v3_io";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vdd_sys: DCDC_BOOST {
				regulator-name = "vdd_sys";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <5000000>;
				regulator-max-microvolt = <5000000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <5000000>;
				};
			};

			/* vcc9 */
			vdd_sd: SWITCH_REG {
				regulator-name = "vdd_sd";
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			/* vcc6 */
			vdd_eth_2v5: LDO_REG2 {
				regulator-name = "vdd_eth_2v5";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <2500000>;
				regulator-max-microvolt = <2500000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <2500000>;
				};
			};

			/* vcc7 */
			vdd_1v0: LDO_REG3 {
				regulator-name = "vdd_1v0";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			/* vcc8 */
			vdd_1v8_lcd_ldo: LDO_REG4 {
				regulator-name = "vdd_1v8_lcd_ldo";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			/* vcc8 */
			vdd_1v0_lcd: LDO_REG6 {
				regulator-name = "vdd_1v0_lcd";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			/* vcc7 */
			vdd_1v8_ldo: LDO_REG7 {
				regulator-name = "vdd_1v8_ldo";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-state-mem {
					regulator-off-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			/* vcc9 */
			vdd_io_sd: LDO_REG9 {
				regulator-name = "vdd_io_sd";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};
		};
	};

	/* M24C32-D */
	i2c_eeprom: eeprom@50 {
		compatible = "atmel,24c32";
		reg = <0x50>;
		pagesize = <32>;
	};

	vdd_cpu: regulator@60 {
		compatible = "fcs,fan53555";
		reg = <0x60>;
		fcs,suspend-voltage-selector = <1>;
		regulator-always-on;
		regulator-boot-on;
		regulator-enable-ramp-delay = <300>;
		regulator-name = "vdd_cpu";
		regulator-min-microvolt = <800000>;
		regulator-max-microvolt = <1430000>;
		regulator-ramp-delay = <8000>;
		vin-supply = <&vdd_sys>;
	};
};

&pinctrl {
	pcfg_output_high: pcfg-output-high {
		output-high;
	};

	emmc {
		/*
		 * We run eMMC at max speed; bump up drive strength.
		 * We also have external pulls, so disable the internal ones.
		 */
		emmc_clk: emmc-clk {
			rockchip,pins = <3 RK_PC2 2 &pcfg_pull_none_12ma>;
		};

		emmc_cmd: emmc-cmd {
			rockchip,pins = <3 RK_PC0 2 &pcfg_pull_none_12ma>;
		};

		emmc_bus8: emmc-bus8 {
			rockchip,pins = <3 RK_PA0 2 &pcfg_pull_none_12ma>,
					<3 RK_PA1 2 &pcfg_pull_none_12ma>,
					<3 RK_PA2 2 &pcfg_pull_none_12ma>,
					<3 RK_PA3 2 &pcfg_pull_none_12ma>,
					<3 RK_PA4 2 &pcfg_pull_none_12ma>,
					<3 RK_PA5 2 &pcfg_pull_none_12ma>,
					<3 RK_PA6 2 &pcfg_pull_none_12ma>,
					<3 RK_PA7 2 &pcfg_pull_none_12ma>;
		};
	};

	gmac {
		phy_int: phy-int {
			rockchip,pins = <4 RK_PA2 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		phy_rst: phy-rst {
			rockchip,pins = <4 RK_PB0 RK_FUNC_GPIO &pcfg_output_high>;
		};
	};

	leds {
		user_led_pin: user-led-pin {
			rockchip,pins = <7 RK_PA2 RK_FUNC_GPIO &pcfg_output_high>;
		};
	};

	pmic {
		pmic_int: pmic-int {
			rockchip,pins = <0 RK_PA4 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		/* Pin for switching state between sleep and non-sleep state */
		pmic_sleep: pmic-sleep {
			rockchip,pins = <0 RK_PA0 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};
};

&pwm1 {
	status = "okay";
};

&saradc {
	status = "okay";
	vref-supply = <&vdd_1v8_ldo>;
};

&spi2 {
	status = "okay";

	serial_flash: flash@0 {
		compatible = "micron,n25q128a13", "jedec,spi-nor";
		reg = <0x0>;
		spi-max-frequency = <50000000>;
		m25p,fast-read;
		#address-cells = <1>;
		#size-cells = <1>;
		status = "okay";
	};
};

&tsadc {
	status = "okay";
	rockchip,hw-tshut-mode = <0>;
	rockchip,hw-tshut-polarity = <0>;
};

&vopb {
	status = "okay";
};

&vopb_mmu {
	status = "okay";
};

&vopl {
	status = "okay";
};

&vopl_mmu {
	status = "okay";
};

&wdt {
	status = "okay";
};
