// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2014, 2015 <PERSON> <<EMAIL>>
 */

/dts-v1/;
#include <dt-bindings/input/input.h>
#include "rk3288.dtsi"

/ {
	model = "PopMetal-RK3288";
	compatible = "chipspark,popmetal-rk3288", "rockchip,rk3288";

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x0 0x0 0x80000000>;
	};

	ext_gmac: external-gmac-clock {
		compatible = "fixed-clock";
		clock-frequency = <125000000>;
		clock-output-names = "ext_gmac";
		#clock-cells = <0>;
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;

		pinctrl-names = "default";
		pinctrl-0 = <&pwrbtn>;

		key-power {
			gpios = <&gpio0 RK_PA5 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			label = "GPIO Key Power";
			linux,input-type = <1>;
			wakeup-source;
			debounce-interval = <100>;
		};
	};

	ir: ir-receiver {
		compatible = "gpio-ir-receiver";
		gpios = <&gpio0 RK_PA6 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&ir_int>;
	};

	vcc_flash: flash-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc_flash";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&vcc_io>;
	};

	vcc_sd: sdmmc-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio7 RK_PB3 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdmmc_pwr>;
		regulator-name = "vcc_sd";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc_io>;
	};

	vcc_sys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc_sys";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	/*
	 * A PT5128 creates both dovdd_1v8 and vcc28_dvp, controlled
	 * by the dvp_pwr pin.
	 */
	vcc18_dvp: vcc18-dvp-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc18-dvp";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&vcc28_dvp>;
	};

	vcc28_dvp: vcc28-dvp-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PC1 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&dvp_pwr>;
		regulator-name = "vcc28_dvp";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		regulator-always-on;
		vin-supply = <&vcc_io>;
	};
};

&cpu0 {
	cpu-supply = <&vdd_cpu>;
};

&cpu1 {
	cpu-supply = <&vdd_cpu>;
};

&cpu2 {
	cpu-supply = <&vdd_cpu>;
};

&cpu3 {
	cpu-supply = <&vdd_cpu>;
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	mmc-ddr-1_8v;
	mmc-hs200-1_8v;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_pwr &emmc_bus8>;
	vmmc-supply = <&vcc_io>;
	vqmmc-supply = <&vcc_flash>;
	status = "okay";
};

&sdmmc {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	disable-wp;                     /* wp not hooked up */
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk &sdmmc_cmd &sdmmc_cd &sdmmc_bus4>;
	sd-uhs-sdr12;
	sd-uhs-sdr25;
	sd-uhs-sdr50;
	sd-uhs-sdr104;
	vmmc-supply = <&vcc_sd>;
	vqmmc-supply = <&vccio_sd>;
	status = "okay";
};

&gmac {
	phy-supply = <&vcc_lan>;
	phy-mode = "rgmii";
	clock_in_out = "input";
	snps,reset-gpio = <&gpio4 RK_PB0 GPIO_ACTIVE_HIGH>;
	snps,reset-active-low;
	snps,reset-delays-us = <0 10000 1000000>;
	assigned-clocks = <&cru SCLK_MAC>;
	assigned-clock-parents = <&ext_gmac>;
	pinctrl-names = "default";
	pinctrl-0 = <&rgmii_pins>;
	tx_delay = <0x30>;
	rx_delay = <0x10>;
	status = "okay";
};

&hdmi {
	ddc-i2c-bus = <&i2c5>;
	status = "okay";
};

&i2c0 {
	status = "okay";
	clock-frequency = <400000>;

	rk808: pmic@1b {
		compatible = "rockchip,rk808";
		reg = <0x1b>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PA4 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&pmic_int &global_pwroff>;
		rockchip,system-power-controller;
		wakeup-source;
		#clock-cells = <1>;
		clock-output-names = "xin32k", "rk808-clkout2";

		vcc1-supply = <&vcc_sys>;
		vcc2-supply = <&vcc_sys>;
		vcc3-supply = <&vcc_sys>;
		vcc4-supply = <&vcc_sys>;
		vcc6-supply = <&vcc_sys>;
		vcc7-supply = <&vcc_sys>;
		vcc8-supply = <&vcc_18>;
		vcc9-supply = <&vcc_io>;
		vcc10-supply = <&vcc_io>;
		vcc11-supply = <&vcc_sys>;
		vcc12-supply = <&vcc_io>;
		vddio-supply = <&vcc_io>;

		regulators {
			vdd_cpu: DCDC_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1350000>;
				regulator-name = "vdd_arm";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_gpu: DCDC_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1250000>;
				regulator-name = "vdd_gpu";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vcc_ddr";
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc_io: DCDC_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcc_io";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcc_lan: LDO_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcc_lan";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vccio_sd: LDO_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vccio_sd";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_10: LDO_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-name = "vdd_10";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc18_lcd: LDO_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc18_lcd";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			ldo5: LDO_REG5 {
				regulator-always-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "ldo5";
			};

			vdd10_lcd: LDO_REG6 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-name = "vdd10_lcd";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc_18: LDO_REG7 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc_18";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcca_33: LDO_REG8 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcca_33";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vccio_wl: SWITCH_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vccio_wl";
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc_lcd: SWITCH_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vcc_lcd";
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};
		};
	};
};

&i2c1 {
	status = "okay";
	clock-frequency = <400000>;

	ak8963: ak8963@d {
		compatible = "asahi-kasei,ak8975";
		reg = <0x0d>;
		interrupt-parent = <&gpio8>;
		interrupts = <RK_PA1 IRQ_TYPE_EDGE_RISING>;
		pinctrl-names = "default";
		pinctrl-0 = <&comp_int>;
		vdd-supply = <&vcc_io>;
		vid-supply = <&vcc_io>;
	};

	l3g4200d: l3g4200d@69 {
		compatible = "st,l3g4200d-gyro";
		st,drdy-int-pin = <2>;
		reg = <0x69>;
		vdd-supply = <&vcc_io>;
		vddio-supply = <&vcc_io>;
	};

	mma8452: mma8452@1d {
		compatible = "fsl,mma8452";
		reg = <0x1d>;
		interrupt-parent = <&gpio8>;
		interrupts = <RK_PA0 IRQ_TYPE_EDGE_RISING>;
		pinctrl-names = "default";
		pinctrl-0 = <&gsensor_int>;
	};
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&i2c4 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&io_domains {
	status = "okay";

	audio-supply = <&vcca_33>;
	bb-supply = <&vcc_io>;
	dvp-supply = <&vcc18_dvp>;
	flash0-supply = <&vcc_flash>;
	flash1-supply = <&vcc_lan>;
	gpio30-supply = <&vcc_io>;
	gpio1830-supply = <&vcc_io>;
	lcdc-supply = <&vcc_io>;
	sdcard-supply = <&vccio_sd>;
	wifi-supply = <&vccio_wl>;
};

&pinctrl {
	ak8963 {
		comp_int: comp-int {
			rockchip,pins = <8 RK_PA1 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	buttons {
		pwrbtn: pwrbtn {
			rockchip,pins = <0 RK_PA5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	dvp {
		dvp_pwr: dvp-pwr {
			rockchip,pins = <0 RK_PC1 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	ir {
		ir_int: ir-int {
			rockchip,pins = <0 RK_PA6 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	mma8452 {
		gsensor_int: gsensor-int {
			rockchip,pins = <8 RK_PA0 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	pmic {
		pmic_int: pmic-int {
			rockchip,pins = <0 RK_PA4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	sdmmc {
		sdmmc_pwr: sdmmc-pwr {
			rockchip,pins = <7 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&tsadc {
	rockchip,hw-tshut-mode = <0>;
	rockchip,hw-tshut-polarity = <0>;
	status = "okay";
};

&vopb {
	status = "okay";
};

&vopb_mmu {
	status = "okay";
};

&vopl {
	status = "okay";
};

&vopl_mmu {
	status = "okay";
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&uart4 {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&usb_otg {
	status = "okay";
};
