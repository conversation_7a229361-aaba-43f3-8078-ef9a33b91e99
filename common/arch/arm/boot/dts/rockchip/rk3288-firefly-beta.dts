// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2014, 2015 FUKAUMI Naoki <<EMAIL>>
 */

/dts-v1/;
#include "rk3288-firefly.dtsi"

/ {
	model = "Firefly-RK3288 Beta";
	compatible = "firefly,firefly-rk3288-beta", "rockchip,rk3288";
};

&ir {
	gpios = <&gpio7 RK_PA5 GPIO_ACTIVE_LOW>;
};

&pinctrl {
	act8846 {
		pmic_vsel: pmic-vsel {
			rockchip,pins = <7 RK_PA1 RK_FUNC_GPIO &pcfg_output_low>;
		};
	};

	ir {
		ir_int: ir-int {
			rockchip,pins = <7 RK_PA5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};
};

&pwm0 {
	status = "okay";
};
