// SPDX-License-Identifier: (GPL-2.0+ OR MIT)

/*
 * Copyright (C) 2018 O.S. Systems Software LTDA.
 */

/dts-v1/;

#include "rv1108.dtsi"

/ {
	model = "Elgin RV1108 R1 board";
	compatible = "elgin,rv1108-r1", "rockchip,rv1108";

	aliases {
		mmc0 = &emmc;
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x08000000>;
	};

	chosen {
		stdout-path = "serial2:1500000n8";
	};

	vcc_sys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vsys";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
	};
};

&cpu0 {
	cpu-supply = <&vdd_core>;
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	no-sd;
	no-sdio;
	non-removable;
	mmc-ddr-1_8v;
	mmc-hs200-1_8v;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_bus8>;
	status = "okay";
};

&gmac {
	clock_in_out = "output";
	pinctrl-names = "default";
	pinctrl-0 = <&rmii_pins>;
	snps,reset-gpio = <&gpio1 RK_PC1 GPIO_ACTIVE_LOW>;
	snps,reset-active-low;
	status = "okay";
};

&i2c0 {
	clock-frequency = <400000>;
	i2c-scl-rising-time-ns = <275>;
	i2c-scl-falling-time-ns = <16>;
	status = "okay";

	rk805: pmic@18 {
		compatible = "rockchip,rk805";
		reg = <0x18>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PB4 IRQ_TYPE_LEVEL_LOW>;
		rockchip,system-power-controller;
		#clock-cells = <0>;

		vcc1-supply = <&vcc_sys>;
		vcc2-supply = <&vcc_sys>;
		vcc3-supply = <&vcc_sys>;
		vcc4-supply = <&vcc_sys>;
		vcc5-supply = <&vdd_buck2>;
		vcc6-supply = <&vdd_buck2>;

		regulators {
			vdd_core: DCDC_REG1 {
				regulator-name = "vdd_core";
				regulator-min-microvolt = <700000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <900000>;
				};
			};

			vdd_buck2: DCDC_REG2 {
				regulator-name = "vdd_buck2";
				regulator-min-microvolt = <2200000>;
				regulator-max-microvolt = <2200000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-name = "vcc_ddr";
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc_io: DCDC_REG4 {
				regulator-name = "vcc_io";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vdd_10: LDO_REG1 {
				regulator-name = "vdd_10";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_18: LDO_REG2 {
				regulator-name = "vcc_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd10_pmu: LDO_REG3 {
				regulator-name = "vdd10_pmu";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};
		};
	};
};

&spi {
	pinctrl-names = "default";
	pinctrl-0 = <&spim1_clk &spim1_cs0 &spim1_tx &spim1_rx>;
	status = "okay";

	dh2228fv: dac@0 {
		compatible = "rohm,dh2228fv";
		reg = <0>;
		spi-max-frequency = <24000000>;
		spi-cpha;
		spi-cpol;
	};
};

&u2phy {
	status = "okay";

	u2phy_host: host-port {
		status = "okay";
	};

	u2phy_otg: otg-port {
		status = "okay";
	};
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_xfer>;
	status = "okay";
};

&uart2 {
	status = "okay";
};

&usb_host_ehci {
	status = "okay";
};

&usb_host_ohci {
	status = "okay";
};

&usb_otg {
	status = "okay";
};
