// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2013 MundoReader S.L.
 * Author: <PERSON><PERSON> <<EMAIL>>
 */

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pinctrl/rockchip.h>
#include <dt-bindings/clock/rk3066a-cru.h>
#include <dt-bindings/power/rk3066-power.h>
#include "rk3xxx.dtsi"

/ {
	compatible = "rockchip,rk3066a";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		enable-method = "rockchip,rk3066-smp";

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <0x0>;
			operating-points =
				/* kHz    uV */
				<1416000 1300000>,
				<1200000 1175000>,
				<1008000 1125000>,
				<816000  1125000>,
				<600000  1100000>,
				<504000  1100000>,
				<312000  1075000>;
			clock-latency = <40000>;
			clocks = <&cru ARMCLK>;
		};
		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			next-level-cache = <&L2>;
			reg = <0x1>;
		};
	};

	display-subsystem {
		compatible = "rockchip,display-subsystem";
		ports = <&vop0_out>, <&vop1_out>;
	};

	sram: sram@10080000 {
		compatible = "mmio-sram";
		reg = <0x10080000 0x10000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x10080000 0x10000>;

		smp-sram@0 {
			compatible = "rockchip,rk3066-smp-sram";
			reg = <0x0 0x50>;
		};
	};

	vop0: vop@1010c000 {
		compatible = "rockchip,rk3066-vop";
		reg = <0x1010c000 0x19c>;
		interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_LCDC0>,
			 <&cru DCLK_LCDC0>,
			 <&cru HCLK_LCDC0>;
		clock-names = "aclk_vop", "dclk_vop", "hclk_vop";
		power-domains = <&power RK3066_PD_VIO>;
		resets = <&cru SRST_LCDC0_AXI>,
			 <&cru SRST_LCDC0_AHB>,
			 <&cru SRST_LCDC0_DCLK>;
		reset-names = "axi", "ahb", "dclk";
		status = "disabled";

		vop0_out: port {
			#address-cells = <1>;
			#size-cells = <0>;

			vop0_out_hdmi: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&hdmi_in_vop0>;
			};
		};
	};

	vop1: vop@1010e000 {
		compatible = "rockchip,rk3066-vop";
		reg = <0x1010e000 0x19c>;
		interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_LCDC1>,
			 <&cru DCLK_LCDC1>,
			 <&cru HCLK_LCDC1>;
		clock-names = "aclk_vop", "dclk_vop", "hclk_vop";
		power-domains = <&power RK3066_PD_VIO>;
		resets = <&cru SRST_LCDC1_AXI>,
			 <&cru SRST_LCDC1_AHB>,
			 <&cru SRST_LCDC1_DCLK>;
		reset-names = "axi", "ahb", "dclk";
		status = "disabled";

		vop1_out: port {
			#address-cells = <1>;
			#size-cells = <0>;

			vop1_out_hdmi: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&hdmi_in_vop1>;
			};
		};
	};

	hdmi: hdmi@10116000 {
		compatible = "rockchip,rk3066-hdmi";
		reg = <0x10116000 0x2000>;
		interrupts = <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_HDMI>;
		clock-names = "hclk";
		pinctrl-names = "default";
		pinctrl-0 = <&hdmii2c_xfer>, <&hdmi_hpd>;
		power-domains = <&power RK3066_PD_VIO>;
		rockchip,grf = <&grf>;
		#sound-dai-cells = <0>;
		status = "disabled";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			hdmi_in: port@0 {
				reg = <0>;
				#address-cells = <1>;
				#size-cells = <0>;

				hdmi_in_vop0: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&vop0_out_hdmi>;
				};

				hdmi_in_vop1: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&vop1_out_hdmi>;
				};
			};

			hdmi_out: port@1 {
				reg = <1>;
			};
		};
	};

	i2s0: i2s@10118000 {
		compatible = "rockchip,rk3066-i2s";
		reg = <0x10118000 0x2000>;
		interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2s0_bus>;
		clocks = <&cru SCLK_I2S0>, <&cru HCLK_I2S0>;
		clock-names = "i2s_clk", "i2s_hclk";
		dmas = <&dmac1_s 4>, <&dmac1_s 5>;
		dma-names = "tx", "rx";
		rockchip,playback-channels = <8>;
		rockchip,capture-channels = <2>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s1: i2s@1011a000 {
		compatible = "rockchip,rk3066-i2s";
		reg = <0x1011a000 0x2000>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2s1_bus>;
		clocks = <&cru SCLK_I2S1>, <&cru HCLK_I2S1>;
		clock-names = "i2s_clk", "i2s_hclk";
		dmas = <&dmac1_s 6>, <&dmac1_s 7>;
		dma-names = "tx", "rx";
		rockchip,playback-channels = <2>;
		rockchip,capture-channels = <2>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s2: i2s@1011c000 {
		compatible = "rockchip,rk3066-i2s";
		reg = <0x1011c000 0x2000>;
		interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2s2_bus>;
		clocks = <&cru SCLK_I2S2>, <&cru HCLK_I2S2>;
		clock-names = "i2s_clk", "i2s_hclk";
		dmas = <&dmac1_s 9>, <&dmac1_s 10>;
		dma-names = "tx", "rx";
		rockchip,playback-channels = <2>;
		rockchip,capture-channels = <2>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	cru: clock-controller@20000000 {
		compatible = "rockchip,rk3066a-cru";
		reg = <0x20000000 0x1000>;
		clocks = <&xin24m>;
		clock-names = "xin24m";
		rockchip,grf = <&grf>;
		#clock-cells = <1>;
		#reset-cells = <1>;
		assigned-clocks = <&cru PLL_CPLL>, <&cru PLL_GPLL>,
				  <&cru ACLK_CPU>, <&cru HCLK_CPU>,
				  <&cru PCLK_CPU>, <&cru ACLK_PERI>,
				  <&cru HCLK_PERI>, <&cru PCLK_PERI>;
		assigned-clock-rates = <400000000>, <594000000>,
				       <300000000>, <150000000>,
				       <75000000>, <300000000>,
				       <150000000>, <75000000>;
	};

	timer2: timer@2000e000 {
		compatible = "snps,dw-apb-timer";
		reg = <0x2000e000 0x100>;
		interrupts = <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_TIMER2>, <&cru PCLK_TIMER2>;
		clock-names = "timer", "pclk";
	};

	efuse: efuse@20010000 {
		compatible = "rockchip,rk3066a-efuse";
		reg = <0x20010000 0x4000>;
		#address-cells = <1>;
		#size-cells = <1>;
		clocks = <&cru PCLK_EFUSE>;
		clock-names = "pclk_efuse";

		cpu_leakage: cpu_leakage@17 {
			reg = <0x17 0x1>;
		};
	};

	timer0: timer@20038000 {
		compatible = "snps,dw-apb-timer";
		reg = <0x20038000 0x100>;
		interrupts = <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_TIMER0>, <&cru PCLK_TIMER0>;
		clock-names = "timer", "pclk";
	};

	timer1: timer@2003a000 {
		compatible = "snps,dw-apb-timer";
		reg = <0x2003a000 0x100>;
		interrupts = <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_TIMER1>, <&cru PCLK_TIMER1>;
		clock-names = "timer", "pclk";
	};

	tsadc: tsadc@20060000 {
		compatible = "rockchip,rk3066-tsadc";
		reg = <0x20060000 0x100>;
		clocks = <&cru SCLK_TSADC>, <&cru PCLK_TSADC>;
		clock-names = "saradc", "apb_pclk";
		interrupts = <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>;
		#io-channel-cells = <1>;
		resets = <&cru SRST_TSADC>;
		reset-names = "saradc-apb";
		status = "disabled";
	};

	pinctrl: pinctrl {
		compatible = "rockchip,rk3066a-pinctrl";
		rockchip,grf = <&grf>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		gpio0: gpio@******** {
			compatible = "rockchip,gpio-bank";
			reg = <0x******** 0x100>;
			interrupts = <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO0>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio1: gpio@2003c000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x2003c000 0x100>;
			interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO1>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio2: gpio@2003e000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x2003e000 0x100>;
			interrupts = <GIC_SPI 56 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO2>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio3: gpio@******** {
			compatible = "rockchip,gpio-bank";
			reg = <0x******** 0x100>;
			interrupts = <GIC_SPI 57 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO3>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio4: gpio@******** {
			compatible = "rockchip,gpio-bank";
			reg = <0x******** 0x100>;
			interrupts = <GIC_SPI 58 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO4>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio6: gpio@2000a000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x2000a000 0x100>;
			interrupts = <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO6>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		pcfg_pull_default: pcfg-pull-default {
			bias-pull-pin-default;
		};

		pcfg_pull_none: pcfg-pull-none {
			bias-disable;
		};

		emac {
			emac_xfer: emac-xfer {
				rockchip,pins = <1 RK_PC0 2 &pcfg_pull_none>, /* mac_clk */
						<1 RK_PC1 2 &pcfg_pull_none>, /* tx_en */
						<1 RK_PC2 2 &pcfg_pull_none>, /* txd1 */
						<1 RK_PC3 2 &pcfg_pull_none>, /* txd0 */
						<1 RK_PC4 2 &pcfg_pull_none>, /* rx_err */
						<1 RK_PC5 2 &pcfg_pull_none>, /* crs_dvalid */
						<1 RK_PC6 2 &pcfg_pull_none>, /* rxd1 */
						<1 RK_PC7 2 &pcfg_pull_none>; /* rxd0 */
			};

			emac_mdio: emac-mdio {
				rockchip,pins = <1 RK_PD0 2 &pcfg_pull_none>, /* mac_md */
						<1 RK_PD1 2 &pcfg_pull_none>; /* mac_mdclk */
			};
		};

		emmc {
			emmc_clk: emmc-clk {
				rockchip,pins = <3 RK_PD7 2 &pcfg_pull_default>;
			};

			emmc_cmd: emmc-cmd {
				rockchip,pins = <4 RK_PB1 2 &pcfg_pull_default>;
			};

			emmc_rst: emmc-rst {
				rockchip,pins = <4 RK_PB2 2 &pcfg_pull_default>;
			};

			/*
			 * The data pins are shared between nandc and emmc and
			 * not accessible through pinctrl. Also they should've
			 * been already set correctly by firmware, as
			 * flash/emmc is the boot-device.
			 */
		};

		hdmi {
			hdmi_hpd: hdmi-hpd {
				rockchip,pins = <0 RK_PA0 1 &pcfg_pull_default>;
			};

			hdmii2c_xfer: hdmii2c-xfer {
				rockchip,pins = <0 RK_PA1 1 &pcfg_pull_none>,
						<0 RK_PA2 1 &pcfg_pull_none>;
			};
		};

		i2c0 {
			i2c0_xfer: i2c0-xfer {
				rockchip,pins = <2 RK_PD4 1 &pcfg_pull_none>,
						<2 RK_PD5 1 &pcfg_pull_none>;
			};
		};

		i2c1 {
			i2c1_xfer: i2c1-xfer {
				rockchip,pins = <2 RK_PD6 1 &pcfg_pull_none>,
						<2 RK_PD7 1 &pcfg_pull_none>;
			};
		};

		i2c2 {
			i2c2_xfer: i2c2-xfer {
				rockchip,pins = <3 RK_PA0 1 &pcfg_pull_none>,
						<3 RK_PA1 1 &pcfg_pull_none>;
			};
		};

		i2c3 {
			i2c3_xfer: i2c3-xfer {
				rockchip,pins = <3 RK_PA2 2 &pcfg_pull_none>,
						<3 RK_PA3 2 &pcfg_pull_none>;
			};
		};

		i2c4 {
			i2c4_xfer: i2c4-xfer {
				rockchip,pins = <3 RK_PA4 1 &pcfg_pull_none>,
						<3 RK_PA5 1 &pcfg_pull_none>;
			};
		};

		pwm0 {
			pwm0_out: pwm0-out {
				rockchip,pins = <0 RK_PA3 1 &pcfg_pull_none>;
			};
		};

		pwm1 {
			pwm1_out: pwm1-out {
				rockchip,pins = <0 RK_PA4 1 &pcfg_pull_none>;
			};
		};

		pwm2 {
			pwm2_out: pwm2-out {
				rockchip,pins = <0 RK_PD6 1 &pcfg_pull_none>;
			};
		};

		pwm3 {
			pwm3_out: pwm3-out {
				rockchip,pins = <0 RK_PD7 1 &pcfg_pull_none>;
			};
		};

		spi0 {
			spi0_clk: spi0-clk {
				rockchip,pins = <1 RK_PA5 2 &pcfg_pull_default>;
			};
			spi0_cs0: spi0-cs0 {
				rockchip,pins = <1 RK_PA4 2 &pcfg_pull_default>;
			};
			spi0_tx: spi0-tx {
				rockchip,pins = <1 RK_PA7 2 &pcfg_pull_default>;
			};
			spi0_rx: spi0-rx {
				rockchip,pins = <1 RK_PA6 2 &pcfg_pull_default>;
			};
			spi0_cs1: spi0-cs1 {
				rockchip,pins = <4 RK_PB7 1 &pcfg_pull_default>;
			};
		};

		spi1 {
			spi1_clk: spi1-clk {
				rockchip,pins = <2 RK_PC3 2 &pcfg_pull_default>;
			};
			spi1_cs0: spi1-cs0 {
				rockchip,pins = <2 RK_PC4 2 &pcfg_pull_default>;
			};
			spi1_rx: spi1-rx {
				rockchip,pins = <2 RK_PC6 2 &pcfg_pull_default>;
			};
			spi1_tx: spi1-tx {
				rockchip,pins = <2 RK_PC5 2 &pcfg_pull_default>;
			};
			spi1_cs1: spi1-cs1 {
				rockchip,pins = <2 RK_PC7 2 &pcfg_pull_default>;
			};
		};

		uart0 {
			uart0_xfer: uart0-xfer {
				rockchip,pins = <1 RK_PA0 1 &pcfg_pull_default>,
						<1 RK_PA1 1 &pcfg_pull_default>;
			};

			uart0_cts: uart0-cts {
				rockchip,pins = <1 RK_PA2 1 &pcfg_pull_default>;
			};

			uart0_rts: uart0-rts {
				rockchip,pins = <1 RK_PA3 1 &pcfg_pull_default>;
			};
		};

		uart1 {
			uart1_xfer: uart1-xfer {
				rockchip,pins = <1 RK_PA4 1 &pcfg_pull_default>,
						<1 RK_PA5 1 &pcfg_pull_default>;
			};

			uart1_cts: uart1-cts {
				rockchip,pins = <1 RK_PA6 1 &pcfg_pull_default>;
			};

			uart1_rts: uart1-rts {
				rockchip,pins = <1 RK_PA7 1 &pcfg_pull_default>;
			};
		};

		uart2 {
			uart2_xfer: uart2-xfer {
				rockchip,pins = <1 RK_PB0 1 &pcfg_pull_default>,
						<1 RK_PB1 1 &pcfg_pull_default>;
			};
			/* no rts / cts for uart2 */
		};

		uart3 {
			uart3_xfer: uart3-xfer {
				rockchip,pins = <3 RK_PD3 1 &pcfg_pull_default>,
						<3 RK_PD4 1 &pcfg_pull_default>;
			};

			uart3_cts: uart3-cts {
				rockchip,pins = <3 RK_PD5 1 &pcfg_pull_default>;
			};

			uart3_rts: uart3-rts {
				rockchip,pins = <3 RK_PD6 1 &pcfg_pull_default>;
			};
		};

		sd0 {
			sd0_clk: sd0-clk {
				rockchip,pins = <3 RK_PB0 1 &pcfg_pull_default>;
			};

			sd0_cmd: sd0-cmd {
				rockchip,pins = <3 RK_PB1 1 &pcfg_pull_default>;
			};

			sd0_cd: sd0-cd {
				rockchip,pins = <3 RK_PB6 1 &pcfg_pull_default>;
			};

			sd0_wp: sd0-wp {
				rockchip,pins = <3 RK_PB7 1 &pcfg_pull_default>;
			};

			sd0_bus1: sd0-bus-width1 {
				rockchip,pins = <3 RK_PB2 1 &pcfg_pull_default>;
			};

			sd0_bus4: sd0-bus-width4 {
				rockchip,pins = <3 RK_PB2 1 &pcfg_pull_default>,
						<3 RK_PB3 1 &pcfg_pull_default>,
						<3 RK_PB4 1 &pcfg_pull_default>,
						<3 RK_PB5 1 &pcfg_pull_default>;
			};
		};

		sd1 {
			sd1_clk: sd1-clk {
				rockchip,pins = <3 RK_PC5 1 &pcfg_pull_default>;
			};

			sd1_cmd: sd1-cmd {
				rockchip,pins = <3 RK_PC0 1 &pcfg_pull_default>;
			};

			sd1_cd: sd1-cd {
				rockchip,pins = <3 RK_PC6 1 &pcfg_pull_default>;
			};

			sd1_wp: sd1-wp {
				rockchip,pins = <3 RK_PC7 1 &pcfg_pull_default>;
			};

			sd1_bus1: sd1-bus-width1 {
				rockchip,pins = <3 RK_PC1 1 &pcfg_pull_default>;
			};

			sd1_bus4: sd1-bus-width4 {
				rockchip,pins = <3 RK_PC1 1 &pcfg_pull_default>,
						<3 RK_PC2 1 &pcfg_pull_default>,
						<3 RK_PC3 1 &pcfg_pull_default>,
						<3 RK_PC4 1 &pcfg_pull_default>;
			};
		};

		i2s0 {
			i2s0_bus: i2s0-bus {
				rockchip,pins = <0 RK_PA7 1 &pcfg_pull_default>,
						<0 RK_PB0 1 &pcfg_pull_default>,
						<0 RK_PB1 1 &pcfg_pull_default>,
						<0 RK_PB2 1 &pcfg_pull_default>,
						<0 RK_PB3 1 &pcfg_pull_default>,
						<0 RK_PB4 1 &pcfg_pull_default>,
						<0 RK_PB5 1 &pcfg_pull_default>,
						<0 RK_PB6 1 &pcfg_pull_default>,
						<0 RK_PB7 1 &pcfg_pull_default>;
			};
		};

		i2s1 {
			i2s1_bus: i2s1-bus {
				rockchip,pins = <0 RK_PC0 1 &pcfg_pull_default>,
						<0 RK_PC1 1 &pcfg_pull_default>,
						<0 RK_PC2 1 &pcfg_pull_default>,
						<0 RK_PC3 1 &pcfg_pull_default>,
						<0 RK_PC4 1 &pcfg_pull_default>,
						<0 RK_PC5 1 &pcfg_pull_default>;
			};
		};

		i2s2 {
			i2s2_bus: i2s2-bus {
				rockchip,pins = <0 RK_PD0 1 &pcfg_pull_default>,
						<0 RK_PD1 1 &pcfg_pull_default>,
						<0 RK_PD2 1 &pcfg_pull_default>,
						<0 RK_PD3 1 &pcfg_pull_default>,
						<0 RK_PD4 1 &pcfg_pull_default>,
						<0 RK_PD5 1 &pcfg_pull_default>;
			};
		};
	};
};

&gpu {
	compatible = "rockchip,rk3066-mali", "arm,mali-400";
	interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
	interrupt-names = "gp",
			  "gpmmu",
			  "pp0",
			  "ppmmu0",
			  "pp1",
			  "ppmmu1",
			  "pp2",
			  "ppmmu2",
			  "pp3",
			  "ppmmu3";
	power-domains = <&power RK3066_PD_GPU>;
};

&grf {
	compatible = "rockchip,rk3066-grf", "syscon", "simple-mfd";

	usbphy: usbphy {
		compatible = "rockchip,rk3066a-usb-phy";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";

		usbphy0: usb-phy@17c {
			reg = <0x17c>;
			clocks = <&cru SCLK_OTGPHY0>;
			clock-names = "phyclk";
			#clock-cells = <0>;
			#phy-cells = <0>;
		};

		usbphy1: usb-phy@188 {
			reg = <0x188>;
			clocks = <&cru SCLK_OTGPHY1>;
			clock-names = "phyclk";
			#clock-cells = <0>;
			#phy-cells = <0>;
		};
	};
};

&i2c0 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c0_xfer>;
};

&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_xfer>;
};

&i2c2 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_xfer>;
};

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_xfer>;
};

&i2c4 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4_xfer>;
};

&mmc0 {
	clock-frequency = <50000000>;
	dmas = <&dmac2 1>;
	dma-names = "rx-tx";
	max-frequency = <50000000>;
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk &sd0_cmd &sd0_cd &sd0_bus4>;
};

&mmc1 {
	dmas = <&dmac2 3>;
	dma-names = "rx-tx";
	pinctrl-names = "default";
	pinctrl-0 = <&sd1_clk &sd1_cmd &sd1_cd &sd1_bus4>;
};

&emmc {
	dmas = <&dmac2 4>;
	dma-names = "rx-tx";
};

&pmu {
	power: power-controller {
		compatible = "rockchip,rk3066-power-controller";
		#power-domain-cells = <1>;
		#address-cells = <1>;
		#size-cells = <0>;

		power-domain@RK3066_PD_VIO {
			reg = <RK3066_PD_VIO>;
			clocks = <&cru ACLK_LCDC0>,
				 <&cru ACLK_LCDC1>,
				 <&cru DCLK_LCDC0>,
				 <&cru DCLK_LCDC1>,
				 <&cru HCLK_LCDC0>,
				 <&cru HCLK_LCDC1>,
				 <&cru SCLK_CIF1>,
				 <&cru ACLK_CIF1>,
				 <&cru HCLK_CIF1>,
				 <&cru SCLK_CIF0>,
				 <&cru ACLK_CIF0>,
				 <&cru HCLK_CIF0>,
				 <&cru HCLK_HDMI>,
				 <&cru ACLK_IPP>,
				 <&cru HCLK_IPP>,
				 <&cru ACLK_RGA>,
				 <&cru HCLK_RGA>;
			pm_qos = <&qos_lcdc0>,
				 <&qos_lcdc1>,
				 <&qos_cif0>,
				 <&qos_cif1>,
				 <&qos_ipp>,
				 <&qos_rga>;
			#power-domain-cells = <0>;
		};

		power-domain@RK3066_PD_VIDEO {
			reg = <RK3066_PD_VIDEO>;
			clocks = <&cru ACLK_VDPU>,
				 <&cru ACLK_VEPU>,
				 <&cru HCLK_VDPU>,
				 <&cru HCLK_VEPU>;
			pm_qos = <&qos_vpu>;
			#power-domain-cells = <0>;
		};

		power-domain@RK3066_PD_GPU {
			reg = <RK3066_PD_GPU>;
			clocks = <&cru ACLK_GPU>;
			pm_qos = <&qos_gpu>;
			#power-domain-cells = <0>;
		};
	};
};

&pwm0 {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm0_out>;
};

&pwm1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm1_out>;
};

&pwm2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm2_out>;
};

&pwm3 {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm3_out>;
};

&spi0 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi0_clk &spi0_tx &spi0_rx &spi0_cs0>;
};

&spi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi1_clk &spi1_tx &spi1_rx &spi1_cs0>;
};

&uart0 {
	compatible = "rockchip,rk3066-uart", "snps,dw-apb-uart";
	dmas = <&dmac1_s 0>, <&dmac1_s 1>;
	dma-names = "tx", "rx";
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_xfer>;
};

&uart1 {
	compatible = "rockchip,rk3066-uart", "snps,dw-apb-uart";
	dmas = <&dmac1_s 2>, <&dmac1_s 3>;
	dma-names = "tx", "rx";
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_xfer>;
};

&uart2 {
	compatible = "rockchip,rk3066-uart", "snps,dw-apb-uart";
	dmas = <&dmac2 6>, <&dmac2 7>;
	dma-names = "tx", "rx";
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_xfer>;
};

&uart3 {
	compatible = "rockchip,rk3066-uart", "snps,dw-apb-uart";
	dmas = <&dmac2 8>, <&dmac2 9>;
	dma-names = "tx", "rx";
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_xfer>;
};

&vpu {
	power-domains = <&power RK3066_PD_VIDEO>;
};

&wdt {
	compatible = "rockchip,rk3066-wdt", "snps,dw-wdt";
};

&emac {
	compatible = "rockchip,rk3066-emac";
};
