// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2013 <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include <dt-bindings/input/input.h>
#include "rk3188.dtsi"

/ {
	model = "Radxa Rock";
	compatible = "radxa,rock", "rockchip,rk3188";

	aliases {
		mmc0 = &mmc0;
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x80000000>;
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;

		key-power {
			gpios = <&gpio0 RK_PA4 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			label = "GPIO Key Power";
			linux,input-type = <1>;
			wakeup-source;
			debounce-interval = <100>;
		};
	};

	gpio-leds {
		compatible = "gpio-leds";

		green_led: led-0 {
			label = "rock:green:user1";
			gpios = <&gpio0 RK_PB4 GPIO_ACTIVE_LOW>;
			default-state = "off";
		};

		blue_led: led-1 {
			label = "rock:blue:user2";
			gpios = <&gpio0 RK_PB6 GPIO_ACTIVE_LOW>;
			default-state = "off";
		};

		sleep_led: led-2 {
			label = "rock:red:power";
			gpios = <&gpio0 RK_PB7 GPIO_ACTIVE_HIGH>;
			default-state = "off";
		};
	};

	sound {
		compatible = "simple-audio-card";
		simple-audio-card,name = "SPDIF";

		simple-audio-card,dai-link@1 {  /* S/PDIF - S/PDIF */
			cpu { sound-dai = <&spdif>; };
			codec { sound-dai = <&spdif_out>; };
		};
	};

	spdif_out: spdif-out {
		compatible = "linux,spdif-dit";
		#sound-dai-cells = <0>;
	};

	ir_recv: ir-receiver {
		compatible = "gpio-ir-receiver";
		gpios = <&gpio0 RK_PB2 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&ir_recv_pin>;
	};

	vcc_otg: usb-otg-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio2 RK_PD7 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&otg_vbus_drv>;
		regulator-name = "otg-vbus";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	vcc_sd0: sdmmc-regulator {
		compatible = "regulator-fixed";
		regulator-name = "sdmmc-supply";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio3 RK_PA1 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdmmc_pwr>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc_io>;
	};

	vcc_host: usb-host-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PA3 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&host_vbus_drv>;
		regulator-name = "host-pwr";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	vsys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vsys";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
	};
};

&emac {
	phy = <&phy0>;
	phy-supply = <&vcc_rmii>;
	pinctrl-names = "default";
	pinctrl-0 = <&emac_xfer>, <&emac_mdio>, <&phy_int>;
	status = "okay";

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;

		phy0: ethernet-phy@0 {
			reg = <0>;
			interrupt-parent = <&gpio3>;
			interrupts = <RK_PD2 IRQ_TYPE_LEVEL_LOW>;
		};
	};
};

&cpu0 {
	cpu-supply = <&vdd_arm>;
};

&cpu1 {
	cpu-supply = <&vdd_arm>;
};

&cpu2 {
	cpu-supply = <&vdd_arm>;
};

&cpu3 {
	cpu-supply = <&vdd_arm>;
};

&gpu {
	status = "okay";
};

&i2c1 {
	status = "okay";
	clock-frequency = <400000>;

	rtc@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PB5 IRQ_TYPE_EDGE_FALLING>;
		pinctrl-names = "default";
		pinctrl-0 = <&rtc_int>;
		#clock-cells = <0>;
		clock-output-names = "xin32k";
	};

	act8846: act8846@5a {
		compatible = "active-semi,act8846";
		reg = <0x5a>;
		status = "okay";
		system-power-controller;

		pinctrl-names = "default";
		pinctrl-0 = <&act8846_dvs0_ctl>;

		vp1-supply = <&vsys>;
		vp2-supply = <&vsys>;
		vp3-supply = <&vsys>;
		vp4-supply = <&vsys>;
		inl1-supply = <&vcc_io>;
		inl2-supply = <&vsys>;
		inl3-supply = <&vsys>;

		regulators {
			vcc_ddr: REG1 {
				regulator-name = "VCC_DDR";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			vdd_log: REG2 {
				regulator-name = "VDD_LOG";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vdd_arm: REG3 {
				regulator-name = "VDD_ARM";
				regulator-min-microvolt = <875000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
			};

			vcc_io: REG4 {
				regulator-name = "VCC_IO";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd_10: REG5 {
				regulator-name = "VDD_10";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vdd_hdmi: REG6 {
				regulator-name = "VDD_HDMI";
				regulator-min-microvolt = <2500000>;
				regulator-max-microvolt = <2500000>;
				regulator-always-on;
			};

			vcc18: REG7 {
				regulator-name = "VCC_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			vcca_33: REG8 {
				regulator-name = "VCCA_33";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vcc_rmii: REG9 {
				regulator-name = "VCC_RMII";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
			};

			vccio_wl: REG10 {
				regulator-name = "VCCIO_WL";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vcc_18: REG11 {
				regulator-name = "VCC18_IO";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			vcc28: REG12 {
				regulator-name = "VCC_28";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
			};
		};
	};
};

&mmc0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk>, <&sd0_cmd>, <&sd0_cd>, <&sd0_bus4>;
	vmmc-supply = <&vcc_sd0>;

	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	disable-wp;
};

&pwm1 {
	status = "okay";
};

&pwm2 {
	status = "okay";
};

&pwm3 {
	status = "okay";
};

&pinctrl {
	pcfg_output_low: pcfg-output-low {
		output-low;
	};

	act8846 {
		act8846_dvs0_ctl: act8846-dvs0-ctl {
			rockchip,pins = <3 RK_PD3 RK_FUNC_GPIO &pcfg_output_low>;
		};
	};

	hym8563 {
		rtc_int: rtc-int {
			rockchip,pins = <0 RK_PA0 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	lan8720a  {
		phy_int: phy-int {
			rockchip,pins = <3 RK_PD2 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	ir-receiver {
		ir_recv_pin: ir-recv-pin {
			rockchip,pins = <0 RK_PB2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	sd0 {
		sdmmc_pwr: sdmmc-pwr {
			rockchip,pins = <3 RK_PA1 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb {
		host_vbus_drv: host-vbus-drv {
			rockchip,pins = <0 RK_PA3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
		otg_vbus_drv: otg-vbus-drv {
			rockchip,pins = <2 RK_PD7 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&spdif {
	status = "okay";
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&usbphy {
	status = "okay";
};

&usb_host {
	status = "okay";
};

&usb_otg {
	status = "okay";
};

&wdt {
	status = "okay";
};
