// SPDX-License-Identifier: (GPL-2.0+ OR MIT)

/dts-v1/;

#include "rk322x.dtsi"

/ {
	model = "Rockchip RK3228 Evaluation board";
	compatible = "rockchip,rk3228-evb", "rockchip,rk3228";

	aliases {
		mmc0 = &emmc;
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x40000000>;
	};

	vcc_phy: vcc-phy-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		regulator-name = "vcc_phy";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
		regulator-boot-on;
	};
};

&emmc {
	cap-mmc-highspeed;
	mmc-ddr-1_8v;
	disable-wp;
	non-removable;
	status = "okay";
};

&gmac {
	assigned-clocks = <&cru SCLK_MAC_SRC>;
	assigned-clock-rates = <50000000>;
	clock_in_out = "output";
	phy-supply = <&vcc_phy>;
	phy-mode = "rmii";
	phy-handle = <&phy>;
	status = "okay";

	mdio {
		compatible = "snps,dwmac-mdio";
		#address-cells = <1>;
		#size-cells = <0>;

		phy: ethernet-phy@0 {
			compatible = "ethernet-phy-id1234.d400", "ethernet-phy-ieee802.3-c22";
			reg = <0>;
			clocks = <&cru SCLK_MAC_PHY>;
			resets = <&cru SRST_MACPHY>;
			phy-is-integrated;
		};
	};
};

&tsadc {
	status = "okay";

	rockchip,hw-tshut-mode = <0>; /* tshut mode 0:CRU 1:GPIO */
	rockchip,hw-tshut-polarity = <1>; /* tshut polarity 0:LOW 1:HIGH */
};

&uart2 {
	status = "okay";
};
