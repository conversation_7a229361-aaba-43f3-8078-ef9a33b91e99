// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2017 Fuzhou Rockchip Electronics Co., Ltd.
 */

/dts-v1/;

#include "rk3288-tinker.dtsi"

/ {
	model = "Rockchip RK3288 Asus Tinker Board S";
	compatible = "asus,rk3288-tinker-s", "rockchip,rk3288";
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_pwr &emmc_bus8>;
	max-frequency = <150000000>;
	mmc-hs200-1_8v;
	mmc-ddr-1_8v;
	status = "okay";
};

&hdmi {
	pinctrl-names = "default";
	pinctrl-0 = <&hdmi_cec_c0>;
};
