// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2020 Fuzhou Rockchip Electronics Co., Ltd
 */

#include <dt-bindings/pinctrl/rockchip.h>
#include <arm64/rockchip/rockchip-pinconf.dtsi>

/*
 * This file is auto generated by pin2dts tool, please keep these code
 * by adding changes at end of this file.
 */
&pinctrl {
	clk_out_ethernet {
		/omit-if-no-ref/
		clk_out_ethernetm1_pins: clk-out-ethernetm1-pins {
			rockchip,pins =
				/* clk_out_ethernet_m1 */
				<2 RK_PC5 2 &pcfg_pull_none>;
		};
	};
	emmc {
		/omit-if-no-ref/
		emmc_rstnout: emmc-rstnout {
			rockchip,pins =
				/* emmc_rstn */
				<1 RK_PA3 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		emmc_bus8: emmc-bus8 {
			rockchip,pins =
				/* emmc_d0 */
				<0 RK_PC4 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d1 */
				<0 RK_PC5 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d2 */
				<0 RK_PC6 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d3 */
				<0 RK_PC7 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d4 */
				<0 RK_PD0 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d5 */
				<0 RK_PD1 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d6 */
				<0 RK_PD2 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d7 */
				<0 RK_PD3 2 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		emmc_clk: emmc-clk {
			rockchip,pins =
				/* emmc_clko */
				<0 RK_PD7 2 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		emmc_cmd: emmc-cmd {
			rockchip,pins =
				/* emmc_cmd */
				<0 RK_PD5 2 &pcfg_pull_up_drv_level_2>;
		};
	};
	fspi {
		/omit-if-no-ref/
		fspi_pins: fspi-pins {
			rockchip,pins =
				/* fspi_clk */
				<1 RK_PA3 3 &pcfg_pull_down>,
				/* fspi_cs0n */
				<0 RK_PD4 3 &pcfg_pull_up>,
				/* fspi_d0 */
				<1 RK_PA0 3 &pcfg_pull_up>,
				/* fspi_d1 */
				<1 RK_PA1 3 &pcfg_pull_up>,
				/* fspi_d2 */
				<0 RK_PD6 3 &pcfg_pull_up>,
				/* fspi_d3 */
				<1 RK_PA2 3 &pcfg_pull_up>;
		};
	};
	i2c0 {
		/omit-if-no-ref/
		i2c0_xfer: i2c0-xfer {
			rockchip,pins =
				/* i2c0_scl */
				<0 RK_PB4 1 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c0_sda */
				<0 RK_PB5 1 &pcfg_pull_none_drv_level_0_smt>;
		};
	};
	rgmii {
		/omit-if-no-ref/
		rgmiim1_pins: rgmiim1-pins {
			rockchip,pins =
				/* rgmii_mdc_m1 */
				<2 RK_PC2 2 &pcfg_pull_none>,
				/* rgmii_mdio_m1 */
				<2 RK_PC1 2 &pcfg_pull_none>,
				/* rgmii_rxclk_m1 */
				<2 RK_PD3 2 &pcfg_pull_none>,
				/* rgmii_rxd0_m1 */
				<2 RK_PB5 2 &pcfg_pull_none>,
				/* rgmii_rxd1_m1 */
				<2 RK_PB6 2 &pcfg_pull_none>,
				/* rgmii_rxd2_m1 */
				<2 RK_PC7 2 &pcfg_pull_none>,
				/* rgmii_rxd3_m1 */
				<2 RK_PD0 2 &pcfg_pull_none>,
				/* rgmii_rxdv_m1 */
				<2 RK_PB4 2 &pcfg_pull_none>,
				/* rgmii_txclk_m1 */
				<2 RK_PD2 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txd0_m1 */
				<2 RK_PC3 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txd1_m1 */
				<2 RK_PC4 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txd2_m1 */
				<2 RK_PD1 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txd3_m1 */
				<2 RK_PA4 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txen_m1 */
				<2 RK_PC6 2 &pcfg_pull_none_drv_level_3>;
		};
	};
	sdmmc0 {
		/omit-if-no-ref/
		sdmmc0_bus4: sdmmc0-bus4 {
			rockchip,pins =
				/* sdmmc0_d0 */
				<1 RK_PA4 1 &pcfg_pull_up_drv_level_2>,
				/* sdmmc0_d1 */
				<1 RK_PA5 1 &pcfg_pull_up_drv_level_2>,
				/* sdmmc0_d2 */
				<1 RK_PA6 1 &pcfg_pull_up_drv_level_2>,
				/* sdmmc0_d3 */
				<1 RK_PA7 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdmmc0_clk: sdmmc0-clk {
			rockchip,pins =
				/* sdmmc0_clk */
				<1 RK_PB0 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdmmc0_cmd: sdmmc0-cmd {
			rockchip,pins =
				/* sdmmc0_cmd */
				<1 RK_PB1 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdmmc0_det: sdmmc0-det {
			rockchip,pins =
				<0 RK_PA3 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sdmmc0_pwr: sdmmc0-pwr {
			rockchip,pins =
				<0 RK_PC0 1 &pcfg_pull_none>;
		};
	};
	sdmmc1 {
		/omit-if-no-ref/
		sdmmc1_bus4: sdmmc1-bus4 {
			rockchip,pins =
				/* sdmmc1_d0 */
				<1 RK_PB4 1 &pcfg_pull_up_drv_level_2>,
				/* sdmmc1_d1 */
				<1 RK_PB5 1 &pcfg_pull_up_drv_level_2>,
				/* sdmmc1_d2 */
				<1 RK_PB6 1 &pcfg_pull_up_drv_level_2>,
				/* sdmmc1_d3 */
				<1 RK_PB7 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdmmc1_clk: sdmmc1-clk {
			rockchip,pins =
				/* sdmmc1_clk */
				<1 RK_PB2 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdmmc1_cmd: sdmmc1-cmd {
			rockchip,pins =
				/* sdmmc1_cmd */
				<1 RK_PB3 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdmmc1_det: sdmmc1-det {
			rockchip,pins =
				<1 RK_PD0 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sdmmc1_pwr: sdmmc1-pwr {
			rockchip,pins =
				<1 RK_PD1 2 &pcfg_pull_none>;
		};
	};
	uart0 {
		/omit-if-no-ref/
		uart0_xfer: uart0-xfer {
			rockchip,pins =
				/* uart0_rx */
				<1 RK_PC2 1 &pcfg_pull_up>,
				/* uart0_tx */
				<1 RK_PC3 1 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart0_ctsn: uart0-ctsn {
			rockchip,pins =
				<1 RK_PC1 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart0_rtsn: uart0-rtsn {
			rockchip,pins =
				<1 RK_PC0 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart0_rtsn_gpio: uart0-rts-pin {
			rockchip,pins =
				<1 RK_PC0 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
	uart1 {
		/omit-if-no-ref/
		uart1m0_xfer: uart1m0-xfer {
			rockchip,pins =
				/* uart1_rx_m0 */
				<0 RK_PB7 2 &pcfg_pull_up>,
				/* uart1_tx_m0 */
				<0 RK_PB6 2 &pcfg_pull_up>;
		};
	};
	uart2 {
		/omit-if-no-ref/
		uart2m1_xfer: uart2m1-xfer {
			rockchip,pins =
				/* uart2_rx_m1 */
				<3 RK_PA3 1 &pcfg_pull_up>,
				/* uart2_tx_m1 */
				<3 RK_PA2 1 &pcfg_pull_up>;
		};
	};
	uart3 {
		/omit-if-no-ref/
		uart3m0_xfer: uart3m0-xfer {
			rockchip,pins =
				/* uart3_rx_m0 */
				<3 RK_PC7 4 &pcfg_pull_up>,
				/* uart3_tx_m0 */
				<3 RK_PC6 4 &pcfg_pull_up>;
		};
	};
	uart4 {
		/omit-if-no-ref/
		uart4m0_xfer: uart4m0-xfer {
			rockchip,pins =
				/* uart4_rx_m0 */
				<3 RK_PA5 4 &pcfg_pull_up>,
				/* uart4_tx_m0 */
				<3 RK_PA4 4 &pcfg_pull_up>;
		};
	};
	uart5 {
		/omit-if-no-ref/
		uart5m0_xfer: uart5m0-xfer {
			rockchip,pins =
				/* uart5_rx_m0 */
				<3 RK_PA7 4 &pcfg_pull_up>,
				/* uart5_tx_m0 */
				<3 RK_PA6 4 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart5m2_xfer: uart5m2-xfer {
			rockchip,pins =
				/* uart5_rx_m2 */
				<2 RK_PA1 3 &pcfg_pull_up>,
				/* uart5_tx_m2 */
				<2 RK_PA0 3 &pcfg_pull_up>;
		};
	};
};
