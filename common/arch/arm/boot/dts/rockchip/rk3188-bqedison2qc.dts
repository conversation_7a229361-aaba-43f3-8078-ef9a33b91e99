// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2018 MundoReader S.L.
 * Author:  <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include <dt-bindings/i2c/i2c.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include "rk3188.dtsi"

/ {
	model = "BQ Edison2 Quad-Core";
	compatible = "mundoreader,bq-edison2qc", "rockchip,rk3188";

	aliases {
		mmc0 = &mmc0;
		mmc1 = &mmc1;
		mmc2 = &emmc;
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x80000000>;
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		power-supply = <&vsys>;
		pwms = <&pwm1 0 25000>;
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;
		pinctrl-names = "default";
		pinctrl-0 = <&pwr_key &usb_int>;

		key-power {
			gpios = <&gpio0 RK_PA4 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			label = "GPIO Key Power";
			linux,input-type = <1>;
			debounce-interval = <100>;
			wakeup-source;
		};

		wake_on_usb: key-wake-on-usb {
			label = "Wake-on-USB";
			gpios = <&gpio0 RK_PA7 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_WAKEUP>;
			wakeup-source;
		};
	};

	gpio-poweroff {
		compatible = "gpio-poweroff";
		gpios = <&gpio0 RK_PA0 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&pwr_hold>;
		/* only drive the pin low until device is off */
		active-delay-ms = <3000>;
	};

	lvds-encoder {
		compatible = "ti,sn75lvds83", "lvds-encoder";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				lvds_in_vop0: endpoint {
					remote-endpoint = <&vop0_out_lvds>;
				};
			};

			port@1 {
				reg = <1>;

				lvds_out_panel: endpoint {
					remote-endpoint = <&panel_in_lvds>;
				};
			};
		};
	};

	panel {
		compatible = "innolux,ee101ia-01d", "panel-lvds";
		backlight = <&backlight>;

		/* pin LCD_CS, Nshtdn input of lvds-encoder */
		enable-gpios = <&gpio3 RK_PD6 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&lcd_cs>;
		power-supply = <&vcc_lcd>;

		data-mapping = "vesa-24";
		height-mm = <163>;
		width-mm = <261>;

		panel-timing {
			clock-frequency = <72000000>;
			hactive = <1280>;
			vactive = <800>;
			hback-porch = <160>;
			hfront-porch = <16>;
			hsync-len = <10>;
			vback-porch = <23>;
			vfront-porch = <12>;
			vsync-len = <3>;
		};

		port {
			panel_in_lvds: endpoint {
				remote-endpoint = <&lvds_out_panel>;
			};
		};
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		clocks = <&hym8563>;
		clock-names = "ext_clock";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_reg_on>;
		reset-gpios = <&gpio3 RK_PD0 GPIO_ACTIVE_LOW>;
	};

	avdd_cif: cif-avdd-regulator {
		compatible = "regulator-fixed";
		regulator-name = "avdd-cif";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		gpio = <&gpio1 RK_PA6 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&cif_avdd_en>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc28_cif>;
	};

	vcc_5v: vcc-5v-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc-5v";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio0 RK_PA3 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&v5_drv>;
		vin-supply = <&vsys>;
	};

	vcc_lcd: lcd-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc-lcd";
		gpio = <&gpio0 RK_PB0 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&lcd_en>;
		startup-delay-us = <50000>;
		vin-supply = <&vcc_io>;
	};

	vcc_otg: usb-otg-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc-otg";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio3 RK_PB1 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&otg_drv>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc_5v>;
	};

	vcc_sd: sdmmc-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc-sd";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio3 RK_PA1 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdmmc_pwr>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc_io>;
	};

	vccq_emmc: emmc-vccq-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vccq-emmc";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		vin-supply = <&vcc_io>;
	};

	/* supplied from the bq24196 */
	vsys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vsys";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
	};
};

&cpu0 {
	cpu-supply = <&vdd_arm>;
};

&cpu1 {
	cpu-supply = <&vdd_arm>;
};

&cpu2 {
	cpu-supply = <&vdd_arm>;
};

&cpu3 {
	cpu-supply = <&vdd_arm>;
};

&cru {
	assigned-clocks = <&cru PLL_GPLL>, <&cru PLL_CPLL>,
			  <&cru ACLK_CPU>,
			  <&cru HCLK_CPU>, <&cru PCLK_CPU>,
			  <&cru ACLK_PERI>, <&cru HCLK_PERI>,
			  <&cru PCLK_PERI>;
	assigned-clock-rates = <594000000>, <504000000>,
			       <300000000>,
			       <150000000>, <75000000>,
			       <300000000>, <150000000>,
			       <75000000>;
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk &emmc_cmd>;
	vmmc-supply = <&vcc_io>;
	vqmmc-supply = <&vccq_emmc>;
	status = "okay";
};

&gpu {
	status = "okay";
};

&i2c0 {
	clock-frequency = <400000>;
	status = "okay";

	lis3de: accelerometer@29 {
		compatible = "st,lis3de";
		reg = <0x29>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PB7 IRQ_TYPE_EDGE_RISING>;
		pinctrl-names = "default";
		pinctrl-0 = <&gsensor_int>;
		rotation-matrix = "1", "0", "0",
				  "0", "-1", "0",
				  "0", "0", "1";
		vdd-supply = <&vcc_io>;
	};
};

&i2c1 {
	clock-frequency = <400000>;
	status = "okay";

	tmp108@48 {
		compatible = "ti,tmp108";
		reg = <0x48>;
		interrupt-parent = <&gpio1>;
		interrupts = <RK_PA7 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&tmp_alrt>;
		#thermal-sensor-cells = <0>;
	};

	hym8563: rtc@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PB5 IRQ_TYPE_EDGE_FALLING>;
		pinctrl-names = "default";
		pinctrl-0 = <&rtc_int>;
		#clock-cells = <0>;
		clock-output-names = "xin32k";
	};

	bat: battery@55 {
		compatible = "ti,bq27541";
		reg = <0x55>;
		power-supplies = <&bq24196>;
	};

	act8846: pmic@5a {
		compatible = "active-semi,act8846";
		reg = <0x5a>;
		pinctrl-names = "default";
		pinctrl-0 = <&dvs0_ctl &pmic_int>;

		vp1-supply = <&vsys>;
		vp2-supply = <&vsys>;
		vp3-supply = <&vsys>;
		vp4-supply = <&vsys>;
		inl1-supply = <&vcc_io>;
		inl2-supply = <&vsys>;
		inl3-supply = <&vsys>;

		regulators {
			vcc_ddr: REG1 {
				regulator-name = "VCC_DDR";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			vdd_log: REG2 {
				regulator-name = "VDD_LOG";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			vdd_arm: REG3 {
				regulator-name = "VDD_ARM";
				regulator-min-microvolt = <875000>;
				regulator-max-microvolt = <1350000>;
				regulator-always-on;
			};

			vcc_io: vcc_hdmi: REG4 {
				regulator-name = "VCC_IO";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-always-on;
			};

			vdd_10: REG5 {
				regulator-name = "VDD_10";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vdd_12: REG6 {
				regulator-name = "VDD_12";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			vcc18_cif: REG7 {
				regulator-name = "VCC18_CIF";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			vcca_33: REG8 {
				regulator-name = "VCCA_33";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vcc_tp: REG9 {
				regulator-name = "VCC_TP";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vccio_wl: REG10 {
				regulator-name = "VCCIO_WL";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
			};

			vcc_18: REG11 {
				regulator-name = "VCC_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			vcc28_cif: REG12 {
				regulator-name = "VCC28_CIF";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-always-on;
			};
		};
	};

	bq24196: charger@6b {
		compatible = "ti,bq24196";
		reg = <0x6b>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PD7 IRQ_TYPE_EDGE_FALLING>;
		pinctrl-names = "default";
		pinctrl-0 = <&charger_int &chg_ctl &otg_en>;
		ti,system-minimum-microvolt = <3200000>;
		monitored-battery = <&bat>;
		omit-battery-class;

		usb_otg_vbus: usb-otg-vbus { };
	};
};

&i2c2 {
	clock-frequency = <400000>;
	status = "okay";

	ft5606: touchscreen@3e {
		compatible = "edt,edt-ft5506";
		reg = <0x3e>;
		interrupt-parent = <&gpio1>;
		interrupts = <RK_PB7 IRQ_TYPE_EDGE_FALLING>;
		pinctrl-names = "default";
		pinctrl-0 = <&tp_int &tp_rst>;
		reset-gpios = <&gpio0 RK_PB6 GPIO_ACTIVE_LOW>;
		touchscreen-inverted-y;
		/* hw ts resolution does not match display */
		touchscreen-size-y = <1024>;
		touchscreen-size-x = <768>;
		touchscreen-swapped-x-y;
	};
};

&i2c3 {
	clock-frequency = <400000>;
	status = "okay";
};

&i2c4 {
	clock-frequency = <400000>;
	status = "okay";

	rt5616: codec@1b {
		compatible = "realtek,rt5616";
		reg = <0x1b>;
		clocks = <&cru SCLK_I2S0>;
		clock-names = "mclk";
		#sound-dai-cells = <0>;
	};
};

&i2s0 {
	status = "okay";
};

&mmc0 {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	disable-wp;
	pinctrl-names = "default";
	pinctrl-0 = <&sd0_clk>, <&sd0_cmd>, <&sd0_cd>, <&sd0_bus4>;
	vmmc-supply = <&vcc_sd>;
	status = "okay";
};

&mmc1 {
	bus-width = <4>;
	cap-sd-highspeed;
	keep-power-in-suspend;
	mmc-pwrseq = <&sdio_pwrseq>;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&sd1_clk>, <&sd1_cmd>, <&sd1_bus4>;
	vqmmc-supply = <&vccio_wl>;
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	brcmf: wifi@1 {
		reg = <1>;
		compatible = "brcm,bcm4329-fmac";
		interrupt-parent = <&gpio3>;
		interrupts = <RK_PD2 IRQ_TYPE_NONE>;
		interrupt-names = "host-wake";
		brcm,drive-strength = <5>;
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_host_wake>;
	};
};

&pwm1 {
	status = "okay";
};

&pinctrl {
	pcfg_output_high: pcfg-output-high {
		output-high;
	};

	pcfg_output_low: pcfg-output-low {
		output-low;
	};

	act8846 {
		dvs0_ctl: dvs0-ctl {
			rockchip,pins = <3 RK_PD3 RK_FUNC_GPIO &pcfg_output_low>;
		};

		pmic_int: pmic-int {
			rockchip,pins = <0 RK_PB3 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	bq24196 {
		charger_int: charger-int {
			rockchip,pins = <0 RK_PD7 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		/* pin hog to make it select usb profile */
		chg_ctl: chg-ctl {
			rockchip,pins = <0 RK_PA1 RK_FUNC_GPIO &pcfg_output_high>;
		};

		/* low: charging, high: complete, fault: blinking */
		chg_det: chg-det {
			rockchip,pins = <0 RK_PA6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		/* charging enabled when pin low and register set */
		chg_en: chg-en {
			rockchip,pins = <0 RK_PC1 RK_FUNC_GPIO &pcfg_output_low>;
		};

		/* bq29196 powergood (when low) signal */
		dc_det: dc-det {
			rockchip,pins = <0 RK_PB2 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		/* wire bq24196 otg pin to high, to enable 500mA charging */
		otg_en: otg-en {
			rockchip,pins = <0 RK_PB1 RK_FUNC_GPIO &pcfg_output_high>;
		};
	};

	camera {
		cif0_pdn: cif0-pdn {
			rockchip,pins = <3 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		cif1_pdn: cif1-pdn {
			rockchip,pins = <3 RK_PB5 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		cif_avdd_en: cif-avdd-en {
			rockchip,pins = <1 RK_PA6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	display {
		lcd_cs: lcd-cs {
			rockchip,pins = <3 RK_PD6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		lcd_en: lcd-en {
			rockchip,pins = <0 RK_PB0 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	ft5606 {
		tp_int: tp-int {
			rockchip,pins = <1 RK_PB7 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		tp_rst: tp-rst {
			rockchip,pins = <0 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	hdmi {
		hdmi_int: hdmi-int {
			rockchip,pins = <2 RK_PD6 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		hdmi_rst: hdmi-rst {
			rockchip,pins = <3 RK_PB2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	hym8563 {
		rtc_int: rtc-int {
			rockchip,pins = <0 RK_PB5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	keys {
		pwr_hold: pwr-hold {
			rockchip,pins = <0 RK_PA0 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		pwr_key: pwr-key {
			rockchip,pins = <0 RK_PA4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	lis3de {
		gsensor_int: gsensor-int {
			rockchip,pins = <0 RK_PB7 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	mmc {
		sdmmc_pwr: sdmmc-pwr {
			rockchip,pins = <3 RK_PA1 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	tmp108 {
		tmp_alrt: tmp-alrt {
			rockchip,pins = <1 RK_PA7 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb {
		v5_drv: v5-drv {
			rockchip,pins = <0 RK_PA3 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		otg_drv: otg-drv {
			rockchip,pins = <3 RK_PB1 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		usb_int: usb-int {
			rockchip,pins = <0 RK_PA7 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	rk903 {
		bt_host_wake: bt-host-wake {
			rockchip,pins = <0 RK_PA5 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		bt_reg_on: bt-reg-on {
			rockchip,pins = <3 RK_PC7 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		/* pin hog to pull the reset high */
		bt_rst: bt-rst {
			rockchip,pins = <3 RK_PD1 RK_FUNC_GPIO &pcfg_output_high>;
		};

		bt_wake: bt-wake {
			rockchip,pins = <3 RK_PC6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		wifi_host_wake: wifi-host-wake {
			rockchip,pins = <3 RK_PD2 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		wifi_reg_on: wifi-reg-on {
			rockchip,pins = <3 RK_PD0 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&saradc {
	vref-supply = <&vcc_18>;
	status = "okay";
};

&spdif {
	status = "okay";
};

&uart0 {
	pinctrl-0 = <&uart0_xfer &uart0_cts &uart0_rts>;
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm43438-bt";
		max-speed = <2000000>;
		device-wakeup-gpios = <&gpio3 RK_PC6 GPIO_ACTIVE_HIGH>;
		host-wakeup-gpios = <&gpio0 RK_PA5 GPIO_ACTIVE_HIGH>;
		shutdown-gpios = <&gpio3 RK_PC7 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&bt_host_wake &bt_reg_on &bt_rst &bt_wake>;
	};
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	pinctrl-0 = <&uart3_xfer &uart3_cts &uart3_rts>;
	status = "okay";
};

&usbphy {
	status = "okay";
};

&usb_host {
	status = "okay";
};

&usb_otg {
	status = "okay";
};

&vop0 {
	status = "okay";
};

&vop0_out {
	vop0_out_lvds: endpoint {
		remote-endpoint = <&lvds_in_vop0>;
	};
};

&vop1 {
	pinctrl-names = "default";
	pinctrl-0 = <&lcdc1_dclk &lcdc1_den &lcdc1_hsync
		     &lcdc1_vsync &lcdc1_rgb24>;
	status = "okay";
};

&wdt {
	status = "okay";
};
