// SPDX-License-Identifier: (GPL-2.0+ OR MIT)

/dts-v1/;

#include "rv1108.dtsi"

/ {
	model = "Rockchip RV1108 Evaluation board";
	compatible = "rockchip,rv1108-evb", "rockchip,rv1108";

	aliases {
		mmc0 = &sdmmc;
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x08000000>;
	};

	chosen {
		stdout-path = "serial2:1500000n8";
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		brightness-levels = <
			  0   1   2   3   4   5   6   7
			  8   9  10  11  12  13  14  15
			 16  17  18  19  20  21  22  23
			 24  25  26  27  28  29  30  31
			 32  33  34  35  36  37  38  39
			 40  41  42  43  44  45  46  47
			 48  49  50  51  52  53  54  55
			 56  57  58  59  60  61  62  63
			 64  65  66  67  68  69  70  71
			 72  73  74  75  76  77  78  79
			 80  81  82  83  84  85  86  87
			 88  89  90  91  92  93  94  95
			 96  97  98  99 100 101 102 103
			104 105 106 107 108 109 110 111
			112 113 114 115 116 117 118 119
			120 121 122 123 124 125 126 127
			128 129 130 131 132 133 134 135
			136 137 138 139 140 141 142 143
			144 145 146 147 148 149 150 151
			152 153 154 155 156 157 158 159
			160 161 162 163 164 165 166 167
			168 169 170 171 172 173 174 175
			176 177 178 179 180 181 182 183
			184 185 186 187 188 189 190 191
			192 193 194 195 196 197 198 199
			200 201 202 203 204 205 206 207
			208 209 210 211 212 213 214 215
			216 217 218 219 220 221 222 223
			224 225 226 227 228 229 230 231
			232 233 234 235 236 237 238 239
			240 241 242 243 244 245 246 247
			248 249 250 251 252 253 254 255>;
		default-brightness-level = <200>;
		pwms = <&pwm0 0 25000 0>;
	};

	vcc_sys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vsys";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
	};
};

&cpu0 {
	cpu-supply = <&vdd_core>;
};

&i2c0 {
	status = "okay";
	i2c-scl-rising-time-ns = <275>;
	i2c-scl-falling-time-ns = <16>;
	clock-frequency = <400000>;

	rk805: pmic@18 {
		compatible = "rockchip,rk805";
		reg = <0x18>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PB4 IRQ_TYPE_LEVEL_LOW>;
		rockchip,system-power-controller;
		#clock-cells = <0>;

		vcc1-supply = <&vcc_sys>;
		vcc2-supply = <&vcc_sys>;
		vcc3-supply = <&vcc_sys>;
		vcc4-supply = <&vcc_sys>;
		vcc5-supply = <&vcc_sys>;
		vcc6-supply = <&vcc_sys>;

		regulators {
			vdd_core: DCDC_REG1 {
				regulator-name = "vdd_core";
				regulator-min-microvolt = <700000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <900000>;
				};
			};

			vdd_cam: DCDC_REG2 {
				regulator-name = "vdd_cam";
				regulator-min-microvolt = <700000>;
				regulator-max-microvolt = <2000000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-name = "vcc_ddr";
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc_io: DCDC_REG4 {
				regulator-name = "vcc_io";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vdd_10: LDO_REG1 {
				regulator-name = "vdd_10";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_18: LDO_REG2 {
				regulator-name = "vcc_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd10_pmu: LDO_REG3 {
				regulator-name = "vdd10_pmu";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};
		};
	};

	bma250: accelerometer@19 {
		compatible = "bosch,bma250e";
		reg = <0x19>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PB3 IRQ_TYPE_LEVEL_LOW>;
	};
};

&pwm0 {
	status = "okay";
};

&sdmmc {
	status = "okay";
};

&tsadc {
	status = "okay";
};

&u2phy {
	status = "okay";

	u2phy_host: host-port {
		status = "okay";
	};

	u2phy_otg: otg-port {
		status = "okay";
	};
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&usb_host_ehci {
	status = "okay";
};

&usb_host_ohci {
	status = "okay";
};

&usb_otg {
	status = "okay";
};
