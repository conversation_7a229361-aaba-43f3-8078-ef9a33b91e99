// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Google Veyron (and derivatives) fragment for sdmmc cards
 *
 * Copyright 2015 Google, Inc
 */

/ {
	aliases {
		mmc1 = &sdmmc;
	};
};

&io_domains {
	sdcard-supply = <&vccio_sd>;
};

&pinctrl {
	sdmmc {
		/*
		 * We run sdmmc at max speed; bump up drive strength.
		 * We also have external pulls, so disable the internal ones.
		 */
		sdmmc_bus4: sdmmc-bus4 {
			rockchip,pins = <6 RK_PC0 1 &pcfg_pull_none_drv_8ma>,
					<6 RK_PC1 1 &pcfg_pull_none_drv_8ma>,
					<6 RK_PC2 1 &pcfg_pull_none_drv_8ma>,
					<6 RK_PC3 1 &pcfg_pull_none_drv_8ma>;
		};

		sdmmc_clk: sdmmc-clk {
			rockchip,pins = <6 RK_PC4 1 &pcfg_pull_none_drv_8ma>;
		};

		sdmmc_cmd: sdmmc-cmd {
			rockchip,pins = <6 RK_PC5 1 &pcfg_pull_none_drv_8ma>;
		};

		/*
		 * Builtin CD line is hooked to ground to prevent JTAG at boot
		 * (and also to get the voltage rail correct).
		 * Configure gpio6_C6 as GPIO so dw_mmc builtin CD doesn't
		 * think there's a card inserted
		 */
		sdmmc_cd_disabled: sdmmc-cd-disabled {
			rockchip,pins = <6 RK_PC6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		/* This is where we actually hook up CD */
		sdmmc_cd_pin: sdmmc-cd-pin {
			rockchip,pins = <7 RK_PA5 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&rk808 {
	vcc9-supply = <&vcc_5v>;

	regulators {
		vccio_sd: LDO_REG4 {
			regulator-name = "vccio_sd";
			regulator-min-microvolt = <1800000>;
			regulator-max-microvolt = <3300000>;
			regulator-state-mem {
				regulator-off-in-suspend;
			};
		};

		vcc33_sd: LDO_REG5 {
			regulator-name = "vcc33_sd";
			regulator-min-microvolt = <3300000>;
			regulator-max-microvolt = <3300000>;
			regulator-state-mem {
				regulator-off-in-suspend;
			};
		};
	};
};

&sdmmc {
	status = "okay";

	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	cd-gpios = <&gpio7 RK_PA5 GPIO_ACTIVE_LOW>;
	rockchip,default-sample-phase = <90>;
	sd-uhs-sdr12;
	sd-uhs-sdr25;
	sd-uhs-sdr50;
	sd-uhs-sdr104;
	vmmc-supply = <&vcc33_sd>;
	vqmmc-supply = <&vccio_sd>;
};
