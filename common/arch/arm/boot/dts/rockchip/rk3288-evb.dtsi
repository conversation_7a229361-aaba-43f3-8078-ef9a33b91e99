// SPDX-License-Identifier: (GPL-2.0+ OR MIT)

#include <dt-bindings/input/input.h>
#include <dt-bindings/pwm/pwm.h>
#include "rk3288.dtsi"

/ {
	memory@0 {
		device_type = "memory";
		reg = <0x0 0x0 0x0 0x80000000>;
	};

	adc-keys {
		compatible = "adc-keys";
		io-channels = <&saradc 1>;
		io-channel-names = "buttons";
		keyup-threshold-microvolt = <1800000>;

		button-up {
			label = "Volume Up";
			linux,code = <KEY_VOLUMEUP>;
			press-threshold-microvolt = <100000>;
		};

		button-down {
			label = "Volume Down";
			linux,code = <KEY_VOLUMEDOWN>;
			press-threshold-microvolt = <300000>;
		};

		button-menu {
			label = "Menu";
			linux,code = <KEY_MENU>;
			press-threshold-microvolt = <640000>;
		};

		button-esc {
			label = "Esc";
			linux,code = <KEY_ESC>;
			press-threshold-microvolt = <1000000>;
		};

		button-home  {
			label = "Home";
			linux,code = <KEY_HOME>;
			press-threshold-microvolt = <1300000>;
		};
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		brightness-levels = <
			  0   1   2   3   4   5   6   7
			  8   9  10  11  12  13  14  15
			 16  17  18  19  20  21  22  23
			 24  25  26  27  28  29  30  31
			 32  33  34  35  36  37  38  39
			 40  41  42  43  44  45  46  47
			 48  49  50  51  52  53  54  55
			 56  57  58  59  60  61  62  63
			 64  65  66  67  68  69  70  71
			 72  73  74  75  76  77  78  79
			 80  81  82  83  84  85  86  87
			 88  89  90  91  92  93  94  95
			 96  97  98  99 100 101 102 103
			104 105 106 107 108 109 110 111
			112 113 114 115 116 117 118 119
			120 121 122 123 124 125 126 127
			128 129 130 131 132 133 134 135
			136 137 138 139 140 141 142 143
			144 145 146 147 148 149 150 151
			152 153 154 155 156 157 158 159
			160 161 162 163 164 165 166 167
			168 169 170 171 172 173 174 175
			176 177 178 179 180 181 182 183
			184 185 186 187 188 189 190 191
			192 193 194 195 196 197 198 199
			200 201 202 203 204 205 206 207
			208 209 210 211 212 213 214 215
			216 217 218 219 220 221 222 223
			224 225 226 227 228 229 230 231
			232 233 234 235 236 237 238 239
			240 241 242 243 244 245 246 247
			248 249 250 251 252 253 254 255>;
		default-brightness-level = <128>;
		enable-gpios = <&gpio7 RK_PA2 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&bl_en>;
		pwms = <&pwm0 0 1000000 PWM_POLARITY_INVERTED>;
	};

	ext_gmac: external-gmac-clock {
		compatible = "fixed-clock";
		clock-frequency = <125000000>;
		clock-output-names = "ext_gmac";
		#clock-cells = <0>;
	};

	panel: panel {
		compatible = "lg,lp079qx1-sp0v";
		backlight = <&backlight>;
		enable-gpios = <&gpio7 RK_PA4 GPIO_ACTIVE_HIGH>;
		pinctrl-0 = <&lcd_cs>;

		ports {
			panel_in: port {
				panel_in_edp: endpoint {
					remote-endpoint = <&edp_out_panel>;
				};
			};
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;

		pinctrl-names = "default";
		pinctrl-0 = <&pwrbtn>;

		key-power {
			gpios = <&gpio0 RK_PA5 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_POWER>;
			label = "GPIO Key Power";
			linux,input-type = <1>;
			wakeup-source;
			debounce-interval = <100>;
		};
	};

	/* This turns on USB vbus for both host0 (ehci) and host1 (dwc2) */
	vcc_host: vcc-host-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB6 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&host_vbus_drv>;
		regulator-name = "vcc_host";
		regulator-always-on;
		regulator-boot-on;
	};

	vcc_phy: vcc-phy-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PA6 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&eth_phy_pwr>;
		regulator-name = "vcc_phy";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
	};

	vcc_sys: vsys-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vcc_sys";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	/*
	 * NOTE: vcc_sd isn't hooked up on v1.0 boards where power comes from
	 * vcc_io directly.  Those boards won't be able to power cycle SD cards
	 * but it shouldn't hurt to toggle this pin there anyway.
	 */
	vcc_sd: sdmmc-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio7 RK_PB3 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdmmc_pwr>;
		regulator-name = "vcc_sd";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		startup-delay-us = <100000>;
		vin-supply = <&vcc_io>;
	};
};

&cpu0 {
	cpu0-supply = <&vdd_cpu>;
};

&edp {
	force-hpd;
	status = "okay";

	ports {
		edp_out: port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			edp_out_panel: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&panel_in_edp>;
			};
		};
	};
};

&edp_phy {
	status = "okay";
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	disable-wp;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_pwr &emmc_bus8>;
	status = "okay";
};

&saradc {
	vref-supply = <&vcc_18>;
	status = "okay";
};

&sdmmc {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	disable-wp;			/* wp not hooked up */
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_clk &sdmmc_cmd &sdmmc_cd &sdmmc_bus4>;
	status = "okay";
	vmmc-supply = <&vcc_sd>;
	vqmmc-supply = <&vccio_sd>;
};

&gmac {
	phy-supply = <&vcc_phy>;
	phy-mode = "rgmii";
	clock_in_out = "input";
	snps,reset-gpio = <&gpio4 RK_PA7 GPIO_ACTIVE_HIGH>;
	snps,reset-active-low;
	snps,reset-delays-us = <0 10000 1000000>;
	assigned-clocks = <&cru SCLK_MAC>;
	assigned-clock-parents = <&ext_gmac>;
	pinctrl-names = "default";
	pinctrl-0 = <&rgmii_pins>;
	tx_delay = <0x30>;
	rx_delay = <0x10>;
	status = "okay";
};

&gpu {
	mali-supply = <&vdd_gpu>;
	status = "okay";
};

&hdmi {
	ddc-i2c-bus = <&i2c5>;
	status = "okay";
};

&i2c0 {
	status = "okay";
};

&i2c5 {
	status = "okay";
};

&wdt {
	status = "okay";
};

&pwm0 {
	status = "okay";
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&uart3 {
	status = "okay";
};

&uart4 {
	status = "okay";
};

&tsadc {
	rockchip,hw-tshut-mode = <0>; /* tshut mode 0:CRU 1:GPIO */
	rockchip,hw-tshut-polarity = <0>; /* tshut polarity 0:LOW 1:HIGH */
	status = "okay";
};

&pinctrl {
	pcfg_pull_none_drv_8ma: pcfg-pull-none-drv-8ma {
		drive-strength = <8>;
	};

	pcfg_pull_up_drv_8ma: pcfg-pull-up-drv-8ma {
		bias-pull-up;
		drive-strength = <8>;
	};

	backlight {
		bl_en: bl-en {
			rockchip,pins = <7 RK_PA2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	buttons {
		pwrbtn: pwrbtn {
			rockchip,pins = <0 RK_PA5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	lcd {
		lcd_cs: lcd-cs {
			rockchip,pins = <7 RK_PA4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	pmic {
		pmic_int: pmic-int {
			rockchip,pins = <0 RK_PA4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	sdmmc {
		/*
		 * Default drive strength isn't enough to achieve even
		 * high-speed mode on EVB board so bump up to 8ma.
		 */
		sdmmc_bus4: sdmmc-bus4 {
			rockchip,pins = <6 RK_PC0 1 &pcfg_pull_up_drv_8ma>,
					<6 RK_PC1 1 &pcfg_pull_up_drv_8ma>,
					<6 RK_PC2 1 &pcfg_pull_up_drv_8ma>,
					<6 RK_PC3 1 &pcfg_pull_up_drv_8ma>;
		};

		sdmmc_clk: sdmmc-clk {
			rockchip,pins = <6 RK_PC4 1 &pcfg_pull_none_drv_8ma>;
		};

		sdmmc_cmd: sdmmc-cmd {
			rockchip,pins = <6 RK_PC5 1 &pcfg_pull_up_drv_8ma>;
		};

		sdmmc_pwr: sdmmc-pwr {
			rockchip,pins = <7 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb {
		host_vbus_drv: host-vbus-drv {
			rockchip,pins = <0 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	eth_phy {
		eth_phy_pwr: eth-phy-pwr {
			rockchip,pins = <0 RK_PA6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&usbphy {
	status = "okay";
};

&usb_host0_ehci {
	status = "okay";
};

&usb_host1 {
	status = "okay";
};

&vopb {
	status = "okay";
};

&vopb_mmu {
	status = "okay";
};

&vopl {
	status = "okay";
};

&vopl_mmu {
	status = "okay";
};
