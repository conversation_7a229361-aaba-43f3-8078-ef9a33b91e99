// SPDX-License-Identifier: (GPL-2.0+ OR MIT)

/dts-v1/;
#include "rk3288-evb.dtsi"

/ {
	model = "Rockchip RK3288 EVB ACT8846";
	compatible = "rockchip,rk3288-evb-act8846", "rockchip,rk3288";

	vcc_lcd: vcc-lcd {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio7 RK_PA3 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&lcd_en>;
		regulator-name = "vcc_lcd";
		vin-supply = <&vcc_io>;
	};

	vcc_wl: vcc-wl {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio7 RK_PB1 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_pwr>;
		regulator-name = "vcc_wl";
		vin-supply = <&vcc_18>;
	};
};

&i2c0 {
	clock-frequency = <400000>;

	vdd_cpu: syr827@40 {
		compatible = "silergy,syr827";
		fcs,suspend-voltage-selector = <1>;
		reg = <0x40>;
		regulator-name = "vdd_cpu";
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <1350000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&vcc_sys>;
	};

	vdd_gpu: syr828@41 {
		compatible = "silergy,syr828";
		fcs,suspend-voltage-selector = <1>;
		reg = <0x41>;
		regulator-name = "vdd_gpu";
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <1350000>;
		regulator-always-on;
		vin-supply = <&vcc_sys>;
	};

	rtc@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;

		interrupt-parent = <&gpio0>;
		interrupts = <RK_PA4 IRQ_TYPE_EDGE_FALLING>;

		pinctrl-names = "default";
		pinctrl-0 = <&pmic_int>;

		#clock-cells = <0>;
		clock-output-names = "xin32k";
	};

	act8846: act8846@5a {
		compatible = "active-semi,act8846";
		reg = <0x5a>;
		status = "okay";

		vp1-supply = <&vcc_sys>;
		vp2-supply = <&vcc_sys>;
		vp3-supply = <&vcc_sys>;
		vp4-supply = <&vcc_sys>;
		inl1-supply = <&vcc_io>;
		inl2-supply = <&vcc_sys>;
		inl3-supply = <&vcc_20>;

		regulators {
			vcc_ddr: REG1 {
				regulator-name = "VCC_DDR";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-always-on;
			};

			vcc_io: REG2 {
				regulator-name = "VCC_IO";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd_log: REG3 {
				regulator-name = "VDD_LOG";
				regulator-min-microvolt = <700000>;
				regulator-max-microvolt = <1500000>;
				regulator-always-on;
			};

			vcc_20: REG4 {
				regulator-name = "VCC_20";
				regulator-min-microvolt = <2000000>;
				regulator-max-microvolt = <2000000>;
				regulator-always-on;
			};

			vccio_sd: REG5 {
				regulator-name = "VCCIO_SD";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd10_lcd: REG6 {
				regulator-name = "VDD10_LCD";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vcca_codec: REG7 {
				regulator-name = "VCCA_CODEC";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vcc_tp: REG8 {
				regulator-name = "VCCA_TP";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vccio_pmu: REG9 {
				regulator-name = "VCCIO_PMU";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd_10: REG10 {
				regulator-name = "VDD_10";
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-always-on;
			};

			vcc_18: REG11 {
				regulator-name = "VCC_18";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			vcc18_lcd: REG12 {
				regulator-name = "VCC18_LCD";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};
		};
	};
};

&panel {
	power-supply = <&vcc_lcd>;
};

&pinctrl {
	lcd {
		lcd_en: lcd-en  {
			rockchip,pins = <7 RK_PA3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	wifi {
		wifi_pwr: wifi-pwr {
			rockchip,pins = <7 RK_PB1 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};
