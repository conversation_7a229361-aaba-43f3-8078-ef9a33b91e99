// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2019 Fuzhou Rockchip Electronics Co., Ltd
 * Copyright (c) 2019 Vamrs Limited
 * Copyright (c) 2019 Amarula Solutions(India)
 */

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pinctrl/rockchip.h>

/ {
	compatible = "vamrs,rk3288-vmarc-som", "rockchip,rk3288";

	vccio_flash: vccio-flash-regulator {
		compatible = "regulator-fixed";
		regulator-name = "vccio_flash";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&vcc_io>;
	};
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	disable-wp;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_pwr &emmc_bus8>;
	vmmc-supply = <&vcc_io>;
	vqmmc-supply = <&vccio_flash>;
	status = "okay";
};

&gmac {
	assigned-clocks = <&cru SCLK_MAC>;
	phy-supply = <&vcc_io>;
	snps,reset-gpio = <&gpio4 RK_PA7 GPIO_ACTIVE_HIGH>;
};

&hdmi {
	ddc-i2c-bus = <&i2c5>;
	pinctrl-names = "default";
	pinctrl-0 = <&hdmi_cec_c0>;
};

&i2c0 {
	clock-frequency = <400000>;
	status = "okay";

	rk808: pmic@1b {
		compatible = "rockchip,rk808";
		reg = <0x1b>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PA4 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&pmic_int &global_pwroff>;
		rockchip,system-power-controller;
		wakeup-source;
		#clock-cells = <1>;
		clock-output-names = "rk808-clkout1", "rk808-clkout2";

		vcc1-supply = <&vcc5v0_sys>;
		vcc2-supply = <&vcc5v0_sys>;
		vcc3-supply = <&vcc5v0_sys>;
		vcc4-supply = <&vcc5v0_sys>;
		vcc6-supply = <&vcc5v0_sys>;
		vcc7-supply = <&vcc5v0_sys>;
		vcc8-supply = <&vcc_io>;
		vcc9-supply = <&vcc_io>;
		vcc10-supply = <&vcc5v0_sys>;
		vcc11-supply = <&vcc5v0_sys>;
		vcc12-supply = <&vcc_io>;
		vddio-supply = <&vcc_io>;

		regulators {
			vdd_cpu: DCDC_REG1 {
				regulator-name = "vdd_arm";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <1400000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_gpu: DCDC_REG2 {
				regulator-name = "vdd_gpu";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <1250000>;
				regulator-ramp-delay = <6000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-name = "vcc_ddr";
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc_io: DCDC_REG4 {
				regulator-name = "vcc_io";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcc_tp: LDO_REG1 {
				regulator-name = "vcc_tp";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcca_codec: LDO_REG2 {
				regulator-name = "vcca_codec";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vdd_10: LDO_REG3 {
				regulator-name = "vdd_10";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1000000>;
				};
			};

			vcc_wl: LDO_REG4 {
				regulator-name = "vcc_wl";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vccio_sd: LDO_REG5 {
				regulator-name = "vccio_sd";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vdd10_lcd: LDO_REG6 {
				regulator-name = "vdd10_lcd";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1000000>;
				regulator-max-microvolt = <1000000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_18: LDO_REG7 {
				regulator-name = "vcc_18";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc18_lcd: LDO_REG8 {
				regulator-name = "vcc18_lcd";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_sd: SWITCH_REG1 {
				regulator-name = "vcc_sd";
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_lcd: SWITCH_REG2 {
				regulator-name = "vcc_lcd";
				regulator-always-on;
				regulator-boot-on;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};
		};
	};
};

&i2c1 {
	clock-frequency = <400000>;
	status = "okay";

	hym8563: rtc@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		interrupt-parent = <&gpio5>;
		interrupts = <RK_PC3 IRQ_TYPE_LEVEL_LOW>;
		#clock-cells = <0>;
		clock-output-names = "hym8563";
		pinctrl-names = "default";
		pinctrl-0 = <&hym8563_int>;
	};
};

&i2c5 {
	status = "okay";
};

&io_domains {
	bb-supply = <&vcc_io>;
	flash0-supply = <&vccio_flash>;
	gpio1830-supply = <&vcc_18>;
	gpio30-supply = <&vcc_io>;
	sdcard-supply = <&vccio_sd>;
	wifi-supply = <&vcc_wl>;
	status = "okay";
};

&pinctrl {
	hym8563 {
		hym8563_int: hym8563-int {
			rockchip,pins = <5 RK_PC3 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	pcfg_pull_none_drv_8ma: pcfg-pull-none-drv-8ma {
		drive-strength = <8>;
	};

	pcfg_pull_up_drv_8ma: pcfg-pull-up-drv-8ma {
		bias-pull-up;
		drive-strength = <8>;
	};

	pmic {
		pmic_int: pmic-int {
			rockchip,pins = <0 RK_PA4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	sdio-pwrseq {
		wifi_enable_h: wifi-enable-h {
			rockchip,pins = <4 RK_PD4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	sdmmc {
		sdmmc_bus4: sdmmc-bus4 {
			rockchip,pins =
				<6 RK_PC0 1 &pcfg_pull_up_drv_8ma>,
				<6 RK_PC1 1 &pcfg_pull_up_drv_8ma>,
				<6 RK_PC2 1 &pcfg_pull_up_drv_8ma>,
				<6 RK_PC3 1 &pcfg_pull_up_drv_8ma>;
		};

		sdmmc_clk: sdmmc-clk {
			rockchip,pins = <6 RK_PC4 1 &pcfg_pull_none_drv_8ma>;
		};

		sdmmc_cmd: sdmmc-cmd {
			rockchip,pins = <6 RK_PC5 1 &pcfg_pull_up_drv_8ma>;
		};
	};

	vbus_host {
		usb1_en_oc: usb1-en-oc {
			rockchip,pins = <0 RK_PC1 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	vbus_typec {
		usb0_en_oc: usb0-en-oc {
			rockchip,pins = <0 RK_PB5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};
};

&sdio_pwrseq {
	/*
	 * On the module itself this is one of these (depending
	 * on the actual card populated):
	 * - SDIO_RESET_L_WL_REG_ON
	 * - PDN (power down when low)
	 */
	reset-gpios = <&gpio4 RK_PD4 GPIO_ACTIVE_LOW>;	/* WIFI_REG_ON */
};

&usbphy {
	status = "okay";
};

&usb_host0_ehci {
	status = "okay";
};

&usb_host0_ohci {
	status = "okay";
};

&usb_host1 {
	status = "okay";
};

&usb_otg {
	status = "okay";
};

&vbus_host {
	enable-active-high;
	gpio = <&gpio0 RK_PC1 GPIO_ACTIVE_HIGH>; /* USB1_EN_OC# */
};

&vbus_typec {
	enable-active-high;
	gpio = <&gpio0 RK_PB5 GPIO_ACTIVE_HIGH>; /* USB0_EN_OC# */
};
