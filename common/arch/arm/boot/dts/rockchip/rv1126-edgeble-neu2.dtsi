// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2020 Rockchip Electronics Co., Ltd.
 * Copyright (c) 2022 Edgeble AI Technologies Pvt. Ltd.
 */

/ {
	compatible = "edgeble,neural-compute-module-2", "rockchip,rv1126";

	aliases {
		mmc0 = &emmc;
	};

	vccio_flash: vccio-flash-regulator {
		compatible = "regulator-fixed";
		enable-active-high;
		gpio = <&gpio0 RK_PB3 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&flash_vol_sel>;
		regulator-name = "vccio_flash";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&vcc_3v3>;
	};

	sdio_pwrseq: pwrseq-sdio {
		compatible = "mmc-pwrseq-simple";
		clocks = <&rk809 1>;
		clock-names = "ext_clock";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_enable_h>;
		reset-gpios = <&gpio1 RK_PD0 GPIO_ACTIVE_LOW>;
	};
};

&cpu0 {
	cpu-supply = <&vdd_arm>;
};

&emmc {
	bus-width = <8>;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&emmc_bus8 &emmc_cmd &emmc_clk>;
	rockchip,default-sample-phase = <90>;
	vmmc-supply = <&vcc_3v3>;
	vqmmc-supply = <&vccio_flash>;
	status = "okay";
};

&i2c0 {
	clock-frequency = <400000>;
	status = "okay";

	rk809: pmic@20 {
		compatible = "rockchip,rk809";
		reg = <0x20>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PB1 IRQ_TYPE_LEVEL_LOW>;
		#clock-cells = <1>;
		clock-output-names = "rk808-clkout1", "rk808-clkout2";
		pinctrl-names = "default";
		pinctrl-0 = <&pmic_int_l>;
		rockchip,system-power-controller;
		wakeup-source;

		vcc1-supply = <&vcc5v0_sys>;
		vcc2-supply = <&vcc5v0_sys>;
		vcc3-supply = <&vcc5v0_sys>;
		vcc4-supply = <&vcc5v0_sys>;
		vcc5-supply = <&vcc_buck5>;
		vcc6-supply = <&vcc_buck5>;
		vcc7-supply = <&vcc5v0_sys>;
		vcc8-supply = <&vcc3v3_sys>;
		vcc9-supply = <&vcc5v0_sys>;

		regulators {
			vdd_npu_vepu: DCDC_REG1 {
				regulator-name = "vdd_npu_vepu";
				regulator-always-on;
				regulator-boot-on;
				regulator-initial-mode = <0x2>;
				regulator-min-microvolt = <650000>;
				regulator-max-microvolt = <950000>;
				regulator-ramp-delay = <6001>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_arm: DCDC_REG2 {
				regulator-name = "vdd_arm";
				regulator-always-on;
				regulator-boot-on;
				regulator-initial-mode = <0x2>;
				regulator-min-microvolt = <725000>;
				regulator-max-microvolt = <1350000>;
				regulator-ramp-delay = <6001>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-name = "vcc_ddr";
				regulator-always-on;
				regulator-boot-on;
				regulator-initial-mode = <0x2>;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc3v3_sys: DCDC_REG4 {
				regulator-name = "vcc3v3_sys";
				regulator-always-on;
				regulator-boot-on;
				regulator-initial-mode = <0x2>;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcc_buck5: DCDC_REG5 {
				regulator-name = "vcc_buck5";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <2200000>;
				regulator-max-microvolt = <2200000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <2200000>;
				};
			};

			vcc_0v8: LDO_REG1 {
				regulator-name = "vcc_0v8";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <800000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc1v8_pmu: LDO_REG2 {
				regulator-name = "vcc1v8_pmu";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vdd0v8_pmu: LDO_REG3 {
				regulator-name = "vcc0v8_pmu";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <800000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <800000>;
				};
			};

			vcc_1v8: LDO_REG4 {
				regulator-name = "vcc_1v8";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc_dovdd: LDO_REG5 {
				regulator-name = "vcc_dovdd";
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_dvdd: LDO_REG6 {
				regulator-name = "vcc_dvdd";
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_avdd: LDO_REG7 {
				regulator-name = "vcc_avdd";
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vccio_sd: LDO_REG8 {
				regulator-name = "vccio_sd";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc3v3_sd: LDO_REG9 {
				regulator-name = "vcc3v3_sd";
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_5v0: SWITCH_REG1 {
				regulator-name = "vcc_5v0";
			};

			vcc_3v3: SWITCH_REG2 {
				regulator-name = "vcc_3v3";
				regulator-always-on;
				regulator-boot-on;
			};
		};
	};
};

&pinctrl {
	bt {
		bt_enable: bt-enable {
			rockchip,pins = <3 RK_PA5 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	flash {
		flash_vol_sel: flash-vol-sel {
			rockchip,pins = <0 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	pmic {
		pmic_int_l: pmic-int-l {
			rockchip,pins = <0 RK_PB1 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	wifi {
		wifi_enable_h: wifi-enable-h {
			rockchip,pins = <1 RK_PD0 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&pmu_io_domains {
	pmuio0-supply = <&vcc1v8_pmu>;
	pmuio1-supply = <&vcc3v3_sys>;
	vccio1-supply = <&vccio_flash>;
	vccio2-supply = <&vccio_sd>;
	vccio3-supply = <&vcc_1v8>;
	vccio4-supply = <&vcc_dovdd>;
	vccio5-supply = <&vcc_1v8>;
	vccio6-supply = <&vcc_1v8>;
	vccio7-supply = <&vcc_dovdd>;
	status = "okay";
};

&saradc {
	vref-supply = <&vcc_1v8>;
	status = "okay";
};

&sfc {
	pinctrl-names = "default";
	pinctrl-0 = <&fspi_pins>;
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	flash@0 {
		compatible = "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <50000000>;
		spi-rx-bus-width = <4>;
		spi-tx-bus-width = <1>;
	};
};

&sdio {
	bus-width = <4>;
	cap-sd-highspeed;
	cap-sdio-irq;
	keep-power-in-suspend;
	max-frequency = <100000000>;
	mmc-pwrseq = <&sdio_pwrseq>;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc1_clk &sdmmc1_cmd &sdmmc1_bus4>;
	rockchip,default-sample-phase = <90>;
	sd-uhs-sdr104;
	vmmc-supply = <&vcc3v3_sys>;
	vqmmc-supply = <&vcc_1v8>;
	status = "okay";
	#address-cells = <1>;
	#size-cells = <0>;
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_xfer &uart0_ctsn &uart0_rtsn>;
	status = "okay";

	bluetooth {
		compatible = "qcom,qca9377-bt";
		clocks = <&rk809 1>;
		enable-gpios = <&gpio3 RK_PA5 GPIO_ACTIVE_HIGH>; /* BT_RST */
		max-speed = <2000000>;
		pinctrl-names = "default";
		pinctrl-0 = <&bt_enable>;
		vddxo-supply = <&vcc3v3_sys>;
		vddio-supply = <&vcc_1v8>;
	};
};
