// SPDX-License-Identifier: (GPL-2.0+ OR MIT)

/dts-v1/;

#include "rk3036.dtsi"

/ {
	model = "Rockchip RK3036 Evaluation board";
	compatible = "rockchip,rk3036-evb", "rockchip,rk3036";

	memory@60000000 {
		device_type = "memory";
		reg = <0x60000000 0x40000000>;
	};
};

&emac {
	phy = <&phy0>;
	phy-reset-duration = <10>; /* millisecond */
	phy-reset-gpios = <&gpio2 RK_PC6 GPIO_ACTIVE_LOW>; /* PHY_RST */
	pinctrl-names = "default";
	pinctrl-0 = <&emac_xfer>, <&emac_mdio>;
	status = "okay";

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;

		phy0: ethernet-phy@0 {
			reg = <0>;
		};
	};
};

&i2c1 {
	status = "okay";

	hym8563: rtc@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-output-names = "xin32k";
	};
};

&uart2 {
	status = "okay";
};
