// SPDX-License-Identifier: (GPL-2.0 OR MIT)
/*
 * Device Tree Include file for Marvell Armada 1500-mini (Berlin BG2CD) SoC
 *
 * <PERSON> <<EMAIL>>
 *
 * based on GPL'ed 2.6 kernel sources
 *  (c) Marvell International Ltd.
 */

#include <dt-bindings/clock/berlin2.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	model = "Marvell Armada 1500-mini (BG2CD) SoC";
	compatible = "marvell,berlin2cd", "marvell,berlin";
	#address-cells = <1>;
	#size-cells = <1>;

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu: cpu@0 {
			compatible = "arm,cortex-a9";
			device_type = "cpu";
			next-level-cache = <&l2>;
			reg = <0>;

			clocks = <&chip_clk CLKID_CPU>;
			clock-latency = <100000>;
			operating-points = <
				/* kHz    uV */
				800000  1200000
				600000  1200000
			>;
		};
	};

	pmu {
		compatible = "arm,cortex-a9-pmu";
		interrupt-parent = <&gic>;
		interrupts = <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>;
	};

	refclk: oscillator {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <25000000>;
	};

	soc@f7000000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		interrupt-parent = <&gic>;

		ranges = <0 0xf7000000 0x1000000>;

		sdhci0: mmc@ab0000 {
			compatible = "mrvl,pxav3-mmc";
			reg = <0xab0000 0x200>;
			clocks = <&chip_clk CLKID_SDIO0XIN>, <&chip_clk CLKID_SDIO0>;
			clock-names = "io", "core";
			interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		l2: cache-controller@ac0000 {
			compatible = "arm,pl310-cache";
			reg = <0xac0000 0x1000>;
			cache-unified;
			cache-level = <2>;
		};

		snoop-control-unit@ad0000 {
			compatible = "arm,cortex-a9-scu";
			reg = <0xad0000 0x100>;
		};

		gic: interrupt-controller@ad1000 {
			compatible = "arm,cortex-a9-gic";
			reg = <0xad1000 0x1000>, <0xad0100 0x0100>;
			interrupt-controller;
			#interrupt-cells = <3>;
		};

		global-timer@ad0200 {
			compatible = "arm,cortex-a9-global-timer";
			reg = <0xad0200 0x20>;
			interrupts = <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_EDGE_RISING)>;
			clocks = <&chip_clk CLKID_TWD>;
		};

		local-timer@ad0600 {
			compatible = "arm,cortex-a9-twd-timer";
			reg = <0xad0600 0x20>;
			interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_EDGE_RISING)>;
			clocks = <&chip_clk CLKID_TWD>;
		};

		local-wdt@ad0620 {
			compatible = "arm,cortex-a9-twd-wdt";
			reg = <0xad0620 0x20>;
			interrupts = <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_EDGE_RISING)>;
			clocks = <&chip_clk CLKID_TWD>;
		};

		usb_phy0: usb-phy@b74000 {
			compatible = "marvell,berlin2cd-usb-phy";
			reg = <0xb74000 0x128>;
			#phy-cells = <0>;
			resets = <&chip_rst 0x178 23>;
			status = "disabled";
		};

		usb_phy1: usb-phy@b78000 {
			compatible = "marvell,berlin2cd-usb-phy";
			reg = <0xb78000 0x128>;
			#phy-cells = <0>;
			resets = <&chip_rst 0x178 24>;
			status = "disabled";
		};

		eth1: ethernet@b90000 {
			compatible = "marvell,pxa168-eth";
			reg = <0xb90000 0x10000>;
			clocks = <&chip_clk CLKID_GETH1>;
			interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
			/* set by bootloader */
			local-mac-address = [00 00 00 00 00 00];
			#address-cells = <1>;
			#size-cells = <0>;
			phy-connection-type = "mii";
			phy-handle = <&ethphy1>;
			status = "disabled";

			ethphy1: ethernet-phy@0 {
				reg = <0>;
			};
		};

		eth0: ethernet@e50000 {
			compatible = "marvell,pxa168-eth";
			reg = <0xe50000 0x10000>;
			clocks = <&chip_clk CLKID_GETH0>;
			interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
			/* set by bootloader */
			local-mac-address = [00 00 00 00 00 00];
			#address-cells = <1>;
			#size-cells = <0>;
			phy-connection-type = "mii";
			phy-handle = <&ethphy0>;
			status = "disabled";

			ethphy0: ethernet-phy@0 {
				reg = <0>;
			};
		};

		apb@e80000 {
			compatible = "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;

			ranges = <0 0xe80000 0x10000>;
			interrupt-parent = <&aic>;

			gpio0: gpio@400 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x0400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				porta: gpio-port@0 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <0>;
				};
			};

			gpio1: gpio@800 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x0800 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portb: gpio-port@1 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <1>;
				};
			};

			gpio2: gpio@c00 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x0c00 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portc: gpio-port@2 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <2>;
				};
			};

			gpio3: gpio@1000 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x1000 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portd: gpio-port@3 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <3>;
				};
			};

			i2c0: i2c@1400 {
				compatible = "snps,designware-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x1400 0x100>;
				interrupts = <16>;
				clocks = <&chip_clk CLKID_CFG>;
				status = "disabled";
			};

			i2c1: i2c@1800 {
				compatible = "snps,designware-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x1800 0x100>;
				interrupts = <17>;
				clocks = <&chip_clk CLKID_CFG>;
				status = "disabled";
			};

			spi0: spi@1c00 {
				compatible = "snps,dw-apb-ssi";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x1c00 0x100>;
				interrupts = <4>;
				clocks = <&chip_clk CLKID_CFG>;
				status = "disabled";
			};

			wdt4: watchdog@2000 {
				compatible = "snps,dw-wdt";
				reg = <0x2000 0x100>;
				clocks = <&chip_clk CLKID_CFG>;
				interrupts = <5>;
				status = "disabled";
			};

			wdt5: watchdog@2400 {
				compatible = "snps,dw-wdt";
				reg = <0x2400 0x100>;
				clocks = <&chip_clk CLKID_CFG>;
				interrupts = <6>;
				status = "disabled";
			};

			wdt6: watchdog@2800 {
				compatible = "snps,dw-wdt";
				reg = <0x2800 0x100>;
				clocks = <&chip_clk CLKID_CFG>;
				interrupts = <7>;
				status = "disabled";
			};

			timer0: timer@2c00 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c00 0x14>;
				interrupts = <8>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "okay";
			};

			timer1: timer@2c14 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c14 0x14>;
				interrupts = <9>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "okay";
			};

			timer2: timer@2c28 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c28 0x14>;
				interrupts = <10>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer3: timer@2c3c {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c3c 0x14>;
				interrupts = <11>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer4: timer@2c50 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c50 0x14>;
				interrupts = <12>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer5: timer@2c64 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c64 0x14>;
				interrupts = <13>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer6: timer@2c78 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c78 0x14>;
				interrupts = <14>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer7: timer@2c8c {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c8c 0x14>;
				interrupts = <15>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			aic: interrupt-controller@3000 {
				compatible = "snps,dw-apb-ictl";
				reg = <0x3000 0xc00>;
				interrupt-controller;
				#interrupt-cells = <1>;
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		chip: chip-control@ea0000 {
			compatible = "simple-mfd", "syscon";
			reg = <0xea0000 0x400>;

			chip_clk: clock {
				compatible = "marvell,berlin2-clk";
				#clock-cells = <1>;
				clocks = <&refclk>;
				clock-names = "refclk";
			};

			soc_pinctrl: pin-controller {
				compatible = "marvell,berlin2cd-soc-pinctrl";

				uart0_pmux: uart0-pmux {
					groups = "G6";
					function = "uart0";
				};
			};

			chip_rst: reset {
				compatible = "marvell,berlin2-reset";
				#reset-cells = <2>;
			};
		};

		usb0: usb@ed0000 {
			compatible = "chipidea,usb2";
			reg = <0xed0000 0x200>;
			interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&chip_clk CLKID_USB0>;
			phys = <&usb_phy0>;
			phy-names = "usb-phy";
			status = "disabled";
		};

		usb1: usb@ee0000 {
			compatible = "chipidea,usb2";
			reg = <0xee0000 0x200>;
			interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&chip_clk CLKID_USB1>;
			phys = <&usb_phy1>;
			phy-names = "usb-phy";
			status = "disabled";
		};

		pwm: pwm@f20000 {
			compatible = "marvell,berlin-pwm";
			reg = <0xf20000 0x40>;
			clocks = <&chip_clk CLKID_CFG>;
			#pwm-cells = <3>;
		};

		apb@fc0000 {
			compatible = "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;

			ranges = <0 0xfc0000 0x10000>;
			interrupt-parent = <&sic>;

			wdt0: watchdog@1000 {
				compatible = "snps,dw-wdt";
				reg = <0x1000 0x100>;
				clocks = <&refclk>;
				interrupts = <0>;
			};

			wdt1: watchdog@2000 {
				compatible = "snps,dw-wdt";
				reg = <0x2000 0x100>;
				clocks = <&refclk>;
				interrupts = <1>;
				status = "disabled";
			};

			wdt2: watchdog@3000 {
				compatible = "snps,dw-wdt";
				reg = <0x3000 0x100>;
				clocks = <&refclk>;
				interrupts = <2>;
				status = "disabled";
			};

			sm_gpio1: gpio@5000 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x5000 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portf: gpio-port@5 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
				};
			};

			spi1: spi@6000 {
				compatible = "snps,dw-apb-ssi";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x6000 0x100>;
				clocks = <&refclk>;
				interrupts = <5>;
				status = "disabled";
			};

			i2c2: i2c@7000 {
				compatible = "snps,designware-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x7000 0x100>;
				interrupts = <6>;
				clocks = <&refclk>;
				status = "disabled";
			};

			i2c3: i2c@8000 {
				compatible = "snps,designware-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x8000 0x100>;
				interrupts = <7>;
				clocks = <&refclk>;
				status = "disabled";
			};

			sm_gpio0: gpio@c000 {
				compatible = "snps,dw-apb-gpio";
				reg = <0xc000 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				porte: gpio-port@4 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
				};
			};

			uart0: serial@9000 {
				compatible = "snps,dw-apb-uart";
				reg = <0x9000 0x100>;
				reg-shift = <2>;
				reg-io-width = <1>;
				interrupts = <8>;
				clocks = <&refclk>;
				pinctrl-0 = <&uart0_pmux>;
				pinctrl-names = "default";
				status = "disabled";
			};

			uart1: serial@a000 {
				compatible = "snps,dw-apb-uart";
				reg = <0xa000 0x100>;
				reg-shift = <2>;
				reg-io-width = <1>;
				interrupts = <9>;
				clocks = <&refclk>;
				status = "disabled";
			};

			uart2: serial@b000 {
				compatible = "snps,dw-apb-uart";
				reg = <0xb000 0x100>;
				reg-shift = <2>;
				reg-io-width = <1>;
				interrupts = <10>;
				clocks = <&refclk>;
				status = "disabled";
			};

			sysctrl: system-controller@d000 {
				compatible = "simple-mfd", "syscon";
				reg = <0xd000 0x100>;

				sys_pinctrl: pin-controller {
					compatible = "marvell,berlin2cd-system-pinctrl";
				};

				adc: adc {
					compatible = "marvell,berlin2-adc";
					interrupts = <12>, <14>;
					interrupt-names = "adc", "tsen";
				};
			};

			sic: interrupt-controller@e000 {
				compatible = "snps,dw-apb-ictl";
				reg = <0xe000 0x400>;
				interrupt-controller;
				#interrupt-cells = <1>;
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>;
			};
		};
	};
};
