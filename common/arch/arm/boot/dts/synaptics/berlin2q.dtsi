// SPDX-License-Identifier: (GPL-2.0 OR MIT)
/*
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 */

#include <dt-bindings/clock/berlin2q.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	model = "Marvell Armada 1500 pro (BG2-Q) SoC";
	compatible = "marvell,berlin2q", "marvell,berlin";
	#address-cells = <1>;
	#size-cells = <1>;

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		enable-method = "marvell,berlin-smp";

		cpu0: cpu@0 {
			compatible = "arm,cortex-a9";
			device_type = "cpu";
			next-level-cache = <&l2>;
			reg = <0>;

			clocks = <&chip_clk CLKID_CPU>;
			clock-latency = <100000>;
			/* Can be modified by the bootloader */
			operating-points = <
				/* kHz    uV */
				1200000 1200000
				1000000 1200000
				800000  1200000
				600000  1200000
			>;
		};

		cpu1: cpu@1 {
			compatible = "arm,cortex-a9";
			device_type = "cpu";
			next-level-cache = <&l2>;
			reg = <1>;

			clocks = <&chip_clk CLKID_CPU>;
			clock-latency = <100000>;
			/* Can be modified by the bootloader */
			operating-points = <
				/* kHz    uV */
				1200000 1200000
				1000000 1200000
				800000  1200000
				600000  1200000
			>;
		};

		cpu2: cpu@2 {
			compatible = "arm,cortex-a9";
			device_type = "cpu";
			next-level-cache = <&l2>;
			reg = <2>;

			clocks = <&chip_clk CLKID_CPU>;
			clock-latency = <100000>;
			/* Can be modified by the bootloader */
			operating-points = <
				/* kHz    uV */
				1200000 1200000
				1000000 1200000
				800000  1200000
				600000  1200000
			>;
		};

		cpu3: cpu@3 {
			compatible = "arm,cortex-a9";
			device_type = "cpu";
			next-level-cache = <&l2>;
			reg = <3>;

			clocks = <&chip_clk CLKID_CPU>;
			clock-latency = <100000>;
			/* Can be modified by the bootloader */
			operating-points = <
				/* kHz    uV */
				1200000 1200000
				1000000 1200000
				800000  1200000
				600000  1200000
			>;
		};
	};

	pmu {
		compatible = "arm,cortex-a9-pmu";
		interrupt-parent = <&gic>;
		interrupts = <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>,
				     <&cpu1>,
				     <&cpu2>,
				     <&cpu3>;
	};

	refclk: oscillator {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <25000000>;
	};

	soc@f7000000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;

		ranges = <0 0xf7000000 0x1000000>;
		interrupt-parent = <&gic>;

		sdhci0: mmc@ab0000 {
			compatible = "mrvl,pxav3-mmc";
			reg = <0xab0000 0x200>;
			clocks = <&chip_clk CLKID_SDIO1XIN>, <&chip_clk CLKID_SDIO>;
			clock-names = "io", "core";
			interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		sdhci1: mmc@ab0800 {
			compatible = "mrvl,pxav3-mmc";
			reg = <0xab0800 0x200>;
			clocks = <&chip_clk CLKID_SDIO1XIN>, <&chip_clk CLKID_SDIO>;
			clock-names = "io", "core";
			interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		sdhci2: mmc@ab1000 {
			compatible = "mrvl,pxav3-mmc";
			reg = <0xab1000 0x200>;
			interrupts = <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&chip_clk CLKID_NFC_ECC>, <&chip_clk CLKID_SDIO>;
			clock-names = "io", "core";
			status = "disabled";
		};

		l2: cache-controller@ac0000 {
			compatible = "arm,pl310-cache";
			reg = <0xac0000 0x1000>;
			cache-unified;
			cache-level = <2>;
			arm,data-latency = <2 2 2>;
			arm,tag-latency = <2 2 2>;
		};

		scu: snoop-control-unit@ad0000 {
			compatible = "arm,cortex-a9-scu";
			reg = <0xad0000 0x58>;
		};

		local-timer@ad0600 {
			compatible = "arm,cortex-a9-twd-timer";
			reg = <0xad0600 0x20>;
			clocks = <&chip_clk CLKID_TWD>;
			interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_EDGE_RISING)>;
		};

		gic: interrupt-controller@ad1000 {
			compatible = "arm,cortex-a9-gic";
			reg = <0xad1000 0x1000>, <0xad0100 0x100>;
			interrupt-controller;
			#interrupt-cells = <3>;
		};

		usb_phy2: phy@a2f400 {
			compatible = "marvell,berlin2cd-usb-phy";
			reg = <0xa2f400 0x128>;
			#phy-cells = <0>;
			resets = <&chip_rst 0x104 14>;
			status = "disabled";
		};

		usb2: usb@a30000 {
			compatible = "chipidea,usb2";
			reg = <0xa30000 0x10000>;
			interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&chip_clk CLKID_USB2>;
			phys = <&usb_phy2>;
			phy-names = "usb-phy";
			status = "disabled";
		};

		usb_phy0: phy@b74000 {
			compatible = "marvell,berlin2cd-usb-phy";
			reg = <0xb74000 0x128>;
			#phy-cells = <0>;
			resets = <&chip_rst 0x104 12>;
			status = "disabled";
		};

		usb_phy1: phy@b78000 {
			compatible = "marvell,berlin2cd-usb-phy";
			reg = <0xb78000 0x128>;
			#phy-cells = <0>;
			resets = <&chip_rst 0x104 13>;
			status = "disabled";
		};

		eth0: ethernet@b90000 {
			compatible = "marvell,pxa168-eth";
			reg = <0xb90000 0x10000>;
			clocks = <&chip_clk CLKID_GETH0>;
			interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
			/* set by bootloader */
			local-mac-address = [00 00 00 00 00 00];
			#address-cells = <1>;
			#size-cells = <0>;
			phy-connection-type = "mii";
			phy-handle = <&ethphy0>;
			status = "disabled";

			ethphy0: ethernet-phy@0 {
				reg = <0>;
			};
		};

		cpu-ctrl@dd0000 {
			compatible = "marvell,berlin-cpu-ctrl";
			reg = <0xdd0000 0x10000>;
		};

		apb@e80000 {
			compatible = "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;

			ranges = <0 0xe80000 0x10000>;
			interrupt-parent = <&aic>;

			gpio0: gpio@400 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x0400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				porta: gpio-port@0 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <32>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <0>;
				};
			};

			gpio1: gpio@800 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x0800 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portb: gpio-port@1 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <32>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <1>;
				};
			};

			gpio2: gpio@c00 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x0c00 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portc: gpio-port@2 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <32>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <2>;
				};
			};

			gpio3: gpio@1000 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x1000 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portd: gpio-port@3 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <32>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <3>;
				};
			};

			i2c0: i2c@1400 {
				compatible = "snps,designware-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x1400 0x100>;
				interrupts = <4>;
				clocks = <&chip_clk CLKID_CFG>;
				pinctrl-0 = <&twsi0_pmux>;
				pinctrl-names = "default";
				status = "disabled";
			};

			i2c1: i2c@1800 {
				compatible = "snps,designware-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x1800 0x100>;
				interrupts = <5>;
				clocks = <&chip_clk CLKID_CFG>;
				pinctrl-0 = <&twsi1_pmux>;
				pinctrl-names = "default";
				status = "disabled";
			};

			timer0: timer@2c00 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c00 0x14>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				interrupts = <8>;
			};

			timer1: timer@2c14 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c14 0x14>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
			};

			timer2: timer@2c28 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c28 0x14>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer3: timer@2c3c {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c3c 0x14>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer4: timer@2c50 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c50 0x14>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer5: timer@2c64 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c64 0x14>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer6: timer@2c78 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c78 0x14>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer7: timer@2c8c {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c8c 0x14>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			aic: interrupt-controller@3800 {
				compatible = "snps,dw-apb-ictl";
				reg = <0x3800 0x30>;
				interrupt-controller;
				#interrupt-cells = <1>;
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		chip: chip-control@ea0000 {
			compatible = "simple-mfd", "syscon";
			reg = <0xea0000 0x400>, <0xdd0170 0x10>;

			chip_clk: clock {
				compatible = "marvell,berlin2q-clk";
				#clock-cells = <1>;
				clocks = <&refclk>;
				clock-names = "refclk";
			};

			soc_pinctrl: pin-controller {
				compatible = "marvell,berlin2q-soc-pinctrl";

				sd1_pmux: sd1-pmux {
					groups = "G31";
					function = "sd1";
				};

				twsi0_pmux: twsi0-pmux {
					groups = "G6";
					function = "twsi0";
				};

				twsi1_pmux: twsi1-pmux {
					groups = "G7";
					function = "twsi1";
				};
			};

			chip_rst: reset {
				compatible = "marvell,berlin2-reset";
				#reset-cells = <2>;
			};
		};

		ahci: sata@e90000 {
			compatible = "marvell,berlin2q-ahci", "generic-ahci";
			reg = <0xe90000 0x1000>;
			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&chip_clk CLKID_SATA>;
			#address-cells = <1>;
			#size-cells = <0>;

			sata0: sata-port@0 {
				reg = <0>;
				phys = <&sata_phy 0>;
				status = "disabled";
			};

			sata1: sata-port@1 {
				reg = <1>;
				phys = <&sata_phy 1>;
				status = "disabled";
			};
		};

		sata_phy: phy@e900a0 {
			compatible = "marvell,berlin2q-sata-phy";
			reg = <0xe900a0 0x200>;
			clocks = <&chip_clk CLKID_SATA>;
			#address-cells = <1>;
			#size-cells = <0>;
			#phy-cells = <1>;
			status = "disabled";

			sata-phy@0 {
				reg = <0>;
			};

			sata-phy@1 {
				reg = <1>;
			};
		};

		usb0: usb@ed0000 {
			compatible = "chipidea,usb2";
			reg = <0xed0000 0x10000>;
			interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&chip_clk CLKID_USB0>;
			phys = <&usb_phy0>;
			phy-names = "usb-phy";
			status = "disabled";
		};

		usb1: usb@ee0000 {
			compatible = "chipidea,usb2";
			reg = <0xee0000 0x10000>;
			interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&chip_clk CLKID_USB1>;
			phys = <&usb_phy1>;
			phy-names = "usb-phy";
			status = "disabled";
		};

		pwm: pwm@f20000 {
			compatible = "marvell,berlin-pwm";
			reg = <0xf20000 0x40>;
			clocks = <&chip_clk CLKID_CFG>;
			#pwm-cells = <3>;
		};

		apb@fc0000 {
			compatible = "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;

			ranges = <0 0xfc0000 0x10000>;
			interrupt-parent = <&sic>;

			wdt0: watchdog@1000 {
				compatible = "snps,dw-wdt";
				reg = <0x1000 0x100>;
				clocks = <&refclk>;
				interrupts = <0>;
			};

			wdt1: watchdog@2000 {
				compatible = "snps,dw-wdt";
				reg = <0x2000 0x100>;
				clocks = <&refclk>;
				interrupts = <1>;
			};

			wdt2: watchdog@3000 {
				compatible = "snps,dw-wdt";
				reg = <0x3000 0x100>;
				clocks = <&refclk>;
				interrupts = <2>;
			};

			sm_gpio1: gpio@5000 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x5000 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portf: gpio-port@5 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <32>;
					reg = <0>;
				};
			};

			i2c2: i2c@7000 {
				compatible = "snps,designware-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x7000 0x100>;
				interrupts = <6>;
				clocks = <&refclk>;
				pinctrl-0 = <&twsi2_pmux>;
				pinctrl-names = "default";
				status = "disabled";
			};

			i2c3: i2c@8000 {
				compatible = "snps,designware-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x8000 0x100>;
				interrupts = <7>;
				clocks = <&refclk>;
				pinctrl-0 = <&twsi3_pmux>;
				pinctrl-names = "default";
				status = "disabled";
			};

			uart0: serial@9000 {
				compatible = "snps,dw-apb-uart";
				reg = <0x9000 0x100>;
				interrupts = <8>;
				clocks = <&refclk>;
				reg-shift = <2>;
				pinctrl-0 = <&uart0_pmux>;
				pinctrl-names = "default";
				status = "disabled";
			};

			uart1: serial@a000 {
				compatible = "snps,dw-apb-uart";
				reg = <0xa000 0x100>;
				interrupts = <9>;
				clocks = <&refclk>;
				reg-shift = <2>;
				pinctrl-0 = <&uart1_pmux>;
				pinctrl-names = "default";
				status = "disabled";
			};

			sm_gpio0: gpio@c000 {
				compatible = "snps,dw-apb-gpio";
				reg = <0xc000 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				porte: gpio-port@4 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <32>;
					reg = <0>;
				};
			};

			sysctrl: pin-controller@d000 {
				compatible = "simple-mfd", "syscon";
				reg = <0xd000 0x100>;

				sys_pinctrl: pin-controller {
					compatible = "marvell,berlin2q-system-pinctrl";

					uart0_pmux: uart0-pmux {
						groups = "GSM12";
						function = "uart0";
					};

					uart1_pmux: uart1-pmux {
						groups = "GSM14";
						function = "uart1";
					};

					twsi2_pmux: twsi2-pmux {
						groups = "GSM13";
						function = "twsi2";
					};

					twsi3_pmux: twsi3-pmux {
						groups = "GSM14";
						function = "twsi3";
					};
				};

				adc: adc {
					compatible = "marvell,berlin2-adc";
					interrupts = <12>, <14>;
					interrupt-names = "adc", "tsen";
				};
			};

			sic: interrupt-controller@e000 {
				compatible = "snps,dw-apb-ictl";
				reg = <0xe000 0x30>;
				interrupt-controller;
				#interrupt-cells = <1>;
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>;
			};
		};
	};
};
