// SPDX-License-Identifier: (GPL-2.0 OR MIT)
/*
 * Device Tree Include file for Marvell Armada 1500 (Berlin BG2) SoC
 *
 * <PERSON> <<EMAIL>>
 *
 * based on GPL'ed 2.6 kernel sources
 *  (c) Marvell International Ltd.
 */

#include <dt-bindings/clock/berlin2.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	model = "Marvell Armada 1500 (BG2) SoC";
	compatible = "marvell,berlin2", "marvell,berlin";
	#address-cells = <1>;
	#size-cells = <1>;

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
		serial2 = &uart2;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		enable-method = "marvell,berlin-smp";

		cpu@0 {
			compatible = "marvell,pj4b";
			device_type = "cpu";
			next-level-cache = <&l2>;
			reg = <0>;

			clocks = <&chip_clk CLKID_CPU>;
			clock-latency = <100000>;
			operating-points = <
				/* kHz    uV */
				1200000 1200000
				1000000 1200000
				800000  1200000
				600000  1200000
			>;
		};

		cpu@1 {
			compatible = "marvell,pj4b";
			device_type = "cpu";
			next-level-cache = <&l2>;
			reg = <1>;

			clocks = <&chip_clk CLKID_CPU>;
			clock-latency = <100000>;
			operating-points = <
				/* kHz    uV */
				1200000 1200000
				1000000 1200000
				800000  1200000
				600000  1200000
			>;
		};
	};

	refclk: oscillator {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <25000000>;
	};

	soc@f7000000 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		interrupt-parent = <&gic>;

		ranges = <0 0xf7000000 0x1000000>;

		sdhci0: mmc@ab0000 {
			compatible = "mrvl,pxav3-mmc";
			reg = <0xab0000 0x200>;
			clocks = <&chip_clk CLKID_SDIO0XIN>, <&chip_clk CLKID_SDIO0>;
			clock-names = "io", "core";
			interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		sdhci1: mmc@ab0800 {
			compatible = "mrvl,pxav3-mmc";
			reg = <0xab0800 0x200>;
			clocks = <&chip_clk CLKID_SDIO1XIN>, <&chip_clk CLKID_SDIO1>;
			clock-names = "io", "core";
			interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		sdhci2: mmc@ab1000 {
			compatible = "mrvl,pxav3-mmc";
			reg = <0xab1000 0x200>;
			interrupts = <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&chip_clk CLKID_NFC_ECC>, <&chip_clk CLKID_NFC>;
			clock-names = "io", "core";
			pinctrl-0 = <&emmc_pmux>;
			pinctrl-names = "default";
			status = "disabled";
		};

		l2: cache-controller@ac0000 {
			compatible = "marvell,tauros3-cache", "arm,pl310-cache";
			reg = <0xac0000 0x1000>;
			cache-unified;
			cache-level = <2>;
		};

		scu: snoop-control-unit@ad0000 {
			compatible = "arm,cortex-a9-scu";
			reg = <0xad0000 0x58>;
		};

		gic: interrupt-controller@ad1000 {
			compatible = "arm,cortex-a9-gic";
			reg = <0xad1000 0x1000>, <0xad0100 0x0100>;
			interrupt-controller;
			#interrupt-cells = <3>;
		};

		local-timer@ad0600 {
			compatible = "arm,cortex-a9-twd-timer";
			reg = <0xad0600 0x20>;
			interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(2) | IRQ_TYPE_EDGE_RISING)>;
			clocks = <&chip_clk CLKID_TWD>;
		};

		eth1: ethernet@b90000 {
			compatible = "marvell,pxa168-eth";
			reg = <0xb90000 0x10000>;
			clocks = <&chip_clk CLKID_GETH1>;
			interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
			/* set by bootloader */
			local-mac-address = [00 00 00 00 00 00];
			#address-cells = <1>;
			#size-cells = <0>;
			phy-connection-type = "mii";
			phy-handle = <&ethphy1>;
			status = "disabled";

			ethphy1: ethernet-phy@0 {
				reg = <0>;
			};
		};

		cpu-ctrl@dd0000 {
			compatible = "marvell,berlin-cpu-ctrl";
			reg = <0xdd0000 0x10000>;
		};

		eth0: ethernet@e50000 {
			compatible = "marvell,pxa168-eth";
			reg = <0xe50000 0x10000>;
			clocks = <&chip_clk CLKID_GETH0>;
			interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
			/* set by bootloader */
			local-mac-address = [00 00 00 00 00 00];
			#address-cells = <1>;
			#size-cells = <0>;
			phy-connection-type = "mii";
			phy-handle = <&ethphy0>;
			status = "disabled";

			ethphy0: ethernet-phy@0 {
				reg = <0>;
			};
		};

		apb@e80000 {
			compatible = "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;

			ranges = <0 0xe80000 0x10000>;
			interrupt-parent = <&aic>;

			gpio0: gpio@400 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x0400 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				porta: gpio-port@0 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <0>;
				};
			};

			gpio1: gpio@800 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x0800 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portb: gpio-port@1 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <1>;
				};
			};

			gpio2: gpio@c00 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x0c00 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portc: gpio-port@2 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <2>;
				};
			};

			gpio3: gpio@1000 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x1000 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portd: gpio-port@3 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <3>;
				};
			};

			timer0: timer@2c00 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c00 0x14>;
				interrupts = <8>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "okay";
			};

			timer1: timer@2c14 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c14 0x14>;
				interrupts = <9>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "okay";
			};

			timer2: timer@2c28 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c28 0x14>;
				interrupts = <10>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer3: timer@2c3c {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c3c 0x14>;
				interrupts = <11>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer4: timer@2c50 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c50 0x14>;
				interrupts = <12>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer5: timer@2c64 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c64 0x14>;
				interrupts = <13>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer6: timer@2c78 {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c78 0x14>;
				interrupts = <14>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			timer7: timer@2c8c {
				compatible = "snps,dw-apb-timer";
				reg = <0x2c8c 0x14>;
				interrupts = <15>;
				clocks = <&chip_clk CLKID_CFG>;
				clock-names = "timer";
				status = "disabled";
			};

			aic: interrupt-controller@3000 {
				compatible = "snps,dw-apb-ictl";
				reg = <0x3000 0xc00>;
				interrupt-controller;
				#interrupt-cells = <1>;
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		ahci: sata@e90000 {
			compatible = "marvell,berlin2-ahci", "generic-ahci";
			reg = <0xe90000 0x1000>;
			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&chip_clk CLKID_SATA>;
			#address-cells = <1>;
			#size-cells = <0>;

			sata0: sata-port@0 {
				reg = <0>;
				phys = <&sata_phy 0>;
				status = "disabled";
			};

			sata1: sata-port@1 {
				reg = <1>;
				phys = <&sata_phy 1>;
				status = "disabled";
			};
		};

		sata_phy: phy@e900a0 {
			compatible = "marvell,berlin2-sata-phy";
			reg = <0xe900a0 0x200>;
			clocks = <&chip_clk CLKID_SATA>;
			#address-cells = <1>;
			#size-cells = <0>;
			#phy-cells = <1>;
			status = "disabled";

			sata-phy@0 {
				reg = <0>;
			};

			sata-phy@1 {
				reg = <1>;
			};
		};

		chip: chip-control@ea0000 {
			compatible = "simple-mfd", "syscon";
			reg = <0xea0000 0x400>;

			chip_clk: clock {
				compatible = "marvell,berlin2-clk";
				#clock-cells = <1>;
				clocks = <&refclk>;
				clock-names = "refclk";
			};

			soc_pinctrl: pin-controller {
				compatible = "marvell,berlin2-soc-pinctrl";

				emmc_pmux: emmc-pmux {
					groups = "G26";
					function = "emmc";
				};
			};

			chip_rst: reset {
				compatible = "marvell,berlin2-reset";
				#reset-cells = <2>;
			};
		};

		pwm: pwm@f20000 {
			compatible = "marvell,berlin-pwm";
			reg = <0xf20000 0x40>;
			clocks = <&chip_clk CLKID_CFG>;
			#pwm-cells = <3>;
		};

		apb@fc0000 {
			compatible = "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;

			ranges = <0 0xfc0000 0x10000>;
			interrupt-parent = <&sic>;

			wdt0: watchdog@1000 {
				compatible = "snps,dw-wdt";
				reg = <0x1000 0x100>;
				clocks = <&refclk>;
				interrupts = <0>;
			};

			wdt1: watchdog@2000 {
				compatible = "snps,dw-wdt";
				reg = <0x2000 0x100>;
				clocks = <&refclk>;
				interrupts = <1>;
			};

			wdt2: watchdog@3000 {
				compatible = "snps,dw-wdt";
				reg = <0x3000 0x100>;
				clocks = <&refclk>;
				interrupts = <2>;
			};

			sm_gpio1: gpio@5000 {
				compatible = "snps,dw-apb-gpio";
				reg = <0x5000 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				portf: gpio-port@5 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
				};
			};

			sm_gpio0: gpio@c000 {
				compatible = "snps,dw-apb-gpio";
				reg = <0xc000 0x400>;
				#address-cells = <1>;
				#size-cells = <0>;

				porte: gpio-port@4 {
					compatible = "snps,dw-apb-gpio-port";
					gpio-controller;
					#gpio-cells = <2>;
					ngpios = <8>;
					reg = <0>;
					interrupt-controller;
					#interrupt-cells = <2>;
					interrupts = <11>;
				};
			};

			uart0: serial@9000 {
				compatible = "snps,dw-apb-uart";
				reg = <0x9000 0x100>;
				reg-shift = <2>;
				reg-io-width = <1>;
				interrupts = <8>;
				clocks = <&refclk>;
				pinctrl-0 = <&uart0_pmux>;
				pinctrl-names = "default";
				status = "disabled";
			};

			uart1: serial@a000 {
				compatible = "snps,dw-apb-uart";
				reg = <0xa000 0x100>;
				reg-shift = <2>;
				reg-io-width = <1>;
				interrupts = <9>;
				clocks = <&refclk>;
				pinctrl-0 = <&uart1_pmux>;
				pinctrl-names = "default";
				status = "disabled";
			};

			uart2: serial@b000 {
				compatible = "snps,dw-apb-uart";
				reg = <0xb000 0x100>;
				reg-shift = <2>;
				reg-io-width = <1>;
				interrupts = <10>;
				clocks = <&refclk>;
				pinctrl-0 = <&uart2_pmux>;
				pinctrl-names = "default";
				status = "disabled";
			};

			sysctrl: system-controller@d000 {
				compatible = "simple-mfd", "syscon";
				reg = <0xd000 0x100>;

				sys_pinctrl: pin-controller {
					compatible = "marvell,berlin2-system-pinctrl";
					uart0_pmux: uart0-pmux {
						groups = "GSM4";
						function = "uart0";
					};

					uart1_pmux: uart1-pmux {
						groups = "GSM5";
						function = "uart1";
					};
					uart2_pmux: uart2-pmux {
						groups = "GSM3";
						function = "uart2";
					};
				};
			};

			sic: interrupt-controller@e000 {
				compatible = "snps,dw-apb-ictl";
				reg = <0xe000 0x400>;
				interrupt-controller;
				#interrupt-cells = <1>;
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>;
			};
		};
	};
};
