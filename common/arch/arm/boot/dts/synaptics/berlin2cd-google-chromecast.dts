// SPDX-License-Identifier: (GPL-2.0 OR MIT)
/*
 * Device Tree file for Google Chromecast
 *
 * <PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "berlin2cd.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Google Chromecast";
	compatible = "google,chromecast", "marvell,berlin2cd", "marvell,berlin";

	chosen {
		bootargs = "earlyprintk";
		stdout-path = "serial0:115200n8";
	};

	memory@0 {
		device_type = "memory";

		/*
		 * We're using "linux,usable-memory" instead of "reg" here
		 * because the (signed and encrypted) bootloader that shipped
		 * with this device provides an incorrect memory range in
		 * ATAG_MEM. Linux helpfully overrides the "reg" property with
		 * data from the ATAG, so we can't specify the proper range
		 * normally. Fortunately, this alternate property is checked
		 * first by the OF driver, so we can (ab)use it instead.
		 */
		linux,usable-memory = <0x00000000 0x20000000>; /* 512 MB */
	};

	led-controller {
		compatible = "pwm-leds";
		pinctrl-0 = <&ledpwm_pmux>;
		pinctrl-names = "default";

		led-1 {
			label = "white";
			pwms = <&pwm 0 600000 0>;
			max-brightness = <255>;
			linux,default-trigger = "default-on";
		};

		led-2 {
			label = "red";
			pwms = <&pwm 1 600000 0>;
			max-brightness = <255>;
		};
	};
};

/*
 * AzureWave AW-NH387 (Marvell 88W8787)
 * 802.11b/g/n + Bluetooth 2.1
 */
&sdhci0 {
	non-removable;
	status = "okay";
};

&uart0 { status = "okay"; };

&usb_phy1 { status = "okay"; };

&usb1 { status = "okay"; };

&soc_pinctrl {
	ledpwm_pmux: ledpwm-pmux {
		groups = "G0";
		function = "pwm";
	};
};
