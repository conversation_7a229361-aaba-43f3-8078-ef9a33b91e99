// SPDX-License-Identifier: (GPL-2.0 OR MIT)
/*
 * Copyright 2018 <PERSON> <am<PERSON><EMAIL>>
 */
/dts-v1/;

#include "berlin2cd.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Valve Steam Link";
	compatible = "valve,steamlink", "marvell,berlin2cd", "marvell,berlin";

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x20000000>; /* 512 MB */
	};

	gpio-restart {
		compatible = "gpio-restart";
		gpios = <&porta 6 GPIO_ACTIVE_HIGH>;
		active-delay = <100>;
		inactive-delay = <10>;
		wait-delay = <100>;
		priority = <200>;
	};
};

&cpu {
	cpu-supply = <&vcpu>;
	operating-points = <
		/* kHz    uV */
		1000000 1325000
	>;
};

&i2c0 {
	status = "okay";

	/* There are two regulators on the board. One is accessible via I2C,
	 * with buck1 providing SoC power (set up by bootloader to 1.325V or
	 * less depending on leakage value in OTP), and buck2 likely used for
	 * DRAM (providing 1.35V). The other regulator on the opposite side
	 * of the board is probably supplying SDIO and NAND fixed voltages. */
	regulator@19 {
		compatible = "marvell,88pg868";
		reg = <0x19>;

		vcpu: buck1 {
			regulator-boot-on;
			regulator-always-on;
			regulator-min-microvolt = <1000000>;
			regulator-max-microvolt = <1325000>;
		};
	};
};

/* Fixed interface to on-board Marvell 8897 Wi-Fi/Bluetooth/NFC chip. */
&sdhci0 {
	keep-power-in-suspend;
	non-removable;
	status = "okay";
};

&uart0 {
	/* RX/TX are routed to TP50/TP51 on the board. */
	status = "okay";
};

/* The SoC is connected to on-board USB hub that in turn has one downstream
 * port wired to the on-board Steam Controller wireless receiver chip. */
&usb_phy1 { status = "okay"; };

&usb1 {
	dr_mode = "host";
	status = "okay";
};

&eth1 { status = "okay"; };
