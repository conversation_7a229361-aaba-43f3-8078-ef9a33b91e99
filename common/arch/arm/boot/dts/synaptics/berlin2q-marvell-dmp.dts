// SPDX-License-Identifier: (GPL-2.0 OR MIT)
/*
 * Copyright (C) 2014 <PERSON> <<EMAIL>>
 */

/dts-v1/;

#include <dt-bindings/gpio/gpio.h>
#include "berlin2q.dtsi"

/ {
	model = "Marvell BG2-Q DMP";
	compatible = "marvell,berlin2q-dmp", "marvell,berlin2q", "marvell,berlin";

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x80000000>;
	};

	chosen {
		bootargs = "earlyprintk";
		stdout-path = "serial0:115200n8";
	};

	regulators {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <0>;

		reg_usb0_vbus: regulator_usb0 {
			compatible = "regulator-fixed";
			regulator-name = "usb0_vbus";
			regulator-min-microvolt = <5000000>;
			regulator-max-microvolt = <5000000>;
			gpio = <&portb 8 GPIO_ACTIVE_HIGH>;
			enable-active-high;
		};

		reg_usb1_vbus: regulator_usb1 {
			compatible = "regulator-fixed";
			regulator-name = "usb1_vbus";
			regulator-min-microvolt = <5000000>;
			regulator-max-microvolt = <5000000>;
			gpio = <&portb 10 GPIO_ACTIVE_HIGH>;
			enable-active-high;
		};

		reg_usb2_vbus: regulator_usb2 {
			compatible = "regulator-fixed";
			regulator-name = "usb2_vbus";
			regulator-min-microvolt = <5000000>;
			regulator-max-microvolt = <5000000>;
			gpio = <&portb 12 GPIO_ACTIVE_HIGH>;
			enable-active-high;
		};

		reg_sdio1_vmmc: regulator_sdio1_vmmc {
			compatible = "regulator-fixed";
			regulator-min-microvolt = <3300000>;
			regulator-max-microvolt = <3300000>;
			regulator-name = "sdio1_vmmc";
			enable-active-high;
			regulator-boot-on;
			gpio = <&portb 21 GPIO_ACTIVE_HIGH>;
		};

		reg_sdio1_vqmmc: regulator_sido1_vqmmc {
			compatible = "regulator-gpio";
			regulator-min-microvolt = <1800000>;
			regulator-max-microvolt = <3300000>;
			regulator-name = "sdio1_vqmmc";
			regulator-type = "voltage";
			enable-active-high;
			gpios = <&portb 16 GPIO_ACTIVE_HIGH>;
			states = <3300000 0x1
				  1800000 0x0>;
		};
	};
};

&soc_pinctrl {
	sd1gpio_pmux: sd1pwr-pmux {
		groups = "G23", "G32";
		function = "gpio";
	};
};

&sdhci1 {
	vmmc-supply = <&reg_sdio1_vmmc>;
	vqmmc-supply = <&reg_sdio1_vqmmc>;
	cd-gpios = <&portc 30 GPIO_ACTIVE_LOW>;
	wp-gpios = <&portd 0 GPIO_ACTIVE_HIGH>;
	pinctrl-0 = <&sd1gpio_pmux>, <&sd1_pmux>;
	pinctrl-names = "default";
	status = "okay";
};

&sdhci2 {
	bus-width = <8>;
	non-removable;
	status = "okay";
};

&i2c0 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&uart0 {
	status = "okay";
};

&usb_phy0 {
	status = "okay";
};

&usb_phy2 {
	status = "okay";
};

&usb0 {
	vbus-supply = <&reg_usb0_vbus>;
	status = "okay";
};

&usb2 {
	vbus-supply = <&reg_usb2_vbus>;
	status = "okay";
};

&eth0 {
	status = "okay";
};

&sata0 {
	status = "okay";
};

&sata_phy {
	status = "okay";
};
