// SPDX-License-Identifier: (GPL-2.0 OR MIT)
/*
 * Device Tree file for Sony NSZ-GS7
 *
 * <PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "berlin2.dtsi"

/ {
	model = "Sony NSZ-GS7";
	compatible = "sony,nsz-gs7", "marvell,berlin2", "marvell,berlin";

	chosen {
		bootargs = "earlyprintk";
		stdout-path = "serial0:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x40000000>; /* 1 GB */
	};
};

&ahci { status = "okay"; };

&eth1 { status = "okay"; };

/* Unpopulated SATA plug on solder side */
&sata0 { status = "okay"; };

&sata_phy { status = "okay"; };

/* Samsung M8G2FA 8GB eMMC */
&sdhci2 {
	non-removable;
	bus-width = <8>;
	status = "okay";
};

&uart0 { status = "okay"; };
